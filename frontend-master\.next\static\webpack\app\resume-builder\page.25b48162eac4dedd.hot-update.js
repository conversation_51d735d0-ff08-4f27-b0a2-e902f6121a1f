"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/resume-builder/page",{

/***/ "(app-pages-browser)/./src/components/resume/ResumeBuilder.jsx":
/*!*************************************************!*\
  !*** ./src/components/resume/ResumeBuilder.jsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Briefcase,CheckCircle,Clock,Download,Eye,FileText,GraduationCap,Lightbulb,RotateCcw,Save,Sparkles,Target,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Briefcase,CheckCircle,Clock,Download,Eye,FileText,GraduationCap,Lightbulb,RotateCcw,Save,Sparkles,Target,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/graduation-cap.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Briefcase,CheckCircle,Clock,Download,Eye,FileText,GraduationCap,Lightbulb,RotateCcw,Save,Sparkles,Target,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Briefcase,CheckCircle,Clock,Download,Eye,FileText,GraduationCap,Lightbulb,RotateCcw,Save,Sparkles,Target,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Briefcase,CheckCircle,Clock,Download,Eye,FileText,GraduationCap,Lightbulb,RotateCcw,Save,Sparkles,Target,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Briefcase,CheckCircle,Clock,Download,Eye,FileText,GraduationCap,Lightbulb,RotateCcw,Save,Sparkles,Target,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Briefcase,CheckCircle,Clock,Download,Eye,FileText,GraduationCap,Lightbulb,RotateCcw,Save,Sparkles,Target,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _StepNavigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./StepNavigation */ \"(app-pages-browser)/./src/components/resume/StepNavigation.jsx\");\n/* harmony import */ var _layout_SmartNavigationBar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../layout/SmartNavigationBar */ \"(app-pages-browser)/./src/components/layout/SmartNavigationBar.jsx\");\n/* harmony import */ var _forms_ResumeFormComponents__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./forms/ResumeFormComponents */ \"(app-pages-browser)/./src/components/resume/forms/ResumeFormComponents.jsx\");\n/* harmony import */ var _forms_ExperienceForm__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./forms/ExperienceForm */ \"(app-pages-browser)/./src/components/resume/forms/ExperienceForm.jsx\");\n/* harmony import */ var _forms_SkillsProjectsForm__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./forms/SkillsProjectsForm */ \"(app-pages-browser)/./src/components/resume/forms/SkillsProjectsForm.jsx\");\n/* harmony import */ var _forms_ProjectsForm__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./forms/ProjectsForm */ \"(app-pages-browser)/./src/components/resume/forms/ProjectsForm.jsx\");\n/* harmony import */ var _forms_SkillsForm__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./forms/SkillsForm */ \"(app-pages-browser)/./src/components/resume/forms/SkillsForm.jsx\");\n/* harmony import */ var _forms_ReviewForm__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./forms/ReviewForm */ \"(app-pages-browser)/./src/components/resume/forms/ReviewForm.jsx\");\n/* harmony import */ var _EnhancedProgressIndicator__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./EnhancedProgressIndicator */ \"(app-pages-browser)/./src/components/resume/EnhancedProgressIndicator.jsx\");\n/* harmony import */ var _EnhancedNavigation__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./EnhancedNavigation */ \"(app-pages-browser)/./src/components/resume/EnhancedNavigation.jsx\");\n/* harmony import */ var _common_ProgressBar__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../common/ProgressBar */ \"(app-pages-browser)/./src/components/common/ProgressBar.jsx\");\n/* harmony import */ var _ResumePreview__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./ResumePreview */ \"(app-pages-browser)/./src/components/resume/ResumePreview.jsx\");\n/* harmony import */ var _SimpleResumePreview__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./SimpleResumePreview */ \"(app-pages-browser)/./src/components/resume/SimpleResumePreview.jsx\");\n/* harmony import */ var _SuccessScreen__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./SuccessScreen */ \"(app-pages-browser)/./src/components/resume/SuccessScreen.jsx\");\n/* harmony import */ var _hooks_useATSAnalysis__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/hooks/useATSAnalysis */ \"(app-pages-browser)/./src/hooks/useATSAnalysis.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst EnhancedResumeBuilder = (param)=>{\n    let { hideHeader = false } = param;\n    _s();\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [selectedTemplate, setSelectedTemplate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('modern');\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        personal: {\n            firstName: \"\",\n            lastName: \"\",\n            email: \"\",\n            phone: \"\",\n            location: \"\",\n            linkedin: \"\",\n            portfolio: \"\",\n            summary: \"\",\n            profileImage: \"\"\n        },\n        education: [\n            {\n                id: 1,\n                degree: \"\",\n                institution: \"\",\n                location: \"\",\n                startDate: \"\",\n                endDate: \"\",\n                gpa: \"\",\n                relevant: \"\"\n            }\n        ],\n        experience: [\n            {\n                id: 1,\n                title: \"\",\n                company: \"\",\n                location: \"\",\n                startDate: \"\",\n                endDate: \"\",\n                current: false,\n                description: \"\"\n            }\n        ],\n        skills: {\n            technical: [],\n            languages: [],\n            certifications: []\n        },\n        projects: [\n            {\n                id: 1,\n                name: \"\",\n                description: \"\",\n                technologies: \"\",\n                link: \"\"\n            }\n        ],\n        jobDescription: \"\"\n    });\n    const [showPreview, setShowPreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showProgressBar, setShowProgressBar] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [resumeGenerated, setResumeGenerated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [validationErrors, setValidationErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [showValidationErrors, setShowValidationErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [completedSteps, setCompletedSteps] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [autoSaveEnabled, setAutoSaveEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [lastSaved, setLastSaved] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [resumeData, setResumeData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [resumeUrl, setResumeUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [atsScore, setAtsScore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [suggestions, setSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [aiSuggestionsEnabled, setAiSuggestionsEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [smartValidation, setSmartValidation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showHints, setShowHints] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const steps = [\n        {\n            id: 0,\n            title: \"Personal Information\",\n            icon: _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n            description: \"Tell us about yourself\",\n            estimatedTime: 3,\n            required: true\n        },\n        {\n            id: 1,\n            title: \"Education\",\n            icon: _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n            description: \"Your academic background\",\n            estimatedTime: 5,\n            required: true\n        },\n        {\n            id: 2,\n            title: \"Experience\",\n            icon: _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n            description: \"Your work experience\",\n            estimatedTime: 8,\n            required: true\n        },\n        {\n            id: 3,\n            title: \"Projects\",\n            icon: _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n            description: \"Showcase your projects\",\n            estimatedTime: 4,\n            required: false\n        },\n        {\n            id: 4,\n            title: \"Skills\",\n            icon: _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"],\n            description: \"Your technical & soft skills\",\n            estimatedTime: 3,\n            required: false\n        },\n        {\n            id: 5,\n            title: \"Review & Generate\",\n            icon: _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n            description: \"Finalize your resume\",\n            estimatedTime: 2,\n            required: false\n        }\n    ];\n    const atsAnalysis = (0,_hooks_useATSAnalysis__WEBPACK_IMPORTED_MODULE_17__[\"default\"])(formData);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EnhancedResumeBuilder.useEffect\": ()=>{\n            // Check for backup data from auth flow first\n            const backupData = localStorage.getItem('resumeFormDataBackup');\n            const authTimestamp = localStorage.getItem('authFlowTimestamp');\n            const savedData = localStorage.getItem('resumeFormData');\n            // If backup data exists and is recent (within 10 minutes), use it\n            if (backupData && authTimestamp) {\n                const timeDiff = Date.now() - parseInt(authTimestamp);\n                if (timeDiff < 10 * 60 * 1000) {\n                    try {\n                        const parsed = JSON.parse(backupData);\n                        setFormData(parsed);\n                        setLastSaved(new Date());\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success('Your work has been restored after sign-in!');\n                        // Clean up backup data\n                        localStorage.removeItem('resumeFormDataBackup');\n                        localStorage.removeItem('authFlowTimestamp');\n                        // Update main storage\n                        localStorage.setItem('resumeFormData', backupData);\n                        localStorage.setItem('resumeLastSaved', new Date().toISOString());\n                        return;\n                    } catch (error) {\n                        console.error('Failed to load backup data:', error);\n                    }\n                }\n            }\n            // Fallback to regular saved data\n            if (savedData) {\n                try {\n                    const parsed = JSON.parse(savedData);\n                    setFormData(parsed);\n                    setLastSaved(new Date(localStorage.getItem('resumeLastSaved') || Date.now()));\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success('Previous work restored!');\n                } catch (error) {\n                    console.error('Failed to load saved data:', error);\n                }\n            }\n            // Clean up old backup data\n            localStorage.removeItem('resumeFormDataBackup');\n            localStorage.removeItem('authFlowTimestamp');\n        }\n    }[\"EnhancedResumeBuilder.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EnhancedResumeBuilder.useEffect\": ()=>{\n            if (autoSaveEnabled && formData) {\n                // Make form data globally accessible for auth flow\n                if (true) {\n                    window.formData = formData;\n                }\n                const timeoutId = setTimeout({\n                    \"EnhancedResumeBuilder.useEffect.timeoutId\": ()=>{\n                        localStorage.setItem('resumeFormData', JSON.stringify(formData));\n                        localStorage.setItem('resumeLastSaved', new Date().toISOString());\n                        setLastSaved(new Date());\n                    }\n                }[\"EnhancedResumeBuilder.useEffect.timeoutId\"], 2000);\n                return ({\n                    \"EnhancedResumeBuilder.useEffect\": ()=>clearTimeout(timeoutId)\n                })[\"EnhancedResumeBuilder.useEffect\"];\n            }\n        }\n    }[\"EnhancedResumeBuilder.useEffect\"], [\n        formData,\n        autoSaveEnabled\n    ]);\n    const validateStep = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[validateStep]\": (stepIndex)=>{\n            const errors = {};\n            switch(stepIndex){\n                case 0:\n                    var _formData_personal_firstName, _formData_personal_lastName, _formData_personal_email;\n                    if (!((_formData_personal_firstName = formData.personal.firstName) === null || _formData_personal_firstName === void 0 ? void 0 : _formData_personal_firstName.trim())) errors.firstName = 'First name is required';\n                    if (!((_formData_personal_lastName = formData.personal.lastName) === null || _formData_personal_lastName === void 0 ? void 0 : _formData_personal_lastName.trim())) errors.lastName = 'Last name is required';\n                    if (!((_formData_personal_email = formData.personal.email) === null || _formData_personal_email === void 0 ? void 0 : _formData_personal_email.trim())) errors.email = 'Email is required';\n                    else if (!/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(formData.personal.email)) errors.email = 'Please enter a valid email address';\n                    break;\n                case 1:\n                    var _formData_education;\n                    const validEducation = (_formData_education = formData.education) === null || _formData_education === void 0 ? void 0 : _formData_education.filter({\n                        \"EnhancedResumeBuilder.useCallback[validateStep]\": (edu)=>{\n                            var _edu_degree, _edu_institution;\n                            return ((_edu_degree = edu.degree) === null || _edu_degree === void 0 ? void 0 : _edu_degree.trim()) && ((_edu_institution = edu.institution) === null || _edu_institution === void 0 ? void 0 : _edu_institution.trim());\n                        }\n                    }[\"EnhancedResumeBuilder.useCallback[validateStep]\"]);\n                    if (!validEducation || validEducation.length === 0) errors.education = 'At least one education entry with degree and institution is required';\n                    break;\n                case 2:\n                    var _formData_experience;\n                    const validExperience = (_formData_experience = formData.experience) === null || _formData_experience === void 0 ? void 0 : _formData_experience.filter({\n                        \"EnhancedResumeBuilder.useCallback[validateStep]\": (exp)=>{\n                            var _exp_title, _exp_company;\n                            return ((_exp_title = exp.title) === null || _exp_title === void 0 ? void 0 : _exp_title.trim()) && ((_exp_company = exp.company) === null || _exp_company === void 0 ? void 0 : _exp_company.trim());\n                        }\n                    }[\"EnhancedResumeBuilder.useCallback[validateStep]\"]);\n                    if (!validExperience || validExperience.length === 0) errors.experience = 'At least one work experience entry with job title and company is required';\n                    break;\n                case 3:\n                    break;\n                case 4:\n                    break;\n                default:\n                    break;\n            }\n            return errors;\n        }\n    }[\"EnhancedResumeBuilder.useCallback[validateStep]\"], [\n        formData\n    ]);\n    const canProceedToNextStep = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[canProceedToNextStep]\": (stepIndex)=>{\n            const errors = validateStep(stepIndex);\n            return Object.keys(errors).length === 0;\n        }\n    }[\"EnhancedResumeBuilder.useCallback[canProceedToNextStep]\"], [\n        validateStep\n    ]);\n    const getCompletedSteps = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[getCompletedSteps]\": ()=>{\n            const completed = [];\n            for(let i = 0; i < currentStep; i++){\n                const errors = validateStep(i);\n                if (Object.keys(errors).length === 0) {\n                    completed.push(i);\n                }\n            }\n            return completed;\n        }\n    }[\"EnhancedResumeBuilder.useCallback[getCompletedSteps]\"], [\n        currentStep,\n        validateStep\n    ]);\n    const getStepCompletionStatus = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[getStepCompletionStatus]\": ()=>{\n            const status = {};\n            steps.forEach({\n                \"EnhancedResumeBuilder.useCallback[getStepCompletionStatus]\": (step, index)=>{\n                    const errors = validateStep(index);\n                    status[index] = Object.keys(errors).length === 0;\n                }\n            }[\"EnhancedResumeBuilder.useCallback[getStepCompletionStatus]\"]);\n            return status;\n        }\n    }[\"EnhancedResumeBuilder.useCallback[getStepCompletionStatus]\"], [\n        steps,\n        validateStep\n    ]);\n    const canGenerateResume = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[canGenerateResume]\": ()=>{\n            for(let i = 0; i < steps.length - 1; i++){\n                if (!canProceedToNextStep(i)) return false;\n            }\n            return true;\n        }\n    }[\"EnhancedResumeBuilder.useCallback[canGenerateResume]\"], [\n        canProceedToNextStep,\n        steps.length\n    ]);\n    const updateFormData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[updateFormData]\": function(section, field, value) {\n            let index = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : null;\n            setFormData({\n                \"EnhancedResumeBuilder.useCallback[updateFormData]\": (prev)=>{\n                    let newData = {\n                        ...prev\n                    };\n                    if (index !== null && Array.isArray(prev[section])) {\n                        const newArray = [\n                            ...prev[section]\n                        ];\n                        newArray[index] = {\n                            ...newArray[index],\n                            [field]: value\n                        };\n                        newData[section] = newArray;\n                    } else if (typeof prev[section] === 'object' && !Array.isArray(prev[section])) {\n                        newData[section] = {\n                            ...prev[section],\n                            [field]: value\n                        };\n                    } else {\n                        newData[field] = value;\n                    }\n                    return newData;\n                }\n            }[\"EnhancedResumeBuilder.useCallback[updateFormData]\"]);\n            if (validationErrors[field]) {\n                setValidationErrors({\n                    \"EnhancedResumeBuilder.useCallback[updateFormData]\": (prev)=>{\n                        const newErrors = {\n                            ...prev\n                        };\n                        delete newErrors[field];\n                        return newErrors;\n                    }\n                }[\"EnhancedResumeBuilder.useCallback[updateFormData]\"]);\n            }\n        }\n    }[\"EnhancedResumeBuilder.useCallback[updateFormData]\"], [\n        validationErrors\n    ]);\n    const addArrayItem = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[addArrayItem]\": (section, template)=>{\n            setFormData({\n                \"EnhancedResumeBuilder.useCallback[addArrayItem]\": (prev)=>({\n                        ...prev,\n                        [section]: [\n                            ...prev[section],\n                            {\n                                ...template,\n                                id: Math.random().toString(36).substring(2, 11)\n                            }\n                        ]\n                    })\n            }[\"EnhancedResumeBuilder.useCallback[addArrayItem]\"]);\n        }\n    }[\"EnhancedResumeBuilder.useCallback[addArrayItem]\"], []);\n    const removeArrayItem = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[removeArrayItem]\": (section, id)=>{\n            setFormData({\n                \"EnhancedResumeBuilder.useCallback[removeArrayItem]\": (prev)=>({\n                        ...prev,\n                        [section]: prev[section].filter({\n                            \"EnhancedResumeBuilder.useCallback[removeArrayItem]\": (item)=>item.id !== id\n                        }[\"EnhancedResumeBuilder.useCallback[removeArrayItem]\"])\n                    })\n            }[\"EnhancedResumeBuilder.useCallback[removeArrayItem]\"]);\n        }\n    }[\"EnhancedResumeBuilder.useCallback[removeArrayItem]\"], []);\n    const nextStep = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[nextStep]\": ()=>{\n            const errors = validateStep(currentStep);\n            if (Object.keys(errors).length > 0) {\n                setValidationErrors(errors);\n                setShowValidationErrors(true);\n                const errorMessages = Object.values(errors);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(errorMessages[0]);\n                return;\n            }\n            setCompletedSteps({\n                \"EnhancedResumeBuilder.useCallback[nextStep]\": (prev)=>[\n                        ...new Set([\n                            ...prev,\n                            currentStep\n                        ])\n                    ]\n            }[\"EnhancedResumeBuilder.useCallback[nextStep]\"]);\n            setValidationErrors({});\n            setShowValidationErrors(false);\n            if (currentStep < steps.length - 1) {\n                setCurrentStep(currentStep + 1);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"\".concat(steps[currentStep].title, \" completed!\"));\n            }\n        }\n    }[\"EnhancedResumeBuilder.useCallback[nextStep]\"], [\n        currentStep,\n        validateStep,\n        steps\n    ]);\n    const prevStep = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[prevStep]\": ()=>{\n            setValidationErrors({});\n            setShowValidationErrors(false);\n            if (currentStep > 0) setCurrentStep(currentStep - 1);\n        }\n    }[\"EnhancedResumeBuilder.useCallback[prevStep]\"], [\n        currentStep\n    ]);\n    const handleStepClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[handleStepClick]\": (stepIndex)=>{\n            if (stepIndex <= currentStep || completedSteps.includes(stepIndex)) {\n                setCurrentStep(stepIndex);\n                setValidationErrors({});\n                setShowValidationErrors(false);\n            }\n        }\n    }[\"EnhancedResumeBuilder.useCallback[handleStepClick]\"], [\n        currentStep,\n        completedSteps\n    ]);\n    const generateResume = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[generateResume]\": async ()=>{\n            setValidationErrors({});\n            setShowValidationErrors(false);\n            if (!canGenerateResume()) {\n                const allErrors = {};\n                for(let i = 0; i < steps.length - 1; i++){\n                    const stepErrors = validateStep(i);\n                    Object.assign(allErrors, stepErrors);\n                }\n                setValidationErrors(allErrors);\n                setShowValidationErrors(true);\n                for(let i = 0; i < steps.length - 1; i++){\n                    const stepErrors = validateStep(i);\n                    if (Object.keys(stepErrors).length > 0) {\n                        setCurrentStep(i);\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Please complete all required fields in \".concat(steps[i].title));\n                        return;\n                    }\n                }\n                return;\n            }\n            try {\n                setIsGenerating(true);\n                setShowProgressBar(true);\n                setResumeGenerated(false);\n                const response = await fetch('/api/generate-resume', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        formData,\n                        templateId: selectedTemplate\n                    })\n                });\n                const contentType = response.headers.get('content-type');\n                if (!contentType || !contentType.includes('application/json')) throw new Error('Server returned non-JSON response');\n                const data = await response.json();\n                if (!response.ok) throw new Error(data.error || 'Failed to generate resume');\n                if (!data.resumeData || !data.downloadUrl) throw new Error('Generated resume data is incomplete');\n                setResumeUrl(data.downloadUrl);\n                setResumeData(data.resumeData);\n                setAtsScore(data.atsScore || 75);\n                setSuggestions(data.suggestions || []);\n                setResumeGenerated(true);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success('Resume generated successfully!');\n            } catch (error) {\n                console.error('Resume generation error:', error);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(error.message || 'Failed to generate resume');\n            } finally{\n                setIsGenerating(false);\n                setShowProgressBar(false);\n            }\n        }\n    }[\"EnhancedResumeBuilder.useCallback[generateResume]\"], [\n        canGenerateResume,\n        formData,\n        steps,\n        validateStep,\n        selectedTemplate\n    ]);\n    const handleSave = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[handleSave]\": ()=>{\n            localStorage.setItem('resumeFormData', JSON.stringify(formData));\n            localStorage.setItem('resumeLastSaved', new Date().toISOString());\n            setLastSaved(new Date());\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success('Progress saved!');\n        }\n    }[\"EnhancedResumeBuilder.useCallback[handleSave]\"], [\n        formData\n    ]);\n    const handleAISuggest = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[handleAISuggest]\": async (section)=>{\n            if (!aiSuggestionsEnabled) return;\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.loading('Getting AI suggestions...');\n            try {\n                await new Promise({\n                    \"EnhancedResumeBuilder.useCallback[handleAISuggest]\": (resolve)=>setTimeout(resolve, 1500)\n                }[\"EnhancedResumeBuilder.useCallback[handleAISuggest]\"]);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.dismiss();\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success('AI suggestions applied!');\n            } catch (error) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.dismiss();\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error('Failed to get AI suggestions');\n            }\n        }\n    }[\"EnhancedResumeBuilder.useCallback[handleAISuggest]\"], [\n        aiSuggestionsEnabled\n    ]);\n    // Set up event listener for resume generation from ReviewForm\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EnhancedResumeBuilder.useEffect\": ()=>{\n            const handleGenerateResume = {\n                \"EnhancedResumeBuilder.useEffect.handleGenerateResume\": ()=>{\n                    // Check if we can generate resume and call the function\n                    if (canGenerateResume()) {\n                        generateResume();\n                    } else {\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error('Please complete all required sections before generating your resume.');\n                    }\n                }\n            }[\"EnhancedResumeBuilder.useEffect.handleGenerateResume\"];\n            if (true) {\n                window.addEventListener('generateResume', handleGenerateResume);\n                return ({\n                    \"EnhancedResumeBuilder.useEffect\": ()=>window.removeEventListener('generateResume', handleGenerateResume)\n                })[\"EnhancedResumeBuilder.useEffect\"];\n            }\n        }\n    }[\"EnhancedResumeBuilder.useEffect\"], [\n        canGenerateResume,\n        generateResume\n    ]);\n    const getStepValidation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[getStepValidation]\": ()=>{\n            const validation = {};\n            steps.forEach({\n                \"EnhancedResumeBuilder.useCallback[getStepValidation]\": (step, index)=>{\n                    const errors = validateStep(index);\n                    validation[index] = Object.keys(errors).length === 0;\n                }\n            }[\"EnhancedResumeBuilder.useCallback[getStepValidation]\"]);\n            return validation;\n        }\n    }[\"EnhancedResumeBuilder.useCallback[getStepValidation]\"], [\n        steps,\n        validateStep\n    ]);\n    const renderStepContent = ()=>{\n        const commonProps = {\n            formData,\n            updateFormData,\n            addArrayItem,\n            removeArrayItem,\n            atsAnalysis,\n            validationErrors,\n            showValidationErrors,\n            onSave: handleSave,\n            onAISuggest: handleAISuggest\n        };\n        switch(currentStep){\n            case 0:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_ResumeFormComponents__WEBPACK_IMPORTED_MODULE_5__.PersonalInfoForm, {\n                    ...commonProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                    lineNumber: 383,\n                    columnNumber: 16\n                }, undefined);\n            case 1:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_ResumeFormComponents__WEBPACK_IMPORTED_MODULE_5__.EducationForm, {\n                    ...commonProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                    lineNumber: 385,\n                    columnNumber: 16\n                }, undefined);\n            case 2:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_ExperienceForm__WEBPACK_IMPORTED_MODULE_6__.EnhancedExperienceForm, {\n                    ...commonProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                    lineNumber: 387,\n                    columnNumber: 16\n                }, undefined);\n            case 3:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_ProjectsForm__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    ...commonProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                    lineNumber: 389,\n                    columnNumber: 16\n                }, undefined);\n            case 4:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_SkillsForm__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    ...commonProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                    lineNumber: 391,\n                    columnNumber: 16\n                }, undefined);\n            case 5:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_ReviewForm__WEBPACK_IMPORTED_MODULE_10__.EnhancedReviewForm, {\n                    ...commonProps,\n                    selectedTemplate: selectedTemplate,\n                    onTemplateSelect: setSelectedTemplate\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                    lineNumber: 393,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return null;\n        }\n    };\n    if (resumeGenerated && resumeData && resumeUrl) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SuccessScreen__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n            formData: formData,\n            resumeData: resumeData,\n            onStartOver: ()=>{\n                setResumeGenerated(false);\n                setResumeData(null);\n                setResumeUrl(\"\");\n                setCurrentStep(0);\n            },\n            onEditResume: ()=>{\n                setResumeGenerated(false);\n                setCurrentStep(4); // Go back to review step\n            }\n        }, void 0, false, {\n            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n            lineNumber: 401,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-b from-black to-[#0A0A0A] text-white relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 opacity-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 bg-[url('/grid.svg')] [mask-image:radial-gradient(ellipse_at_center,white,transparent)]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                    lineNumber: 421,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                lineNumber: 420,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_common_ProgressBar__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                isVisible: showProgressBar,\n                onComplete: ()=>setShowProgressBar(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                lineNumber: 423,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 pb-32\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 py-8\",\n                    children: [\n                        !hideHeader && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_23__.motion.div, {\n                            className: \"text-center mb-8\",\n                            initial: {\n                                opacity: 0,\n                                y: -20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center gap-3 mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    className: \"h-10 w-10 text-neural-pink animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                    lineNumber: 430,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-neural-pink opacity-20 rounded-full blur-md\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                    lineNumber: 431,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                            lineNumber: 429,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-4xl lg:text-5xl font-bold text-white\",\n                                            children: \"Enhanced Resume Builder\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                            lineNumber: 433,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                    lineNumber: 428,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 text-lg max-w-2xl mx-auto\",\n                                    children: \"Create professional, ATS-friendly resumes with our enhanced AI-powered builder\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                    lineNumber: 435,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                            lineNumber: 427,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col xl:flex-row gap-8 max-w-7xl mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 max-w-4xl\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"sticky top-4 z-30 bg-gray-900/95 backdrop-blur-sm rounded-2xl border border-gray-700/50 mb-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EnhancedProgressIndicator__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                steps: steps,\n                                                currentStep: currentStep,\n                                                completedSteps: completedSteps,\n                                                onStepClick: handleStepClick,\n                                                variant: \"minimal\",\n                                                showLabels: true,\n                                                showProgress: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                lineNumber: 443,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                            lineNumber: 442,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_25__.AnimatePresence, {\n                                                mode: \"wait\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_23__.motion.div, {\n                                                    initial: {\n                                                        opacity: 0,\n                                                        x: 20\n                                                    },\n                                                    animate: {\n                                                        opacity: 1,\n                                                        x: 0\n                                                    },\n                                                    exit: {\n                                                        opacity: 0,\n                                                        x: -20\n                                                    },\n                                                    transition: {\n                                                        duration: 0.3\n                                                    },\n                                                    children: renderStepContent()\n                                                }, currentStep, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                    lineNumber: 457,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                lineNumber: 456,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                            lineNumber: 455,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                    lineNumber: 440,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden xl:block xl:w-80 xl:flex-shrink-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"sticky top-4 h-[calc(100vh-2rem)]\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SimpleResumePreview__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            formData: formData,\n                                            selectedTemplate: selectedTemplate,\n                                            showPreview: showPreview,\n                                            onTogglePreview: ()=>setShowPreview(!showPreview)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                            lineNumber: 468,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                        lineNumber: 467,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                    lineNumber: 465,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"xl:hidden mt-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_23__.motion.div, {\n                                        className: \"bg-gray-900/60 backdrop-blur-md rounded-2xl p-6 border border-white/10\",\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            delay: 0.2\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                className: \"h-5 w-5 text-neural-blue\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                                lineNumber: 487,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            \"Resume Preview\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                        lineNumber: 486,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setShowPreview(!showPreview),\n                                                        className: \"px-3 py-1.5 rounded-lg text-sm font-medium transition-colors \".concat(showPreview ? 'bg-neural-purple text-white' : 'bg-gray-800 text-gray-300 hover:bg-gray-700'),\n                                                        children: showPreview ? 'Hide' : 'Show'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                        lineNumber: 490,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                lineNumber: 485,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            showPreview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"max-h-[600px] overflow-y-auto border border-white/10 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ResumePreview__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    formData: formData,\n                                                    selectedTemplate: selectedTemplate\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                    lineNumber: 501,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                lineNumber: 500,\n                                                columnNumber: 19\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-12 text-gray-400\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                        className: \"h-12 w-12 mx-auto mb-3 opacity-30\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                        lineNumber: 505,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm\",\n                                                        children: 'Click \"Show\" to preview your resume'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                        lineNumber: 506,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                lineNumber: 504,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                        lineNumber: 479,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                    lineNumber: 478,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                            lineNumber: 438,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                    lineNumber: 425,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                lineNumber: 424,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_layout_SmartNavigationBar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                currentStep: currentStep,\n                totalSteps: steps.length,\n                onPrevious: prevStep,\n                onNext: nextStep,\n                onGenerate: generateResume,\n                onSave: handleSave,\n                onPreview: ()=>setShowPreview(!showPreview),\n                isGenerating: isGenerating,\n                canProceed: currentStep === steps.length - 1 ? canGenerateResume() : canProceedToNextStep(currentStep),\n                showPreview: showPreview,\n                steps: steps,\n                formData: formData,\n                atsScore: atsAnalysis.overallScore,\n                autoSaveEnabled: autoSaveEnabled\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                lineNumber: 514,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n        lineNumber: 419,\n        columnNumber: 5\n    }, undefined);\n};\n_s(EnhancedResumeBuilder, \"cH7kc5DhB7P/1+m/Kivxo+IObKI=\", false, function() {\n    return [\n        _hooks_useATSAnalysis__WEBPACK_IMPORTED_MODULE_17__[\"default\"]\n    ];\n});\n_c = EnhancedResumeBuilder;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (EnhancedResumeBuilder);\nvar _c;\n$RefreshReg$(_c, \"EnhancedResumeBuilder\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/resume/ResumeBuilder.jsx\n"));

/***/ })

});
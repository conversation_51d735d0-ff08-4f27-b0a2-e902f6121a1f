// Simple test script to verify admin API
const testAdminAPI = async () => {
  try {
    console.log('🧪 Testing Admin API...');
    
    // Test GET endpoint
    const getResponse = await fetch('/api/admin');
    const getData = await getResponse.json();
    console.log('✅ GET /api/admin:', getData);
    
    // Test POST endpoint - check admin status
    const postResponse = await fetch('/api/admin', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        action: 'checkAdmin',
        email: '<EMAIL>'
      })
    });
    const postData = await postResponse.json();
    console.log('✅ POST /api/admin (checkAdmin):', postData);
    
    // Test POST endpoint - log access
    const logResponse = await fetch('/api/admin', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        action: 'logAccess',
        email: '<EMAIL>'
      })
    });
    const logData = await logResponse.json();
    console.log('✅ POST /api/admin (logAccess):', logData);
    
    return {
      success: true,
      results: { getData, postData, logData }
    };
    
  } catch (error) {
    console.error('❌ Admin API test failed:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// Make function available globally for testing
if (typeof window !== 'undefined') {
  window.testAdminAPI = testAdminAPI;
}

export default testAdminAPI;

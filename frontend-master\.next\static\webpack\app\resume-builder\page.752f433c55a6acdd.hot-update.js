"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/resume-builder/page",{

/***/ "(app-pages-browser)/./src/components/resume/forms/ReviewForm.jsx":
/*!****************************************************!*\
  !*** ./src/components/resume/forms/ReviewForm.jsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EnhancedReviewForm: () => (/* binding */ EnhancedReviewForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowRight,Award,Briefcase,Building,Calendar,CheckCircle,Clock,Code,Crown,Download,Edit3,Eye,FileText,Folder,Globe,GraduationCap,Info,Link,Mail,MapPin,Palette,Phone,Save,Shield,Sparkles,Star,Target,TrendingUp,User,Wand2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowRight,Award,Briefcase,Building,Calendar,CheckCircle,Clock,Code,Crown,Download,Edit3,Eye,FileText,Folder,Globe,GraduationCap,Info,Link,Mail,MapPin,Palette,Phone,Save,Shield,Sparkles,Star,Target,TrendingUp,User,Wand2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/graduation-cap.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowRight,Award,Briefcase,Building,Calendar,CheckCircle,Clock,Code,Crown,Download,Edit3,Eye,FileText,Folder,Globe,GraduationCap,Info,Link,Mail,MapPin,Palette,Phone,Save,Shield,Sparkles,Star,Target,TrendingUp,User,Wand2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowRight,Award,Briefcase,Building,Calendar,CheckCircle,Clock,Code,Crown,Download,Edit3,Eye,FileText,Folder,Globe,GraduationCap,Info,Link,Mail,MapPin,Palette,Phone,Save,Shield,Sparkles,Star,Target,TrendingUp,User,Wand2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowRight,Award,Briefcase,Building,Calendar,CheckCircle,Clock,Code,Crown,Download,Edit3,Eye,FileText,Folder,Globe,GraduationCap,Info,Link,Mail,MapPin,Palette,Phone,Save,Shield,Sparkles,Star,Target,TrendingUp,User,Wand2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowRight,Award,Briefcase,Building,Calendar,CheckCircle,Clock,Code,Crown,Download,Edit3,Eye,FileText,Folder,Globe,GraduationCap,Info,Link,Mail,MapPin,Palette,Phone,Save,Shield,Sparkles,Star,Target,TrendingUp,User,Wand2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowRight,Award,Briefcase,Building,Calendar,CheckCircle,Clock,Code,Crown,Download,Edit3,Eye,FileText,Folder,Globe,GraduationCap,Info,Link,Mail,MapPin,Palette,Phone,Save,Shield,Sparkles,Star,Target,TrendingUp,User,Wand2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowRight,Award,Briefcase,Building,Calendar,CheckCircle,Clock,Code,Crown,Download,Edit3,Eye,FileText,Folder,Globe,GraduationCap,Info,Link,Mail,MapPin,Palette,Phone,Save,Shield,Sparkles,Star,Target,TrendingUp,User,Wand2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowRight,Award,Briefcase,Building,Calendar,CheckCircle,Clock,Code,Crown,Download,Edit3,Eye,FileText,Folder,Globe,GraduationCap,Info,Link,Mail,MapPin,Palette,Phone,Save,Shield,Sparkles,Star,Target,TrendingUp,User,Wand2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowRight,Award,Briefcase,Building,Calendar,CheckCircle,Clock,Code,Crown,Download,Edit3,Eye,FileText,Folder,Globe,GraduationCap,Info,Link,Mail,MapPin,Palette,Phone,Save,Shield,Sparkles,Star,Target,TrendingUp,User,Wand2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowRight,Award,Briefcase,Building,Calendar,CheckCircle,Clock,Code,Crown,Download,Edit3,Eye,FileText,Folder,Globe,GraduationCap,Info,Link,Mail,MapPin,Palette,Phone,Save,Shield,Sparkles,Star,Target,TrendingUp,User,Wand2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowRight,Award,Briefcase,Building,Calendar,CheckCircle,Clock,Code,Crown,Download,Edit3,Eye,FileText,Folder,Globe,GraduationCap,Info,Link,Mail,MapPin,Palette,Phone,Save,Shield,Sparkles,Star,Target,TrendingUp,User,Wand2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowRight,Award,Briefcase,Building,Calendar,CheckCircle,Clock,Code,Crown,Download,Edit3,Eye,FileText,Folder,Globe,GraduationCap,Info,Link,Mail,MapPin,Palette,Phone,Save,Shield,Sparkles,Star,Target,TrendingUp,User,Wand2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowRight,Award,Briefcase,Building,Calendar,CheckCircle,Clock,Code,Crown,Download,Edit3,Eye,FileText,Folder,Globe,GraduationCap,Info,Link,Mail,MapPin,Palette,Phone,Save,Shield,Sparkles,Star,Target,TrendingUp,User,Wand2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen-line.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowRight,Award,Briefcase,Building,Calendar,CheckCircle,Clock,Code,Crown,Download,Edit3,Eye,FileText,Folder,Globe,GraduationCap,Info,Link,Mail,MapPin,Palette,Phone,Save,Shield,Sparkles,Star,Target,TrendingUp,User,Wand2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowRight,Award,Briefcase,Building,Calendar,CheckCircle,Clock,Code,Crown,Download,Edit3,Eye,FileText,Folder,Globe,GraduationCap,Info,Link,Mail,MapPin,Palette,Phone,Save,Shield,Sparkles,Star,Target,TrendingUp,User,Wand2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/palette.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowRight,Award,Briefcase,Building,Calendar,CheckCircle,Clock,Code,Crown,Download,Edit3,Eye,FileText,Folder,Globe,GraduationCap,Info,Link,Mail,MapPin,Palette,Phone,Save,Shield,Sparkles,Star,Target,TrendingUp,User,Wand2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowRight,Award,Briefcase,Building,Calendar,CheckCircle,Clock,Code,Crown,Download,Edit3,Eye,FileText,Folder,Globe,GraduationCap,Info,Link,Mail,MapPin,Palette,Phone,Save,Shield,Sparkles,Star,Target,TrendingUp,User,Wand2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowRight,Award,Briefcase,Building,Calendar,CheckCircle,Clock,Code,Crown,Download,Edit3,Eye,FileText,Folder,Globe,GraduationCap,Info,Link,Mail,MapPin,Palette,Phone,Save,Shield,Sparkles,Star,Target,TrendingUp,User,Wand2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _EnhancedFormField__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./EnhancedFormField */ \"(app-pages-browser)/./src/components/resume/forms/EnhancedFormField.jsx\");\n/* harmony import */ var _EnhancedTemplateSelector__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../EnhancedTemplateSelector */ \"(app-pages-browser)/./src/components/resume/EnhancedTemplateSelector.jsx\");\n/* __next_internal_client_entry_do_not_use__ EnhancedReviewForm auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst EnhancedReviewForm = (param)=>{\n    let { formData, updateFormData, atsAnalysis, onSave, onAISuggest, selectedTemplate, onTemplateSelect } = param;\n    _s();\n    const [showTemplateSelector, setShowTemplateSelector] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [jobDescription, setJobDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(formData.jobDescription || '');\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('overview'); // 'overview', 'content', 'optimization'\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const reviewRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Update job description in parent state\n    const handleJobDescriptionChange = (e)=>{\n        setJobDescription(e.target.value);\n        updateFormData('jobDescription', '', e.target.value);\n    };\n    // Calculate completion score\n    const getCompletionScore = ()=>{\n        let score = 0;\n        let maxScore = 0;\n        // Personal info (25 points)\n        maxScore += 25;\n        if (formData.personal.firstName && formData.personal.lastName) score += 10;\n        if (formData.personal.email && formData.personal.phone) score += 10;\n        if (formData.personal.summary && formData.personal.summary.length >= 100) score += 5;\n        // Education (20 points)\n        maxScore += 20;\n        if (formData.education.some((edu)=>edu.degree && edu.institution)) score += 20;\n        // Experience (35 points)\n        maxScore += 35;\n        if (formData.experience.length > 0) score += 15;\n        if (formData.experience.some((exp)=>exp.description && exp.description.length >= 50)) score += 20;\n        // Skills (20 points)\n        maxScore += 20;\n        if (formData.skills.technical && formData.skills.technical.length > 0) score += 10;\n        if (formData.skills.languages && formData.skills.languages.length > 0) score += 5;\n        if (formData.skills.certifications && formData.skills.certifications.length > 0) score += 5;\n        return Math.round(score / maxScore * 100);\n    };\n    const completionScore = getCompletionScore();\n    // Render Overview Tab\n    const renderOverviewTab = ()=>{\n        var _formData_skills_technical, _formData_skills_languages;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-800/50 backdrop-blur-sm rounded-xl p-4 border border-gray-700\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-10 h-10 bg-blue-500/20 rounded-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"h-5 w-5 text-blue-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                            lineNumber: 73,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                        lineNumber: 72,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: formData.experience.length\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                lineNumber: 76,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-gray-400 text-sm\",\n                                                children: \"Work Experiences\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                lineNumber: 79,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                lineNumber: 71,\n                                columnNumber: 11\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                            lineNumber: 70,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-800/50 backdrop-blur-sm rounded-xl p-4 border border-gray-700\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-10 h-10 bg-green-500/20 rounded-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"h-5 w-5 text-green-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                            lineNumber: 87,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                        lineNumber: 86,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: formData.education.length\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                lineNumber: 90,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-gray-400 text-sm\",\n                                                children: \"Education Entries\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                lineNumber: 93,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                lineNumber: 85,\n                                columnNumber: 11\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                            lineNumber: 84,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-800/50 backdrop-blur-sm rounded-xl p-4 border border-gray-700\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-10 h-10 bg-purple-500/20 rounded-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-5 w-5 text-purple-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: (((_formData_skills_technical = formData.skills.technical) === null || _formData_skills_technical === void 0 ? void 0 : _formData_skills_technical.length) || 0) + (((_formData_skills_languages = formData.skills.languages) === null || _formData_skills_languages === void 0 ? void 0 : _formData_skills_languages.length) || 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                lineNumber: 104,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-gray-400 text-sm\",\n                                                children: \"Skills Listed\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                lineNumber: 107,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                lineNumber: 99,\n                                columnNumber: 11\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                            lineNumber: 98,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                    lineNumber: 69,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-800/50 backdrop-blur-sm rounded-xl p-6 border border-gray-700\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-white mb-4 flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"h-5 w-5 text-neural-purple\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 11\n                                }, undefined),\n                                \"Resume Summary\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                            lineNumber: 115,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"h-5 w-5 text-gray-400 mt-0.5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-medium text-white\",\n                                                    children: [\n                                                        formData.personal.firstName,\n                                                        \" \",\n                                                        formData.personal.lastName\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                    lineNumber: 124,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-400 text-sm\",\n                                                    children: [\n                                                        formData.personal.email,\n                                                        \" • \",\n                                                        formData.personal.phone\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                    lineNumber: 127,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                formData.personal.location && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-400 text-sm\",\n                                                    children: formData.personal.location\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                    lineNumber: 131,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 11\n                                }, undefined),\n                                formData.personal.summary && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-700/30 rounded-lg p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-300 text-sm leading-relaxed\",\n                                        children: formData.personal.summary\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                            lineNumber: 120,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                    lineNumber: 114,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-r from-neural-purple/10 to-neural-pink/10 border border-neural-purple/30 rounded-xl p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-12 h-12 bg-gradient-to-r from-neural-purple to-neural-pink rounded-xl flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-6 w-6 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 13\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-white\",\n                                            children: \"Ready to Generate?\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-300 text-sm\",\n                                            children: [\n                                                \"Your resume is \",\n                                                completionScore,\n                                                \"% complete\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                            lineNumber: 148,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row gap-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    setIsGenerating(true);\n                                    // Simulate generation process\n                                    setTimeout(()=>{\n                                        setIsGenerating(false);\n                                        if (true) {\n                                            const event = new CustomEvent('generateResume');\n                                            window.dispatchEvent(event);\n                                        }\n                                    }, 2000);\n                                },\n                                disabled: completionScore < 70 || isGenerating,\n                                className: \"flex-1 flex items-center justify-center gap-2 px-6 py-3 bg-gradient-to-r from-neural-purple to-neural-pink text-white font-semibold rounded-lg hover:opacity-90 transition-opacity shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed\",\n                                children: isGenerating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                            animate: {\n                                                rotate: 360\n                                            },\n                                            transition: {\n                                                duration: 1,\n                                                repeat: Infinity,\n                                                ease: \"linear\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        \"Generating...\"\n                                    ]\n                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        \"Generate Resume\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                lineNumber: 159,\n                                columnNumber: 11\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                            lineNumber: 158,\n                            columnNumber: 9\n                        }, undefined),\n                        completionScore < 70 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 p-3 bg-yellow-500/10 border border-yellow-500/20 rounded-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-yellow-300 text-sm flex items-start gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-4 w-4 mt-0.5 flex-shrink-0\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Complete at least 70% of your resume to generate. Add more details to your experience and education sections.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                lineNumber: 196,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                            lineNumber: 195,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                    lineNumber: 147,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n            lineNumber: 67,\n            columnNumber: 5\n        }, undefined);\n    };\n    // Render Content Review Tab\n    const renderContentTab = ()=>{\n        var _formData_personal_summary, _formData_skills_technical, _formData_skills_technical1, _formData_personal_summary1, _formData_personal_summary2, _formData_personal_summary3, _formData_skills_technical2, _formData_skills_languages, _formData_skills_technical3, _formData_skills_languages1, _formData_skills_technical4, _formData_skills_languages2;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-800/50 backdrop-blur-sm rounded-xl p-6 border border-gray-700\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-white mb-4 flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"h-5 w-5 text-green-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 11\n                                }, undefined),\n                                \"Content Requirements\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                            lineNumber: 213,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: [\n                                {\n                                    label: 'Personal Information',\n                                    check: formData.personal.firstName && formData.personal.lastName && formData.personal.email,\n                                    details: 'Name and contact information'\n                                },\n                                {\n                                    label: 'Professional Summary',\n                                    check: formData.personal.summary && formData.personal.summary.length >= 100,\n                                    details: \"\".concat(((_formData_personal_summary = formData.personal.summary) === null || _formData_personal_summary === void 0 ? void 0 : _formData_personal_summary.length) || 0, \"/100 characters minimum\")\n                                },\n                                {\n                                    label: 'Work Experience',\n                                    check: formData.experience.some((exp)=>{\n                                        var _exp_description;\n                                        return exp.title && exp.company && ((_exp_description = exp.description) === null || _exp_description === void 0 ? void 0 : _exp_description.length) >= 50;\n                                    }),\n                                    details: \"\".concat(formData.experience.length, \" entries, descriptions 50+ chars each\")\n                                },\n                                {\n                                    label: 'Education',\n                                    check: formData.education.some((edu)=>edu.degree && edu.institution),\n                                    details: \"\".concat(formData.education.length, \" education entries\")\n                                },\n                                {\n                                    label: 'Skills',\n                                    check: (((_formData_skills_technical = formData.skills.technical) === null || _formData_skills_technical === void 0 ? void 0 : _formData_skills_technical.length) || 0) >= 3,\n                                    details: \"\".concat(((_formData_skills_technical1 = formData.skills.technical) === null || _formData_skills_technical1 === void 0 ? void 0 : _formData_skills_technical1.length) || 0, \" technical skills listed\")\n                                }\n                            ].map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between p-3 bg-gray-700/30 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3\",\n                                            children: [\n                                                item.check ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"h-5 w-5 text-green-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                    lineNumber: 249,\n                                                    columnNumber: 19\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-5 w-5 text-yellow-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                    lineNumber: 251,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-medium \".concat(item.check ? 'text-green-400' : 'text-yellow-400'),\n                                                            children: item.label\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                            lineNumber: 254,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-gray-400 text-sm\",\n                                                            children: item.details\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                            lineNumber: 257,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                    lineNumber: 253,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                            lineNumber: 247,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"px-2 py-1 rounded text-xs font-medium \".concat(item.check ? 'bg-green-500/20 text-green-400' : 'bg-yellow-500/20 text-yellow-400'),\n                                            children: item.check ? 'Complete' : 'Needs Work'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 13\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                            lineNumber: 218,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                    lineNumber: 212,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-800/50 backdrop-blur-sm rounded-xl p-6 border border-gray-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-semibold text-neural-purple mb-3 flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                            lineNumber: 275,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        \"Personal Information\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2 text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-400\",\n                                                    children: \"Name:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                    lineNumber: 280,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white\",\n                                                    children: [\n                                                        formData.personal.firstName,\n                                                        \" \",\n                                                        formData.personal.lastName\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                            lineNumber: 279,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-400\",\n                                                    children: \"Email:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                    lineNumber: 284,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white\",\n                                                    children: formData.personal.email\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                    lineNumber: 285,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-400\",\n                                                    children: \"Phone:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                    lineNumber: 288,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white\",\n                                                    children: formData.personal.phone\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                    lineNumber: 289,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        formData.personal.location && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-400\",\n                                                    children: \"Location:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                    lineNumber: 293,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white\",\n                                                    children: formData.personal.location\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                    lineNumber: 294,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                            lineNumber: 273,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-800/50 backdrop-blur-sm rounded-xl p-6 border border-gray-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-semibold text-neural-purple mb-3 flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                            lineNumber: 303,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        \"Experience Summary\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                    lineNumber: 302,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        formData.experience.slice(0, 3).map((exp, index)=>{\n                                            var _exp_description;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium text-white\",\n                                                        children: exp.title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                        lineNumber: 309,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-gray-400\",\n                                                        children: exp.company\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                        lineNumber: 310,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: [\n                                                            ((_exp_description = exp.description) === null || _exp_description === void 0 ? void 0 : _exp_description.length) || 0,\n                                                            \" characters\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                        lineNumber: 311,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                lineNumber: 308,\n                                                columnNumber: 15\n                                            }, undefined);\n                                        }),\n                                        formData.experience.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-gray-400\",\n                                            children: [\n                                                \"+\",\n                                                formData.experience.length - 3,\n                                                \" more experiences\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                            lineNumber: 317,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                    lineNumber: 306,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                            lineNumber: 301,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                    lineNumber: 271,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-800/50 backdrop-blur-sm rounded-xl p-6 border border-gray-700\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-white mb-4 flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    className: \"h-5 w-5 text-neural-purple\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                    lineNumber: 328,\n                                    columnNumber: 11\n                                }, undefined),\n                                \"Content Analysis\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                            lineNumber: 327,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center p-4 bg-gray-700/30 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-white mb-1\",\n                                            children: ((_formData_personal_summary1 = formData.personal.summary) === null || _formData_personal_summary1 === void 0 ? void 0 : _formData_personal_summary1.length) || 0\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                            lineNumber: 334,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-400 text-sm\",\n                                            children: \"Summary Characters\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                            lineNumber: 337,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs mt-1 \".concat((((_formData_personal_summary2 = formData.personal.summary) === null || _formData_personal_summary2 === void 0 ? void 0 : _formData_personal_summary2.length) || 0) >= 100 ? 'text-green-400' : 'text-yellow-400'),\n                                            children: (((_formData_personal_summary3 = formData.personal.summary) === null || _formData_personal_summary3 === void 0 ? void 0 : _formData_personal_summary3.length) || 0) >= 100 ? 'Optimal' : 'Needs more'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                            lineNumber: 338,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                    lineNumber: 333,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center p-4 bg-gray-700/30 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-white mb-1\",\n                                            children: formData.experience.reduce((total, exp)=>{\n                                                var _exp_description;\n                                                return total + (((_exp_description = exp.description) === null || _exp_description === void 0 ? void 0 : _exp_description.length) || 0);\n                                            }, 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                            lineNumber: 346,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-400 text-sm\",\n                                            children: \"Experience Characters\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                            lineNumber: 349,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs mt-1 \".concat(formData.experience.reduce((total, exp)=>{\n                                                var _exp_description;\n                                                return total + (((_exp_description = exp.description) === null || _exp_description === void 0 ? void 0 : _exp_description.length) || 0);\n                                            }, 0) >= 300 ? 'text-green-400' : 'text-yellow-400'),\n                                            children: formData.experience.reduce((total, exp)=>{\n                                                var _exp_description;\n                                                return total + (((_exp_description = exp.description) === null || _exp_description === void 0 ? void 0 : _exp_description.length) || 0);\n                                            }, 0) >= 300 ? 'Good detail' : 'Add more detail'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                            lineNumber: 350,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                    lineNumber: 345,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center p-4 bg-gray-700/30 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-white mb-1\",\n                                            children: (((_formData_skills_technical2 = formData.skills.technical) === null || _formData_skills_technical2 === void 0 ? void 0 : _formData_skills_technical2.length) || 0) + (((_formData_skills_languages = formData.skills.languages) === null || _formData_skills_languages === void 0 ? void 0 : _formData_skills_languages.length) || 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                            lineNumber: 358,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-400 text-sm\",\n                                            children: \"Total Skills\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                            lineNumber: 361,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs mt-1 \".concat((((_formData_skills_technical3 = formData.skills.technical) === null || _formData_skills_technical3 === void 0 ? void 0 : _formData_skills_technical3.length) || 0) + (((_formData_skills_languages1 = formData.skills.languages) === null || _formData_skills_languages1 === void 0 ? void 0 : _formData_skills_languages1.length) || 0) >= 5 ? 'text-green-400' : 'text-yellow-400'),\n                                            children: (((_formData_skills_technical4 = formData.skills.technical) === null || _formData_skills_technical4 === void 0 ? void 0 : _formData_skills_technical4.length) || 0) + (((_formData_skills_languages2 = formData.skills.languages) === null || _formData_skills_languages2 === void 0 ? void 0 : _formData_skills_languages2.length) || 0) >= 5 ? 'Well-rounded' : 'Add more skills'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                            lineNumber: 362,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                    lineNumber: 357,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                            lineNumber: 332,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                    lineNumber: 326,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n            lineNumber: 210,\n            columnNumber: 5\n        }, undefined);\n    };\n    // Render Optimization Tab\n    const renderOptimizationTab = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-r from-neural-purple/10 to-neural-pink/10 border border-neural-purple/30 rounded-xl p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-white mb-3 flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    className: \"h-5 w-5 text-neural-purple\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                    lineNumber: 379,\n                                    columnNumber: 11\n                                }, undefined),\n                                \"Target Job Optimization\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                            lineNumber: 378,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-300 text-sm mb-4\",\n                            children: \"Paste a job description to optimize your resume for specific requirements and keywords.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                            lineNumber: 382,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EnhancedFormField__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            label: \"Job Description\",\n                            type: \"textarea\",\n                            value: jobDescription,\n                            onChange: handleJobDescriptionChange,\n                            placeholder: \"Paste the job description here for AI-powered resume optimization...\",\n                            rows: 6,\n                            variant: \"modern\",\n                            helpText: \"Adding a job description helps our AI tailor your resume to match specific requirements and improve ATS compatibility.\",\n                            hint: \"\\uD83D\\uDCA1 This helps optimize keywords and highlight relevant experience\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                            lineNumber: 386,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                    lineNumber: 377,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-800/50 backdrop-blur-sm rounded-xl p-6 border border-gray-700\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-white mb-4 flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    className: \"h-5 w-5 text-green-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                    lineNumber: 402,\n                                    columnNumber: 11\n                                }, undefined),\n                                \"ATS Compatibility Score\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                            lineNumber: 401,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center p-4 bg-green-500/10 border border-green-500/20 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl font-bold text-green-400 mb-1\",\n                                            children: \"95%\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                            lineNumber: 408,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-green-300 text-sm font-medium\",\n                                            children: \"ATS Score\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                            lineNumber: 409,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-green-400/70 text-xs mt-1\",\n                                            children: \"Excellent\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                            lineNumber: 410,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                    lineNumber: 407,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center p-4 bg-blue-500/10 border border-blue-500/20 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl font-bold text-blue-400 mb-1\",\n                                            children: \"12\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                            lineNumber: 414,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-blue-300 text-sm font-medium\",\n                                            children: \"Keywords Matched\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                            lineNumber: 415,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-blue-400/70 text-xs mt-1\",\n                                            children: \"Good coverage\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                            lineNumber: 416,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                    lineNumber: 413,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center p-4 bg-purple-500/10 border border-purple-500/20 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl font-bold text-purple-400 mb-1\",\n                                            children: \"A+\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                            lineNumber: 420,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-purple-300 text-sm font-medium\",\n                                            children: \"Format Grade\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                            lineNumber: 421,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-purple-400/70 text-xs mt-1\",\n                                            children: \"Perfect structure\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                            lineNumber: 422,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                    lineNumber: 419,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                            lineNumber: 406,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between p-3 bg-green-500/10 border border-green-500/20 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"h-4 w-4 text-green-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                    lineNumber: 429,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-300 text-sm font-medium\",\n                                                    children: \"Single-column layout\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                    lineNumber: 430,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                            lineNumber: 428,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-green-400 text-xs\",\n                                            children: \"Optimal\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                            lineNumber: 432,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                    lineNumber: 427,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between p-3 bg-green-500/10 border border-green-500/20 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"h-4 w-4 text-green-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                    lineNumber: 437,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-300 text-sm font-medium\",\n                                                    children: \"Standard fonts used\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                    lineNumber: 438,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                            lineNumber: 436,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-green-400 text-xs\",\n                                            children: \"Perfect\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                            lineNumber: 440,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                    lineNumber: 435,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between p-3 bg-green-500/10 border border-green-500/20 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"h-4 w-4 text-green-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                    lineNumber: 445,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-300 text-sm font-medium\",\n                                                    children: \"Clear section headers\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                    lineNumber: 446,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                            lineNumber: 444,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-green-400 text-xs\",\n                                            children: \"Excellent\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                            lineNumber: 448,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                    lineNumber: 443,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between p-3 bg-yellow-500/10 border border-yellow-500/20 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-4 w-4 text-yellow-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                    lineNumber: 453,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-yellow-300 text-sm font-medium\",\n                                                    children: \"Add more industry keywords\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                    lineNumber: 454,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                            lineNumber: 452,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-yellow-400 text-xs\",\n                                            children: \"Improve\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                            lineNumber: 456,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                    lineNumber: 451,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                            lineNumber: 426,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                    lineNumber: 400,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-800/50 backdrop-blur-sm rounded-xl p-6 border border-gray-700\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-white mb-4 flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-5 w-5 text-neural-purple\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                    lineNumber: 464,\n                                    columnNumber: 11\n                                }, undefined),\n                                \"AI Enhancement Suggestions\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                            lineNumber: 463,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 bg-neural-purple/10 border border-neural-purple/20 rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 bg-neural-purple/20 rounded-lg flex items-center justify-center flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"h-4 w-4 text-neural-purple\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                    lineNumber: 472,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                lineNumber: 471,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium text-white mb-1\",\n                                                        children: \"Enhance Professional Summary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                        lineNumber: 475,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-gray-300 text-sm mb-2\",\n                                                        children: \"Add more quantifiable achievements and industry-specific keywords to your summary.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                        lineNumber: 476,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"text-neural-purple text-sm hover:text-neural-purple/80 transition-colors\",\n                                                        children: \"Apply Suggestion →\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                        lineNumber: 479,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                lineNumber: 474,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                        lineNumber: 470,\n                                        columnNumber: 13\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                    lineNumber: 469,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 bg-blue-500/10 border border-blue-500/20 rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 bg-blue-500/20 rounded-lg flex items-center justify-center flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"h-4 w-4 text-blue-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                    lineNumber: 489,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                lineNumber: 488,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium text-white mb-1\",\n                                                        children: \"Optimize Experience Descriptions\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                        lineNumber: 492,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-gray-300 text-sm mb-2\",\n                                                        children: \"Use more action verbs and quantify your achievements with specific metrics.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                        lineNumber: 493,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"text-blue-400 text-sm hover:text-blue-400/80 transition-colors\",\n                                                        children: \"Apply Suggestion →\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                        lineNumber: 496,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                lineNumber: 491,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                        lineNumber: 487,\n                                        columnNumber: 13\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                    lineNumber: 486,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 bg-green-500/10 border border-green-500/20 rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 bg-green-500/20 rounded-lg flex items-center justify-center flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"h-4 w-4 text-green-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                    lineNumber: 506,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                lineNumber: 505,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium text-white mb-1\",\n                                                        children: \"Add Relevant Skills\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                        lineNumber: 509,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-gray-300 text-sm mb-2\",\n                                                        children: \"Include these trending skills in your field: React, TypeScript, AWS, Docker.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                        lineNumber: 510,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"text-green-400 text-sm hover:text-green-400/80 transition-colors\",\n                                                        children: \"Apply Suggestion →\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                        lineNumber: 513,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                lineNumber: 508,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                        lineNumber: 504,\n                                        columnNumber: 13\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                    lineNumber: 503,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                            lineNumber: 468,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                    lineNumber: 462,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-800/50 backdrop-blur-sm rounded-xl p-6 border border-gray-700\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-white mb-4 flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                    className: \"h-5 w-5 text-neural-purple\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                    lineNumber: 525,\n                                    columnNumber: 11\n                                }, undefined),\n                                \"Template Recommendations\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                            lineNumber: 524,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 bg-neural-purple/10 border border-neural-purple/20 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"h-4 w-4 text-neural-purple\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                    lineNumber: 532,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium text-white\",\n                                                    children: \"Current: Classic ATS\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                    lineNumber: 533,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                            lineNumber: 531,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-300 text-sm mb-3\",\n                                            children: \"Perfect for maximum ATS compatibility with traditional formatting.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                            lineNumber: 535,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-green-400 rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                    lineNumber: 539,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-400 text-sm\",\n                                                    children: \"100% ATS Score\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                    lineNumber: 540,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                            lineNumber: 538,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                    lineNumber: 530,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 bg-gray-700/30 border border-gray-600 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    className: \"h-4 w-4 text-yellow-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                    lineNumber: 546,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium text-white\",\n                                                    children: \"Recommended: Modern Minimal\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                    lineNumber: 547,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                            lineNumber: 545,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-300 text-sm mb-3\",\n                                            children: \"Clean contemporary design with subtle styling for your industry.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                            lineNumber: 549,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-green-400 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                            lineNumber: 554,\n                                                            columnNumber: 17\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-green-400 text-sm\",\n                                                            children: \"98% ATS Score\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                            lineNumber: 555,\n                                                            columnNumber: 17\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                    lineNumber: 553,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"text-neural-purple text-sm hover:text-neural-purple/80 transition-colors\",\n                                                    children: \"Switch Template\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                    lineNumber: 557,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                            lineNumber: 552,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                    lineNumber: 544,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                            lineNumber: 529,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                    lineNumber: 523,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n            lineNumber: 375,\n            columnNumber: 5\n        }, undefined);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"sticky top-4 z-30 bg-gray-900/95 backdrop-blur-sm rounded-2xl border border-gray-700/50 p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-gradient-to-r from-neural-purple to-neural-pink rounded-xl flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"h-6 w-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                lineNumber: 580,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                            lineNumber: 579,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-2xl font-bold text-white\",\n                                                    children: \"Review & Generate\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                    lineNumber: 583,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-400 text-sm\",\n                                                    children: \"Finalize your professional resume\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                    lineNumber: 584,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                            lineNumber: 582,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                    lineNumber: 578,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-right\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 mb-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-3 h-3 rounded-full \".concat(completionScore >= 90 ? 'bg-green-400' : completionScore >= 70 ? 'bg-yellow-400' : 'bg-red-400')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                    lineNumber: 591,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white font-semibold\",\n                                                    children: [\n                                                        completionScore,\n                                                        \"% Complete\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                    lineNumber: 595,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                            lineNumber: 590,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-24 bg-gray-700 rounded-full h-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                                className: \"h-2 rounded-full \".concat(completionScore >= 90 ? 'bg-green-400' : completionScore >= 70 ? 'bg-yellow-400' : 'bg-red-400'),\n                                                initial: {\n                                                    width: 0\n                                                },\n                                                animate: {\n                                                    width: \"\".concat(completionScore, \"%\")\n                                                },\n                                                transition: {\n                                                    duration: 0.5\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                lineNumber: 598,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                            lineNumber: 597,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                    lineNumber: 589,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                            lineNumber: 577,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-1 bg-gray-800/50 rounded-lg p-1\",\n                            children: [\n                                {\n                                    id: 'overview',\n                                    label: 'Overview',\n                                    icon: _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n                                },\n                                {\n                                    id: 'content',\n                                    label: 'Content Review',\n                                    icon: _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"]\n                                },\n                                {\n                                    id: 'optimization',\n                                    label: 'Optimization',\n                                    icon: _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n                                }\n                            ].map((tab)=>{\n                                const Icon = tab.icon;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setActiveTab(tab.id),\n                                    className: \"flex items-center gap-2 px-4 py-2 rounded-lg transition-all duration-200 \".concat(activeTab === tab.id ? 'bg-neural-purple text-white shadow-lg' : 'text-gray-400 hover:text-white hover:bg-gray-700/50'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                            lineNumber: 629,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium\",\n                                            children: tab.label\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                            lineNumber: 630,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, tab.id, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                    lineNumber: 620,\n                                    columnNumber: 17\n                                }, undefined);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                            lineNumber: 612,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onSave,\n                                    className: \"flex items-center gap-2 px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                            lineNumber: 642,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Save Draft\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                            lineNumber: 643,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                    lineNumber: 638,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowTemplateSelector(true),\n                                    className: \"flex items-center gap-2 px-4 py-2 bg-neural-purple/20 hover:bg-neural-purple/30 text-neural-purple border border-neural-purple/30 rounded-lg transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                            lineNumber: 650,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Change Template\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                            lineNumber: 651,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                    lineNumber: 646,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 text-sm text-gray-400\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Template:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                            lineNumber: 655,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-white font-medium capitalize\",\n                                            children: (selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.replace('_', ' ')) || 'Classic ATS'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                            lineNumber: 656,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                    lineNumber: 654,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                            lineNumber: 637,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                    lineNumber: 571,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                lineNumber: 570,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_24__.AnimatePresence, {\n                mode: \"wait\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    exit: {\n                        opacity: 0,\n                        y: -20\n                    },\n                    transition: {\n                        duration: 0.2\n                    },\n                    className: \"space-y-6\",\n                    children: [\n                        activeTab === 'overview' && renderOverviewTab(),\n                        activeTab === 'content' && renderContentTab(),\n                        activeTab === 'optimization' && renderOptimizationTab()\n                    ]\n                }, activeTab, true, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                    lineNumber: 666,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                lineNumber: 665,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_24__.AnimatePresence, {\n                children: showTemplateSelector && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    className: \"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            scale: 0.95\n                        },\n                        animate: {\n                            opacity: 1,\n                            scale: 1\n                        },\n                        exit: {\n                            opacity: 0,\n                            scale: 0.95\n                        },\n                        className: \"bg-gray-900 rounded-2xl border border-gray-700 w-full max-w-6xl max-h-[90vh] overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EnhancedTemplateSelector__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                selectedTemplate: selectedTemplate,\n                                onTemplateSelect: (templateId)=>{\n                                    onTemplateSelect(templateId);\n                                    setShowTemplateSelector(false);\n                                },\n                                formData: formData,\n                                showPreview: true\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                lineNumber: 695,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 border-t border-gray-700 flex justify-end\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowTemplateSelector(false),\n                                    className: \"px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors\",\n                                    children: \"Close\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                    lineNumber: 705,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                lineNumber: 704,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                        lineNumber: 689,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                    lineNumber: 683,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                lineNumber: 681,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n        lineNumber: 568,\n        columnNumber: 5\n    }, undefined);\n};\n_s(EnhancedReviewForm, \"6UkKSbXdkAY97MFqygISNkFmYGs=\");\n_c = EnhancedReviewForm;\nvar _c;\n$RefreshReg$(_c, \"EnhancedReviewForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/resume/forms/ReviewForm.jsx\n"));

/***/ })

});
// Firebase Database Initialization Script
// Run this script to populate your Firebase database with initial data

import { firestore, database } from './config.js';
import { collection, doc, setDoc, addDoc } from 'firebase/firestore';
import { ref, set } from 'firebase/database';

// Sample data for initial setup
const initialData = {
  // Sample users for testing
  users: [
    {
      id: 'demo-user-1',
      name: '<PERSON>',
      email: '<EMAIL>',
      profilePicture: '/public/OurTeamImages/portfolio.jpg',
      joinedAt: new Date().toISOString(),
      isActive: true
    },
    {
      id: 'demo-user-2',
      name: '<PERSON>',
      email: '<EMAIL>',
      profilePicture: '/public/OurTeamImages/portfolio1.jpg',
      joinedAt: new Date(Date.now() - 86400000).toISOString(), // Yesterday
      isActive: true
    }
  ],

  // Sample reviews
  reviews: [
    {
      userId: 'demo-user-1',
      userName: '<PERSON>',
      rating: 5,
      comment: '<PERSON><PERSON><PERSON><PERSON> helped me land my dream job! The resume builder is amazing.',
      createdAt: new Date().toISOString(),
      category: 'resume-builder'
    },
    {
      userId: 'demo-user-2',
      userName: '<PERSON>',
      rating: 4,
      comment: 'Great platform for job searching. Very user-friendly interface.',
      createdAt: new Date(Date.now() - 3600000).toISOString(), // 1 hour ago
      category: 'job-search'
    }
  ],

  // Platform statistics for real-time display
  stats: {
    totalUsers: 1234,
    totalReviews: 456,
    totalApplications: 789,
    activeJobs: 150,
    lastUpdated: new Date().toISOString()
  }
};

export const initializeFirebaseData = async () => {
  console.log('🚀 Initializing Firebase database with sample data...');
  
  try {
    // Initialize Firestore collections
    console.log('📝 Setting up Firestore collections...');
    
    // Add sample users
    for (const user of initialData.users) {
      await setDoc(doc(firestore, 'users', user.id), user);
      console.log(`✅ Added user: ${user.name}`);
    }

    // Add sample reviews
    for (const review of initialData.reviews) {
      await addDoc(collection(firestore, 'reviews'), review);
      console.log(`✅ Added review by: ${review.userName}`);
    }

    // Initialize Realtime Database stats
    console.log('📊 Setting up Realtime Database stats...');
    await set(ref(database, 'stats'), initialData.stats);
    console.log('✅ Platform statistics initialized');

    // Create public stats collection in Firestore as well
    await setDoc(doc(firestore, 'public', 'stats'), initialData.stats);
    console.log('✅ Public stats collection created');

    console.log('🎉 Firebase database initialization complete!');
    return { success: true, message: 'Database initialized successfully' };

  } catch (error) {
    console.error('❌ Firebase initialization failed:', error);
    return { success: false, error: error.message };
  }
};

// Auto-run in development if called directly
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  // Make initialization function available globally for manual testing
  window.initializeFirebaseData = initializeFirebaseData;
  console.log('🔧 Firebase initialization function available as window.initializeFirebaseData()');
}

export default initializeFirebaseData;

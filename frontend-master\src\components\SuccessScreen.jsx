import { motion } from "framer-motion";
import {
  CheckCircle,
  Download,
  Eye,
  Share2,
  RefreshCw,
  FileText,
  Sparkles,
  Target,
  TrendingUp,
  Award,
  BarChart3
} from "lucide-react";
import { useState, useRef, useEffect } from "react";
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import ResumeTemplateSelector from './ResumeTemplateSelector';
import AuthGuardModal from './auth/AuthGuard';
import ReviewModal from './ReviewModal';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'react-hot-toast';

const SuccessScreen = ({ formData, resumeData, onStartOver, onEditResume }) => {
  const [isDownloading, setIsDownloading] = useState(false);
  const [showFullPreview, setShowFullPreview] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState('ats_optimized');
  const [showTemplateSelector, setShowTemplateSelector] = useState(false);
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [showReviewModal, setShowReviewModal] = useState(false);
  const [hasSubmittedReview, setHasSubmittedReview] = useState(false);
  const [mounted, setMounted] = useState(false);
  const resumeRef = useRef();
  const { currentUser } = useAuth();

  // Check mounting state to avoid hydration mismatch
  useEffect(() => {
    setMounted(true);
  }, []);

  // Check if user has already reviewed (stored in localStorage for simplicity)
  const hasUserReviewed = () => {
    if (!currentUser || !mounted) return false;
    const reviewedUsers = JSON.parse(localStorage.getItem('reviewedUsers') || '[]');
    return reviewedUsers.includes(currentUser.uid);
  };

  const handleDownloadClick = () => {
    // Check if user is authenticated
    if (!currentUser) {
      setShowAuthModal(true);
      return;
    }

    // Check if user has submitted a review
    if (!hasUserReviewed() && !hasSubmittedReview) {
      setShowReviewModal(true);
      return;
    }

    // Proceed with download
    downloadPDF();
  };  const handleReviewSubmit = async (reviewData) => {
    try {
      // Save review (API call is handled in ReviewModal)
      console.log('Review submitted:', reviewData);
      
      // Mark user as having reviewed
      if (mounted) {
        const reviewedUsers = JSON.parse(localStorage.getItem('reviewedUsers') || '[]');
        if (currentUser && !reviewedUsers.includes(currentUser.uid)) {
          reviewedUsers.push(currentUser.uid);
          localStorage.setItem('reviewedUsers', JSON.stringify(reviewedUsers));
        }
      }
      
      setHasSubmittedReview(true);
      setShowReviewModal(false);
      
      // Show success and proceed with download
      toast.success('🎉 Review submitted successfully! Starting download...');
      
      // Now proceed with download after a short delay
      setTimeout(() => {
        downloadPDF();
      }, 1500);
    } catch (error) {
      console.error('Error submitting review:', error);
      toast.error('Failed to submit review. Please try again.');
      throw error;
    }
  };  const downloadPDF = async () => {
    try {
      setIsDownloading(true);      // Create a temporary div with improved PDF-optimized styling
      const tempDiv = document.createElement('div');
      tempDiv.style.position = 'absolute';
      tempDiv.style.left = '-9999px';
      tempDiv.style.width = '210mm'; // A4 width
      tempDiv.style.backgroundColor = 'white';
      tempDiv.style.color = 'black';
      tempDiv.style.padding = '15mm 20mm'; // Reduced top/bottom padding from 20mm to 15mm
      tempDiv.style.fontSize = '12px';
      tempDiv.style.lineHeight = '1.6';
      tempDiv.style.fontFamily = 'Arial, sans-serif';

      // Generate PDF-optimized resume content
      const resumeContent = formatResumeForPDFDownload(selectedTemplate, formData, resumeData);
      tempDiv.innerHTML = resumeContent;

      document.body.appendChild(tempDiv);      // Enhanced PDF generation settings
      const canvas = await html2canvas(tempDiv, {
        scale: 3,
        useCORS: true,
        backgroundColor: '#ffffff',
        allowTaint: true,
        foreignObjectRendering: false,
        imageTimeout: 15000,
        removeContainer: true,
        logging: false,
        letterRendering: true,
        width: tempDiv.scrollWidth,
        height: tempDiv.scrollHeight,
        ignoreElements: (element) => {
          // Ignore any problematic elements that might cause rendering issues
          return element.tagName === 'SCRIPT' || element.tagName === 'NOSCRIPT';
        }
      });

      const imgData = canvas.toDataURL('image/png');
      const pdf = new jsPDF('p', 'mm', 'a4');

      const imgWidth = 210; // A4 width in mm
      const pageHeight = 295; // A4 height in mm
      const imgHeight = (canvas.height * imgWidth) / canvas.width;
      let heightLeft = imgHeight;

      let position = 0;

      pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
      heightLeft -= pageHeight;

      while (heightLeft >= 0) {
        position = heightLeft - imgHeight;
        pdf.addPage();
        pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
        heightLeft -= pageHeight;
      }

      // Download the PDF
      const templateNames = {
        ats_optimized: 'ATS_Champion',
        modern: 'Professional_Standard',
        executive: 'Executive_Professional',
        minimal: 'Clean_Minimalist',
        tech: 'Tech_Professional',
        classic: 'Traditional_Professional'
      };      const templateName = templateNames[selectedTemplate] || 'Professional';
      const fileName = `${formData.personal.firstName}_${formData.personal.lastName}_Resume_${templateName}.pdf`;
      pdf.save(fileName);

      // Show success message
      toast.success('Resume PDF downloaded successfully!');      // Clean up
      document.body.removeChild(tempDiv);

    } catch (error) {
      console.error('Error generating PDF:', error);
      toast.error('Error generating PDF. Please try again.');
    } finally {
      setIsDownloading(false);
    }
  };  // PDF-Optimized resume formatter (improved spacing, reduced padding, no footer watermark)
  const formatResumeForPDFDownload = (template, formData, resumeData) => {
    const { personal } = formData;
    const enhanced = resumeData?.enhancedContent || {};

    // Template color schemes
    const colors = {
      ats_optimized: { accent: '#000000', header: '#000000', body: '#333333' },
      modern: { accent: '#2563eb', header: '#1f2937', body: '#374151' },
      executive: { accent: '#1e40af', header: '#111827', body: '#374151' },
      minimal: { accent: '#374151', header: '#111827', body: '#374151' },
      tech: { accent: '#059669', header: '#065f46', body: '#374151' },
      classic: { accent: '#1f2937', header: '#111827', body: '#374151' }
    };

    const color = colors[template] || colors.ats_optimized;return `
      <div style="font-family: Arial, sans-serif; color: ${color.body}; line-height: 1.6; max-width: 100%; margin: 0; padding: 0; box-sizing: border-box;">
          <!-- Header Section -->
        <div style="text-align: center; margin-bottom: 20px; padding-bottom: 15px;">
          <h1 style="margin: 0 0 10px 0; font-size: 22pt; font-weight: bold; color: ${color.header}; text-transform: uppercase; letter-spacing: 1px;">
            ${personal.firstName || 'FIRST'} ${personal.lastName || 'LAST'}
          </h1>
          <div style="margin: 8px 0; color: ${color.body}; font-size: 11pt; font-weight: 500;">
            ${personal.email ? `${personal.email}` : ''}${personal.phone ? ` | ${personal.phone}` : ''}${personal.location ? ` | ${personal.location}` : ''}
          </div>
          ${personal.linkedin ? `<div style="margin: 6px 0; color: ${color.accent}; font-size: 10pt;">${personal.linkedin}</div>` : ''}
          <div style="width: 60%; height: 2px; background: ${color.accent}; margin: 12px auto;"></div>
        </div>        <!-- Professional Summary -->
        ${enhanced.professionalSummary ? `
          <div style="margin-bottom: 20px;">
            <h2 style="font-size: 14pt; font-weight: bold; color: ${color.header}; margin: 0 0 10px 0; text-transform: uppercase; letter-spacing: 0.8px;">
              PROFESSIONAL SUMMARY
            </h2>
            <hr style="border: none; border-top: 2px solid ${color.accent}; width: 50px; margin: 0 0 15px 0;" />
            <p style="margin: 0; font-size: 11pt; line-height: 1.5; color: ${color.body}; text-align: justify;">
              ${enhanced.professionalSummary}
            </p>
          </div>
        ` : ''}        <!-- Core Competencies -->
        ${enhanced.skills && enhanced.skills.technical?.length > 0 ? `
          <div style="margin-bottom: 20px;">
            <h2 style="font-size: 14pt; font-weight: bold; color: ${color.header}; margin: 0 0 10px 0; text-transform: uppercase; letter-spacing: 0.8px;">
              CORE COMPETENCIES
            </h2>
            <hr style="border: none; border-top: 2px solid ${color.accent}; width: 50px; margin: 0 0 15px 0;" />
            <p style="margin: 0; font-size: 11pt; line-height: 1.5; color: ${color.body};">
              ${enhanced.skills.technical.join(' • ')}
            </p>
          </div>
        ` : ''}        <!-- Professional Experience -->
        ${enhanced.experience && enhanced.experience.length > 0 ? `
          <div style="margin-bottom: 20px;">
            <h2 style="font-size: 14pt; font-weight: bold; color: ${color.header}; margin: 0 0 10px 0; text-transform: uppercase; letter-spacing: 0.8px;">
              PROFESSIONAL EXPERIENCE
            </h2>
            <hr style="border: none; border-top: 2px solid ${color.accent}; width: 50px; margin: 0 0 15px 0;" />
            ${enhanced.experience.map(exp => `
              <div style="margin-bottom: 18px;">
                <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 8px;">
                  <div style="flex: 1;">
                    <h3 style="margin: 0; font-size: 13pt; font-weight: bold; color: ${color.header};">${exp.title}</h3>
                    <p style="margin: 3px 0; font-size: 12pt; color: ${color.accent}; font-weight: 600;">${exp.company}</p>
                    ${exp.location ? `<p style="margin: 1px 0; font-size: 10pt; color: ${color.body};">${exp.location}</p>` : ''}
                  </div>
                  <div style="text-align: right; font-size: 11pt; color: ${color.body}; font-weight: 500; margin-left: 20px; white-space: nowrap;">
                    ${exp.startDate} - ${exp.endDate}
                  </div>
                </div>
                ${exp.achievements && exp.achievements.length > 0 ? `
                  <ul style="margin: 6px 0 0 20px; padding: 0; list-style-type: disc;">
                    ${exp.achievements.map(achievement => `
                      <li style="margin-bottom: 4px; font-size: 11pt; line-height: 1.5; color: ${color.body};">${achievement}</li>
                    `).join('')}
                  </ul>
                ` : ''}
              </div>
            `).join('')}
          </div>
        ` : ''}        <!-- Education -->
        ${enhanced.education && enhanced.education.length > 0 ? `
          <div style="margin-bottom: 20px;">
            <h2 style="font-size: 14pt; font-weight: bold; color: ${color.header}; margin: 0 0 10px 0; text-transform: uppercase; letter-spacing: 0.8px;">
              EDUCATION
            </h2>
            <hr style="border: none; border-top: 2px solid ${color.accent}; width: 50px; margin: 0 0 15px 0;" />
            ${enhanced.education.map(edu => `
              <div style="margin-bottom: 12px;">
                <div style="display: flex; justify-content: space-between; align-items: flex-start;">
                  <div style="flex: 1;">
                    <h3 style="margin: 0; font-size: 13pt; font-weight: bold; color: ${color.header};">${edu.degree}</h3>
                    <p style="margin: 3px 0; font-size: 12pt; color: ${color.accent};">${edu.institution}</p>
                    ${edu.location ? `<p style="margin: 1px 0; font-size: 10pt; color: ${color.body};">${edu.location}</p>` : ''}
                  </div>
                  <div style="text-align: right; font-size: 11pt; color: ${color.body}; font-weight: 500; margin-left: 20px; white-space: nowrap;">
                    ${edu.startDate} - ${edu.endDate}
                  </div>
                </div>
                ${edu.gpa ? `<p style="margin: 3px 0; font-size: 11pt; color: ${color.body};">GPA: ${edu.gpa}</p>` : ''}
              </div>
            `).join('')}
          </div>
        ` : ''}

        <!-- Skills & Certifications -->
        ${enhanced.skills && (enhanced.skills.languages?.length > 0 || enhanced.skills.certifications?.length > 0) ? `
          <div style="margin-bottom: 25px;">
            ${enhanced.skills.languages?.length > 0 ? `
              <div style="margin-bottom: 15px;">
                <h3 style="font-size: 13pt; font-weight: bold; color: ${color.header}; margin: 0 0 8px 0;">LANGUAGES</h3>
                <p style="margin: 0; font-size: 11pt; color: ${color.body};">${enhanced.skills.languages.join(' • ')}</p>
              </div>
            ` : ''}
            ${enhanced.skills.certifications?.length > 0 ? `
              <div style="margin-bottom: 15px;">
                <h3 style="font-size: 13pt; font-weight: bold; color: ${color.header}; margin: 0 0 8px 0;">CERTIFICATIONS</h3>
                <p style="margin: 0; font-size: 11pt; color: ${color.body};">${enhanced.skills.certifications.join(' • ')}</p>
              </div>
            ` : ''}
          </div>
        ` : ''}        <!-- Key Projects -->
        ${enhanced.projects && enhanced.projects.length > 0 && enhanced.projects.length <= 2 ? `
          <div style="margin-bottom: 25px;">
            <h2 style="font-size: 14pt; font-weight: bold; color: ${color.header}; margin: 0 0 12px 0; text-transform: uppercase; letter-spacing: 0.8px; position: relative;">
              KEY PROJECTS
            </h2>
            <hr style="border: none; border-top: 2px solid ${color.accent}; width: 50px; margin: 0 0 20px 0;" />
            ${enhanced.projects.slice(0, 2).map(project => `
              <div style="margin-bottom: 12px;">
                <h3 style="margin: 0 0 4px 0; font-size: 13pt; font-weight: bold; color: ${color.header};">${project.name}</h3>
                <p style="margin: 0; font-size: 11pt; line-height: 1.6; color: ${color.body};">${project.description}</p>
                ${project.technologies ? `
                  <p style="margin: 4px 0 0 0; font-size: 10pt; color: ${color.body}; font-style: italic;">Technologies: ${project.technologies}</p>
                ` : ''}
              </div>
            `).join('')}          </div>
        ` : ''}
      </div>
    `;
  };

  const formatResumeForPDF = (formData, resumeData) => {
    const { personal } = formData;
    const enhanced = resumeData?.enhancedContent || {};

    return `
      <div style="max-width: 100%; margin: 0 auto; font-family: 'Arial', 'Helvetica', sans-serif; line-height: 1.4; color: #333;">
        <!-- Header -->
        <div style="text-align: center; border-bottom: 3px solid #2563eb; padding-bottom: 20px; margin-bottom: 25px; background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%); padding: 20px; border-radius: 8px;">
          <h1 style="margin: 0; font-size: 28px; font-weight: bold; color: #1e293b; letter-spacing: 1px;">
            ${personal.firstName} ${personal.lastName}
          </h1>
          <div style="margin-top: 10px; color: #475569; font-size: 12px; font-weight: 500;">
            ${personal.email}${personal.phone ? ` • ${personal.phone}` : ''}${personal.location ? ` • ${personal.location}` : ''}
          </div>
          ${personal.linkedin ? `<div style="margin-top: 6px; color: #2563eb; font-size: 11px; font-weight: 500;">${personal.linkedin}</div>` : ''}
          ${personal.portfolio ? `<div style="margin-top: 4px; color: #2563eb; font-size: 11px; font-weight: 500;">${personal.portfolio}</div>` : ''}
        </div>

        <!-- Professional Summary -->
        ${enhanced.professionalSummary ? `
          <div style="margin-bottom: 25px;">
            <h2 style="font-size: 16px; font-weight: bold; color: #1e293b; border-bottom: 2px solid #2563eb; padding-bottom: 6px; margin-bottom: 12px; text-transform: uppercase; letter-spacing: 0.5px;">
              PROFESSIONAL SUMMARY
            </h2>
            <p style="margin: 0; color: #374151; line-height: 1.6; font-size: 12px; text-align: justify;">
              ${enhanced.professionalSummary}
            </p>
          </div>
        ` : ''}

        <!-- Experience -->
        ${enhanced.experience && enhanced.experience.length > 0 ? `
          <div style="margin-bottom: 25px;">
            <h2 style="font-size: 16px; font-weight: bold; color: #1e293b; border-bottom: 2px solid #2563eb; padding-bottom: 6px; margin-bottom: 12px; text-transform: uppercase; letter-spacing: 0.5px;">
              PROFESSIONAL EXPERIENCE
            </h2>
            ${enhanced.experience.map(exp => `
              <div style="margin-bottom: 18px; border-left: 3px solid #e2e8f0; padding-left: 15px;">
                <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 8px;">
                  <div style="flex: 1;">
                    <h3 style="margin: 0; font-size: 14px; font-weight: bold; color: #1e293b;">${exp.title}</h3>
                    <p style="margin: 2px 0; font-size: 12px; color: #2563eb; font-weight: 600;">${exp.company}</p>
                    ${exp.location ? `<p style="margin: 1px 0; font-size: 10px; color: #64748b;">${exp.location}</p>` : ''}
                  </div>
                  <div style="text-align: right; font-size: 10px; color: #64748b; font-weight: 500; background: #f1f5f9; padding: 4px 8px; border-radius: 4px;">
                    ${exp.startDate} - ${exp.endDate}
                  </div>
                </div>
                ${exp.achievements && exp.achievements.length > 0 ? `
                  <ul style="margin: 8px 0 0 0; padding-left: 0; list-style: none;">
                    ${exp.achievements.map(achievement => `
                      <li style="margin-bottom: 4px; font-size: 11px; color: #374151; line-height: 1.5; position: relative; padding-left: 12px;">
                        <span style="position: absolute; left: 0; top: 0; color: #2563eb; font-weight: bold;">•</span>
                        ${achievement}
                      </li>
                    `).join('')}
                  </ul>
                ` : ''}
              </div>
            `).join('')}
          </div>
        ` : ''}

        <!-- Education -->
        ${enhanced.education && enhanced.education.length > 0 ? `
          <div style="margin-bottom: 25px;">
            <h2 style="font-size: 16px; font-weight: bold; color: #1e293b; border-bottom: 2px solid #2563eb; padding-bottom: 6px; margin-bottom: 12px; text-transform: uppercase; letter-spacing: 0.5px;">
              EDUCATION
            </h2>
            ${enhanced.education.map(edu => edu.degree ? `
              <div style="margin-bottom: 12px; border-left: 3px solid #e2e8f0; padding-left: 15px;">
                <div style="display: flex; justify-content: space-between; align-items: flex-start;">
                  <div style="flex: 1;">
                    <h3 style="margin: 0; font-size: 13px; font-weight: bold; color: #1e293b;">${edu.degree}</h3>
                    <p style="margin: 2px 0; font-size: 12px; color: #2563eb; font-weight: 600;">${edu.institution}</p>
                    ${edu.location ? `<p style="margin: 1px 0; font-size: 10px; color: #64748b;">${edu.location}</p>` : ''}
                  </div>
                  <div style="text-align: right; font-size: 10px; color: #64748b; font-weight: 500; background: #f1f5f9; padding: 4px 8px; border-radius: 4px;">
                    ${edu.startDate} - ${edu.endDate}
                  </div>
                </div>
                ${edu.gpa ? `<p style="margin: 4px 0; font-size: 10px; color: #374151; font-weight: 500;">GPA: ${edu.gpa}</p>` : ''}
                ${edu.relevant ? `<p style="margin: 6px 0; font-size: 11px; color: #374151; line-height: 1.4;">${edu.relevant}</p>` : ''}
              </div>
            ` : '').join('')}
          </div>
        ` : ''}

        <!-- Skills -->
        ${enhanced.skills && (enhanced.skills.technical?.length > 0 || enhanced.skills.languages?.length > 0 || enhanced.skills.certifications?.length > 0) ? `
          <div style="margin-bottom: 25px;">
            <h2 style="font-size: 16px; font-weight: bold; color: #1e293b; border-bottom: 2px solid #2563eb; padding-bottom: 6px; margin-bottom: 12px; text-transform: uppercase; letter-spacing: 0.5px;">
              CORE COMPETENCIES
            </h2>
            <div style="background: #f8fafc; padding: 15px; border-radius: 8px; border-left: 4px solid #2563eb;">
              ${enhanced.skills.technical?.length > 0 ? `
                <div style="margin-bottom: 10px;">
                  <span style="font-weight: bold; font-size: 12px; color: #1e293b; display: block; margin-bottom: 4px;">Technical Skills:</span>
                  <div style="display: flex; flex-wrap: wrap; gap: 6px;">
                    ${enhanced.skills.technical.map(skill => `
                      <span style="background: #2563eb; color: white; padding: 2px 8px; border-radius: 12px; font-size: 10px; font-weight: 500;">${skill}</span>
                    `).join('')}
                  </div>
                </div>
              ` : ''}
              ${enhanced.skills.languages?.length > 0 ? `
                <div style="margin-bottom: 10px;">
                  <span style="font-weight: bold; font-size: 12px; color: #1e293b; display: block; margin-bottom: 4px;">Languages:</span>
                  <span style="font-size: 11px; color: #374151;">${enhanced.skills.languages.join(' • ')}</span>
                </div>
              ` : ''}
              ${enhanced.skills.certifications?.length > 0 ? `
                <div style="margin-bottom: 0;">
                  <span style="font-weight: bold; font-size: 12px; color: #1e293b; display: block; margin-bottom: 4px;">Certifications:</span>
                  <span style="font-size: 11px; color: #374151;">${enhanced.skills.certifications.join(' • ')}</span>
                </div>
              ` : ''}
            </div>
          </div>
        ` : ''}

        <!-- Projects -->
        ${enhanced.projects && enhanced.projects.length > 0 ? `
          <div style="margin-bottom: 25px;">
            <h2 style="font-size: 16px; font-weight: bold; color: #1e293b; border-bottom: 2px solid #2563eb; padding-bottom: 6px; margin-bottom: 12px; text-transform: uppercase; letter-spacing: 0.5px;">
              KEY PROJECTS
            </h2>
            ${enhanced.projects.map(project => project.name ? `
              <div style="margin-bottom: 15px; border-left: 3px solid #e2e8f0; padding-left: 15px; background: #f8fafc; padding: 12px; border-radius: 6px;">
                <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 6px;">
                  <h3 style="margin: 0; font-size: 13px; font-weight: bold; color: #1e293b;">${project.name}</h3>
                  ${project.link ? `<a href="${project.link}" style="font-size: 10px; color: #2563eb; font-weight: 500; text-decoration: none;">View Project →</a>` : ''}
                </div>
                ${project.technologies ? `
                  <div style="margin-bottom: 6px;">
                    <span style="font-size: 10px; color: #64748b; font-weight: 500;">Technologies: </span>
                    <span style="font-size: 10px; color: #374151; font-style: italic;">${project.technologies}</span>
                  </div>
                ` : ''}
                ${project.description ? `<p style="margin: 6px 0 0 0; font-size: 11px; color: #374151; line-height: 1.4;">${project.description}</p>` : ''}
              </div>
            ` : '').join('')}
          </div>        ` : ''}
      </div>    `;
  };
  const formatResumeWithTemplate = (template, formData, resumeData) => {
    const { personal } = formData;
    const enhanced = resumeData?.enhancedContent || {};    // ATS-Optimized Template Styles - Based on 2024/2025 Best Practices
    const templateStyles = {
      ats_optimized: {
        headerBg: '#ffffff',
        accentColor: '#000000',
        fontFamily: "'Arial', 'Helvetica', sans-serif",
        sectionBorder: 'none',
        headerTextColor: '#000000',
        bodyTextColor: '#000000'
      },
      modern: {
        headerBg: '#ffffff',
        accentColor: '#2563eb',
        fontFamily: "'Arial', 'Helvetica', sans-serif",
        sectionBorder: 'none',
        headerTextColor: '#1f2937',
        bodyTextColor: '#374151'
      },
      executive: {
        headerBg: '#ffffff',
        accentColor: '#1e40af',
        fontFamily: "'Calibri', 'Arial', sans-serif",
        sectionBorder: 'none',
        headerTextColor: '#111827',
        bodyTextColor: '#374151'
      },
      minimal: {
        headerBg: '#ffffff',
        accentColor: '#374151',
        fontFamily: "'Arial', 'Helvetica', sans-serif",
        sectionBorder: 'none',
        headerTextColor: '#111827',
        bodyTextColor: '#374151'
      },
      tech: {
        headerBg: '#ffffff',
        accentColor: '#059669',
        fontFamily: "'Arial', 'Helvetica', sans-serif",
        sectionBorder: 'none',
        headerTextColor: '#065f46',
        bodyTextColor: '#374151'
      },
      classic: {
        headerBg: '#ffffff',
        accentColor: '#1f2937',
        fontFamily: "'Times New Roman', serif",
        sectionBorder: 'none',
        headerTextColor: '#111827',
        bodyTextColor: '#374151'
      }
    };

    const style = templateStyles[template] || templateStyles.ats_optimized;

    return `
      <div style="font-family: ${style.fontFamily}; color: ${style.bodyTextColor}; line-height: 1.4; max-width: 8.5in; margin: 0 auto; background: white; padding: 0.5in; font-size: 11pt;">
        
        <!-- Header Section - ATS Optimized -->
        <div style="text-align: center; margin-bottom: 20px; border-bottom: 1px solid #e5e7eb; padding-bottom: 15px;">
          <h1 style="margin: 0 0 8px 0; font-size: 18pt; font-weight: bold; color: ${style.headerTextColor}; text-transform: uppercase;">
            ${personal.firstName || 'FIRST'} ${personal.lastName || 'LAST'}
          </h1>
          <div style="margin: 8px 0; color: ${style.bodyTextColor}; font-size: 10pt;">
            ${personal.email ? `${personal.email}` : ''}${personal.phone ? ` | ${personal.phone}` : ''}${personal.location ? ` | ${personal.location}` : ''}
          </div>
          ${personal.linkedin ? `<div style="margin: 4px 0; color: ${style.accentColor}; font-size: 10pt;">${personal.linkedin}</div>` : ''}
        </div>        <!-- Professional Summary -->
        ${enhanced.professionalSummary ? `
          <div style="margin-bottom: 18px;">
            <h2 style="font-size: 12pt; font-weight: bold; color: ${style.headerTextColor}; margin: 0 0 12px 0; text-transform: uppercase;">
              PROFESSIONAL SUMMARY
            </h2>
            <hr style="border: none; border-top: 2px solid ${style.accentColor}; width: 50px; margin: 0 0 15px 0;" />
            <p style="margin: 0; font-size: 11pt; line-height: 1.4; text-align: justify;">
              ${enhanced.professionalSummary}
            </p>
          </div>
        ` : ''}        <!-- Core Competencies / Skills -->
        ${enhanced.skills && enhanced.skills.technical?.length > 0 ? `
          <div style="margin-bottom: 18px;">
            <h2 style="font-size: 12pt; font-weight: bold; color: ${style.headerTextColor}; margin: 0 0 12px 0; text-transform: uppercase;">
              CORE COMPETENCIES
            </h2>
            <hr style="border: none; border-top: 2px solid ${style.accentColor}; width: 50px; margin: 0 0 15px 0;" />
            <p style="margin: 0; font-size: 11pt; line-height: 1.4;">
              ${enhanced.skills.technical.join(' • ')}
            </p>
          </div>
        ` : ''}        <!-- Professional Experience -->
        ${enhanced.experience && enhanced.experience.length > 0 ? `
          <div style="margin-bottom: 18px;">
            <h2 style="font-size: 12pt; font-weight: bold; color: ${style.headerTextColor}; margin: 0 0 12px 0; text-transform: uppercase;">
              PROFESSIONAL EXPERIENCE
            </h2>
            <hr style="border: none; border-top: 2px solid ${style.accentColor}; width: 50px; margin: 0 0 15px 0;" />
            ${enhanced.experience.map(exp => `
              <div style="margin-bottom: 14px;">
                <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 4px;">
                  <div style="flex: 1;">
                    <h3 style="margin: 0; font-size: 11pt; font-weight: bold; color: ${style.headerTextColor};">${exp.title}</h3>
                    <p style="margin: 2px 0; font-size: 11pt; color: ${style.accentColor}; font-weight: 600;">${exp.company}</p>
                    ${exp.location ? `<p style="margin: 1px 0; font-size: 10pt; color: ${style.bodyTextColor};">${exp.location}</p>` : ''}
                  </div>
                  <div style="text-align: right; font-size: 10pt; color: ${style.bodyTextColor}; font-weight: 500;">
                    ${exp.startDate} - ${exp.endDate}
                  </div>
                </div>
                ${exp.achievements && exp.achievements.length > 0 ? `
                  <ul style="margin: 6px 0 0 16px; padding: 0; list-style-type: disc;">
                    ${exp.achievements.map(achievement => `
                      <li style="margin-bottom: 3px; font-size: 10pt; line-height: 1.3;">${achievement}</li>
                    `).join('')}
                  </ul>
                ` : ''}
              </div>
            `).join('')}
          </div>
        ` : ''}        <!-- Education -->
        ${enhanced.education && enhanced.education.length > 0 ? `
          <div style="margin-bottom: 18px;">
            <h2 style="font-size: 12pt; font-weight: bold; color: ${style.headerTextColor}; margin: 0 0 12px 0; text-transform: uppercase;">
              EDUCATION
            </h2>
            <hr style="border: none; border-top: 2px solid ${style.accentColor}; width: 50px; margin: 0 0 15px 0;" />
            ${enhanced.education.map(edu => `
              <div style="margin-bottom: 8px;">
                <div style="display: flex; justify-content: space-between; align-items: flex-start;">
                  <div style="flex: 1;">
                    <h3 style="margin: 0; font-size: 11pt; font-weight: bold; color: ${style.headerTextColor};">${edu.degree}</h3>
                    <p style="margin: 2px 0; font-size: 10pt; color: ${style.accentColor};">${edu.institution}</p>
                    ${edu.location ? `<p style="margin: 1px 0; font-size: 10pt; color: ${style.bodyTextColor};">${edu.location}</p>` : ''}
                  </div>
                  <div style="text-align: right; font-size: 10pt; color: ${style.bodyTextColor}; font-weight: 500;">
                    ${edu.startDate} - ${edu.endDate}
                  </div>
                </div>
                ${edu.gpa ? `<p style="margin: 3px 0; font-size: 10pt; color: ${style.bodyTextColor};">GPA: ${edu.gpa}</p>` : ''}
              </div>
            `).join('')}
          </div>
        ` : ''}

        <!-- Additional Skills/Certifications -->
        ${enhanced.skills && (enhanced.skills.languages?.length > 0 || enhanced.skills.certifications?.length > 0) ? `
          <div style="margin-bottom: 18px;">
            ${enhanced.skills.languages?.length > 0 ? `
              <div style="margin-bottom: 8px;">
                <h3 style="font-size: 11pt; font-weight: bold; color: ${style.headerTextColor}; margin: 0 0 4px 0;">LANGUAGES:</h3>
                <p style="margin: 0; font-size: 10pt;">${enhanced.skills.languages.join(' • ')}</p>
              </div>
            ` : ''}
            ${enhanced.skills.certifications?.length > 0 ? `
              <div style="margin-bottom: 8px;">
                <h3 style="font-size: 11pt; font-weight: bold; color: ${style.headerTextColor}; margin: 0 0 4px 0;">CERTIFICATIONS:</h3>
                <p style="margin: 0; font-size: 10pt;">${enhanced.skills.certifications.join(' • ')}</p>
              </div>
            ` : ''}
          </div>
        ` : ''}        <!-- Key Projects (if space allows) -->
        ${enhanced.projects && enhanced.projects.length > 0 && enhanced.projects.length <= 2 ? `
          <div style="margin-bottom: 12px;">
            <h2 style="font-size: 12pt; font-weight: bold; color: ${style.headerTextColor}; margin: 0 0 12px 0; text-transform: uppercase;">
              KEY PROJECTS
            </h2>
            <hr style="border: none; border-top: 2px solid ${style.accentColor}; width: 50px; margin: 0 0 15px 0;" />
            ${enhanced.projects.slice(0, 2).map(project => `
              <div style="margin-bottom: 6px;">
                <h3 style="margin: 0 0 2px 0; font-size: 11pt; font-weight: bold; color: ${style.headerTextColor};">${project.name}</h3>
                <p style="margin: 0; font-size: 10pt; line-height: 1.3;">${project.description}</p>
                ${project.technologies ? `
                  <p style="margin: 2px 0 0 0; font-size: 9pt; color: ${style.bodyTextColor}; font-style: italic;">Technologies: ${project.technologies}</p>
                ` : ''}
              </div>
            `).join('')}
          </div>        ` : ''}
      </div>
    `;
  };

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      className="min-h-screen bg-gradient-to-b from-black to-[#0A0A0A] text-white flex items-center justify-center p-4"
    >
      <div className="max-w-4xl w-full">
        {/* Success Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="text-center mb-8"
        >
          <div className="flex items-center justify-center mb-4">
            <CheckCircle className="h-16 w-16 text-green-500 mr-4" />
            <Sparkles className="h-8 w-8 text-neural-pink" />
          </div>
          <h1 className="text-4xl md:text-5xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-neural-purple to-neural-pink mb-4">
            Resume Generated Successfully!
          </h1>
          <p className="text-gray-300 text-lg">
            Your professional, ATS-optimized resume is ready for download
          </p>
        </motion.div>

        {/* ATS Score Display */}
        {resumeData?.atsScore && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="max-w-4xl mx-auto mb-8"
          >
            <div className="bg-gray-900/50 backdrop-blur-md rounded-xl p-6 border border-white/10">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center gap-3">
                  <Target className="h-6 w-6 text-neural-blue" />
                  <h2 className="text-2xl font-bold">ATS Compatibility Score</h2>
                </div>
                <div className="flex items-center gap-2">
                  <Award className="h-5 w-5 text-yellow-500" />
                  <span className="text-sm text-gray-400">AI-Enhanced</span>
                </div>
              </div>

              {/* Overall Score */}
              <div className="text-center mb-8">
                <div className="relative inline-flex items-center justify-center w-32 h-32 mb-4">
                  <svg className="w-32 h-32 transform -rotate-90" viewBox="0 0 120 120">
                    <circle
                      cx="60"
                      cy="60"
                      r="50"
                      stroke="currentColor"
                      strokeWidth="8"
                      fill="none"
                      className="text-gray-700"
                    />
                    <circle
                      cx="60"
                      cy="60"
                      r="50"
                      stroke="currentColor"
                      strokeWidth="8"
                      fill="none"
                      strokeLinecap="round"
                      className={`${
                        resumeData.atsScore.overall >= 80 ? 'text-green-500' :
                        resumeData.atsScore.overall >= 60 ? 'text-yellow-500' : 'text-red-500'
                      }`}
                      style={{
                        strokeDasharray: `${2 * Math.PI * 50}`,
                        strokeDashoffset: `${2 * Math.PI * 50 * (1 - resumeData.atsScore.overall / 100)}`
                      }}
                    />
                  </svg>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="text-center">
                      <div className="text-3xl font-bold text-white">{resumeData.atsScore.overall}</div>
                      <div className="text-sm text-gray-400">/ 100</div>
                    </div>
                  </div>
                </div>
                <h3 className="text-xl font-semibold mb-2">
                  {resumeData.atsScore.overall >= 80 ? 'Excellent' :
                   resumeData.atsScore.overall >= 60 ? 'Good' : 'Needs Improvement'}
                </h3>
                <p className="text-gray-400">
                  Your resume is {resumeData.atsScore.overall >= 80 ? 'highly' : resumeData.atsScore.overall >= 60 ? 'moderately' : 'minimally'} optimized for ATS systems
                </p>
              </div>              {/* Score Breakdown */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                {Object.entries(resumeData.atsScore.breakdown).map(([category, score]) => (
                  <div key={category} className="bg-gray-800/50 rounded-lg p-4 text-center">
                    <div className="flex items-center justify-center mb-2">
                      {category === 'keywords' && <BarChart3 className="h-5 w-5 text-neural-blue" />}
                      {category === 'formatting' && <FileText className="h-5 w-5 text-neural-purple" />}
                      {category === 'content' && <TrendingUp className="h-5 w-5 text-neural-pink" />}
                      {category === 'completeness' && <Target className="h-5 w-5 text-green-500" />}
                      {/* Legacy support for old categories */}
                      {category === 'achievements' && <TrendingUp className="h-5 w-5 text-neural-pink" />}
                      {category === 'skills' && <Target className="h-5 w-5 text-green-500" />}
                    </div>
                    <div className="text-lg font-bold text-white mb-1">{score}%</div>
                    <div className="text-xs text-gray-400 capitalize">
                      {category === 'content' ? 'Content Quality' : 
                       category === 'completeness' ? 'Section Coverage' : category}
                    </div>
                    <div className="w-full bg-gray-700 rounded-full h-2 mt-2">
                      <div
                        className={`h-2 rounded-full ${
                          score >= 80 ? 'bg-green-500' :
                          score >= 60 ? 'bg-yellow-500' : 'bg-red-500'
                        }`}
                        style={{ width: `${score}%` }}
                      />
                    </div>
                  </div>
                ))}
              </div>

              {/* Improvements */}
              {resumeData.atsScore.improvements && resumeData.atsScore.improvements.length > 0 && (
                <div className="bg-gray-800/30 rounded-lg p-4">
                  <h4 className="text-sm font-semibold text-gray-300 mb-3 flex items-center gap-2">
                    <TrendingUp className="h-4 w-4" />
                    Suggestions for Improvement
                  </h4>
                  <ul className="space-y-2">
                    {resumeData.atsScore.improvements.map((improvement, index) => (
                      <li key={index} className="text-sm text-gray-400 flex items-start gap-2">
                        <span className="text-neural-blue mt-1">•</span>
                        {improvement}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </motion.div>        )}

        {/* Template Selection Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.35 }}
          className="max-w-4xl mx-auto mb-8"
        >
          <div className="bg-gray-900/50 backdrop-blur-md rounded-xl p-6 border border-white/10">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center gap-3">
                <FileText className="h-6 w-6 text-neural-purple" />
                <h2 className="text-2xl font-bold">Choose Your Template</h2>
              </div>
              <button
                onClick={() => setShowTemplateSelector(!showTemplateSelector)}
                className="px-4 py-2 bg-gradient-to-r from-neural-purple to-neural-pink text-white text-sm rounded-lg hover:opacity-90 transition-opacity"
              >
                {showTemplateSelector ? 'Hide Templates' : 'Browse Templates'}
              </button>
            </div>
            
            {showTemplateSelector && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                className="mt-6"
              >
                <ResumeTemplateSelector
                  selectedTemplate={selectedTemplate}
                  onTemplateChange={setSelectedTemplate}
                  formData={formData}
                  resumeData={resumeData}
                />
              </motion.div>
            )}
            
            {!showTemplateSelector && (
              <div className="text-center py-4">
                <p className="text-gray-400">
                  Selected Template: <span className="text-neural-purple font-semibold capitalize">{selectedTemplate} Professional</span>
                </p>
                <p className="text-sm text-gray-500 mt-2">
                  Click "Browse Templates" to explore 6 beautiful, ATS-optimized templates
                </p>
              </div>
            )}
          </div>
        </motion.div>

        {/* Action Buttons */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="flex flex-wrap justify-center gap-4 mb-8"
        >          <button
            onClick={handleDownloadClick}
            disabled={isDownloading}
            className="flex items-center gap-2 px-8 py-4 bg-gradient-to-r from-neural-purple to-neural-pink text-white font-semibold rounded-lg hover:opacity-90 transition-opacity disabled:opacity-50"
          >
            <Download className="h-5 w-5" />
            {isDownloading ? 'Generating PDF...' : 'Download PDF'}
          </button>

          <button
            onClick={() => setShowFullPreview(!showFullPreview)}
            className="flex items-center gap-2 px-6 py-4 bg-gray-800 hover:bg-gray-700 text-white font-semibold rounded-lg transition-colors"
          >
            <Eye className="h-5 w-5" />
            {showFullPreview ? 'Hide Preview' : 'Preview Resume'}
          </button>

          <button
            onClick={onEditResume}
            className="flex items-center gap-2 px-6 py-4 bg-gray-800 hover:bg-gray-700 text-white font-semibold rounded-lg transition-colors"
          >
            <FileText className="h-5 w-5" />
            Edit Resume
          </button>

          <button
            onClick={onStartOver}
            className="flex items-center gap-2 px-6 py-4 bg-gray-700 hover:bg-gray-600 text-white font-semibold rounded-lg transition-colors"
          >
            <RefreshCw className="h-5 w-5" />
            Start Over
          </button>
        </motion.div>        {/* Resume Preview */}
        {showFullPreview && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="bg-white text-black rounded-lg p-8 max-h-96 overflow-y-auto mb-8"
          >
            <div ref={resumeRef} dangerouslySetInnerHTML={{
              __html: formatResumeWithTemplate(selectedTemplate, formData, resumeData)
            }} />
          </motion.div>
        )}

        {/* Enhanced Stats */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-8"
        >
          <div className="bg-gray-900/50 backdrop-blur-md rounded-xl p-4 border border-white/10 text-center">
            <h3 className="text-xl font-bold text-neural-purple mb-1">
              {resumeData?.atsScore?.overall || 0}%
            </h3>
            <p className="text-gray-400 text-sm">ATS Score</p>
          </div>

          <div className="bg-gray-900/50 backdrop-blur-md rounded-xl p-4 border border-white/10 text-center">
            <h3 className="text-xl font-bold text-neural-pink mb-1">
              {resumeData?.enhancedContent?.experience?.length || formData.experience?.length || 0}
            </h3>
            <p className="text-gray-400 text-sm">Experiences</p>
          </div>

          <div className="bg-gray-900/50 backdrop-blur-md rounded-xl p-4 border border-white/10 text-center">
            <h3 className="text-xl font-bold text-neural-blue mb-1">
              {resumeData?.keywords?.length || 0}
            </h3>
            <p className="text-gray-400 text-sm">Keywords</p>
          </div>

          <div className="bg-gray-900/50 backdrop-blur-md rounded-xl p-4 border border-white/10 text-center">
            <h3 className="text-xl font-bold text-green-500 mb-1">
              {resumeData?.type === 'ai-enhanced' ? 'AI' : 'Template'}
            </h3>
            <p className="text-gray-400 text-sm">Enhanced</p>
          </div>        </motion.div>
      </div>

      {/* Authentication Guard Modal */}
      <AuthGuardModal
        isOpen={showAuthModal}
        onClose={() => setShowAuthModal(false)}
        featureName="download your resume"
      />

      {/* Review Modal */}
      <ReviewModal
        isOpen={showReviewModal}
        onClose={() => setShowReviewModal(false)}
        onSubmitReview={handleReviewSubmit}
        userInfo={currentUser}
      />
    </motion.div>
  );
};

export default SuccessScreen;
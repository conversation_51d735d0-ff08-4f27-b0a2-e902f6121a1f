# Gemini Pro API key
GEMINI_API_KEY=AIzaSyBpbTY-HKaAjgel4sjfYyXdB3eA2VUwEo4


# Firebase Configuration
NEXT_PUBLIC_FIREBASE_API_KEY=AIzaSyAzqqKdReH-D0Vjg84kTHeSxFDfRo-Mi-k
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=blinkfind-a8517.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=blinkfind-a8517
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=blinkfind-a8517.firebasestorage.app
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=257986295350
NEXT_PUBLIC_FIREBASE_APP_ID=1:257986295350:web:60de4e79b9b8316e0f0a97
NEXT_PUBLIC_FIREBASE_DATABASE_URL=https://blinkfind-a8517-default-rtdb.firebaseio.com

# (Optional) measurement ID for Analytics
NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID=G-VD3CRECNBK

# API URL for backend services
NEXT_PUBLIC_API_URL=http://localhost:5000

# Gemini API Key (public)
NEXT_PUBLIC_GEMINI_API_KEY=AIzaSyBpbTY-HKaAjgel4sjfYyXdB3eA2VUwEo4

# Admin Configuration
ADMIN_EMAILS=<EMAIL>,<EMAIL>
NEXT_PUBLIC_ADMIN_EMAILS=<EMAIL>,<EMAIL>
NEXT_PUBLIC_ADMIN_DOMAIN=blinkfind.com
.phone-wrapper {
  position: relative;
}

.phone-input-container {
  width: 100% !important;
}

.phone-input-container .form-control {
  width: 100% !important;
  padding: 16px 14px 12px 90px !important;
  background-color: white !important;
  border: 1px solid #E5E7EB !important;
  border-radius: 0.375rem !important;
  font-size: 16px !important;
  font-weight: normal !important;
  color: #344054 !important;
}

.phone-input-container .form-control:focus {
  border-color: #51B504 !important;
  outline: none !important;
  box-shadow: none !important;
}

.phone-input-container .form-control::placeholder {
  color: #667085 !important;
  font-size: 16px !important;
  font-weight: normal !important;
}

.phone-input-container .flag-dropdown {
  position: absolute !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  left: 0 !important;
  border: none !important;
  background: transparent !important;
  border-right: 1px solid #E5E7EB !important;
  padding: 0 8px !important;
}

.phone-input-container .selected-flag {
  padding: 0 8px !important;
  background: transparent !important;
  display: flex !important;
  align-items: center !important;
  height: 100% !important;
}

.phone-input-container .selected-flag:focus {
  background: transparent !important;
}

.phone-input-container .selected-flag .flag {
  margin-right: 8px !important;
}

/* Country dropdown */
.phone-input-container .country-list {
  margin-top: 2px !important;
  border: 1px solid #E5E7EB !important;
  border-radius: 0.375rem !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;
  background: white !important;
  max-height: 300px !important;
}

.phone-input-container .country {
  padding: 8px 12px !important;
  display: flex !important;
  align-items: center !important;
}

.phone-input-container .country:hover {
  background-color: rgba(81, 181, 4, 0.1) !important;
}

.phone-input-container .country.highlight {
  background-color: rgba(81, 181, 4, 0.2) !important;
}

.phone-input-container .country-name {
  margin-left: 8px !important;
  font-size: 14px !important;
  color: #344054 !important;
}

.phone-input-container .dial-code {
  color: #667085 !important;
  font-size: 14px !important;
  margin-left: 8px !important;
}

/* Search box */
.phone-input-container .search-box {
  padding: 8px !important;
  border-bottom: 1px solid #E5E7EB !important;
}

.phone-input-container .search-box input {
  width: 100% !important;
  padding: 8px 12px !important;
  border: 1px solid #E5E7EB !important;
  border-radius: 0.375rem !important;
  font-size: 14px !important;
  color: #344054 !important;
}

.phone-input-container .search-box input:focus {
  border-color: #51B504 !important;
  outline: none !important;
}

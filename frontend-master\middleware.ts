import { NextResponse } from 'next/server';
import { NextRequest } from 'next/server';

// Admin routes that require authentication
const ADMIN_ROUTES = ['/admin'];

// Public routes that don't require any authentication
const PUBLIC_ROUTES = ['/', '/login', '/signup', '/contact', '/privacy-policy'];

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  
  // Check if this is an admin route
  const isAdminRoute = ADMIN_ROUTES.some(route => pathname.startsWith(route));
  
  if (isAdminRoute) {
    // For admin routes, we'll let the client-side handle authentication
    // This is because Firebase auth state is handled on the client side
    
    // Add security headers for admin routes
    const response = NextResponse.next();
    
    // Add security headers
    response.headers.set('X-Frame-Options', 'DENY');
    response.headers.set('X-Content-Type-Options', 'nosniff');
    response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
    response.headers.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=()');
    
    // Add admin-specific headers
    response.headers.set('X-Admin-Route', 'true');
    
    return response;
  }
  
  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!api|_next/static|_next/image|favicon.ico|public).*)',
  ],
};

'use client';
import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  SparklesIcon, 
  MagnifyingGlassIcon,
  ShieldCheckIcon,
  CameraIcon,
  UserGroupIcon,
  ClockIcon,
  MapPinIcon,
  CheckCircleIcon,
  PlayIcon
} from '@heroicons/react/24/solid';
import Link from 'next/link';

export default function QuickFind() {
  const [activeTab, setActiveTab] = useState('report');
  const [isVideoPlaying, setIsVideoPlaying] = useState(false);

  const stats = [
    { number: '10,000+', label: 'Items Recovered' },
    { number: '50,000+', label: 'Active Users' },
    { number: '95%', label: 'Success Rate' },
    { number: '24/7', label: 'AI Matching' }
  ];

  const howItWorks = [
    {
      step: '01',
      title: 'Report Your Lost Item',
      description: 'Upload photos and details about your lost item with our easy-to-use interface.',
      icon: <CameraIcon className="h-8 w-8" />
    },
    {
      step: '02',
      title: 'AI Powered Matching',
      description: 'Our advanced AI analyzes and matches your item with found items in real-time.',
      icon: <MagnifyingGlassIcon className="h-8 w-8" />
    },
    {
      step: '03',
      title: 'Secure Connection',
      description: 'Get connected with the finder through our secure, privacy-focused platform.',
      icon: <ShieldCheckIcon className="h-8 w-8" />
    },
    {
      step: '04',
      title: 'Happy Reunion',
      description: 'Coordinate the safe return of your item and celebrate the reunion!',
      icon: <CheckCircleIcon className="h-8 w-8" />
    }
  ];

  const features = [
    {
      title: 'Smart Image Recognition',
      description: 'Advanced AI that can identify and categorize items from photos automatically.',
      icon: <CameraIcon className="h-6 w-6" />
    },
    {
      title: 'Real-time Notifications',
      description: 'Get instant alerts when potential matches are found for your lost items.',
      icon: <ClockIcon className="h-6 w-6" />
    },
    {
      title: 'Location-based Search',
      description: 'Find items lost in specific areas with our geolocation technology.',
      icon: <MapPinIcon className="h-6 w-6" />
    },
    {
      title: 'Community Network',
      description: 'Join a helpful community of users working together to reunite lost items.',
      icon: <UserGroupIcon className="h-6 w-6" />
    }
  ];

  const testimonials = [
    {
      name: 'Sarah Johnson',
      role: 'Student',
      content: 'Found my lost laptop within 2 hours of posting! QuickFind is amazing.',
      rating: 5
    },
    {
      name: 'Mike Chen',
      role: 'Business Owner',
      content: 'The AI matching is incredibly accurate. Helped me find my wallet in downtown.',
      rating: 5
    },
    {
      name: 'Emma Davis',
      role: 'Parent',
      content: 'My daughter lost her toy at the park. QuickFind helped us get it back the same day!',
      rating: 5
    }
  ];

  return (
    <div className="relative overflow-hidden bg-gradient-to-b from-black to-[#0A0A0A] min-h-screen">
      {/* Animated background */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute inset-0 bg-[url('/grid.svg')] [mask-image:radial-gradient(ellipse_at_center,white,transparent)]" />
      </div>
      
      {/* Floating elements */}
      <div className="absolute inset-0 overflow-hidden">
        {[...Array(6)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute rounded-full bg-gradient-to-r from-neural-purple to-neural-pink opacity-20 blur-xl"
            initial={{
              x: Math.random() * 100,
              y: Math.random() * 100,
              width: Math.random() * 300 + 100,
              height: Math.random() * 300 + 100,
            }}
            animate={{
              x: Math.random() * 200,
              y: Math.random() * 200,
              transition: {
                duration: Math.random() * 15 + 10,
                repeat: Infinity,
                repeatType: 'reverse',
              },
            }}
          />
        ))}
      </div>

      {/* Hero Section */}
      <section className="relative pt-32 pb-20">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <motion.div 
              className="flex justify-center mb-6"
              initial={{ opacity: 0, scale: 0.5 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5 }}
            >
              <div className="p-4 bg-gradient-to-r from-neural-purple to-neural-pink rounded-full">
                <MagnifyingGlassIcon className="h-12 w-12 text-white" />
              </div>
            </motion.div>
            
            <motion.h1 
              className="text-6xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-neural-purple to-neural-pink"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              QuickFind
            </motion.h1>
            
            <motion.p 
              className="text-2xl max-w-3xl mx-auto text-gray-300 mb-8"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
            >
              The World's Most Advanced AI-Powered Lost & Found Platform
            </motion.p>
            
            <motion.p 
              className="text-lg max-w-2xl mx-auto text-gray-400 mb-12"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.4 }}
            >
              Reuniting people with their precious belongings using cutting-edge artificial intelligence, 
              computer vision, and a caring community network.
            </motion.p>            <motion.div
              className="flex flex-col sm:flex-row gap-4 justify-center"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.5 }}
            >
              <button className="px-8 py-4 bg-gradient-to-r from-neural-purple to-neural-pink text-white rounded-lg font-semibold hover:opacity-90 transition-opacity">
                Download App
              </button>
              <button className="px-8 py-4 border-2 border-neural-purple text-neural-purple rounded-lg font-semibold hover:bg-neural-purple/10 transition-colors">
                Watch Demo
              </button>
            </motion.div>
          </div>

          {/* Stats */}
          <motion.div 
            className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto"
            initial={{ opacity: 0, y: 40 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
          >
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="text-3xl font-bold text-white mb-2">{stat.number}</div>
                <div className="text-gray-400">{stat.label}</div>
              </div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="relative py-20">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-4">How QuickFind Works</h2>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto">
              Four simple steps to reunite you with your lost items
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {howItWorks.map((step, index) => (
              <motion.div
                key={index}
                className="relative"
                initial={{ opacity: 0, y: 40 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <div className="bg-gray-900/50 backdrop-blur-md p-8 rounded-xl border border-white/10 hover:border-neural-purple/30 transition-all group h-full">
                  <div className="text-neural-purple font-bold text-lg mb-4">{step.step}</div>
                  <div className="p-3 bg-gradient-to-r from-neural-purple to-neural-pink rounded-lg w-fit mb-6">
                    {step.icon}
                  </div>
                  <h3 className="text-xl font-semibold text-white mb-4">{step.title}</h3>
                  <p className="text-gray-300">{step.description}</p>
                </div>
                {index < howItWorks.length - 1 && (
                  <div className="hidden lg:block absolute top-1/2 -right-4 w-8 h-px bg-gradient-to-r from-neural-purple to-neural-pink"></div>
                )}
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="relative py-20">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-4">Powerful Features</h2>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto">
              Advanced technology meets human compassion
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            {features.map((feature, index) => (
              <motion.div
                key={index}
                className="bg-gray-900/50 backdrop-blur-md p-8 rounded-xl border border-white/10 hover:border-neural-purple/30 transition-all group"
                initial={{ opacity: 0, x: index % 2 === 0 ? -40 : 40 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <div className="flex items-start gap-4">
                  <div className="p-3 bg-gradient-to-r from-neural-purple to-neural-pink rounded-lg">
                    {feature.icon}
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-white mb-3">{feature.title}</h3>
                    <p className="text-gray-300">{feature.description}</p>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Video Demo Section */}
      <section className="relative py-20">
        <div className="container mx-auto px-6">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-4xl font-bold text-white mb-4">See QuickFind in Action</h2>
              <p className="text-xl text-gray-300">
                Watch how easy it is to find your lost items
              </p>
            </div>

            <motion.div 
              className="relative aspect-video rounded-2xl overflow-hidden bg-gray-900/50 backdrop-blur-md border border-white/10"
              initial={{ opacity: 0, scale: 0.9 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
            >
              {isVideoPlaying ? (
                <video
                  className="w-full h-full object-cover"
                  src="/Videos/quickFind_video.mp4"
                  autoPlay
                  controls
                  muted
                />
              ) : (
                <div className="absolute inset-0 flex items-center justify-center bg-gradient-to-br from-neural-purple/20 to-neural-pink/20">
                  <button
                    onClick={() => setIsVideoPlaying(true)}
                    className="p-6 bg-gradient-to-r from-neural-purple to-neural-pink rounded-full hover:scale-110 transition-transform"
                  >
                    <PlayIcon className="h-12 w-12 text-white" />
                  </button>
                </div>
              )}
            </motion.div>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="relative py-20">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-4">Success Stories</h2>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto">
              Real people, real recoveries, real happiness
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {testimonials.map((testimonial, index) => (
              <motion.div
                key={index}
                className="bg-gray-900/50 backdrop-blur-md p-8 rounded-xl border border-white/10 hover:border-neural-purple/30 transition-all"
                initial={{ opacity: 0, y: 40 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <div className="flex gap-1 mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <SparklesIcon key={i} className="h-5 w-5 text-neural-pink" />
                  ))}
                </div>
                <p className="text-gray-300 mb-6 italic">"{testimonial.content}"</p>
                <div>
                  <div className="font-semibold text-white">{testimonial.name}</div>
                  <div className="text-sm text-gray-400">{testimonial.role}</div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="relative py-20">
        <div className="container mx-auto px-6 text-center">
          <motion.div
            className="max-w-3xl mx-auto"
            initial={{ opacity: 0, y: 40 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-4xl font-bold text-white mb-6">
              Ready to Find What You've Lost?
            </h2>
            <p className="text-xl text-gray-300 mb-8">
              Join thousands of happy users who have successfully recovered their precious items with QuickFind.
            </p>            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/dashboard">
                <button className="px-8 py-4 bg-gradient-to-r from-neural-purple to-neural-pink text-white rounded-lg font-semibold hover:opacity-90 transition-opacity">
                  Download QuickFind
                </button>
              </Link>
              <Link href="/contact">
                <button className="px-8 py-4 border-2 border-neural-purple text-neural-purple rounded-lg font-semibold hover:bg-neural-purple/10 transition-colors">
                  Contact Support
                </button>
              </Link>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
}
'use client';

import { motion, AnimatePresence } from 'framer-motion';
import { useState } from 'react';
import { 
  FileText,
  Plus,
  Trash2,
  Globe,
  Github,
  ExternalLink,
  Sparkles,
  Save,
  Wand2,
  AlertTriangle,
  CheckCircle,
  Clock,
  Target,
  Info
} from 'lucide-react';
import SmartForm<PERSON>ield from './SmartFormField';
import ClientOnly from '../../common/ClientOnly';
import ATSFieldIndicator from '../ats/FieldIndicator';

const EnhancedProjectsForm = ({
  formData, 
  updateFormData, 
  addArrayItem, 
  removeArrayItem, 
  atsAnalysis, 
  validationErrors = {}, 
  showValidationErrors = false,
  onSave,
  onAISuggest
}) => {
  const [expandedItems, setExpandedItems] = useState(new Set([0]));
  const [showAIHelper, setShowAIHelper] = useState(false);

  const toggleExpanded = (index) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(index)) {
      newExpanded.delete(index);
    } else {
      newExpanded.add(index);
    }
    setExpandedItems(newExpanded);
  };

  const addProject = () => {
    const newIndex = formData.projects.length;
    addArrayItem('projects', {
      name: "",
      description: "",
      technologies: "",
      link: "",
      github: "",
      startDate: "",
      endDate: ""
    });
    setExpandedItems(new Set([...expandedItems, newIndex]));
  };

  const removeProject = (index) => {
    removeArrayItem('projects', index);
    const newExpanded = new Set(expandedItems);
    newExpanded.delete(index);
    setExpandedItems(newExpanded);
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="space-y-6"
    >
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-neural-purple/20 rounded-xl flex items-center justify-center">
            <FileText className="h-5 w-5 text-neural-purple" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-white">Projects</h2>
            <p className="text-gray-400 text-sm">Showcase your personal and professional projects (Optional)</p>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <button
            onClick={addProject}
            className="flex items-center gap-2 px-4 py-2 bg-neural-purple hover:bg-neural-purple/80 text-white rounded-xl transition-colors"
          >
            <Plus className="h-5 w-5" />
            Add Project
          </button>
        </div>
      </div>

      {/* Optional Section Notice */}
      <motion.div
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-blue-500/10 border border-blue-500/20 rounded-xl p-4"
      >
        <div className="flex items-center gap-2 mb-2">
          <Info className="h-4 w-4 text-blue-400" />
          <h4 className="text-blue-400 font-medium">Optional Section</h4>
        </div>
        <p className="text-gray-300 text-sm">
          Projects help showcase your practical skills and experience. You can skip this section if you don't have relevant projects to include.
        </p>
      </motion.div>

      {/* Validation Errors */}
      <AnimatePresence>
        {showValidationErrors && validationErrors.projects && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="bg-red-500/10 border border-red-500/20 rounded-xl p-4"
          >
            <div className="flex items-center gap-2 mb-2">
              <AlertTriangle className="h-4 w-4 text-red-400" />
              <h4 className="text-red-400 font-medium">Projects Information</h4>
            </div>
            <p className="text-red-300 text-sm">{validationErrors.projects}</p>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Projects List */}
      <div className="space-y-4">
        {formData.projects.map((project, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-gray-900/40 backdrop-blur-sm rounded-2xl border border-white/10 overflow-hidden"
          >
            {/* Project Header */}
            <div 
              className="p-4 cursor-pointer hover:bg-white/5 transition-colors"
              onClick={() => toggleExpanded(index)}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-neural-purple/20 rounded-lg flex items-center justify-center">
                    <FileText className="h-4 w-4 text-neural-purple" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-white">
                      {project.name || `Project ${index + 1}`}
                    </h3>
                    <p className="text-gray-400 text-sm">
                      {project.technologies || 'Technologies not specified'}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      removeProject(index);
                    }}
                    className="p-2 text-red-400 hover:bg-red-500/20 rounded-lg transition-colors"
                    title="Remove project"
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                  <motion.div
                    animate={{ rotate: expandedItems.has(index) ? 180 : 0 }}
                    transition={{ duration: 0.2 }}
                  >
                    <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </motion.div>
                </div>
              </div>
            </div>

            {/* Project Details */}
            <AnimatePresence>
              {expandedItems.has(index) && (
                <motion.div
                  initial={{ height: 0, opacity: 0 }}
                  animate={{ height: 'auto', opacity: 1 }}
                  exit={{ height: 0, opacity: 0 }}
                  transition={{ duration: 0.3 }}
                  className="border-t border-white/10"
                >
                  <div className="p-6 space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {/* Project Name */}
                      <SmartFormField
                        label="Project Name"
                        type="text"
                        value={project.name}
                        onChange={(e) => updateFormData('projects', 'name', e.target.value, index)}
                        placeholder="My Awesome Project"
                        icon={FileText}
                        hint="Give your project a clear, descriptive name"
                      />

                      {/* Technologies */}
                      <SmartFormField
                        label="Technologies Used"
                        type="text"
                        value={project.technologies}
                        onChange={(e) => updateFormData('projects', 'technologies', e.target.value, index)}
                        placeholder="React, Node.js, MongoDB, AWS"
                        icon={Wand2}
                        hint="List the main technologies, frameworks, and tools used"
                      />

                      {/* Project Link */}
                      <SmartFormField
                        label="Live Demo URL"
                        type="url"
                        value={project.link}
                        onChange={(e) => updateFormData('projects', 'link', e.target.value, index)}
                        placeholder="https://myproject.com"
                        icon={ExternalLink}
                        hint="Link to the live project or demo"
                      />

                      {/* GitHub Link */}
                      <SmartFormField
                        label="GitHub Repository"
                        type="url"
                        value={project.github}
                        onChange={(e) => updateFormData('projects', 'github', e.target.value, index)}
                        placeholder="https://github.com/username/project"
                        icon={Github}
                        hint="Link to the source code repository"
                      />
                    </div>

                    {/* Project Description */}
                    <SmartFormField
                      label="Project Description"
                      type="textarea"
                      value={project.description}
                      onChange={(e) => updateFormData('projects', 'description', e.target.value, index)}
                      placeholder="Describe what the project does, the problem it solves, and your role in developing it..."
                      rows={4}
                      maxLength={500}
                      showCharCount
                      hint="Focus on the impact, challenges solved, and your specific contributions"
                    />

                    <ClientOnly>
                      <ATSFieldIndicator
                        fieldName={`project_${index}_description`}
                        value={project.description}
                        analysis={atsAnalysis?.fieldAnalysis?.[`project_${index}_description`]}
                        showDetails={true}
                      />
                    </ClientOnly>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>
        ))}
      </div>

      {/* Empty State */}
      {formData.projects.length === 0 && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="text-center py-12 bg-gray-900/20 rounded-2xl border border-white/10"
        >
          <FileText className="h-12 w-12 text-gray-600 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-400 mb-2">No Projects Added</h3>
          <p className="text-gray-500 mb-4">Projects help showcase your practical skills and experience</p>
          <button
            onClick={addProject}
            className="inline-flex items-center gap-2 px-4 py-2 bg-neural-purple hover:bg-neural-purple/80 text-white rounded-lg transition-colors"
          >
            <Plus className="h-4 w-4" />
            Add Your First Project
          </button>
        </motion.div>
      )}

      {/* Progress Indicator */}
      <div className="bg-gray-900/40 backdrop-blur-sm rounded-xl p-4 border border-white/10">
        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-400">Projects Section Progress</span>
          <span className="text-neural-purple font-medium">
            {formData.projects.filter(proj => proj.name && proj.description).length} / {formData.projects.length} completed
          </span>
        </div>
        <div className="w-full bg-gray-700 rounded-full h-2 mt-2">
          <motion.div
            className="bg-gradient-to-r from-neural-purple to-neural-pink h-2 rounded-full"
            initial={{ width: 0 }}
            animate={{ 
              width: `${formData.projects.length > 0 ? (formData.projects.filter(proj => proj.name && proj.description).length / formData.projects.length) * 100 : 0}%` 
            }}
            transition={{ duration: 0.5 }}
          />
        </div>
      </div>
    </motion.div>
  );
};

export default EnhancedProjectsForm;

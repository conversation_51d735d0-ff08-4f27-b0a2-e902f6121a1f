'use client';

import { motion, AnimatePresence } from 'framer-motion';
import { useState } from 'react';
import { 
  Award,
  Plus,
  X,
  Code,
  Globe,
  Certificate,
  Sparkles,
  Save,
  Wand2,
  AlertTriangle,
  CheckCircle,
  Clock,
  Target,
  Info,
  Lightbulb
} from 'lucide-react';
import Smart<PERSON>orm<PERSON>ield from './SmartFormField';
import ClientOnly from '../../common/ClientOnly';
import ATSFieldIndicator from '../ats/FieldIndicator';

export const EnhancedSkillsForm = ({ 
  formData, 
  updateFormData, 
  atsAnalysis, 
  validationErrors = {}, 
  showValidationErrors = false,
  onSave,
  onAISuggest
}) => {
  const [activeCategory, setActiveCategory] = useState('technical');
  const [showAIHelper, setShowAIHelper] = useState(false);
  const [newSkill, setNewSkill] = useState({ technical: '', languages: '', certifications: '' });

  const skillCategories = [
    { id: 'technical', label: 'Technical Skills', icon: Code, color: 'neural-purple' },
    { id: 'languages', label: 'Languages', icon: Globe, color: 'neural-blue' },
    { id: 'certifications', label: 'Certifications', icon: Certificate, color: 'neural-pink' }
  ];

  const addSkill = (category) => {
    const skill = newSkill[category].trim();
    if (skill && !formData.skills[category].includes(skill)) {
      const updatedSkills = [...formData.skills[category], skill];
      updateFormData('skills', category, updatedSkills);
      setNewSkill({ ...newSkill, [category]: '' });
    }
  };

  const removeSkill = (category, skillToRemove) => {
    const updatedSkills = formData.skills[category].filter(skill => skill !== skillToRemove);
    updateFormData('skills', category, updatedSkills);
  };

  const handleKeyPress = (e, category) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      addSkill(category);
    }
  };

  const getSkillSuggestions = (category) => {
    const suggestions = {
      technical: ['JavaScript', 'Python', 'React', 'Node.js', 'SQL', 'AWS', 'Docker', 'Git', 'TypeScript', 'MongoDB'],
      languages: ['English', 'Spanish', 'French', 'German', 'Mandarin', 'Japanese', 'Portuguese', 'Italian', 'Russian', 'Arabic'],
      certifications: ['AWS Certified', 'Google Cloud Professional', 'Microsoft Azure', 'PMP', 'Scrum Master', 'CompTIA Security+', 'Cisco CCNA', 'Oracle Certified']
    };
    return suggestions[category] || [];
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="space-y-6"
    >
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-neural-purple/20 rounded-xl flex items-center justify-center">
            <Award className="h-5 w-5 text-neural-purple" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-white">Skills</h2>
            <p className="text-gray-400 text-sm">Highlight your technical and soft skills (Optional)</p>
          </div>
        </div>
      </div>

      {/* Optional Section Notice */}
      <motion.div
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-blue-500/10 border border-blue-500/20 rounded-xl p-4"
      >
        <div className="flex items-center gap-2 mb-2">
          <Info className="h-4 w-4 text-blue-400" />
          <h4 className="text-blue-400 font-medium">Optional Section</h4>
        </div>
        <p className="text-gray-300 text-sm">
          Skills help employers quickly identify your capabilities. You can skip this section if your experience and projects already demonstrate your skills clearly.
        </p>
      </motion.div>

      {/* Validation Errors */}
      <AnimatePresence>
        {showValidationErrors && validationErrors.skills && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="bg-red-500/10 border border-red-500/20 rounded-xl p-4"
          >
            <div className="flex items-center gap-2 mb-2">
              <AlertTriangle className="h-4 w-4 text-red-400" />
              <h4 className="text-red-400 font-medium">Skills Information</h4>
            </div>
            <p className="text-red-300 text-sm">{validationErrors.skills}</p>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Category Tabs */}
      <div className="flex gap-2 p-1 bg-gray-900/40 rounded-xl border border-white/10">
        {skillCategories.map((category) => {
          const IconComponent = category.icon;
          return (
            <button
              key={category.id}
              onClick={() => setActiveCategory(category.id)}
              className={`flex items-center gap-2 px-4 py-3 rounded-lg text-sm font-medium transition-all duration-300 flex-1 ${
                activeCategory === category.id
                  ? `bg-${category.color} text-white shadow-lg`
                  : 'text-gray-300 hover:bg-white/5 hover:text-white'
              }`}
            >
              <IconComponent className="h-4 w-4" />
              {category.label}
              <span className="ml-auto bg-white/20 text-xs px-2 py-1 rounded-full">
                {formData.skills[category.id]?.length || 0}
              </span>
            </button>
          );
        })}
      </div>

      {/* Skills Content */}
      <div className="bg-gray-900/40 backdrop-blur-sm rounded-2xl p-6 border border-white/10">
        <AnimatePresence mode="wait">
          <motion.div
            key={activeCategory}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.2 }}
            className="space-y-6"
          >
            {/* Add Skill Input */}
            <div className="space-y-4">
              <div className="flex gap-2">
                <input
                  type="text"
                  value={newSkill[activeCategory]}
                  onChange={(e) => setNewSkill({ ...newSkill, [activeCategory]: e.target.value })}
                  onKeyPress={(e) => handleKeyPress(e, activeCategory)}
                  placeholder={`Add ${skillCategories.find(c => c.id === activeCategory)?.label.toLowerCase()}...`}
                  className="flex-1 px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400"
                />
                <button
                  onClick={() => addSkill(activeCategory)}
                  disabled={!newSkill[activeCategory].trim()}
                  className="px-4 py-3 bg-neural-purple hover:bg-neural-purple/80 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
                >
                  <Plus className="h-4 w-4" />
                </button>
              </div>

              {/* Skill Suggestions */}
              <div className="space-y-2">
                <p className="text-sm text-gray-400">Popular suggestions:</p>
                <div className="flex flex-wrap gap-2">
                  {getSkillSuggestions(activeCategory)
                    .filter(suggestion => !formData.skills[activeCategory].includes(suggestion))
                    .slice(0, 8)
                    .map((suggestion) => (
                      <button
                        key={suggestion}
                        onClick={() => {
                          setNewSkill({ ...newSkill, [activeCategory]: suggestion });
                          addSkill(activeCategory);
                        }}
                        className="px-3 py-1.5 bg-gray-700 hover:bg-gray-600 text-gray-300 hover:text-white text-sm rounded-lg transition-colors"
                      >
                        {suggestion}
                      </button>
                    ))}
                </div>
              </div>
            </div>

            {/* Skills List */}
            <div className="space-y-3">
              <h4 className="font-medium text-white flex items-center gap-2">
                {(() => {
                  const category = skillCategories.find(c => c.id === activeCategory);
                  if (category?.icon) {
                    const IconComponent = category.icon;
                    return <IconComponent className="h-4 w-4" />;
                  }
                  return null;
                })()}
                Your {skillCategories.find(c => c.id === activeCategory)?.label}
              </h4>
              
              {formData.skills[activeCategory]?.length > 0 ? (
                <div className="flex flex-wrap gap-2">
                  {formData.skills[activeCategory].map((skill, index) => (
                    <motion.div
                      key={skill}
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      exit={{ opacity: 0, scale: 0.8 }}
                      className="flex items-center gap-2 px-3 py-2 bg-gradient-to-r from-neural-purple/20 to-neural-pink/20 border border-neural-purple/30 rounded-lg text-white text-sm"
                    >
                      <span>{skill}</span>
                      <button
                        onClick={() => removeSkill(activeCategory, skill)}
                        className="text-gray-400 hover:text-red-400 transition-colors"
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </motion.div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <Award className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p>No {skillCategories.find(c => c.id === activeCategory)?.label.toLowerCase()} added yet</p>
                  <p className="text-sm">Start typing to add your first skill</p>
                </div>
              )}
            </div>

            <ClientOnly>
              <ATSFieldIndicator
                fieldName={`skills_${activeCategory}`}
                value={formData.skills[activeCategory]?.join(', ')}
                analysis={atsAnalysis?.fieldAnalysis?.[`skills_${activeCategory}`]}
                showDetails={true}
              />
            </ClientOnly>
          </motion.div>
        </AnimatePresence>
      </div>

      {/* AI Helper Panel */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <Lightbulb className="h-5 w-5 text-neural-purple" />
            <h3 className="text-lg font-semibold text-white">Skills Optimization Assistant</h3>
          </div>
          <button
            onClick={() => setShowAIHelper(!showAIHelper)}
            className="flex items-center gap-2 px-3 py-2 bg-neural-purple/20 hover:bg-neural-purple/30 border border-neural-purple/50 rounded-lg transition-colors text-sm"
          >
            <Sparkles className="h-4 w-4" />
            {showAIHelper ? 'Hide' : 'Show'} AI Helper
          </button>
        </div>

        <AnimatePresence>
          {showAIHelper && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="bg-gradient-to-r from-neural-purple/10 to-neural-pink/10 border border-neural-purple/30 rounded-xl p-6"
            >
              <div className="flex items-start gap-3 mb-4">
                <div className="w-10 h-10 bg-neural-purple/20 rounded-lg flex items-center justify-center flex-shrink-0">
                  <Wand2 className="h-5 w-5 text-neural-purple" />
                </div>
                <div>
                  <h4 className="font-semibold text-white mb-2">Enhance Your Skills Section</h4>
                  <p className="text-gray-300 text-sm leading-relaxed mb-3">
                    Our AI can suggest relevant skills based on your experience and industry trends:
                  </p>
                  <ul className="text-gray-300 text-sm space-y-1">
                    <li className="flex items-center gap-2">
                      <div className="w-1.5 h-1.5 bg-neural-purple rounded-full"></div>
                      Identify missing technical skills for your field
                    </li>
                    <li className="flex items-center gap-2">
                      <div className="w-1.5 h-1.5 bg-neural-purple rounded-full"></div>
                      Suggest industry-relevant certifications
                    </li>
                    <li className="flex items-center gap-2">
                      <div className="w-1.5 h-1.5 bg-neural-purple rounded-full"></div>
                      Optimize skill keywords for ATS systems
                    </li>
                  </ul>
                </div>
              </div>
              <div className="flex gap-3">
                <button
                  onClick={() => onAISuggest?.('skills')}
                  className="flex items-center gap-2 px-4 py-2 bg-neural-purple hover:bg-neural-purple/80 text-white rounded-lg text-sm font-medium transition-colors"
                >
                  <Sparkles className="h-4 w-4" />
                  Generate Skill Suggestions
                </button>
                <button
                  onClick={() => setShowAIHelper(false)}
                  className="px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg text-sm font-medium transition-colors"
                >
                  Close Helper
                </button>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Progress Summary */}
      <div className="bg-gray-900/40 backdrop-blur-sm rounded-xl p-4 border border-white/10">
        <div className="grid grid-cols-3 gap-4 text-center">
          {skillCategories.map((category) => {
            const IconComponent = category.icon;
            return (
              <div key={category.id} className="space-y-2">
                <div className={`w-8 h-8 bg-${category.color}/20 rounded-lg flex items-center justify-center mx-auto`}>
                  <IconComponent className={`h-4 w-4 text-${category.color}`} />
                </div>
                <div>
                  <div className={`text-lg font-bold text-${category.color}`}>
                    {formData.skills[category.id]?.length || 0}
                  </div>
                  <div className="text-xs text-gray-400">{category.label}</div>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </motion.div>
  );
};

export default EnhancedSkillsForm;

'use client';
import { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Eye, EyeOff, HelpCircle, Sparkles, Check, AlertTriangle, Info, 
  ChevronDown, Calendar, Upload, X, Plus, Minus 
} from 'lucide-react';

const EnhancedFormField = ({
  label,
  type = 'text',
  value,
  onChange,
  placeholder,
  required = false,
  disabled = false,
  error,
  success,
  hint,
  helpText,
  icon: Icon,
  rows = 3,
  maxLength,
  showCharCount = false,
  aiSuggestions = [],
  onAISuggestionApply,
  className = '',
  size = 'default', // 'sm', 'default', 'lg'
  variant = 'modern', // 'modern', 'minimal', 'outlined'
  validation,
  autoComplete,
  options = [], // for select fields
  multiple = false,
  tags = [], // for tag input
  onTagAdd,
  onTagRemove,
  ...props
}) => {
  const [showPassword, setShowPassword] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [showHint, setShowHint] = useState(false);
  const [localValue, setLocalValue] = useState(value || '');
  const inputRef = useRef(null);

  const isTextarea = type === 'textarea';
  const isPassword = type === 'password';
  const isSelect = type === 'select';
  const isFile = type === 'file';
  const isDate = type === 'date';
  const isTags = type === 'tags';
  const hasError = !!error;
  const hasSuccess = !!success;
  const charCount = localValue?.length || 0;
  const isOverLimit = maxLength && charCount > maxLength;
  const hasValue = localValue && localValue.length > 0;

  useEffect(() => {
    setLocalValue(value || '');
  }, [value]);

  // Enhanced validation
  const getValidationState = () => {
    if (hasError) return 'error';
    if (hasSuccess) return 'success';
    if (validation && hasValue) {
      if (validation.pattern && !validation.pattern.test(localValue)) return 'warning';
      if (validation.minLength && localValue.length < validation.minLength) return 'warning';
      if (validation.required && !localValue) return 'error';
      return 'success';
    }
    return 'default';
  };

  const validationState = getValidationState();

  // Size variants
  const sizeClasses = {
    sm: 'px-3 py-2 text-sm',
    default: 'px-4 py-3 text-base',
    lg: 'px-5 py-4 text-lg'
  };

  // Variant styles
  const variantClasses = {
    modern: `
      bg-gray-800/50 border border-gray-700 rounded-xl
      focus:ring-2 focus:ring-neural-purple/50 focus:border-neural-purple
      hover:border-gray-600 transition-all duration-200
    `,
    minimal: `
      bg-transparent border-0 border-b-2 border-gray-700 rounded-none
      focus:border-neural-purple focus:ring-0
      hover:border-gray-600 transition-all duration-200
    `,
    outlined: `
      bg-transparent border-2 border-gray-600 rounded-lg
      focus:ring-2 focus:ring-neural-purple/30 focus:border-neural-purple
      hover:border-gray-500 transition-all duration-200
    `
  };

  // Validation state colors
  const stateClasses = {
    default: 'border-gray-700 focus:border-neural-purple',
    success: 'border-green-500 focus:border-green-400',
    warning: 'border-yellow-500 focus:border-yellow-400',
    error: 'border-red-500 focus:border-red-400'
  };

  const handleInputChange = (e) => {
    const newValue = e.target.value;
    setLocalValue(newValue);
    onChange(e);
  };

  const handleFocus = () => {
    setIsFocused(true);
  };

  const handleBlur = () => {
    setIsFocused(false);
  };

  const handleTagAdd = (tag) => {
    if (onTagAdd && tag.trim() && !tags.includes(tag.trim())) {
      onTagAdd(tag.trim());
      setLocalValue('');
    }
  };

  const handleKeyPress = (e) => {
    if (isTags && e.key === 'Enter') {
      e.preventDefault();
      handleTagAdd(localValue);
    }
  };

  const fieldId = `field-${label?.toLowerCase().replace(/\s+/g, '-')}`;

  const baseInputClasses = `
    w-full text-white placeholder-gray-400
    ${sizeClasses[size]}
    ${variantClasses[variant]}
    ${stateClasses[validationState]}
    ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
    ${className}
  `;

  const renderInput = () => {
    if (isTextarea) {
      return (
        <textarea
          ref={inputRef}
          id={fieldId}
          value={localValue}
          onChange={handleInputChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          placeholder={placeholder}
          required={required}
          disabled={disabled}
          rows={rows}
          maxLength={maxLength}
          autoComplete={autoComplete}
          className={baseInputClasses}
          {...props}
        />
      );
    }

    if (isSelect) {
      return (
        <div className="relative">
          <select
            ref={inputRef}
            id={fieldId}
            value={localValue}
            onChange={handleInputChange}
            onFocus={handleFocus}
            onBlur={handleBlur}
            required={required}
            disabled={disabled}
            className={`${baseInputClasses} appearance-none cursor-pointer`}
            {...props}
          >
            {placeholder && (
              <option value="" disabled>
                {placeholder}
              </option>
            )}
            {options.map((option, index) => (
              <option key={index} value={option.value || option}>
                {option.label || option}
              </option>
            ))}
          </select>
          <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
        </div>
      );
    }

    if (isFile) {
      return (
        <div className="relative">
          <input
            ref={inputRef}
            type="file"
            id={fieldId}
            onChange={handleInputChange}
            onFocus={handleFocus}
            onBlur={handleBlur}
            required={required}
            disabled={disabled}
            className="hidden"
            {...props}
          />
          <label
            htmlFor={fieldId}
            className={`${baseInputClasses} cursor-pointer flex items-center justify-center gap-2 border-2 border-dashed`}
          >
            <Upload className="h-4 w-4" />
            <span>{hasValue ? localValue : placeholder || 'Choose file'}</span>
          </label>
        </div>
      );
    }

    if (isTags) {
      return (
        <div className="space-y-2">
          {/* Tags Display */}
          {tags && tags.length > 0 && (
            <div className="flex flex-wrap gap-2">
              {tags.map((tag, index) => (
                <motion.span
                  key={index}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  className="inline-flex items-center gap-1 px-2 py-1 bg-neural-purple/20 text-neural-purple text-sm rounded-lg border border-neural-purple/30"
                >
                  {tag}
                  {onTagRemove && (
                    <button
                      type="button"
                      onClick={() => onTagRemove(index)}
                      className="text-neural-purple/70 hover:text-neural-purple"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  )}
                </motion.span>
              ))}
            </div>
          )}
          {/* Tag Input */}
          <input
            ref={inputRef}
            type="text"
            id={fieldId}
            value={localValue}
            onChange={handleInputChange}
            onFocus={handleFocus}
            onBlur={handleBlur}
            onKeyPress={handleKeyPress}
            placeholder={placeholder}
            disabled={disabled}
            className={baseInputClasses}
            {...props}
          />
        </div>
      );
    }

    return (
      <div className="relative">
        <input
          ref={inputRef}
          type={isPassword ? (showPassword ? 'text' : 'password') : type}
          id={fieldId}
          value={localValue}
          onChange={handleInputChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          placeholder={placeholder}
          required={required}
          disabled={disabled}
          maxLength={maxLength}
          autoComplete={autoComplete}
          className={baseInputClasses}
          {...props}
        />
        
        {/* Password Toggle */}
        {isPassword && (
          <button
            type="button"
            onClick={() => setShowPassword(!showPassword)}
            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white transition-colors"
          >
            {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
          </button>
        )}

        {/* Icon */}
        {Icon && !isPassword && (
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400">
            <Icon className="h-4 w-4" />
          </div>
        )}
      </div>
    );
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-2"
    >
      {/* Label */}
      {label && (
        <div className="flex items-center justify-between">
          <label
            htmlFor={fieldId}
            className={`block text-sm font-medium transition-colors ${
              isFocused ? 'text-neural-purple' : 'text-gray-300'
            }`}
          >
            {label}
            {required && <span className="text-red-400 ml-1">*</span>}
          </label>
          
          {/* Help Text Toggle */}
          {helpText && (
            <button
              type="button"
              onClick={() => setShowHint(!showHint)}
              className="text-gray-400 hover:text-neural-purple transition-colors"
            >
              <HelpCircle className="h-4 w-4" />
            </button>
          )}
        </div>
      )}

      {/* Help Text */}
      <AnimatePresence>
        {showHint && helpText && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-3"
          >
            <div className="flex items-start gap-2">
              <Info className="h-4 w-4 text-blue-400 mt-0.5 flex-shrink-0" />
              <p className="text-blue-300 text-sm">{helpText}</p>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Input Field */}
      <div className="relative">
        {renderInput()}
        
        {/* AI Suggestions Button */}
        {aiSuggestions.length > 0 && (
          <button
            type="button"
            onClick={() => setShowSuggestions(!showSuggestions)}
            className="absolute right-2 top-2 p-1 text-neural-purple hover:bg-neural-purple/20 rounded transition-colors"
          >
            <Sparkles className="h-4 w-4" />
          </button>
        )}
      </div>

      {/* Character Count */}
      {showCharCount && maxLength && (
        <div className="flex justify-end">
          <span className={`text-xs ${
            isOverLimit ? 'text-red-400' : charCount > maxLength * 0.8 ? 'text-yellow-400' : 'text-gray-500'
          }`}>
            {charCount}/{maxLength}
          </span>
        </div>
      )}

      {/* Validation Messages */}
      <AnimatePresence>
        {(hasError || hasSuccess || hint) && (
          <motion.div
            initial={{ opacity: 0, y: -5 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -5 }}
            className="space-y-1"
          >
            {hasError && (
              <div className="flex items-center gap-2 text-red-400 text-sm">
                <AlertTriangle className="h-3 w-3" />
                <span>{error}</span>
              </div>
            )}
            {hasSuccess && (
              <div className="flex items-center gap-2 text-green-400 text-sm">
                <Check className="h-3 w-3" />
                <span>{success}</span>
              </div>
            )}
            {hint && !hasError && !hasSuccess && (
              <div className="flex items-center gap-2 text-gray-400 text-sm">
                <Info className="h-3 w-3" />
                <span>{hint}</span>
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>

      {/* AI Suggestions */}
      <AnimatePresence>
        {showSuggestions && aiSuggestions.length > 0 && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="bg-gray-800/80 border border-neural-purple/30 rounded-lg p-3 space-y-2"
          >
            <div className="flex items-center gap-2 text-neural-purple text-sm font-medium">
              <Sparkles className="h-4 w-4" />
              AI Suggestions
            </div>
            {aiSuggestions.map((suggestion, index) => (
              <button
                key={index}
                type="button"
                onClick={() => {
                  if (onAISuggestionApply) {
                    onAISuggestionApply(suggestion);
                  } else {
                    setLocalValue(suggestion);
                    onChange({ target: { value: suggestion } });
                  }
                  setShowSuggestions(false);
                }}
                className="w-full text-left p-2 text-gray-300 hover:bg-neural-purple/20 hover:text-white rounded transition-colors text-sm"
              >
                {suggestion}
              </button>
            ))}
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
};

export default EnhancedFormField;

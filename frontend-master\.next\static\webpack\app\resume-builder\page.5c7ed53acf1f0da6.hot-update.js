"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/resume-builder/page",{

/***/ "(app-pages-browser)/./src/components/resume/forms/SkillsForm.jsx":
/*!****************************************************!*\
  !*** ./src/components/resume/forms/SkillsForm.jsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EnhancedSkillsForm: () => (/* binding */ EnhancedSkillsForm),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Certificate_CheckCircle_Clock_Code_Globe_Info_Lightbulb_Plus_Save_Sparkles_Target_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Certificate,CheckCircle,Clock,Code,Globe,Info,Lightbulb,Plus,Save,Sparkles,Target,Wand2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Certificate_CheckCircle_Clock_Code_Globe_Info_Lightbulb_Plus_Save_Sparkles_Target_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Certificate,CheckCircle,Clock,Code,Globe,Info,Lightbulb,Plus,Save,Sparkles,Target,Wand2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Certificate_CheckCircle_Clock_Code_Globe_Info_Lightbulb_Plus_Save_Sparkles_Target_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Certificate,CheckCircle,Clock,Code,Globe,Info,Lightbulb,Plus,Save,Sparkles,Target,Wand2,X!=!lucide-react */ \"(app-pages-browser)/__barrel_optimize__?names=AlertTriangle,Award,Certificate,CheckCircle,Clock,Code,Globe,Info,Lightbulb,Plus,Save,Sparkles,Target,Wand2,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Certificate_CheckCircle_Clock_Code_Globe_Info_Lightbulb_Plus_Save_Sparkles_Target_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Certificate,CheckCircle,Clock,Code,Globe,Info,Lightbulb,Plus,Save,Sparkles,Target,Wand2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Certificate_CheckCircle_Clock_Code_Globe_Info_Lightbulb_Plus_Save_Sparkles_Target_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Certificate,CheckCircle,Clock,Code,Globe,Info,Lightbulb,Plus,Save,Sparkles,Target,Wand2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Certificate_CheckCircle_Clock_Code_Globe_Info_Lightbulb_Plus_Save_Sparkles_Target_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Certificate,CheckCircle,Clock,Code,Globe,Info,Lightbulb,Plus,Save,Sparkles,Target,Wand2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Certificate_CheckCircle_Clock_Code_Globe_Info_Lightbulb_Plus_Save_Sparkles_Target_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Certificate,CheckCircle,Clock,Code,Globe,Info,Lightbulb,Plus,Save,Sparkles,Target,Wand2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Certificate_CheckCircle_Clock_Code_Globe_Info_Lightbulb_Plus_Save_Sparkles_Target_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Certificate,CheckCircle,Clock,Code,Globe,Info,Lightbulb,Plus,Save,Sparkles,Target,Wand2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Certificate_CheckCircle_Clock_Code_Globe_Info_Lightbulb_Plus_Save_Sparkles_Target_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Certificate,CheckCircle,Clock,Code,Globe,Info,Lightbulb,Plus,Save,Sparkles,Target,Wand2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lightbulb.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Certificate_CheckCircle_Clock_Code_Globe_Info_Lightbulb_Plus_Save_Sparkles_Target_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Certificate,CheckCircle,Clock,Code,Globe,Info,Lightbulb,Plus,Save,Sparkles,Target,Wand2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Certificate_CheckCircle_Clock_Code_Globe_Info_Lightbulb_Plus_Save_Sparkles_Target_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Certificate,CheckCircle,Clock,Code,Globe,Info,Lightbulb,Plus,Save,Sparkles,Target,Wand2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wand-sparkles.js\");\n/* harmony import */ var _SmartFormField__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./SmartFormField */ \"(app-pages-browser)/./src/components/resume/forms/SmartFormField.jsx\");\n/* harmony import */ var _common_ClientOnly__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../common/ClientOnly */ \"(app-pages-browser)/./src/components/common/ClientOnly.jsx\");\n/* harmony import */ var _ats_FieldIndicator__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../ats/FieldIndicator */ \"(app-pages-browser)/./src/components/resume/ats/FieldIndicator.jsx\");\n/* __next_internal_client_entry_do_not_use__ EnhancedSkillsForm,default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst EnhancedSkillsForm = (param)=>{\n    let { formData, updateFormData, atsAnalysis, validationErrors = {}, showValidationErrors = false, onSave, onAISuggest } = param;\n    var _skillCategories_find, _skillCategories_find1, _formData_skills_activeCategory, _skillCategories_find2, _formData_skills_activeCategory1, _atsAnalysis_fieldAnalysis;\n    _s();\n    const [activeCategory, setActiveCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('technical');\n    const [showAIHelper, setShowAIHelper] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [newSkill, setNewSkill] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        technical: '',\n        languages: '',\n        certifications: ''\n    });\n    const skillCategories = [\n        {\n            id: 'technical',\n            label: 'Technical Skills',\n            icon: _barrel_optimize_names_AlertTriangle_Award_Certificate_CheckCircle_Clock_Code_Globe_Info_Lightbulb_Plus_Save_Sparkles_Target_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            color: 'neural-purple'\n        },\n        {\n            id: 'languages',\n            label: 'Languages',\n            icon: _barrel_optimize_names_AlertTriangle_Award_Certificate_CheckCircle_Clock_Code_Globe_Info_Lightbulb_Plus_Save_Sparkles_Target_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            color: 'neural-blue'\n        },\n        {\n            id: 'certifications',\n            label: 'Certifications',\n            icon: _barrel_optimize_names_AlertTriangle_Award_Certificate_CheckCircle_Clock_Code_Globe_Info_Lightbulb_Plus_Save_Sparkles_Target_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__.Certificate,\n            color: 'neural-pink'\n        }\n    ];\n    const addSkill = (category)=>{\n        const skill = newSkill[category].trim();\n        if (skill && !formData.skills[category].includes(skill)) {\n            const updatedSkills = [\n                ...formData.skills[category],\n                skill\n            ];\n            updateFormData('skills', category, updatedSkills);\n            setNewSkill({\n                ...newSkill,\n                [category]: ''\n            });\n        }\n    };\n    const removeSkill = (category, skillToRemove)=>{\n        const updatedSkills = formData.skills[category].filter((skill)=>skill !== skillToRemove);\n        updateFormData('skills', category, updatedSkills);\n    };\n    const handleKeyPress = (e, category)=>{\n        if (e.key === 'Enter') {\n            e.preventDefault();\n            addSkill(category);\n        }\n    };\n    const getSkillSuggestions = (category)=>{\n        const suggestions = {\n            technical: [\n                'JavaScript',\n                'Python',\n                'React',\n                'Node.js',\n                'SQL',\n                'AWS',\n                'Docker',\n                'Git',\n                'TypeScript',\n                'MongoDB'\n            ],\n            languages: [\n                'English',\n                'Spanish',\n                'French',\n                'German',\n                'Mandarin',\n                'Japanese',\n                'Portuguese',\n                'Italian',\n                'Russian',\n                'Arabic'\n            ],\n            certifications: [\n                'AWS Certified',\n                'Google Cloud Professional',\n                'Microsoft Azure',\n                'PMP',\n                'Scrum Master',\n                'CompTIA Security+',\n                'Cisco CCNA',\n                'Oracle Certified'\n            ]\n        };\n        return suggestions[category] || [];\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n        initial: {\n            opacity: 0,\n            y: 20\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        exit: {\n            opacity: 0,\n            y: -20\n        },\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-10 h-10 bg-neural-purple/20 rounded-xl flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Award_Certificate_CheckCircle_Clock_Code_Globe_Info_Lightbulb_Plus_Save_Sparkles_Target_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"h-5 w-5 text-neural-purple\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                lineNumber: 86,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                            lineNumber: 85,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold text-white\",\n                                    children: \"Skills\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400 text-sm\",\n                                    children: \"Highlight your technical and soft skills (Optional)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                            lineNumber: 88,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                    lineNumber: 84,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                lineNumber: 83,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: -10\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                className: \"bg-blue-500/10 border border-blue-500/20 rounded-xl p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Award_Certificate_CheckCircle_Clock_Code_Globe_Info_Lightbulb_Plus_Save_Sparkles_Target_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"h-4 w-4 text-blue-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                lineNumber: 102,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-blue-400 font-medium\",\n                                children: \"Optional Section\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                lineNumber: 103,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                        lineNumber: 101,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-300 text-sm\",\n                        children: \"Skills help employers quickly identify your capabilities. You can skip this section if your experience and projects already demonstrate your skills clearly.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                        lineNumber: 105,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.AnimatePresence, {\n                children: showValidationErrors && validationErrors.skills && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: -10\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    exit: {\n                        opacity: 0,\n                        y: -10\n                    },\n                    className: \"bg-red-500/10 border border-red-500/20 rounded-xl p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 mb-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Award_Certificate_CheckCircle_Clock_Code_Globe_Info_Lightbulb_Plus_Save_Sparkles_Target_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-4 w-4 text-red-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-red-400 font-medium\",\n                                    children: \"Skills Information\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                            lineNumber: 119,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-300 text-sm\",\n                            children: validationErrors.skills\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                            lineNumber: 123,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                    lineNumber: 113,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                lineNumber: 111,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex gap-2 p-1 bg-gray-900/40 rounded-xl border border-white/10\",\n                children: skillCategories.map((category)=>{\n                    var _formData_skills_category_id;\n                    const IconComponent = category.icon;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setActiveCategory(category.id),\n                        className: \"flex items-center gap-2 px-4 py-3 rounded-lg text-sm font-medium transition-all duration-300 flex-1 \".concat(activeCategory === category.id ? \"bg-\".concat(category.color, \" text-white shadow-lg\") : 'text-gray-300 hover:bg-white/5 hover:text-white'),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                lineNumber: 142,\n                                columnNumber: 15\n                            }, undefined),\n                            category.label,\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"ml-auto bg-white/20 text-xs px-2 py-1 rounded-full\",\n                                children: ((_formData_skills_category_id = formData.skills[category.id]) === null || _formData_skills_category_id === void 0 ? void 0 : _formData_skills_category_id.length) || 0\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                lineNumber: 144,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, category.id, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                        lineNumber: 133,\n                        columnNumber: 13\n                    }, undefined);\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                lineNumber: 129,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-900/40 backdrop-blur-sm rounded-2xl p-6 border border-white/10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.AnimatePresence, {\n                    mode: \"wait\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            x: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            x: 0\n                        },\n                        exit: {\n                            opacity: 0,\n                            x: -20\n                        },\n                        transition: {\n                            duration: 0.2\n                        },\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: newSkill[activeCategory],\n                                                onChange: (e)=>setNewSkill({\n                                                        ...newSkill,\n                                                        [activeCategory]: e.target.value\n                                                    }),\n                                                onKeyPress: (e)=>handleKeyPress(e, activeCategory),\n                                                placeholder: \"Add \".concat((_skillCategories_find = skillCategories.find((c)=>c.id === activeCategory)) === null || _skillCategories_find === void 0 ? void 0 : _skillCategories_find.label.toLowerCase(), \"...\"),\n                                                className: \"flex-1 px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                                lineNumber: 166,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>addSkill(activeCategory),\n                                                disabled: !newSkill[activeCategory].trim(),\n                                                className: \"px-4 py-3 bg-neural-purple hover:bg-neural-purple/80 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-lg transition-colors\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Award_Certificate_CheckCircle_Clock_Code_Globe_Info_Lightbulb_Plus_Save_Sparkles_Target_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                                    lineNumber: 179,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: \"Popular suggestions:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap gap-2\",\n                                                children: getSkillSuggestions(activeCategory).filter((suggestion)=>!formData.skills[activeCategory].includes(suggestion)).slice(0, 8).map((suggestion)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>{\n                                                            setNewSkill({\n                                                                ...newSkill,\n                                                                [activeCategory]: suggestion\n                                                            });\n                                                            addSkill(activeCategory);\n                                                        },\n                                                        className: \"px-3 py-1.5 bg-gray-700 hover:bg-gray-600 text-gray-300 hover:text-white text-sm rounded-lg transition-colors\",\n                                                        children: suggestion\n                                                    }, suggestion, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                                        lineNumber: 191,\n                                                        columnNumber: 23\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                lineNumber: 164,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-medium text-white flex items-center gap-2\",\n                                        children: [\n                                            (()=>{\n                                                const category = skillCategories.find((c)=>c.id === activeCategory);\n                                                if (category === null || category === void 0 ? void 0 : category.icon) {\n                                                    const IconComponent = category.icon;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                                        lineNumber: 213,\n                                                        columnNumber: 28\n                                                    }, undefined);\n                                                }\n                                                return null;\n                                            })(),\n                                            \"Your \",\n                                            (_skillCategories_find1 = skillCategories.find((c)=>c.id === activeCategory)) === null || _skillCategories_find1 === void 0 ? void 0 : _skillCategories_find1.label\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    ((_formData_skills_activeCategory = formData.skills[activeCategory]) === null || _formData_skills_activeCategory === void 0 ? void 0 : _formData_skills_activeCategory.length) > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-2\",\n                                        children: formData.skills[activeCategory].map((skill, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                                initial: {\n                                                    opacity: 0,\n                                                    scale: 0.8\n                                                },\n                                                animate: {\n                                                    opacity: 1,\n                                                    scale: 1\n                                                },\n                                                exit: {\n                                                    opacity: 0,\n                                                    scale: 0.8\n                                                },\n                                                className: \"flex items-center gap-2 px-3 py-2 bg-gradient-to-r from-neural-purple/20 to-neural-pink/20 border border-neural-purple/30 rounded-lg text-white text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: skill\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                                        lineNumber: 230,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>removeSkill(activeCategory, skill),\n                                                        className: \"text-gray-400 hover:text-red-400 transition-colors\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Award_Certificate_CheckCircle_Clock_Code_Globe_Info_Lightbulb_Plus_Save_Sparkles_Target_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                                            lineNumber: 235,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                                        lineNumber: 231,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, skill, true, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 21\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 17\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8 text-gray-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Award_Certificate_CheckCircle_Clock_Code_Globe_Info_Lightbulb_Plus_Save_Sparkles_Target_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-8 w-8 mx-auto mb-2 opacity-50\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"No \",\n                                                    (_skillCategories_find2 = skillCategories.find((c)=>c.id === activeCategory)) === null || _skillCategories_find2 === void 0 ? void 0 : _skillCategories_find2.label.toLowerCase(),\n                                                    \" added yet\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm\",\n                                                children: \"Start typing to add your first skill\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                                lineNumber: 244,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                lineNumber: 207,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_common_ClientOnly__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ats_FieldIndicator__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    fieldName: \"skills_\".concat(activeCategory),\n                                    value: (_formData_skills_activeCategory1 = formData.skills[activeCategory]) === null || _formData_skills_activeCategory1 === void 0 ? void 0 : _formData_skills_activeCategory1.join(', '),\n                                    analysis: atsAnalysis === null || atsAnalysis === void 0 ? void 0 : (_atsAnalysis_fieldAnalysis = atsAnalysis.fieldAnalysis) === null || _atsAnalysis_fieldAnalysis === void 0 ? void 0 : _atsAnalysis_fieldAnalysis[\"skills_\".concat(activeCategory)],\n                                    showDetails: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                    lineNumber: 250,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                lineNumber: 249,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, activeCategory, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                        lineNumber: 155,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                    lineNumber: 154,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                lineNumber: 153,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Award_Certificate_CheckCircle_Clock_Code_Globe_Info_Lightbulb_Plus_Save_Sparkles_Target_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-5 w-5 text-neural-purple\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                        lineNumber: 265,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-white\",\n                                        children: \"Skills Optimization Assistant\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                lineNumber: 264,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowAIHelper(!showAIHelper),\n                                className: \"flex items-center gap-2 px-3 py-2 bg-neural-purple/20 hover:bg-neural-purple/30 border border-neural-purple/50 rounded-lg transition-colors text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Award_Certificate_CheckCircle_Clock_Code_Globe_Info_Lightbulb_Plus_Save_Sparkles_Target_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                        lineNumber: 272,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    showAIHelper ? 'Hide' : 'Show',\n                                    \" AI Helper\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                lineNumber: 268,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                        lineNumber: 263,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.AnimatePresence, {\n                        children: showAIHelper && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                height: 0\n                            },\n                            animate: {\n                                opacity: 1,\n                                height: 'auto'\n                            },\n                            exit: {\n                                opacity: 0,\n                                height: 0\n                            },\n                            className: \"bg-gradient-to-r from-neural-purple/10 to-neural-pink/10 border border-neural-purple/30 rounded-xl p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start gap-3 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-neural-purple/20 rounded-lg flex items-center justify-center flex-shrink-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Award_Certificate_CheckCircle_Clock_Code_Globe_Info_Lightbulb_Plus_Save_Sparkles_Target_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-5 w-5 text-neural-purple\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                                lineNumber: 287,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                            lineNumber: 286,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-semibold text-white mb-2\",\n                                                    children: \"Enhance Your Skills Section\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                                    lineNumber: 290,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-300 text-sm leading-relaxed mb-3\",\n                                                    children: \"Our AI can suggest relevant skills based on your experience and industry trends:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                                    lineNumber: 291,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"text-gray-300 text-sm space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-1.5 h-1.5 bg-neural-purple rounded-full\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                                                    lineNumber: 296,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                \"Identify missing technical skills for your field\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                                            lineNumber: 295,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-1.5 h-1.5 bg-neural-purple rounded-full\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                                                    lineNumber: 300,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                \"Suggest industry-relevant certifications\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                                            lineNumber: 299,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-1.5 h-1.5 bg-neural-purple rounded-full\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                                                    lineNumber: 304,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                \"Optimize skill keywords for ATS systems\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                                            lineNumber: 303,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                                    lineNumber: 294,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                    lineNumber: 285,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>onAISuggest === null || onAISuggest === void 0 ? void 0 : onAISuggest('skills'),\n                                            className: \"flex items-center gap-2 px-4 py-2 bg-neural-purple hover:bg-neural-purple/80 text-white rounded-lg text-sm font-medium transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Award_Certificate_CheckCircle_Clock_Code_Globe_Info_Lightbulb_Plus_Save_Sparkles_Target_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                                    lineNumber: 315,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"Generate Skill Suggestions\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                            lineNumber: 311,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowAIHelper(false),\n                                            className: \"px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg text-sm font-medium transition-colors\",\n                                            children: \"Close Helper\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                            lineNumber: 318,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                    lineNumber: 310,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                            lineNumber: 279,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                        lineNumber: 277,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                lineNumber: 262,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-900/40 backdrop-blur-sm rounded-xl p-4 border border-white/10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-3 gap-4 text-center\",\n                    children: skillCategories.map((category)=>{\n                        var _formData_skills_category_id;\n                        const IconComponent = category.icon;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-\".concat(category.color, \"/20 rounded-lg flex items-center justify-center mx-auto\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                        className: \"h-4 w-4 text-\".concat(category.color)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                        lineNumber: 338,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                    lineNumber: 337,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-lg font-bold text-\".concat(category.color),\n                                            children: ((_formData_skills_category_id = formData.skills[category.id]) === null || _formData_skills_category_id === void 0 ? void 0 : _formData_skills_category_id.length) || 0\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                            lineNumber: 341,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-gray-400\",\n                                            children: category.label\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                            lineNumber: 344,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                    lineNumber: 340,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, category.id, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                            lineNumber: 336,\n                            columnNumber: 15\n                        }, undefined);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                    lineNumber: 332,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                lineNumber: 331,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n        lineNumber: 76,\n        columnNumber: 5\n    }, undefined);\n};\n_s(EnhancedSkillsForm, \"3AMfliK8dwk4WCV+B2p/IUeFBIQ=\");\n_c = EnhancedSkillsForm;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (EnhancedSkillsForm);\nvar _c;\n$RefreshReg$(_c, \"EnhancedSkillsForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/resume/forms/SkillsForm.jsx\n"));

/***/ })

});
'use client';

import { useState, useEffect } from 'react';
import { testFirebaseConnection } from '@/firebase/test';
import { Wifi, WifiOff, Database, Clock } from 'lucide-react';

const FirebaseStatus = ({ showDetails = false }) => {
  const [status, setStatus] = useState({
    firestore: null,
    realtimeDatabase: null,
    errors: [],
    lastChecked: null,
    isLoading: true
  });

  const checkConnection = async () => {
    setStatus(prev => ({ ...prev, isLoading: true }));
    const result = await testFirebaseConnection();
    setStatus({
      ...result,
      lastChecked: new Date().toLocaleTimeString(),
      isLoading: false
    });
  };

  useEffect(() => {
    checkConnection();
    // Check connection every 30 seconds in development
    if (process.env.NODE_ENV === 'development') {
      const interval = setInterval(checkConnection, 30000);
      return () => clearInterval(interval);
    }
  }, []);

  const getStatusIcon = (isConnected) => {
    if (status.isLoading) return <Clock className="w-4 h-4 animate-spin" />;
    return isConnected ? 
      <Wifi className="w-4 h-4 text-green-500" /> : 
      <WifiOff className="w-4 h-4 text-red-500" />;
  };

  const getStatusColor = (isConnected) => {
    if (status.isLoading) return 'text-yellow-500';
    return isConnected ? 'text-green-500' : 'text-red-500';
  };

  if (!showDetails) {
    // Compact status indicator
    const allConnected = status.firestore && status.realtimeDatabase;
    return (
      <div className="flex items-center gap-2 text-sm">
        {getStatusIcon(allConnected)}
        <span className={getStatusColor(allConnected)}>
          Firebase {status.isLoading ? 'Checking...' : allConnected ? 'Connected' : 'Disconnected'}
        </span>
      </div>
    );
  }

  return (
    <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg p-4 max-w-md">
      <div className="flex items-center gap-2 mb-3">
        <Database className="w-5 h-5 text-purple-400" />
        <h3 className="font-semibold text-white">Firebase Status</h3>
        <button
          onClick={checkConnection}
          className="ml-auto text-xs bg-purple-500/20 hover:bg-purple-500/30 px-2 py-1 rounded transition-colors"
          disabled={status.isLoading}
        >
          {status.isLoading ? 'Checking...' : 'Refresh'}
        </button>
      </div>

      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-300">Firestore</span>
          <div className="flex items-center gap-1">
            {getStatusIcon(status.firestore)}
            <span className={`text-sm ${getStatusColor(status.firestore)}`}>
              {status.isLoading ? '...' : status.firestore ? 'Connected' : 'Failed'}
            </span>
          </div>
        </div>

        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-300">Realtime DB</span>
          <div className="flex items-center gap-1">
            {getStatusIcon(status.realtimeDatabase)}
            <span className={`text-sm ${getStatusColor(status.realtimeDatabase)}`}>
              {status.isLoading ? '...' : status.realtimeDatabase ? 'Connected' : 'Failed'}
            </span>
          </div>
        </div>

        {status.lastChecked && (
          <div className="text-xs text-gray-400 pt-2 border-t border-white/10">
            Last checked: {status.lastChecked}
          </div>
        )}

        {status.errors.length > 0 && (
          <div className="mt-3 p-2 bg-red-500/10 border border-red-500/20 rounded text-xs">
            <div className="text-red-400 font-medium mb-1">Errors:</div>
            {status.errors.map((error, index) => (
              <div key={index} className="text-red-300">{error}</div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default FirebaseStatus;

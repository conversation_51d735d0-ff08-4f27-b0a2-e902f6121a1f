'use client';
import { useState } from 'react';
import { motion } from 'framer-motion';
import { Plus, Trash2, HelpCircle, Star, Calendar, MapPin, Building, User } from 'lucide-react';

// Universal Personal Information Form
export const UniversalPersonalForm = ({ formData, updateFormData, validationErrors }) => {
  return (
    <div className="space-y-6">
      <div className="bg-gray-900/60 backdrop-blur-md rounded-2xl border border-gray-700/50 p-6">
        <div className="mb-6">
          <h2 className="text-xl font-semibold text-white mb-2 flex items-center gap-2">
            <User className="h-5 w-5 text-neural-purple" />
            Personal Information
          </h2>
          <p className="text-sm text-gray-400">
            ⚡ AI will optimize your information for maximum impact
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              First Name *
            </label>
            <input
              type="text"
              value={formData.personal?.firstName || ''}
              onChange={(e) => updateFormData('personal', 'firstName', e.target.value)}
              className={`w-full px-3 py-2 bg-gray-800 border rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-neural-purple text-white placeholder-gray-400 ${
                validationErrors?.firstName ? 'border-red-500' : 'border-gray-600'
              }`}
              placeholder="Enter your first name"
            />
            {validationErrors?.firstName && (
              <p className="text-red-400 text-xs mt-1">{validationErrors.firstName}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Last Name *
            </label>
            <input
              type="text"
              value={formData.personal?.lastName || ''}
              onChange={(e) => updateFormData('personal', 'lastName', e.target.value)}
              className={`w-full px-3 py-2 bg-gray-800 border rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-neural-purple text-white placeholder-gray-400 ${
                validationErrors?.lastName ? 'border-red-500' : 'border-gray-600'
              }`}
              placeholder="Enter your last name"
            />
            {validationErrors?.lastName && (
              <p className="text-red-400 text-xs mt-1">{validationErrors.lastName}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Email Address *
            </label>
            <input
              type="email"
              value={formData.personal?.email || ''}
              onChange={(e) => updateFormData('personal', 'email', e.target.value)}
              className={`w-full px-3 py-2 bg-gray-800 border rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-neural-purple text-white placeholder-gray-400 ${
                validationErrors?.email ? 'border-red-500' : 'border-gray-600'
              }`}
              placeholder="<EMAIL>"
            />
            {validationErrors?.email && (
              <p className="text-red-400 text-xs mt-1">{validationErrors.email}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Phone Number
            </label>
            <input
              type="tel"
              value={formData.personal?.phone || ''}
              onChange={(e) => updateFormData('personal', 'phone', e.target.value)}
              className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-neural-purple text-white placeholder-gray-400"
              placeholder="(*************"
            />
          </div>

          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Location
            </label>
            <input
              type="text"
              value={formData.personal?.location || ''}
              onChange={(e) => updateFormData('personal', 'location', e.target.value)}
              className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-neural-purple text-white placeholder-gray-400"
              placeholder="City, State or City, Country"
            />
          </div>

          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Professional Summary
              <span className="text-neural-blue text-xs ml-1">✨ AI will enhance this for maximum impact</span>
            </label>
            <textarea
              value={formData.personal?.summary || ''}
              onChange={(e) => updateFormData('personal', 'summary', e.target.value)}
              rows={4}
              className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-neural-purple text-white placeholder-gray-400"
              placeholder="Write 2-3 sentences about your background. Our AI will optimize it for ATS systems and hiring managers..."
            />
          </div>
        </div>
      </div>
    </div>
  );
};

// Universal Experience Form
export const UniversalExperienceForm = ({ formData, updateFormData, addArrayItem, removeArrayItem }) => {
  const addExperience = () => {
    addArrayItem('experience', {
      title: '',
      company: '',
      location: '',
      startDate: '',
      endDate: '',
      current: false,
      description: ''
    });
  };

  return (
    <div className="space-y-6">
      <div className="bg-gray-900/60 backdrop-blur-md rounded-2xl border border-gray-700/50 p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-xl font-semibold text-white flex items-center gap-2 mb-2">
              <Building className="h-5 w-5 text-neural-purple" />
              Work Experience
            </h2>
            <p className="text-sm text-gray-400">
              🚀 AI will craft compelling job descriptions that get you noticed
            </p>
          </div>
          <button
            onClick={addExperience}
            className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-neural-purple to-neural-pink text-white rounded-lg hover:opacity-90 transition-opacity"
          >
            <Plus className="h-4 w-4" />
            Add Position
          </button>
        </div>

        <div className="space-y-6">
          {formData.experience?.map((exp, index) => (
            <motion.div
              key={exp.id || index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="border border-gray-700/50 rounded-lg p-4 relative bg-gray-800/30"
            >
              {formData.experience.length > 1 && (
                <button
                  onClick={() => removeArrayItem('experience', exp.id)}
                  className="absolute top-4 right-4 p-1 text-red-400 hover:text-red-300 hover:bg-red-500/20 rounded"
                >
                  <Trash2 className="h-4 w-4" />
                </button>
              )}

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">
                    Job Title / Position *
                  </label>
                  <input
                    type="text"
                    value={exp.title || ''}
                    onChange={(e) => updateFormData('experience', 'title', e.target.value, index)}
                    className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-neural-purple text-white placeholder-gray-400"
                    placeholder="e.g., Sales Manager, Teacher, Nurse, etc."
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">
                    Company / Organization *
                  </label>
                  <input
                    type="text"
                    value={exp.company || ''}
                    onChange={(e) => updateFormData('experience', 'company', e.target.value, index)}
                    className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-neural-purple text-white placeholder-gray-400"
                    placeholder="Company, school, hospital, etc."
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Location
                  </label>
                  <input
                    type="text"
                    value={exp.location || ''}
                    onChange={(e) => updateFormData('experience', 'location', e.target.value, index)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="City, State"
                  />
                </div>

                <div className="flex gap-2">
                  <div className="flex-1">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Start Date
                    </label>
                    <input
                      type="text"
                      value={exp.startDate || ''}
                      onChange={(e) => updateFormData('experience', 'startDate', e.target.value, index)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="MM/YYYY"
                    />
                  </div>
                  <div className="flex-1">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      End Date
                    </label>
                    <input
                      type="text"
                      value={exp.endDate || ''}
                      onChange={(e) => updateFormData('experience', 'endDate', e.target.value, index)}
                      disabled={exp.current}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100"
                      placeholder="MM/YYYY"
                    />
                  </div>
                </div>

                <div className="md:col-span-2">
                  <label className="flex items-center gap-2 text-sm font-medium text-gray-700 mb-2">
                    <input
                      type="checkbox"
                      checked={exp.current || false}
                      onChange={(e) => updateFormData('experience', 'current', e.target.checked, index)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    I currently work here
                  </label>
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Key Responsibilities & Achievements
                    <span className="text-gray-500 text-xs ml-1">(Use bullet points)</span>
                  </label>
                  <textarea
                    value={exp.description || ''}
                    onChange={(e) => updateFormData('experience', 'description', e.target.value, index)}
                    rows={4}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="• Managed team of 10 employees and increased sales by 25%&#10;• Developed new training program that improved efficiency&#10;• Led project that resulted in $50K cost savings"
                  />
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  );
};

// Universal Education Form
export const UniversalEducationForm = ({ formData, updateFormData, addArrayItem, removeArrayItem }) => {
  const addEducation = () => {
    addArrayItem('education', {
      degree: '',
      field: '',
      institution: '',
      location: '',
      graduationDate: '',
      gpa: ''
    });
  };

  return (
    <div className="space-y-6">
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-gray-900 flex items-center gap-2">
            <Star className="h-5 w-5 text-blue-600" />
            Education
          </h2>
          <button
            onClick={addEducation}
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <Plus className="h-4 w-4" />
            Add Education
          </button>
        </div>

        <div className="space-y-6">
          {formData.education?.map((edu, index) => (
            <motion.div
              key={edu.id || index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="border border-gray-200 rounded-lg p-4 relative"
            >
              {formData.education.length > 1 && (
                <button
                  onClick={() => removeArrayItem('education', edu.id)}
                  className="absolute top-4 right-4 p-1 text-red-500 hover:text-red-700 hover:bg-red-50 rounded"
                >
                  <Trash2 className="h-4 w-4" />
                </button>
              )}

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Degree / Certification *
                  </label>
                  <input
                    type="text"
                    value={edu.degree || ''}
                    onChange={(e) => updateFormData('education', 'degree', e.target.value, index)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="e.g., Bachelor's, Master's, Certificate, etc."
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Field of Study
                  </label>
                  <input
                    type="text"
                    value={edu.field || ''}
                    onChange={(e) => updateFormData('education', 'field', e.target.value, index)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="e.g., Business, Education, Nursing, etc."
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Institution *
                  </label>
                  <input
                    type="text"
                    value={edu.institution || ''}
                    onChange={(e) => updateFormData('education', 'institution', e.target.value, index)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="University, college, training center, etc."
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Graduation Date
                  </label>
                  <input
                    type="text"
                    value={edu.graduationDate || ''}
                    onChange={(e) => updateFormData('education', 'graduationDate', e.target.value, index)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="MM/YYYY or Expected MM/YYYY"
                  />
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  );
};

// Universal Skills Form
export const UniversalSkillsForm = ({ formData, updateFormData }) => {
  const [newSkill, setNewSkill] = useState('');
  const [newLanguage, setNewLanguage] = useState('');

  const addSkill = (type) => {
    const value = type === 'technical' ? newSkill : newLanguage;
    if (!value.trim()) return;

    const currentSkills = formData.skills?.[type] || [];
    updateFormData('skills', type, [...currentSkills, value.trim()]);
    
    if (type === 'technical') setNewSkill('');
    else setNewLanguage('');
  };

  const removeSkill = (type, index) => {
    const currentSkills = formData.skills?.[type] || [];
    const newSkills = currentSkills.filter((_, i) => i !== index);
    updateFormData('skills', type, newSkills);
  };

  return (
    <div className="space-y-6">
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center gap-2">
          <Star className="h-5 w-5 text-blue-600" />
          Skills & Competencies
        </h2>

        <div className="space-y-6">
          {/* Core Skills */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Core Skills & Competencies
              <span className="text-gray-500 text-xs ml-1">(relevant to your profession)</span>
            </label>
            <div className="flex gap-2 mb-3">
              <input
                type="text"
                value={newSkill}
                onChange={(e) => setNewSkill(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && addSkill('technical')}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="e.g., Customer Service, Project Management, Teaching, etc."
              />
              <button
                onClick={() => addSkill('technical')}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Add
              </button>
            </div>
            <div className="flex flex-wrap gap-2">
              {formData.skills?.technical?.map((skill, index) => (
                <span
                  key={index}
                  className="inline-flex items-center gap-1 px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm"
                >
                  {skill}
                  <button
                    onClick={() => removeSkill('technical', index)}
                    className="text-blue-600 hover:text-blue-800"
                  >
                    ×
                  </button>
                </span>
              ))}
            </div>
          </div>

          {/* Languages */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Languages
            </label>
            <div className="flex gap-2 mb-3">
              <input
                type="text"
                value={newLanguage}
                onChange={(e) => setNewLanguage(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && addSkill('languages')}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="e.g., English (Native), Spanish (Fluent), etc."
              />
              <button
                onClick={() => addSkill('languages')}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Add
              </button>
            </div>
            <div className="flex flex-wrap gap-2">
              {formData.skills?.languages?.map((language, index) => (
                <span
                  key={index}
                  className="inline-flex items-center gap-1 px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm"
                >
                  {language}
                  <button
                    onClick={() => removeSkill('languages', index)}
                    className="text-green-600 hover:text-green-800"
                  >
                    ×
                  </button>
                </span>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/resume-builder/page",{

/***/ "(app-pages-browser)/./src/components/resume/SimplifiedNavigation.jsx":
/*!********************************************************!*\
  !*** ./src/components/resume/SimplifiedNavigation.jsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_CheckCircle_Circle_Download_Eye_FileText_GraduationCap_Home_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,CheckCircle,Circle,Download,Eye,FileText,GraduationCap,Home,Save,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_CheckCircle_Circle_Download_Eye_FileText_GraduationCap_Home_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,CheckCircle,Circle,Download,Eye,FileText,GraduationCap,Home,Save,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/graduation-cap.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_CheckCircle_Circle_Download_Eye_FileText_GraduationCap_Home_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,CheckCircle,Circle,Download,Eye,FileText,GraduationCap,Home,Save,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_CheckCircle_Circle_Download_Eye_FileText_GraduationCap_Home_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,CheckCircle,Circle,Download,Eye,FileText,GraduationCap,Home,Save,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_CheckCircle_Circle_Download_Eye_FileText_GraduationCap_Home_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,CheckCircle,Circle,Download,Eye,FileText,GraduationCap,Home,Save,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_CheckCircle_Circle_Download_Eye_FileText_GraduationCap_Home_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,CheckCircle,Circle,Download,Eye,FileText,GraduationCap,Home,Save,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_CheckCircle_Circle_Download_Eye_FileText_GraduationCap_Home_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,CheckCircle,Circle,Download,Eye,FileText,GraduationCap,Home,Save,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_CheckCircle_Circle_Download_Eye_FileText_GraduationCap_Home_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,CheckCircle,Circle,Download,Eye,FileText,GraduationCap,Home,Save,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_CheckCircle_Circle_Download_Eye_FileText_GraduationCap_Home_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,CheckCircle,Circle,Download,Eye,FileText,GraduationCap,Home,Save,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_CheckCircle_Circle_Download_Eye_FileText_GraduationCap_Home_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,CheckCircle,Circle,Download,Eye,FileText,GraduationCap,Home,Save,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_CheckCircle_Circle_Download_Eye_FileText_GraduationCap_Home_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,CheckCircle,Circle,Download,Eye,FileText,GraduationCap,Home,Save,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_CheckCircle_Circle_Download_Eye_FileText_GraduationCap_Home_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,CheckCircle,Circle,Download,Eye,FileText,GraduationCap,Home,Save,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst SimplifiedNavigation = (param)=>{\n    let { currentStep, totalSteps, onPrevious, onNext, onSave, onDownload, onHome, canProceed = true, isSaving = false, completionPercentage = 0, stepTitles = [] } = param;\n    const isFirstStep = currentStep === 0;\n    const isLastStep = currentStep === totalSteps - 1;\n    const getStepIcon = (stepIndex)=>{\n        const icons = [\n            _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_CheckCircle_Circle_Download_Eye_FileText_GraduationCap_Home_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n            _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_CheckCircle_Circle_Download_Eye_FileText_GraduationCap_Home_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n            _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_CheckCircle_Circle_Download_Eye_FileText_GraduationCap_Home_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_CheckCircle_Circle_Download_Eye_FileText_GraduationCap_Home_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_CheckCircle_Circle_Download_Eye_FileText_GraduationCap_Home_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n        ];\n        const Icon = icons[stepIndex] || _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_CheckCircle_Circle_Download_Eye_FileText_GraduationCap_Home_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\n        return Icon;\n    };\n    const getStepStatus = (stepIndex)=>{\n        if (stepIndex < currentStep) return 'completed';\n        if (stepIndex === currentStep) return 'current';\n        return 'upcoming';\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gray-900/95 backdrop-blur-sm border-t border-gray-700/50 shadow-2xl\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"hidden md:flex items-center justify-center py-4 border-b border-gray-700/30\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-8\",\n                        children: stepTitles.map((title, index)=>{\n                            const Icon = getStepIcon(index);\n                            const status = getStepStatus(index);\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"\\n                      w-10 h-10 rounded-full border-2 flex items-center justify-center transition-all duration-200\\n                      \".concat(status === 'completed' ? 'bg-neural-pink border-neural-pink text-white' : status === 'current' ? 'bg-neural-purple border-neural-purple text-white' : 'bg-gray-800 border-gray-600 text-gray-400', \"\\n                    \"),\n                                                children: status === 'completed' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_CheckCircle_Circle_Download_Eye_FileText_GraduationCap_Home_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimplifiedNavigation.jsx\",\n                                                    lineNumber: 60,\n                                                    columnNumber: 25\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimplifiedNavigation.jsx\",\n                                                    lineNumber: 62,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimplifiedNavigation.jsx\",\n                                                lineNumber: 50,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"\\n                      mt-2 text-xs font-medium\\n                      \".concat(status === 'current' ? 'text-blue-600' : 'text-gray-500', \"\\n                    \"),\n                                                children: title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimplifiedNavigation.jsx\",\n                                                lineNumber: 65,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimplifiedNavigation.jsx\",\n                                        lineNumber: 49,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    index < stepTitles.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-16 h-0.5 mx-4 bg-gray-200 relative\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                            className: \"h-full bg-green-500\",\n                                            initial: {\n                                                width: 0\n                                            },\n                                            animate: {\n                                                width: index < currentStep ? '100%' : '0%'\n                                            },\n                                            transition: {\n                                                duration: 0.3\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimplifiedNavigation.jsx\",\n                                            lineNumber: 76,\n                                            columnNumber: 23\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimplifiedNavigation.jsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 21\n                                    }, undefined)\n                                ]\n                            }, index, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimplifiedNavigation.jsx\",\n                                lineNumber: 48,\n                                columnNumber: 17\n                            }, undefined);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimplifiedNavigation.jsx\",\n                        lineNumber: 42,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimplifiedNavigation.jsx\",\n                    lineNumber: 41,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"md:hidden py-3 border-b border-gray-100\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-medium text-gray-700\",\n                                    children: [\n                                        \"Step \",\n                                        currentStep + 1,\n                                        \" of \",\n                                        totalSteps\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimplifiedNavigation.jsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-gray-500\",\n                                    children: [\n                                        Math.round(completionPercentage),\n                                        \"% Complete\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimplifiedNavigation.jsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimplifiedNavigation.jsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full bg-gray-200 rounded-full h-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                className: \"bg-blue-500 h-2 rounded-full\",\n                                initial: {\n                                    width: 0\n                                },\n                                animate: {\n                                    width: \"\".concat(completionPercentage, \"%\")\n                                },\n                                transition: {\n                                    duration: 0.5\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimplifiedNavigation.jsx\",\n                                lineNumber: 103,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimplifiedNavigation.jsx\",\n                            lineNumber: 102,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimplifiedNavigation.jsx\",\n                    lineNumber: 93,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between py-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onHome,\n                                    className: \"p-2 text-gray-400 hover:text-gray-600 transition-colors\",\n                                    title: \"Back to Home\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_CheckCircle_Circle_Download_Eye_FileText_GraduationCap_Home_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimplifiedNavigation.jsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimplifiedNavigation.jsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onPrevious,\n                                    disabled: isFirstStep,\n                                    className: \"\\n                flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-all duration-200\\n                \".concat(isFirstStep ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-gray-100 text-gray-700 hover:bg-gray-200', \"\\n              \"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_CheckCircle_Circle_Download_Eye_FileText_GraduationCap_Home_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimplifiedNavigation.jsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Previous\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimplifiedNavigation.jsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimplifiedNavigation.jsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimplifiedNavigation.jsx\",\n                            lineNumber: 115,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden sm:flex flex-col items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-900\",\n                                    children: stepTitles[currentStep] || \"Step \".concat(currentStep + 1)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimplifiedNavigation.jsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-500\",\n                                    children: isLastStep ? 'Review and download your resume' : 'Fill in your information'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimplifiedNavigation.jsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimplifiedNavigation.jsx\",\n                            lineNumber: 141,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onSave,\n                                    disabled: isSaving,\n                                    className: \"flex items-center space-x-2 px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg font-medium transition-colors disabled:opacity-50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_CheckCircle_Circle_Download_Eye_FileText_GraduationCap_Home_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimplifiedNavigation.jsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"hidden sm:inline\",\n                                            children: isSaving ? 'Saving...' : 'Save'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimplifiedNavigation.jsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimplifiedNavigation.jsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 13\n                                }, undefined),\n                                isLastStep && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onDownload,\n                                    className: \"flex items-center space-x-2 px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg font-medium transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_CheckCircle_Circle_Download_Eye_FileText_GraduationCap_Home_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimplifiedNavigation.jsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"hidden sm:inline\",\n                                            children: \"Download\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimplifiedNavigation.jsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimplifiedNavigation.jsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onNext,\n                                    disabled: !canProceed,\n                                    className: \"\\n                flex items-center space-x-2 px-6 py-2 rounded-lg font-medium transition-all duration-200\\n                \".concat(canProceed ? 'bg-blue-600 hover:bg-blue-700 text-white shadow-sm' : 'bg-gray-200 text-gray-400 cursor-not-allowed', \"\\n              \"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: isLastStep ? 'Complete' : 'Next'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimplifiedNavigation.jsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_CheckCircle_Circle_Download_Eye_FileText_GraduationCap_Home_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimplifiedNavigation.jsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimplifiedNavigation.jsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimplifiedNavigation.jsx\",\n                            lineNumber: 151,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimplifiedNavigation.jsx\",\n                    lineNumber: 113,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimplifiedNavigation.jsx\",\n            lineNumber: 39,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimplifiedNavigation.jsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, undefined);\n};\n_c = SimplifiedNavigation;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SimplifiedNavigation);\nvar _c;\n$RefreshReg$(_c, \"SimplifiedNavigation\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/resume/SimplifiedNavigation.jsx\n"));

/***/ })

});
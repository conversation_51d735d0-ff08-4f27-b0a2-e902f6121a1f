'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';

export default function LoginPage() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const { login } = useAuth();
  const router = useRouter();  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setLoading(true);
    try {
      await login(email, password);
      
      // Check for pending actions and redirects
      if (typeof window !== 'undefined') {
        const redirectPath = localStorage.getItem('redirectAfterAuth');
        const pendingAction = localStorage.getItem('pendingAction');
        
        // Clear the stored items
        localStorage.removeItem('redirectAfterAuth');
        localStorage.removeItem('pendingAction');
        
        // Redirect based on stored preference or default to dashboard
        if (redirectPath) {
          router.push(redirectPath);
        } else {
          router.push('/dashboard');
        }
      } else {
        router.push('/dashboard');
      }
      
    } catch (err) {
      setError(err.message || 'Failed to log in. Please check your credentials.');
      console.error(err);
    }
    setLoading(false);
  };
  return (
    <div className="relative overflow-hidden bg-gradient-to-b from-black to-[#0A0A0A] min-h-screen flex flex-col items-center justify-center p-4 pt-20">
      {/* Animated Grid Background */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute inset-0 bg-[url('/grid.svg')] [mask-image:radial-gradient(ellipse_at_center,white,transparent)]" />
      </div>
      
      {/* Floating Neural Network Nodes */}
      <div className="absolute inset-0 overflow-hidden">
        {[...Array(12)].map((_, i) => (
          <div
            key={i}
            className="absolute rounded-full bg-neural-purple opacity-10 blur-xl animate-pulse"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              width: `${Math.random() * 300 + 100}px`,
              height: `${Math.random() * 300 + 100}px`,
              animationDelay: `${Math.random() * 3}s`,
              animationDuration: `${Math.random() * 3 + 3}s`,
            }}
          />
        ))}
        
        {/* Pink accent nodes */}
        {[...Array(8)].map((_, i) => (
          <div
            key={`pink-${i}`}
            className="absolute rounded-full bg-neural-pink opacity-5 blur-2xl animate-neural-float"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              width: `${Math.random() * 200 + 80}px`,
              height: `${Math.random() * 200 + 80}px`,
              animationDelay: `${Math.random() * 4}s`,
              animationDuration: `${Math.random() * 4 + 4}s`,
            }}
          />
        ))}
      </div>

      {/* Neural Network Connecting Lines */}
      <div className="absolute inset-0 pointer-events-none">
        <svg className="w-full h-full" xmlns="http://www.w3.org/2000/svg">
          <defs>
            <linearGradient id="neural-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor="#8B5CF6" />
              <stop offset="50%" stopColor="#EC4899" />
              <stop offset="100%" stopColor="#06B6D4" />
            </linearGradient>
          </defs>
          {[...Array(6)].map((_, i) => (
            <line
              key={i}
              x1={`${Math.random() * 100}%`}
              y1={`${Math.random() * 100}%`}
              x2={`${Math.random() * 100}%`}
              y2={`${Math.random() * 100}%`}
              stroke="url(#neural-gradient)"
              strokeWidth="1"
              opacity="0.1"
              className="animate-neural-pulse"
              style={{
                animationDelay: `${Math.random() * 2}s`,
                animationDuration: `${Math.random() * 3 + 2}s`,
              }}
            />
          ))}
        </svg>
      </div>

      <div className="relative w-full max-w-sm z-[60]">
        {/* Glowing Card Background */}
        <div className="absolute inset-0 rounded-lg bg-gradient-to-tr from-neural-purple to-neural-blue opacity-30 blur-lg" />
        <div className="relative bg-gray-900/25 backdrop-blur-md p-5 rounded-lg shadow-2xl border border-white/10 transition-all duration-500 hover:border-neural-purple/50 hover:shadow-neural-purple/25 hover:shadow-2xl">          <div className="text-center mb-5">
            <Link href="/" className="inline-block mb-2">
              <h1 className="text-2xl font-bold bg-gradient-to-r from-neural-purple to-neural-pink bg-clip-text text-transparent animate-pulse">
                BlinkFind
              </h1>
            </Link>
            <p className="text-gray-300 text-sm">Welcome back! Please sign in to your account.</p>
          </div>

          {error && (
            <div className="mb-3 p-3 bg-red-500/20 border border-red-500/30 rounded-lg text-red-300 text-sm text-center backdrop-blur-sm animate-shake">
              {error}
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="group">
              <label htmlFor="email" className="block text-sm font-medium text-gray-300 mb-1 group-focus-within:text-neural-purple transition-colors">
                Email Address
              </label>
              <input
                type="email"
                id="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                className="w-full px-3 py-2 bg-gray-900/50 border border-white/10 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-neural-purple/50 focus:border-neural-purple text-white placeholder-gray-400 transition-all duration-300 ease-in-out backdrop-blur-sm text-sm hover:border-neural-purple/30 hover:shadow-lg hover:shadow-neural-purple/10"
                placeholder="<EMAIL>"
              />
            </div>

            <div className="group">
              <label htmlFor="password" className="block text-sm font-medium text-gray-300 mb-1 group-focus-within:text-neural-purple transition-colors">
                Password
              </label>
              <input
                type="password"
                id="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
                className="w-full px-3 py-2 bg-gray-900/50 border border-white/10 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-neural-purple/50 focus:border-neural-purple text-white placeholder-gray-400 transition-all duration-300 ease-in-out backdrop-blur-sm text-sm hover:border-neural-purple/30 hover:shadow-lg hover:shadow-neural-purple/10"
                placeholder="••••••••"
              />
            </div>

            <button
              type="submit"
              disabled={loading}
              className="w-full bg-gradient-to-r from-neural-purple to-neural-pink text-white font-semibold py-2 px-4 rounded-lg hover:opacity-90 focus:outline-none focus:ring-2 focus:ring-neural-purple/50 focus:ring-offset-2 focus:ring-offset-gray-900 disabled:opacity-50 transition-all duration-300 ease-in-out transform hover:scale-105 active:scale-95 shadow-lg hover:shadow-neural-purple/50 text-sm"
            >
              {loading ? (
                <div className="flex items-center justify-center">
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Logging in...
                </div>              ) : (
                'Login'
              )}
            </button>
          </form>

          <p className="mt-5 text-center text-sm text-gray-400">
            Don't have an account?{' '}
            <Link href="/signup" className="font-semibold text-neural-purple hover:text-neural-pink hover:underline transition-colors">
              Sign up here
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
}

import { NextResponse } from 'next/server';
import Stripe from 'stripe';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
  apiVersion: '2023-10-16',
});

export async function POST(request) {
  try {
    const { planId, amount, currency, billingInterval, customerId } = await request.json();

    // Validate required fields
    if (!planId || !amount || !currency) {
      return NextResponse.json(
        { error: 'Missing required fields: planId, amount, currency' },
        { status: 400 }
      );
    }

    // Create or retrieve customer
    let customer;
    if (customerId) {
      try {
        customer = await stripe.customers.retrieve(customerId);
      } catch (error) {
        // Customer doesn't exist, create new one
        customer = await stripe.customers.create({
          metadata: {
            planId,
            billingInterval: billingInterval || 'monthly'
          }
        });
      }
    } else {
      customer = await stripe.customers.create({
        metadata: {
          planId,
          billingInterval: billingInterval || 'monthly'
        }
      });
    }

    // Create payment intent
    const paymentIntent = await stripe.paymentIntents.create({
      amount: Math.round(amount * 100), // Convert to cents
      currency: currency.toLowerCase(),
      customer: customer.id,
      metadata: {
        planId,
        billingInterval: billingInterval || 'monthly',
        type: 'subscription'
      },
      automatic_payment_methods: {
        enabled: true,
      },
    });

    return NextResponse.json({
      success: true,
      clientSecret: paymentIntent.client_secret,
      customerId: customer.id,
      paymentIntentId: paymentIntent.id
    });

  } catch (error) {
    console.error('Stripe payment error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to create payment intent',
        details: error.message 
      },
      { status: 500 }
    );
  }
}

// Handle payment confirmation
export async function PUT(request) {
  try {
    const { paymentIntentId, planId, userId } = await request.json();

    if (!paymentIntentId) {
      return NextResponse.json(
        { error: 'Payment intent ID is required' },
        { status: 400 }
      );
    }

    // Retrieve payment intent to confirm payment
    const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);

    if (paymentIntent.status !== 'succeeded') {
      return NextResponse.json(
        { error: 'Payment not completed' },
        { status: 400 }
      );
    }

    // Here you would typically:
    // 1. Update user's subscription in your database
    // 2. Send confirmation email
    // 3. Update user permissions
    // 4. Create invoice record

    // For now, we'll simulate this
    const subscriptionData = {
      userId,
      planId,
      status: 'active',
      paymentIntentId,
      amount: paymentIntent.amount / 100,
      currency: paymentIntent.currency,
      startDate: new Date().toISOString(),
      nextBillingDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() // 30 days from now
    };

    return NextResponse.json({
      success: true,
      subscription: subscriptionData,
      message: 'Payment confirmed and subscription activated'
    });

  } catch (error) {
    console.error('Payment confirmation error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to confirm payment',
        details: error.message 
      },
      { status: 500 }
    );
  }
}

// Handle subscription creation for recurring payments
export async function PATCH(request) {
  try {
    const { customerId, priceId, planId } = await request.json();

    if (!customerId || !priceId) {
      return NextResponse.json(
        { error: 'Customer ID and Price ID are required' },
        { status: 400 }
      );
    }

    // Create subscription
    const subscription = await stripe.subscriptions.create({
      customer: customerId,
      items: [{ price: priceId }],
      payment_behavior: 'default_incomplete',
      payment_settings: { save_default_payment_method: 'on_subscription' },
      expand: ['latest_invoice.payment_intent'],
      metadata: {
        planId
      }
    });

    return NextResponse.json({
      success: true,
      subscriptionId: subscription.id,
      clientSecret: subscription.latest_invoice.payment_intent.client_secret,
      status: subscription.status
    });

  } catch (error) {
    console.error('Subscription creation error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to create subscription',
        details: error.message 
      },
      { status: 500 }
    );
  }
}

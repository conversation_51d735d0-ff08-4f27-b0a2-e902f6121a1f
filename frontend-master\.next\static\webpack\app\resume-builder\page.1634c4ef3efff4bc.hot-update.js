"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/resume-builder/page",{

/***/ "(app-pages-browser)/./src/components/resume/FullscreenPreviewModal.jsx":
/*!**********************************************************!*\
  !*** ./src/components/resume/FullscreenPreviewModal.jsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Maximize2_Minimize2_Palette_Printer_RotateCcw_Settings_Share2_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Maximize2,Minimize2,Palette,Printer,RotateCcw,Settings,Share2,X,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Maximize2_Minimize2_Palette_Printer_RotateCcw_Settings_Share2_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Maximize2,Minimize2,Palette,Printer,RotateCcw,Settings,Share2,X,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Maximize2_Minimize2_Palette_Printer_RotateCcw_Settings_Share2_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Maximize2,Minimize2,Palette,Printer,RotateCcw,Settings,Share2,X,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zoom-out.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Maximize2_Minimize2_Palette_Printer_RotateCcw_Settings_Share2_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Maximize2,Minimize2,Palette,Printer,RotateCcw,Settings,Share2,X,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zoom-in.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Maximize2_Minimize2_Palette_Printer_RotateCcw_Settings_Share2_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Maximize2,Minimize2,Palette,Printer,RotateCcw,Settings,Share2,X,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Maximize2_Minimize2_Palette_Printer_RotateCcw_Settings_Share2_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Maximize2,Minimize2,Palette,Printer,RotateCcw,Settings,Share2,X,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/palette.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Maximize2_Minimize2_Palette_Printer_RotateCcw_Settings_Share2_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Maximize2,Minimize2,Palette,Printer,RotateCcw,Settings,Share2,X,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/printer.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Maximize2_Minimize2_Palette_Printer_RotateCcw_Settings_Share2_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Maximize2,Minimize2,Palette,Printer,RotateCcw,Settings,Share2,X,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Maximize2_Minimize2_Palette_Printer_RotateCcw_Settings_Share2_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Maximize2,Minimize2,Palette,Printer,RotateCcw,Settings,Share2,X,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _utils_enhancedPdfGenerator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/enhancedPdfGenerator */ \"(app-pages-browser)/./src/utils/enhancedPdfGenerator.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst FullscreenPreviewModal = (param)=>{\n    let { isOpen, onClose, formData, selectedTemplate = 'classic_ats', onTemplateChange } = param;\n    var _formData_personal, _formData_personal1;\n    _s();\n    const [zoom, setZoom] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0.85);\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showTemplateSelector, setShowTemplateSelector] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const previewRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const modalRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Handle escape key\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FullscreenPreviewModal.useEffect\": ()=>{\n            const handleEscape = {\n                \"FullscreenPreviewModal.useEffect.handleEscape\": (e)=>{\n                    if (e.key === 'Escape' && isOpen) {\n                        onClose();\n                    }\n                }\n            }[\"FullscreenPreviewModal.useEffect.handleEscape\"];\n            if (isOpen) {\n                document.addEventListener('keydown', handleEscape);\n                document.body.style.overflow = 'hidden'; // Prevent background scroll\n            }\n            return ({\n                \"FullscreenPreviewModal.useEffect\": ()=>{\n                    document.removeEventListener('keydown', handleEscape);\n                    document.body.style.overflow = 'unset';\n                }\n            })[\"FullscreenPreviewModal.useEffect\"];\n        }\n    }[\"FullscreenPreviewModal.useEffect\"], [\n        isOpen,\n        onClose\n    ]);\n    // Handle backdrop click\n    const handleBackdropClick = (e)=>{\n        if (e.target === modalRef.current) {\n            onClose();\n        }\n    };\n    // Zoom controls\n    const handleZoomIn = ()=>setZoom((prev)=>Math.min(prev + 0.1, 1.5));\n    const handleZoomOut = ()=>setZoom((prev)=>Math.max(prev - 0.1, 0.4));\n    const handleResetZoom = ()=>setZoom(0.85);\n    // Download PDF\n    const handleDownloadPDF = async ()=>{\n        if (!formData || !previewRef.current) return;\n        setIsGenerating(true);\n        try {\n            var _formData_personal, _formData_personal1;\n            const filename = \"\".concat(((_formData_personal = formData.personal) === null || _formData_personal === void 0 ? void 0 : _formData_personal.firstName) || 'Resume', \"_\").concat(((_formData_personal1 = formData.personal) === null || _formData_personal1 === void 0 ? void 0 : _formData_personal1.lastName) || 'Document', \".pdf\");\n            await (0,_utils_enhancedPdfGenerator__WEBPACK_IMPORTED_MODULE_2__.convertHTMLToPDF)(previewRef.current, filename);\n        } catch (error) {\n            console.error('Error generating PDF:', error);\n            alert('Error generating PDF. Please try again.');\n        } finally{\n            setIsGenerating(false);\n        }\n    };\n    // Print functionality\n    const handlePrint = ()=>{\n        var _formData_personal, _formData_personal1;\n        if (!previewRef.current) return;\n        const printWindow = window.open('', '_blank');\n        printWindow.document.write(\"\\n      <html>\\n        <head>\\n          <title>Resume - \".concat(((_formData_personal = formData.personal) === null || _formData_personal === void 0 ? void 0 : _formData_personal.firstName) || '', \" \").concat(((_formData_personal1 = formData.personal) === null || _formData_personal1 === void 0 ? void 0 : _formData_personal1.lastName) || '', \"</title>\\n          <style>\\n            body { \\n              margin: 0; \\n              padding: 20px; \\n              font-family: 'Inter', 'Helvetica Neue', Arial, sans-serif;\\n              font-size: 11pt;\\n              line-height: 1.4;\\n              color: #000000;\\n            }\\n            @media print { \\n              body { padding: 0; }\\n              .no-print { display: none; }\\n            }\\n            .resume-content { \\n              max-width: 8.5in; \\n              margin: 0 auto; \\n              background: white;\\n            }\\n          </style>\\n        </head>\\n        <body>\\n          <div class=\\\"resume-content\\\">\\n            \").concat(previewRef.current.innerHTML, \"\\n          </div>\\n        </body>\\n      </html>\\n    \"));\n        printWindow.document.close();\n        printWindow.print();\n    };\n    // Render resume content (same as SimpleResumePreview but optimized for fullscreen)\n    const renderResumeContent = ()=>{\n        if (!formData || !formData.personal || !formData.personal.firstName) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-full text-gray-400\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Maximize2_Minimize2_Palette_Printer_RotateCcw_Settings_Share2_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            className: \"h-16 w-16 mx-auto mb-4 opacity-50\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                            lineNumber: 117,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-medium mb-2\",\n                            children: \"No Resume Data\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                            lineNumber: 118,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm\",\n                            children: \"Fill out the form to see your resume preview\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                            lineNumber: 119,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                    lineNumber: 116,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                lineNumber: 115,\n                columnNumber: 9\n            }, undefined);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-8 space-y-6 max-w-4xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center border-b-2 border-gray-300 pb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-gray-900 mb-3\",\n                            children: [\n                                formData.personal.firstName,\n                                \" \",\n                                formData.personal.lastName\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                            lineNumber: 129,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-base text-gray-600 space-y-1\",\n                            children: [\n                                formData.personal.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: formData.personal.email\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 41\n                                }, undefined),\n                                formData.personal.phone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: formData.personal.phone\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 41\n                                }, undefined),\n                                formData.personal.location && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: formData.personal.location\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 44\n                                }, undefined),\n                                formData.personal.linkedin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: formData.personal.linkedin\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 44\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                            lineNumber: 132,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                    lineNumber: 128,\n                    columnNumber: 9\n                }, undefined),\n                formData.personal.summary && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-lg font-bold text-gray-900 mb-3 uppercase border-b border-gray-300 pb-1\",\n                            children: \"Professional Summary\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                            lineNumber: 143,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-800 leading-relaxed text-justify\",\n                            children: formData.personal.summary\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                            lineNumber: 146,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                    lineNumber: 142,\n                    columnNumber: 11\n                }, undefined),\n                formData.experience && formData.experience.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-lg font-bold text-gray-900 mb-3 uppercase border-b border-gray-300 pb-1\",\n                            children: \"Professional Experience\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                            lineNumber: 155,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: formData.experience.map((exp, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-start mb-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-base font-semibold text-gray-900\",\n                                                    children: exp.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                                                    lineNumber: 162,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-600 text-sm font-medium\",\n                                                    children: [\n                                                        exp.startDate,\n                                                        \" - \",\n                                                        exp.current ? 'Present' : exp.endDate\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                                                    lineNumber: 163,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-700 font-medium mb-2\",\n                                            children: [\n                                                exp.company,\n                                                \" \",\n                                                exp.location && \"• \".concat(exp.location)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        exp.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-600 text-sm leading-relaxed\",\n                                            children: exp.description.split('\\n').map((line, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-1\",\n                                                    children: line.trim().startsWith('•') ? line : \"• \".concat(line.trim())\n                                                }, i, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                                                    lineNumber: 173,\n                                                    columnNumber: 25\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 17\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                            lineNumber: 158,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                    lineNumber: 154,\n                    columnNumber: 11\n                }, undefined),\n                formData.education && formData.education.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-lg font-bold text-gray-900 mb-3 uppercase border-b border-gray-300 pb-1\",\n                            children: \"Education\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                            lineNumber: 188,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: formData.education.map((edu, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-start\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-base font-semibold text-gray-900\",\n                                                        children: [\n                                                            edu.degree,\n                                                            \" \",\n                                                            edu.field && \"in \".concat(edu.field)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                                                        lineNumber: 196,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-gray-700 font-medium\",\n                                                        children: edu.institution\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                                                        lineNumber: 199,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    edu.location && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-gray-600\",\n                                                        children: edu.location\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                                                        lineNumber: 200,\n                                                        columnNumber: 40\n                                                    }, undefined),\n                                                    edu.gpa && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-gray-600\",\n                                                        children: [\n                                                            \"GPA: \",\n                                                            edu.gpa\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                                                        lineNumber: 201,\n                                                        columnNumber: 35\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-600 text-sm font-medium\",\n                                                children: edu.graduationDate\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, index, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 17\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                            lineNumber: 191,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                    lineNumber: 187,\n                    columnNumber: 11\n                }, undefined),\n                formData.skills && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-lg font-bold text-gray-900 mb-3 uppercase border-b border-gray-300 pb-1\",\n                            children: \"Skills\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                            lineNumber: 214,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm space-y-2\",\n                            children: [\n                                formData.skills.technical && formData.skills.technical.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-semibold text-gray-900\",\n                                            children: \"Technical: \"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                                            lineNumber: 220,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-700\",\n                                            children: formData.skills.technical.join(', ')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                                            lineNumber: 221,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 17\n                                }, undefined),\n                                formData.skills.languages && formData.skills.languages.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-semibold text-gray-900\",\n                                            children: \"Languages: \"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                                            lineNumber: 228,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-700\",\n                                            children: formData.skills.languages.join(', ')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 17\n                                }, undefined),\n                                formData.skills.certifications && formData.skills.certifications.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-semibold text-gray-900\",\n                                            children: \"Certifications: \"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-700\",\n                                            children: formData.skills.certifications.join(', ')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                            lineNumber: 217,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                    lineNumber: 213,\n                    columnNumber: 11\n                }, undefined),\n                formData.projects && formData.projects.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-lg font-bold text-gray-900 mb-3 uppercase border-b border-gray-300 pb-1\",\n                            children: \"Projects\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                            lineNumber: 249,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: formData.projects.map((project, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-base font-semibold text-gray-900 mb-1\",\n                                            children: [\n                                                project.name,\n                                                project.technologies && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-normal text-gray-600 ml-2\",\n                                                    children: [\n                                                        \"(\",\n                                                        project.technologies,\n                                                        \")\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                                                    lineNumber: 258,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                                            lineNumber: 255,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        project.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-700 leading-relaxed mb-2 text-justify\",\n                                            children: project.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                                            lineNumber: 264,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        (project.link || project.github) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-600 text-sm\",\n                                            children: [\n                                                project.link && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        \"Demo: \",\n                                                        project.link\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                                                    lineNumber: 270,\n                                                    columnNumber: 40\n                                                }, undefined),\n                                                project.link && project.github && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \" • \"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                                                    lineNumber: 271,\n                                                    columnNumber: 58\n                                                }, undefined),\n                                                project.github && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        \"Code: \",\n                                                        project.github\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                                                    lineNumber: 272,\n                                                    columnNumber: 42\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                                            lineNumber: 269,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                                    lineNumber: 254,\n                                    columnNumber: 17\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                            lineNumber: 252,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                    lineNumber: 248,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n            lineNumber: 126,\n            columnNumber: 7\n        }, undefined);\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.AnimatePresence, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n            ref: modalRef,\n            initial: {\n                opacity: 0\n            },\n            animate: {\n                opacity: 1\n            },\n            exit: {\n                opacity: 0\n            },\n            className: \"fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center\",\n            onClick: handleBackdropClick,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        scale: 0.95,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        scale: 1,\n                        y: 0\n                    },\n                    exit: {\n                        opacity: 0,\n                        scale: 0.95,\n                        y: 20\n                    },\n                    className: \"bg-gray-900 rounded-2xl border border-gray-700 w-full h-full max-w-7xl max-h-[95vh] m-4 flex flex-col overflow-hidden\",\n                    onClick: (e)=>e.stopPropagation(),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-4 border-b border-gray-700 bg-gray-800/50 flex-shrink-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 bg-neural-purple/20 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Maximize2_Minimize2_Palette_Printer_RotateCcw_Settings_Share2_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"h-4 w-4 text-neural-purple\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                                                lineNumber: 308,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                                            lineNumber: 307,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-lg font-semibold text-white\",\n                                                    children: \"Resume Preview\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                                                    lineNumber: 311,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-400 text-sm\",\n                                                    children: [\n                                                        formData === null || formData === void 0 ? void 0 : (_formData_personal = formData.personal) === null || _formData_personal === void 0 ? void 0 : _formData_personal.firstName,\n                                                        \" \",\n                                                        formData === null || formData === void 0 ? void 0 : (_formData_personal1 = formData.personal) === null || _formData_personal1 === void 0 ? void 0 : _formData_personal1.lastName,\n                                                        \" • \",\n                                                        (selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.replace('_', ' ')) || 'Classic ATS'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                                                    lineNumber: 312,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                                            lineNumber: 310,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                                    lineNumber: 306,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-1 bg-gray-700/50 rounded-lg p-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: handleZoomOut,\n                                                    className: \"p-1 text-gray-400 hover:text-white transition-colors\",\n                                                    title: \"Zoom Out\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Maximize2_Minimize2_Palette_Printer_RotateCcw_Settings_Share2_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                                                        lineNumber: 326,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                                                    lineNumber: 321,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"px-2 text-sm text-gray-300 min-w-[3rem] text-center\",\n                                                    children: [\n                                                        Math.round(zoom * 100),\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                                                    lineNumber: 328,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: handleZoomIn,\n                                                    className: \"p-1 text-gray-400 hover:text-white transition-colors\",\n                                                    title: \"Zoom In\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Maximize2_Minimize2_Palette_Printer_RotateCcw_Settings_Share2_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                                                        lineNumber: 336,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                                                    lineNumber: 331,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: handleResetZoom,\n                                                    className: \"p-1 text-gray-400 hover:text-white transition-colors\",\n                                                    title: \"Reset Zoom\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Maximize2_Minimize2_Palette_Printer_RotateCcw_Settings_Share2_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                                                        lineNumber: 343,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                                                    lineNumber: 338,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowTemplateSelector(!showTemplateSelector),\n                                            className: \"flex items-center gap-2 px-3 py-2 bg-neural-purple/20 hover:bg-neural-purple/30 text-neural-purple border border-neural-purple/30 rounded-lg transition-colors\",\n                                            title: \"Change Template\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Maximize2_Minimize2_Palette_Printer_RotateCcw_Settings_Share2_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                                                    lineNumber: 353,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"hidden sm:inline\",\n                                                    children: \"Template\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                                                    lineNumber: 354,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                                            lineNumber: 348,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handlePrint,\n                                            className: \"p-2 text-gray-400 hover:text-white transition-colors\",\n                                            title: \"Print\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Maximize2_Minimize2_Palette_Printer_RotateCcw_Settings_Share2_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                                                lineNumber: 362,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                                            lineNumber: 357,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleDownloadPDF,\n                                            disabled: isGenerating,\n                                            className: \"flex items-center gap-2 px-3 py-2 bg-green-600 hover:bg-green-700 disabled:opacity-50 text-white rounded-lg transition-colors\",\n                                            title: \"Download PDF\",\n                                            children: [\n                                                isGenerating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                                    animate: {\n                                                        rotate: 360\n                                                    },\n                                                    transition: {\n                                                        duration: 1,\n                                                        repeat: Infinity,\n                                                        ease: \"linear\"\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Maximize2_Minimize2_Palette_Printer_RotateCcw_Settings_Share2_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                                                        lineNumber: 376,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                                                    lineNumber: 372,\n                                                    columnNumber: 19\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Maximize2_Minimize2_Palette_Printer_RotateCcw_Settings_Share2_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                                                    lineNumber: 379,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"hidden sm:inline\",\n                                                    children: isGenerating ? 'Generating...' : 'Download'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                                                    lineNumber: 381,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                                            lineNumber: 365,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: onClose,\n                                            className: \"p-2 text-gray-400 hover:text-white transition-colors\",\n                                            title: \"Close\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Maximize2_Minimize2_Palette_Printer_RotateCcw_Settings_Share2_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                                                lineNumber: 391,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                                            lineNumber: 386,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                                    lineNumber: 318,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                            lineNumber: 305,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 overflow-auto bg-gray-100 p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                    className: \"bg-white shadow-2xl rounded-lg\",\n                                    style: {\n                                        transform: \"scale(\".concat(zoom, \")\"),\n                                        transformOrigin: 'top center',\n                                        minWidth: '8.5in',\n                                        minHeight: '11in'\n                                    },\n                                    animate: {\n                                        scale: zoom\n                                    },\n                                    transition: {\n                                        duration: 0.2\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        ref: previewRef,\n                                        className: \"w-full h-full\",\n                                        style: {\n                                            fontFamily: \"'Inter', 'Helvetica Neue', Arial, sans-serif\",\n                                            fontSize: '11pt',\n                                            lineHeight: '1.4',\n                                            color: '#000000',\n                                            backgroundColor: '#ffffff',\n                                            minHeight: '11in',\n                                            width: '8.5in'\n                                        },\n                                        children: renderResumeContent()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                                        lineNumber: 410,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                                    lineNumber: 399,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                                lineNumber: 398,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                            lineNumber: 397,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-3 border-t border-gray-700 bg-gray-800/50 flex items-center justify-between text-sm flex-shrink-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-4 text-gray-400\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"Template: \",\n                                                (selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.replace('_', ' ')) || 'Classic ATS'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                                            lineNumber: 432,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"•\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                                            lineNumber: 433,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: 'A4 Format (8.5\" \\xd7 11\")'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                                            lineNumber: 434,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"•\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                                            lineNumber: 435,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"ATS Optimized\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                                            lineNumber: 436,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                                    lineNumber: 431,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-400\",\n                                    children: [\n                                        \"Press \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"kbd\", {\n                                            className: \"px-1 py-0.5 bg-gray-700 rounded text-xs\",\n                                            children: \"Esc\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                                            lineNumber: 440,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        \" to close\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                                    lineNumber: 439,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                            lineNumber: 430,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                    lineNumber: 297,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.AnimatePresence, {\n                    children: isGenerating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        initial: {\n                            opacity: 0\n                        },\n                        animate: {\n                            opacity: 1\n                        },\n                        exit: {\n                            opacity: 0\n                        },\n                        className: \"absolute inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-900 rounded-xl p-6 text-center border border-gray-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                    animate: {\n                                        rotate: 360\n                                    },\n                                    transition: {\n                                        duration: 1,\n                                        repeat: Infinity,\n                                        ease: \"linear\"\n                                    },\n                                    className: \"w-8 h-8 border-4 border-neural-purple border-t-transparent rounded-full mx-auto mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                                    lineNumber: 455,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white font-medium\",\n                                    children: \"Generating PDF...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                                    lineNumber: 460,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400 text-sm mt-1\",\n                                    children: \"This may take a few seconds\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                                    lineNumber: 461,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                            lineNumber: 454,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                        lineNumber: 448,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n                    lineNumber: 446,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n            lineNumber: 288,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\FullscreenPreviewModal.jsx\",\n        lineNumber: 287,\n        columnNumber: 5\n    }, undefined);\n};\n_s(FullscreenPreviewModal, \"mRplBr6GpkEF6bCTHgj4EeCdAWw=\");\n_c = FullscreenPreviewModal;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FullscreenPreviewModal);\nvar _c;\n$RefreshReg$(_c, \"FullscreenPreviewModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/resume/FullscreenPreviewModal.jsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/resume/ResumeBuilder.jsx":
/*!*************************************************!*\
  !*** ./src/components/resume/ResumeBuilder.jsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Briefcase,CheckCircle,Clock,Download,Eye,FileText,GraduationCap,Lightbulb,RotateCcw,Save,Sparkles,Target,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Briefcase,CheckCircle,Clock,Download,Eye,FileText,GraduationCap,Lightbulb,RotateCcw,Save,Sparkles,Target,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/graduation-cap.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Briefcase,CheckCircle,Clock,Download,Eye,FileText,GraduationCap,Lightbulb,RotateCcw,Save,Sparkles,Target,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Briefcase,CheckCircle,Clock,Download,Eye,FileText,GraduationCap,Lightbulb,RotateCcw,Save,Sparkles,Target,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Briefcase,CheckCircle,Clock,Download,Eye,FileText,GraduationCap,Lightbulb,RotateCcw,Save,Sparkles,Target,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Briefcase,CheckCircle,Clock,Download,Eye,FileText,GraduationCap,Lightbulb,RotateCcw,Save,Sparkles,Target,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Briefcase,CheckCircle,Clock,Download,Eye,FileText,GraduationCap,Lightbulb,RotateCcw,Save,Sparkles,Target,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _StepNavigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./StepNavigation */ \"(app-pages-browser)/./src/components/resume/StepNavigation.jsx\");\n/* harmony import */ var _layout_SmartNavigationBar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../layout/SmartNavigationBar */ \"(app-pages-browser)/./src/components/layout/SmartNavigationBar.jsx\");\n/* harmony import */ var _forms_ResumeFormComponents__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./forms/ResumeFormComponents */ \"(app-pages-browser)/./src/components/resume/forms/ResumeFormComponents.jsx\");\n/* harmony import */ var _forms_ExperienceForm__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./forms/ExperienceForm */ \"(app-pages-browser)/./src/components/resume/forms/ExperienceForm.jsx\");\n/* harmony import */ var _forms_SkillsProjectsForm__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./forms/SkillsProjectsForm */ \"(app-pages-browser)/./src/components/resume/forms/SkillsProjectsForm.jsx\");\n/* harmony import */ var _forms_ProjectsForm__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./forms/ProjectsForm */ \"(app-pages-browser)/./src/components/resume/forms/ProjectsForm.jsx\");\n/* harmony import */ var _forms_SkillsForm__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./forms/SkillsForm */ \"(app-pages-browser)/./src/components/resume/forms/SkillsForm.jsx\");\n/* harmony import */ var _forms_ReviewForm__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./forms/ReviewForm */ \"(app-pages-browser)/./src/components/resume/forms/ReviewForm.jsx\");\n/* harmony import */ var _EnhancedProgressIndicator__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./EnhancedProgressIndicator */ \"(app-pages-browser)/./src/components/resume/EnhancedProgressIndicator.jsx\");\n/* harmony import */ var _EnhancedNavigation__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./EnhancedNavigation */ \"(app-pages-browser)/./src/components/resume/EnhancedNavigation.jsx\");\n/* harmony import */ var _common_ProgressBar__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../common/ProgressBar */ \"(app-pages-browser)/./src/components/common/ProgressBar.jsx\");\n/* harmony import */ var _ResumePreview__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./ResumePreview */ \"(app-pages-browser)/./src/components/resume/ResumePreview.jsx\");\n/* harmony import */ var _SimpleResumePreview__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./SimpleResumePreview */ \"(app-pages-browser)/./src/components/resume/SimpleResumePreview.jsx\");\n/* harmony import */ var _FullscreenPreviewModal__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./FullscreenPreviewModal */ \"(app-pages-browser)/./src/components/resume/FullscreenPreviewModal.jsx\");\n/* harmony import */ var _SuccessScreen__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./SuccessScreen */ \"(app-pages-browser)/./src/components/resume/SuccessScreen.jsx\");\n/* harmony import */ var _hooks_useATSAnalysis__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/hooks/useATSAnalysis */ \"(app-pages-browser)/./src/hooks/useATSAnalysis.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst EnhancedResumeBuilder = (param)=>{\n    let { hideHeader = false } = param;\n    _s();\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [selectedTemplate, setSelectedTemplate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('modern');\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        personal: {\n            firstName: \"\",\n            lastName: \"\",\n            email: \"\",\n            phone: \"\",\n            location: \"\",\n            linkedin: \"\",\n            portfolio: \"\",\n            summary: \"\",\n            profileImage: \"\"\n        },\n        education: [\n            {\n                id: 1,\n                degree: \"\",\n                institution: \"\",\n                location: \"\",\n                startDate: \"\",\n                endDate: \"\",\n                gpa: \"\",\n                relevant: \"\"\n            }\n        ],\n        experience: [\n            {\n                id: 1,\n                title: \"\",\n                company: \"\",\n                location: \"\",\n                startDate: \"\",\n                endDate: \"\",\n                current: false,\n                description: \"\"\n            }\n        ],\n        skills: {\n            technical: [],\n            languages: [],\n            certifications: []\n        },\n        projects: [\n            {\n                id: 1,\n                name: \"\",\n                description: \"\",\n                technologies: \"\",\n                link: \"\"\n            }\n        ],\n        jobDescription: \"\"\n    });\n    const [showPreview, setShowPreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showProgressBar, setShowProgressBar] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [resumeGenerated, setResumeGenerated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [validationErrors, setValidationErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [showValidationErrors, setShowValidationErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [completedSteps, setCompletedSteps] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [autoSaveEnabled, setAutoSaveEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [lastSaved, setLastSaved] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [resumeData, setResumeData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [resumeUrl, setResumeUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [atsScore, setAtsScore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [suggestions, setSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [aiSuggestionsEnabled, setAiSuggestionsEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [smartValidation, setSmartValidation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showHints, setShowHints] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const steps = [\n        {\n            id: 0,\n            title: \"Personal Information\",\n            icon: _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n            description: \"Tell us about yourself\",\n            estimatedTime: 3,\n            required: true\n        },\n        {\n            id: 1,\n            title: \"Education\",\n            icon: _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n            description: \"Your academic background\",\n            estimatedTime: 5,\n            required: true\n        },\n        {\n            id: 2,\n            title: \"Experience\",\n            icon: _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n            description: \"Your work experience\",\n            estimatedTime: 8,\n            required: true\n        },\n        {\n            id: 3,\n            title: \"Projects\",\n            icon: _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"],\n            description: \"Showcase your projects\",\n            estimatedTime: 4,\n            required: false\n        },\n        {\n            id: 4,\n            title: \"Skills\",\n            icon: _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"],\n            description: \"Your technical & soft skills\",\n            estimatedTime: 3,\n            required: false\n        },\n        {\n            id: 5,\n            title: \"Review & Generate\",\n            icon: _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"],\n            description: \"Finalize your resume\",\n            estimatedTime: 2,\n            required: false\n        }\n    ];\n    const atsAnalysis = (0,_hooks_useATSAnalysis__WEBPACK_IMPORTED_MODULE_18__[\"default\"])(formData);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EnhancedResumeBuilder.useEffect\": ()=>{\n            // Check for backup data from auth flow first\n            const backupData = localStorage.getItem('resumeFormDataBackup');\n            const authTimestamp = localStorage.getItem('authFlowTimestamp');\n            const savedData = localStorage.getItem('resumeFormData');\n            // If backup data exists and is recent (within 10 minutes), use it\n            if (backupData && authTimestamp) {\n                const timeDiff = Date.now() - parseInt(authTimestamp);\n                if (timeDiff < 10 * 60 * 1000) {\n                    try {\n                        const parsed = JSON.parse(backupData);\n                        setFormData(parsed);\n                        setLastSaved(new Date());\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success('Your work has been restored after sign-in!');\n                        // Clean up backup data\n                        localStorage.removeItem('resumeFormDataBackup');\n                        localStorage.removeItem('authFlowTimestamp');\n                        // Update main storage\n                        localStorage.setItem('resumeFormData', backupData);\n                        localStorage.setItem('resumeLastSaved', new Date().toISOString());\n                        return;\n                    } catch (error) {\n                        console.error('Failed to load backup data:', error);\n                    }\n                }\n            }\n            // Fallback to regular saved data\n            if (savedData) {\n                try {\n                    const parsed = JSON.parse(savedData);\n                    setFormData(parsed);\n                    setLastSaved(new Date(localStorage.getItem('resumeLastSaved') || Date.now()));\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success('Previous work restored!');\n                } catch (error) {\n                    console.error('Failed to load saved data:', error);\n                }\n            }\n            // Clean up old backup data\n            localStorage.removeItem('resumeFormDataBackup');\n            localStorage.removeItem('authFlowTimestamp');\n        }\n    }[\"EnhancedResumeBuilder.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EnhancedResumeBuilder.useEffect\": ()=>{\n            if (autoSaveEnabled && formData) {\n                // Make form data globally accessible for auth flow\n                if (true) {\n                    window.formData = formData;\n                }\n                const timeoutId = setTimeout({\n                    \"EnhancedResumeBuilder.useEffect.timeoutId\": ()=>{\n                        localStorage.setItem('resumeFormData', JSON.stringify(formData));\n                        localStorage.setItem('resumeLastSaved', new Date().toISOString());\n                        setLastSaved(new Date());\n                    }\n                }[\"EnhancedResumeBuilder.useEffect.timeoutId\"], 2000);\n                return ({\n                    \"EnhancedResumeBuilder.useEffect\": ()=>clearTimeout(timeoutId)\n                })[\"EnhancedResumeBuilder.useEffect\"];\n            }\n        }\n    }[\"EnhancedResumeBuilder.useEffect\"], [\n        formData,\n        autoSaveEnabled\n    ]);\n    const validateStep = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[validateStep]\": (stepIndex)=>{\n            const errors = {};\n            switch(stepIndex){\n                case 0:\n                    var _formData_personal_firstName, _formData_personal_lastName, _formData_personal_email;\n                    if (!((_formData_personal_firstName = formData.personal.firstName) === null || _formData_personal_firstName === void 0 ? void 0 : _formData_personal_firstName.trim())) errors.firstName = 'First name is required';\n                    if (!((_formData_personal_lastName = formData.personal.lastName) === null || _formData_personal_lastName === void 0 ? void 0 : _formData_personal_lastName.trim())) errors.lastName = 'Last name is required';\n                    if (!((_formData_personal_email = formData.personal.email) === null || _formData_personal_email === void 0 ? void 0 : _formData_personal_email.trim())) errors.email = 'Email is required';\n                    else if (!/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(formData.personal.email)) errors.email = 'Please enter a valid email address';\n                    break;\n                case 1:\n                    var _formData_education;\n                    const validEducation = (_formData_education = formData.education) === null || _formData_education === void 0 ? void 0 : _formData_education.filter({\n                        \"EnhancedResumeBuilder.useCallback[validateStep]\": (edu)=>{\n                            var _edu_degree, _edu_institution;\n                            return ((_edu_degree = edu.degree) === null || _edu_degree === void 0 ? void 0 : _edu_degree.trim()) && ((_edu_institution = edu.institution) === null || _edu_institution === void 0 ? void 0 : _edu_institution.trim());\n                        }\n                    }[\"EnhancedResumeBuilder.useCallback[validateStep]\"]);\n                    if (!validEducation || validEducation.length === 0) errors.education = 'At least one education entry with degree and institution is required';\n                    break;\n                case 2:\n                    var _formData_experience;\n                    const validExperience = (_formData_experience = formData.experience) === null || _formData_experience === void 0 ? void 0 : _formData_experience.filter({\n                        \"EnhancedResumeBuilder.useCallback[validateStep]\": (exp)=>{\n                            var _exp_title, _exp_company;\n                            return ((_exp_title = exp.title) === null || _exp_title === void 0 ? void 0 : _exp_title.trim()) && ((_exp_company = exp.company) === null || _exp_company === void 0 ? void 0 : _exp_company.trim());\n                        }\n                    }[\"EnhancedResumeBuilder.useCallback[validateStep]\"]);\n                    if (!validExperience || validExperience.length === 0) errors.experience = 'At least one work experience entry with job title and company is required';\n                    break;\n                case 3:\n                    break;\n                case 4:\n                    break;\n                default:\n                    break;\n            }\n            return errors;\n        }\n    }[\"EnhancedResumeBuilder.useCallback[validateStep]\"], [\n        formData\n    ]);\n    const canProceedToNextStep = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[canProceedToNextStep]\": (stepIndex)=>{\n            const errors = validateStep(stepIndex);\n            return Object.keys(errors).length === 0;\n        }\n    }[\"EnhancedResumeBuilder.useCallback[canProceedToNextStep]\"], [\n        validateStep\n    ]);\n    const getCompletedSteps = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[getCompletedSteps]\": ()=>{\n            const completed = [];\n            for(let i = 0; i < currentStep; i++){\n                const errors = validateStep(i);\n                if (Object.keys(errors).length === 0) {\n                    completed.push(i);\n                }\n            }\n            return completed;\n        }\n    }[\"EnhancedResumeBuilder.useCallback[getCompletedSteps]\"], [\n        currentStep,\n        validateStep\n    ]);\n    const getStepCompletionStatus = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[getStepCompletionStatus]\": ()=>{\n            const status = {};\n            steps.forEach({\n                \"EnhancedResumeBuilder.useCallback[getStepCompletionStatus]\": (step, index)=>{\n                    const errors = validateStep(index);\n                    status[index] = Object.keys(errors).length === 0;\n                }\n            }[\"EnhancedResumeBuilder.useCallback[getStepCompletionStatus]\"]);\n            return status;\n        }\n    }[\"EnhancedResumeBuilder.useCallback[getStepCompletionStatus]\"], [\n        steps,\n        validateStep\n    ]);\n    const canGenerateResume = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[canGenerateResume]\": ()=>{\n            for(let i = 0; i < steps.length - 1; i++){\n                if (!canProceedToNextStep(i)) return false;\n            }\n            return true;\n        }\n    }[\"EnhancedResumeBuilder.useCallback[canGenerateResume]\"], [\n        canProceedToNextStep,\n        steps.length\n    ]);\n    const updateFormData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[updateFormData]\": function(section, field, value) {\n            let index = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : null;\n            setFormData({\n                \"EnhancedResumeBuilder.useCallback[updateFormData]\": (prev)=>{\n                    let newData = {\n                        ...prev\n                    };\n                    if (index !== null && Array.isArray(prev[section])) {\n                        const newArray = [\n                            ...prev[section]\n                        ];\n                        newArray[index] = {\n                            ...newArray[index],\n                            [field]: value\n                        };\n                        newData[section] = newArray;\n                    } else if (typeof prev[section] === 'object' && !Array.isArray(prev[section])) {\n                        newData[section] = {\n                            ...prev[section],\n                            [field]: value\n                        };\n                    } else {\n                        newData[field] = value;\n                    }\n                    return newData;\n                }\n            }[\"EnhancedResumeBuilder.useCallback[updateFormData]\"]);\n            if (validationErrors[field]) {\n                setValidationErrors({\n                    \"EnhancedResumeBuilder.useCallback[updateFormData]\": (prev)=>{\n                        const newErrors = {\n                            ...prev\n                        };\n                        delete newErrors[field];\n                        return newErrors;\n                    }\n                }[\"EnhancedResumeBuilder.useCallback[updateFormData]\"]);\n            }\n        }\n    }[\"EnhancedResumeBuilder.useCallback[updateFormData]\"], [\n        validationErrors\n    ]);\n    const addArrayItem = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[addArrayItem]\": (section, template)=>{\n            setFormData({\n                \"EnhancedResumeBuilder.useCallback[addArrayItem]\": (prev)=>({\n                        ...prev,\n                        [section]: [\n                            ...prev[section],\n                            {\n                                ...template,\n                                id: Math.random().toString(36).substring(2, 11)\n                            }\n                        ]\n                    })\n            }[\"EnhancedResumeBuilder.useCallback[addArrayItem]\"]);\n        }\n    }[\"EnhancedResumeBuilder.useCallback[addArrayItem]\"], []);\n    const removeArrayItem = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[removeArrayItem]\": (section, id)=>{\n            setFormData({\n                \"EnhancedResumeBuilder.useCallback[removeArrayItem]\": (prev)=>({\n                        ...prev,\n                        [section]: prev[section].filter({\n                            \"EnhancedResumeBuilder.useCallback[removeArrayItem]\": (item)=>item.id !== id\n                        }[\"EnhancedResumeBuilder.useCallback[removeArrayItem]\"])\n                    })\n            }[\"EnhancedResumeBuilder.useCallback[removeArrayItem]\"]);\n        }\n    }[\"EnhancedResumeBuilder.useCallback[removeArrayItem]\"], []);\n    const nextStep = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[nextStep]\": ()=>{\n            const errors = validateStep(currentStep);\n            if (Object.keys(errors).length > 0) {\n                setValidationErrors(errors);\n                setShowValidationErrors(true);\n                const errorMessages = Object.values(errors);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(errorMessages[0]);\n                return;\n            }\n            setCompletedSteps({\n                \"EnhancedResumeBuilder.useCallback[nextStep]\": (prev)=>[\n                        ...new Set([\n                            ...prev,\n                            currentStep\n                        ])\n                    ]\n            }[\"EnhancedResumeBuilder.useCallback[nextStep]\"]);\n            setValidationErrors({});\n            setShowValidationErrors(false);\n            if (currentStep < steps.length - 1) {\n                setCurrentStep(currentStep + 1);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"\".concat(steps[currentStep].title, \" completed!\"));\n            }\n        }\n    }[\"EnhancedResumeBuilder.useCallback[nextStep]\"], [\n        currentStep,\n        validateStep,\n        steps\n    ]);\n    const prevStep = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[prevStep]\": ()=>{\n            setValidationErrors({});\n            setShowValidationErrors(false);\n            if (currentStep > 0) setCurrentStep(currentStep - 1);\n        }\n    }[\"EnhancedResumeBuilder.useCallback[prevStep]\"], [\n        currentStep\n    ]);\n    const handleStepClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[handleStepClick]\": (stepIndex)=>{\n            if (stepIndex <= currentStep || completedSteps.includes(stepIndex)) {\n                setCurrentStep(stepIndex);\n                setValidationErrors({});\n                setShowValidationErrors(false);\n            }\n        }\n    }[\"EnhancedResumeBuilder.useCallback[handleStepClick]\"], [\n        currentStep,\n        completedSteps\n    ]);\n    const generateResume = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[generateResume]\": async ()=>{\n            setValidationErrors({});\n            setShowValidationErrors(false);\n            if (!canGenerateResume()) {\n                const allErrors = {};\n                for(let i = 0; i < steps.length - 1; i++){\n                    const stepErrors = validateStep(i);\n                    Object.assign(allErrors, stepErrors);\n                }\n                setValidationErrors(allErrors);\n                setShowValidationErrors(true);\n                for(let i = 0; i < steps.length - 1; i++){\n                    const stepErrors = validateStep(i);\n                    if (Object.keys(stepErrors).length > 0) {\n                        setCurrentStep(i);\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Please complete all required fields in \".concat(steps[i].title));\n                        return;\n                    }\n                }\n                return;\n            }\n            try {\n                setIsGenerating(true);\n                setShowProgressBar(true);\n                setResumeGenerated(false);\n                const response = await fetch('/api/generate-resume', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        formData,\n                        templateId: selectedTemplate\n                    })\n                });\n                const contentType = response.headers.get('content-type');\n                if (!contentType || !contentType.includes('application/json')) throw new Error('Server returned non-JSON response');\n                const data = await response.json();\n                if (!response.ok) throw new Error(data.error || 'Failed to generate resume');\n                if (!data.resumeData || !data.downloadUrl) throw new Error('Generated resume data is incomplete');\n                setResumeUrl(data.downloadUrl);\n                setResumeData(data.resumeData);\n                setAtsScore(data.atsScore || 75);\n                setSuggestions(data.suggestions || []);\n                setResumeGenerated(true);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success('Resume generated successfully!');\n            } catch (error) {\n                console.error('Resume generation error:', error);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(error.message || 'Failed to generate resume');\n            } finally{\n                setIsGenerating(false);\n                setShowProgressBar(false);\n            }\n        }\n    }[\"EnhancedResumeBuilder.useCallback[generateResume]\"], [\n        canGenerateResume,\n        formData,\n        steps,\n        validateStep,\n        selectedTemplate\n    ]);\n    const handleSave = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[handleSave]\": ()=>{\n            localStorage.setItem('resumeFormData', JSON.stringify(formData));\n            localStorage.setItem('resumeLastSaved', new Date().toISOString());\n            setLastSaved(new Date());\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success('Progress saved!');\n        }\n    }[\"EnhancedResumeBuilder.useCallback[handleSave]\"], [\n        formData\n    ]);\n    const handleAISuggest = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[handleAISuggest]\": async (section)=>{\n            if (!aiSuggestionsEnabled) return;\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.loading('Getting AI suggestions...');\n            try {\n                await new Promise({\n                    \"EnhancedResumeBuilder.useCallback[handleAISuggest]\": (resolve)=>setTimeout(resolve, 1500)\n                }[\"EnhancedResumeBuilder.useCallback[handleAISuggest]\"]);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.dismiss();\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success('AI suggestions applied!');\n            } catch (error) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.dismiss();\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error('Failed to get AI suggestions');\n            }\n        }\n    }[\"EnhancedResumeBuilder.useCallback[handleAISuggest]\"], [\n        aiSuggestionsEnabled\n    ]);\n    // Set up event listener for resume generation from ReviewForm\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EnhancedResumeBuilder.useEffect\": ()=>{\n            const handleGenerateResume = {\n                \"EnhancedResumeBuilder.useEffect.handleGenerateResume\": ()=>{\n                    // Check if we can generate resume and call the function\n                    if (canGenerateResume()) {\n                        generateResume();\n                    } else {\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error('Please complete all required sections before generating your resume.');\n                    }\n                }\n            }[\"EnhancedResumeBuilder.useEffect.handleGenerateResume\"];\n            if (true) {\n                window.addEventListener('generateResume', handleGenerateResume);\n                return ({\n                    \"EnhancedResumeBuilder.useEffect\": ()=>window.removeEventListener('generateResume', handleGenerateResume)\n                })[\"EnhancedResumeBuilder.useEffect\"];\n            }\n        }\n    }[\"EnhancedResumeBuilder.useEffect\"], [\n        canGenerateResume,\n        generateResume\n    ]);\n    const getStepValidation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[getStepValidation]\": ()=>{\n            const validation = {};\n            steps.forEach({\n                \"EnhancedResumeBuilder.useCallback[getStepValidation]\": (step, index)=>{\n                    const errors = validateStep(index);\n                    validation[index] = Object.keys(errors).length === 0;\n                }\n            }[\"EnhancedResumeBuilder.useCallback[getStepValidation]\"]);\n            return validation;\n        }\n    }[\"EnhancedResumeBuilder.useCallback[getStepValidation]\"], [\n        steps,\n        validateStep\n    ]);\n    const renderStepContent = ()=>{\n        const commonProps = {\n            formData,\n            updateFormData,\n            addArrayItem,\n            removeArrayItem,\n            atsAnalysis,\n            validationErrors,\n            showValidationErrors,\n            onSave: handleSave,\n            onAISuggest: handleAISuggest\n        };\n        switch(currentStep){\n            case 0:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_ResumeFormComponents__WEBPACK_IMPORTED_MODULE_5__.PersonalInfoForm, {\n                    ...commonProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                    lineNumber: 384,\n                    columnNumber: 16\n                }, undefined);\n            case 1:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_ResumeFormComponents__WEBPACK_IMPORTED_MODULE_5__.EducationForm, {\n                    ...commonProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                    lineNumber: 386,\n                    columnNumber: 16\n                }, undefined);\n            case 2:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_ExperienceForm__WEBPACK_IMPORTED_MODULE_6__.EnhancedExperienceForm, {\n                    ...commonProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                    lineNumber: 388,\n                    columnNumber: 16\n                }, undefined);\n            case 3:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_ProjectsForm__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    ...commonProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                    lineNumber: 390,\n                    columnNumber: 16\n                }, undefined);\n            case 4:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_SkillsForm__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    ...commonProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                    lineNumber: 392,\n                    columnNumber: 16\n                }, undefined);\n            case 5:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_ReviewForm__WEBPACK_IMPORTED_MODULE_10__.EnhancedReviewForm, {\n                    ...commonProps,\n                    selectedTemplate: selectedTemplate,\n                    onTemplateSelect: setSelectedTemplate\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                    lineNumber: 394,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return null;\n        }\n    };\n    if (resumeGenerated && resumeData && resumeUrl) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SuccessScreen__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n            formData: formData,\n            resumeData: resumeData,\n            onStartOver: ()=>{\n                setResumeGenerated(false);\n                setResumeData(null);\n                setResumeUrl(\"\");\n                setCurrentStep(0);\n            },\n            onEditResume: ()=>{\n                setResumeGenerated(false);\n                setCurrentStep(4); // Go back to review step\n            }\n        }, void 0, false, {\n            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n            lineNumber: 402,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-b from-black to-[#0A0A0A] text-white relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 opacity-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 bg-[url('/grid.svg')] [mask-image:radial-gradient(ellipse_at_center,white,transparent)]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                    lineNumber: 422,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                lineNumber: 421,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_common_ProgressBar__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                isVisible: showProgressBar,\n                onComplete: ()=>setShowProgressBar(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                lineNumber: 424,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 pb-32\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 py-8\",\n                    children: [\n                        !hideHeader && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_24__.motion.div, {\n                            className: \"text-center mb-8\",\n                            initial: {\n                                opacity: 0,\n                                y: -20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center gap-3 mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                    className: \"h-10 w-10 text-neural-pink animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                    lineNumber: 431,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-neural-pink opacity-20 rounded-full blur-md\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                    lineNumber: 432,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                            lineNumber: 430,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-4xl lg:text-5xl font-bold text-white\",\n                                            children: \"Enhanced Resume Builder\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                            lineNumber: 434,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                    lineNumber: 429,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 text-lg max-w-2xl mx-auto\",\n                                    children: \"Create professional, ATS-friendly resumes with our enhanced AI-powered builder\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                    lineNumber: 436,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                            lineNumber: 428,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col xl:flex-row gap-8 max-w-7xl mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 max-w-4xl\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"sticky top-4 z-30 bg-gray-900/95 backdrop-blur-sm rounded-2xl border border-gray-700/50 mb-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EnhancedProgressIndicator__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                steps: steps,\n                                                currentStep: currentStep,\n                                                completedSteps: completedSteps,\n                                                onStepClick: handleStepClick,\n                                                variant: \"minimal\",\n                                                showLabels: true,\n                                                showProgress: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                lineNumber: 444,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                            lineNumber: 443,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_26__.AnimatePresence, {\n                                                mode: \"wait\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_24__.motion.div, {\n                                                    initial: {\n                                                        opacity: 0,\n                                                        x: 20\n                                                    },\n                                                    animate: {\n                                                        opacity: 1,\n                                                        x: 0\n                                                    },\n                                                    exit: {\n                                                        opacity: 0,\n                                                        x: -20\n                                                    },\n                                                    transition: {\n                                                        duration: 0.3\n                                                    },\n                                                    children: renderStepContent()\n                                                }, currentStep, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                    lineNumber: 458,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                lineNumber: 457,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                            lineNumber: 456,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                    lineNumber: 441,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden xl:block xl:w-80 xl:flex-shrink-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"sticky top-4 h-[calc(100vh-2rem)]\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SimpleResumePreview__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            formData: formData,\n                                            selectedTemplate: selectedTemplate,\n                                            showPreview: showPreview,\n                                            onTogglePreview: ()=>setShowPreview(!showPreview)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                            lineNumber: 469,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                        lineNumber: 468,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                    lineNumber: 466,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"xl:hidden mt-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_24__.motion.div, {\n                                        className: \"bg-gray-900/60 backdrop-blur-md rounded-2xl p-6 border border-white/10\",\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            delay: 0.2\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                className: \"h-5 w-5 text-neural-blue\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                                lineNumber: 488,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            \"Resume Preview\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                        lineNumber: 487,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setShowPreview(!showPreview),\n                                                        className: \"px-3 py-1.5 rounded-lg text-sm font-medium transition-colors \".concat(showPreview ? 'bg-neural-purple text-white' : 'bg-gray-800 text-gray-300 hover:bg-gray-700'),\n                                                        children: showPreview ? 'Hide' : 'Show'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                        lineNumber: 491,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                lineNumber: 486,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            showPreview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"max-h-[600px] overflow-y-auto border border-white/10 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ResumePreview__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    formData: formData,\n                                                    selectedTemplate: selectedTemplate\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                    lineNumber: 502,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                lineNumber: 501,\n                                                columnNumber: 19\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-12 text-gray-400\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                        className: \"h-12 w-12 mx-auto mb-3 opacity-30\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                        lineNumber: 506,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm\",\n                                                        children: 'Click \"Show\" to preview your resume'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                        lineNumber: 507,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                lineNumber: 505,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                        lineNumber: 480,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                    lineNumber: 479,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                            lineNumber: 439,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                    lineNumber: 426,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                lineNumber: 425,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_layout_SmartNavigationBar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                currentStep: currentStep,\n                totalSteps: steps.length,\n                onPrevious: prevStep,\n                onNext: nextStep,\n                onGenerate: generateResume,\n                onSave: handleSave,\n                onPreview: ()=>setShowPreview(!showPreview),\n                isGenerating: isGenerating,\n                canProceed: currentStep === steps.length - 1 ? canGenerateResume() : canProceedToNextStep(currentStep),\n                showPreview: showPreview,\n                steps: steps,\n                formData: formData,\n                atsScore: atsAnalysis.overallScore,\n                autoSaveEnabled: autoSaveEnabled\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                lineNumber: 515,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n        lineNumber: 420,\n        columnNumber: 5\n    }, undefined);\n};\n_s(EnhancedResumeBuilder, \"fDIam3iIEkONwVfjFA/qcKl+co8=\", false, function() {\n    return [\n        _hooks_useATSAnalysis__WEBPACK_IMPORTED_MODULE_18__[\"default\"]\n    ];\n});\n_c = EnhancedResumeBuilder;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (EnhancedResumeBuilder);\nvar _c;\n$RefreshReg$(_c, \"EnhancedResumeBuilder\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/resume/ResumeBuilder.jsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/utils/enhancedPdfGenerator.js":
/*!*******************************************!*\
  !*** ./src/utils/enhancedPdfGenerator.js ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EnhancedHTMLToPDF: () => (/* binding */ EnhancedHTMLToPDF),\n/* harmony export */   EnhancedPDFGenerator: () => (/* binding */ EnhancedPDFGenerator),\n/* harmony export */   convertHTMLToPDF: () => (/* binding */ convertHTMLToPDF),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   downloadPDF: () => (/* binding */ downloadPDF),\n/* harmony export */   generatePDFPreview: () => (/* binding */ generatePDFPreview)\n/* harmony export */ });\n/* harmony import */ var jspdf__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jspdf */ \"(app-pages-browser)/./node_modules/jspdf/dist/jspdf.es.min.js\");\n/* harmony import */ var html2canvas__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! html2canvas */ \"(app-pages-browser)/./node_modules/html2canvas/dist/html2canvas.js\");\n/* harmony import */ var html2canvas__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(html2canvas__WEBPACK_IMPORTED_MODULE_1__);\n\n\n// Enhanced PDF Generator with improved formatting and ATS optimization\nclass EnhancedPDFGenerator {\n    // Initialize PDF document\n    initDocument() {\n        this.doc = new jspdf__WEBPACK_IMPORTED_MODULE_0__[\"default\"]({\n            orientation: 'portrait',\n            unit: 'mm',\n            format: 'a4'\n        });\n        // Set default font\n        this.doc.setFont('helvetica', 'normal');\n        this.currentY = this.margins.top;\n    }\n    // Add text with proper formatting and bullet point handling\n    addText(text) {\n        let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        const { fontSize = this.fontSize.body, fontStyle = 'normal', color = [\n            0,\n            0,\n            0\n        ], align = 'left', maxWidth = this.pageWidth - this.margins.left - this.margins.right, indent = 0, isBullet = false } = options;\n        this.doc.setFontSize(fontSize);\n        this.doc.setFont('helvetica', fontStyle);\n        this.doc.setTextColor(...color);\n        // Handle bullet points properly\n        let processedText = text;\n        if (isBullet && !text.trim().startsWith('•')) {\n            processedText = \"• \".concat(text.trim());\n        }\n        // Remove duplicate bullet points\n        processedText = processedText.replace(/^•\\s*•\\s*/, '• ');\n        // Split text into lines that fit the width\n        const lines = this.doc.splitTextToSize(processedText, maxWidth - indent);\n        lines.forEach((line, index)=>{\n            // Check if we need a new page\n            if (this.currentY + this.lineHeight > this.pageHeight - this.margins.bottom) {\n                this.doc.addPage();\n                this.currentY = this.margins.top;\n            }\n            // Add the text\n            this.doc.text(line, this.margins.left + indent, this.currentY, {\n                align\n            });\n            this.currentY += this.lineHeight;\n        });\n        return this;\n    }\n    // Add section heading with consistent formatting\n    addSectionHeading(title) {\n        this.currentY += 3; // Add space before heading\n        this.addText(title.toUpperCase(), {\n            fontSize: this.fontSize.heading,\n            fontStyle: 'bold',\n            color: [\n                0,\n                0,\n                0\n            ]\n        });\n        // Add underline\n        const textWidth = this.doc.getTextWidth(title.toUpperCase());\n        this.doc.setLineWidth(0.5);\n        this.doc.line(this.margins.left, this.currentY - 2, this.margins.left + textWidth, this.currentY - 2);\n        this.currentY += 2; // Add space after heading\n        return this;\n    }\n    // Add contact information in header\n    addContactHeader(personalInfo) {\n        // Name\n        this.addText(\"\".concat(personalInfo.firstName, \" \").concat(personalInfo.lastName), {\n            fontSize: this.fontSize.title,\n            fontStyle: 'bold',\n            align: 'center'\n        });\n        this.currentY += 2;\n        // Contact details\n        const contactDetails = [];\n        if (personalInfo.email) contactDetails.push(personalInfo.email);\n        if (personalInfo.phone) contactDetails.push(personalInfo.phone);\n        if (personalInfo.location) contactDetails.push(personalInfo.location);\n        if (personalInfo.linkedin) contactDetails.push(personalInfo.linkedin);\n        if (contactDetails.length > 0) {\n            this.addText(contactDetails.join(' • '), {\n                fontSize: this.fontSize.small,\n                align: 'center',\n                color: [\n                    60,\n                    60,\n                    60\n                ]\n            });\n        }\n        this.currentY += 5; // Add space after header\n        return this;\n    }\n    // Add experience section with proper formatting\n    addExperience(experiences) {\n        if (!experiences || experiences.length === 0) return this;\n        this.addSectionHeading('Professional Experience');\n        experiences.forEach((exp, index)=>{\n            // Job title and dates\n            const titleLine = exp.title;\n            const dateLine = \"\".concat(exp.startDate, \" - \").concat(exp.current ? 'Present' : exp.endDate);\n            // Add job title\n            this.addText(titleLine, {\n                fontSize: this.fontSize.subheading,\n                fontStyle: 'bold'\n            });\n            // Add company and location on same line as dates\n            const companyInfo = exp.company + (exp.location ? \" • \".concat(exp.location) : '');\n            this.currentY -= this.lineHeight; // Move back up\n            this.addText(dateLine, {\n                fontSize: this.fontSize.small,\n                align: 'right',\n                color: [\n                    60,\n                    60,\n                    60\n                ]\n            });\n            this.addText(companyInfo, {\n                fontSize: this.fontSize.body,\n                fontStyle: 'bold',\n                color: [\n                    60,\n                    60,\n                    60\n                ]\n            });\n            // Add description with proper bullet formatting\n            if (exp.description) {\n                const descriptions = exp.description.split('\\n').filter((line)=>line.trim());\n                descriptions.forEach((desc)=>{\n                    this.addText(desc.trim(), {\n                        fontSize: this.fontSize.body,\n                        indent: 5,\n                        isBullet: true\n                    });\n                });\n            }\n            // Add space between experiences\n            if (index < experiences.length - 1) {\n                this.currentY += 3;\n            }\n        });\n        this.currentY += 5;\n        return this;\n    }\n    // Add education section\n    addEducation(education) {\n        if (!education || education.length === 0) return this;\n        this.addSectionHeading('Education');\n        education.forEach((edu, index)=>{\n            // Degree and graduation date\n            const degreeLine = edu.degree + (edu.field ? \" in \".concat(edu.field) : '');\n            const dateLine = edu.graduationDate;\n            this.addText(degreeLine, {\n                fontSize: this.fontSize.subheading,\n                fontStyle: 'bold'\n            });\n            this.currentY -= this.lineHeight; // Move back up\n            this.addText(dateLine, {\n                fontSize: this.fontSize.small,\n                align: 'right',\n                color: [\n                    60,\n                    60,\n                    60\n                ]\n            });\n            // Institution and location\n            const institutionInfo = edu.institution + (edu.location ? \" • \".concat(edu.location) : '');\n            this.addText(institutionInfo, {\n                fontSize: this.fontSize.body,\n                color: [\n                    60,\n                    60,\n                    60\n                ]\n            });\n            // GPA if provided\n            if (edu.gpa) {\n                this.addText(\"GPA: \".concat(edu.gpa), {\n                    fontSize: this.fontSize.small,\n                    color: [\n                        60,\n                        60,\n                        60\n                    ]\n                });\n            }\n            // Add space between education entries\n            if (index < education.length - 1) {\n                this.currentY += 2;\n            }\n        });\n        this.currentY += 5;\n        return this;\n    }\n    // Add skills section with proper formatting\n    addSkills(skills) {\n        if (!skills) return this;\n        this.addSectionHeading('Skills');\n        if (skills.technical && skills.technical.length > 0) {\n            this.addText('Technical: ' + skills.technical.join(', '), {\n                fontSize: this.fontSize.body\n            });\n        }\n        if (skills.languages && skills.languages.length > 0) {\n            this.addText('Languages: ' + skills.languages.join(', '), {\n                fontSize: this.fontSize.body\n            });\n        }\n        if (skills.certifications && skills.certifications.length > 0) {\n            this.addText('Certifications: ' + skills.certifications.join(', '), {\n                fontSize: this.fontSize.body\n            });\n        }\n        this.currentY += 5;\n        return this;\n    }\n    // Add projects section\n    addProjects(projects) {\n        if (!projects || projects.length === 0) return this;\n        this.addSectionHeading('Projects');\n        projects.forEach((project, index)=>{\n            // Project name and technologies\n            let projectTitle = project.name;\n            if (project.technologies) {\n                projectTitle += \" (\".concat(project.technologies, \")\");\n            }\n            this.addText(projectTitle, {\n                fontSize: this.fontSize.subheading,\n                fontStyle: 'bold'\n            });\n            // Description\n            if (project.description) {\n                this.addText(project.description, {\n                    fontSize: this.fontSize.body,\n                    indent: 0\n                });\n            }\n            // Links\n            const links = [];\n            if (project.link) links.push(\"Demo: \".concat(project.link));\n            if (project.github) links.push(\"Code: \".concat(project.github));\n            if (links.length > 0) {\n                this.addText(links.join(' • '), {\n                    fontSize: this.fontSize.small,\n                    color: [\n                        60,\n                        60,\n                        60\n                    ]\n                });\n            }\n            // Add space between projects\n            if (index < projects.length - 1) {\n                this.currentY += 3;\n            }\n        });\n        this.currentY += 5;\n        return this;\n    }\n    // Generate complete resume PDF\n    generateResume(formData) {\n        let templateId = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'classic_ats';\n        this.initDocument();\n        // Add header with contact information\n        this.addContactHeader(formData.personal);\n        // Add professional summary\n        if (formData.personal.summary) {\n            this.addSectionHeading('Professional Summary');\n            this.addText(formData.personal.summary, {\n                fontSize: this.fontSize.body\n            });\n            this.currentY += 5;\n        }\n        // Add sections in order\n        this.addExperience(formData.experience);\n        this.addEducation(formData.education);\n        this.addSkills(formData.skills);\n        this.addProjects(formData.projects);\n        return this.doc;\n    }\n    // Save PDF with proper filename\n    savePDF(formData, filename) {\n        const doc = this.generateResume(formData);\n        const name = \"\".concat(formData.personal.firstName, \"_\").concat(formData.personal.lastName);\n        const finalFilename = filename || \"\".concat(name, \"_Resume.pdf\");\n        doc.save(finalFilename);\n    }\n    // Get PDF as blob for preview\n    getPDFBlob(formData) {\n        const doc = this.generateResume(formData);\n        return doc.output('blob');\n    }\n    // Get PDF as data URL for preview\n    getPDFDataURL(formData) {\n        const doc = this.generateResume(formData);\n        return doc.output('dataurlstring');\n    }\n    constructor(){\n        this.doc = null;\n        this.pageWidth = 210; // A4 width in mm\n        this.pageHeight = 297; // A4 height in mm\n        this.margins = {\n            top: 20,\n            right: 20,\n            bottom: 20,\n            left: 20\n        };\n        this.currentY = this.margins.top;\n        this.lineHeight = 5;\n        this.fontSize = {\n            title: 16,\n            heading: 12,\n            subheading: 10,\n            body: 9,\n            small: 8\n        };\n    }\n}\n// Enhanced HTML to PDF converter with better formatting\nclass EnhancedHTMLToPDF {\n    async convertElementToPDF(element) {\n        let filename = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'resume.pdf';\n        try {\n            // Create canvas from HTML element\n            const canvas = await html2canvas__WEBPACK_IMPORTED_MODULE_1___default()(element, this.options);\n            // Create PDF\n            const pdf = new jspdf__WEBPACK_IMPORTED_MODULE_0__[\"default\"]({\n                orientation: 'portrait',\n                unit: 'mm',\n                format: 'a4'\n            });\n            // Calculate dimensions\n            const imgWidth = 210; // A4 width in mm\n            const imgHeight = canvas.height * imgWidth / canvas.width;\n            // Add image to PDF\n            const imgData = canvas.toDataURL('image/png');\n            pdf.addImage(imgData, 'PNG', 0, 0, imgWidth, imgHeight);\n            // Save PDF\n            pdf.save(filename);\n            return pdf;\n        } catch (error) {\n            console.error('Error converting HTML to PDF:', error);\n            throw error;\n        }\n    }\n    async getPDFBlob(element) {\n        try {\n            const canvas = await html2canvas__WEBPACK_IMPORTED_MODULE_1___default()(element, this.options);\n            const pdf = new jspdf__WEBPACK_IMPORTED_MODULE_0__[\"default\"]({\n                orientation: 'portrait',\n                unit: 'mm',\n                format: 'a4'\n            });\n            const imgWidth = 210;\n            const imgHeight = canvas.height * imgWidth / canvas.width;\n            const imgData = canvas.toDataURL('image/png');\n            pdf.addImage(imgData, 'PNG', 0, 0, imgWidth, imgHeight);\n            return pdf.output('blob');\n        } catch (error) {\n            console.error('Error generating PDF blob:', error);\n            throw error;\n        }\n    }\n    constructor(){\n        this.options = {\n            scale: 2,\n            useCORS: true,\n            allowTaint: true,\n            backgroundColor: '#ffffff',\n            width: 794,\n            height: 1123,\n            scrollX: 0,\n            scrollY: 0\n        };\n    }\n}\n// Utility functions\nconst downloadPDF = (formData, templateId, filename)=>{\n    const generator = new EnhancedPDFGenerator();\n    generator.savePDF(formData, filename);\n};\nconst generatePDFPreview = (formData, templateId)=>{\n    const generator = new EnhancedPDFGenerator();\n    return generator.getPDFDataURL(formData);\n};\nconst convertHTMLToPDF = async (element, filename)=>{\n    const converter = new EnhancedHTMLToPDF();\n    return await converter.convertElementToPDF(element, filename);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (EnhancedPDFGenerator);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/enhancedPdfGenerator.js\n"));

/***/ })

});
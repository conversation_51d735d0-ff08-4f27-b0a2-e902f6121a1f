/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: ["class"],
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      fontFamily: {
        sans: ['Inter', 'sans-serif'],
        mono: ['IBM Plex Mono', 'monospace'],
      },
      colors: {
        background: 'hsl(0 0% 100%)',
        foreground: 'hsl(240 10% 3.9%)',
        primary: {
          DEFAULT: 'hsl(263 70% 50%)',
          foreground: 'hsl(0 0% 98%)',
        },
        secondary: {
          DEFAULT: 'hsl(240 4.8% 95.9%)',
          foreground: 'hsl(240 5.9% 10%)',
        },
        accent: {
          DEFAULT: 'hsl(258 90% 66%)',
          foreground: 'hsl(0 0% 98%)',
        },
        ai: {
          DEFAULT: 'hsl(258 90% 66%)',
          light: 'hsl(258 90% 80%)',
          dark: 'hsl(258 90% 50%)',
        },
        neural: {
          purple: 'hsl(258 90% 66%)',
          pink: 'hsl(330 81% 60%)',
          blue: 'hsl(210 100% 50%)',
        },
      },      animation: {
        'gradient-x': 'gradient-x 8s ease infinite',
        'gradient-y': 'gradient-y 8s ease infinite',
        'gradient-xy': 'gradient-xy 8s ease infinite',
        'pulse-slow': 'pulse 5s cubic-bezier(0.4, 0, 0.6, 1) infinite',
        'float-1': 'float-1 20s ease-in-out infinite',
        'float-2': 'float-2 25s ease-in-out infinite',
        'float-3': 'float-3 30s ease-in-out infinite',
        'float-4': 'float-4 18s ease-in-out infinite',
        'float-5': 'float-5 22s ease-in-out infinite',
        'float-6': 'float-6 28s ease-in-out infinite',
        'float-7': 'float-7 24s ease-in-out infinite',
        'float-8': 'float-8 26s ease-in-out infinite',
        'neural-pulse': 'neural-pulse 3s ease-in-out infinite',
        'neural-float': 'neural-float 6s ease-in-out infinite',
        'neural-glow': 'neural-glow 4s ease-in-out infinite',
        'shake': 'shake 0.5s ease-in-out',
      },
      keyframes: {
        'gradient-x': {
          '0%, 100%': {
            'background-size': '200% 200%',
            'background-position': 'left center',
          },
          '50%': {
            'background-size': '200% 200%',
            'background-position': 'right center',
          },
        },
      },
    },
  },
  plugins: [
    require("tailwindcss-animate"),
    require('@tailwindcss/typography'),
  ],
}
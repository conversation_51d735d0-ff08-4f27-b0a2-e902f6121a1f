"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/resume-builder/page",{

/***/ "(app-pages-browser)/./src/components/resume/forms/SkillsForm.jsx":
/*!****************************************************!*\
  !*** ./src/components/resume/forms/SkillsForm.jsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EnhancedSkillsForm: () => (/* binding */ EnhancedSkillsForm),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Certificate_CheckCircle_Clock_Code_Globe_Info_Lightbulb_Plus_Save_Sparkles_Target_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Certificate,CheckCircle,Clock,Code,Globe,Info,Lightbulb,Plus,Save,Sparkles,Target,Wand2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Certificate_CheckCircle_Clock_Code_Globe_Info_Lightbulb_Plus_Save_Sparkles_Target_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Certificate,CheckCircle,Clock,Code,Globe,Info,Lightbulb,Plus,Save,Sparkles,Target,Wand2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Certificate_CheckCircle_Clock_Code_Globe_Info_Lightbulb_Plus_Save_Sparkles_Target_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Certificate,CheckCircle,Clock,Code,Globe,Info,Lightbulb,Plus,Save,Sparkles,Target,Wand2,X!=!lucide-react */ \"(app-pages-browser)/__barrel_optimize__?names=AlertTriangle,Award,Certificate,CheckCircle,Clock,Code,Globe,Info,Lightbulb,Plus,Save,Sparkles,Target,Wand2,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Certificate_CheckCircle_Clock_Code_Globe_Info_Lightbulb_Plus_Save_Sparkles_Target_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Certificate,CheckCircle,Clock,Code,Globe,Info,Lightbulb,Plus,Save,Sparkles,Target,Wand2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Certificate_CheckCircle_Clock_Code_Globe_Info_Lightbulb_Plus_Save_Sparkles_Target_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Certificate,CheckCircle,Clock,Code,Globe,Info,Lightbulb,Plus,Save,Sparkles,Target,Wand2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Certificate_CheckCircle_Clock_Code_Globe_Info_Lightbulb_Plus_Save_Sparkles_Target_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Certificate,CheckCircle,Clock,Code,Globe,Info,Lightbulb,Plus,Save,Sparkles,Target,Wand2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Certificate_CheckCircle_Clock_Code_Globe_Info_Lightbulb_Plus_Save_Sparkles_Target_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Certificate,CheckCircle,Clock,Code,Globe,Info,Lightbulb,Plus,Save,Sparkles,Target,Wand2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Certificate_CheckCircle_Clock_Code_Globe_Info_Lightbulb_Plus_Save_Sparkles_Target_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Certificate,CheckCircle,Clock,Code,Globe,Info,Lightbulb,Plus,Save,Sparkles,Target,Wand2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Certificate_CheckCircle_Clock_Code_Globe_Info_Lightbulb_Plus_Save_Sparkles_Target_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Certificate,CheckCircle,Clock,Code,Globe,Info,Lightbulb,Plus,Save,Sparkles,Target,Wand2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lightbulb.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Certificate_CheckCircle_Clock_Code_Globe_Info_Lightbulb_Plus_Save_Sparkles_Target_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Certificate,CheckCircle,Clock,Code,Globe,Info,Lightbulb,Plus,Save,Sparkles,Target,Wand2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Certificate_CheckCircle_Clock_Code_Globe_Info_Lightbulb_Plus_Save_Sparkles_Target_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Certificate,CheckCircle,Clock,Code,Globe,Info,Lightbulb,Plus,Save,Sparkles,Target,Wand2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wand-sparkles.js\");\n/* harmony import */ var _SmartFormField__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./SmartFormField */ \"(app-pages-browser)/./src/components/resume/forms/SmartFormField.jsx\");\n/* harmony import */ var _common_ClientOnly__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../common/ClientOnly */ \"(app-pages-browser)/./src/components/common/ClientOnly.jsx\");\n/* harmony import */ var _ats_FieldIndicator__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../ats/FieldIndicator */ \"(app-pages-browser)/./src/components/resume/ats/FieldIndicator.jsx\");\n/* __next_internal_client_entry_do_not_use__ EnhancedSkillsForm,default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst EnhancedSkillsForm = (param)=>{\n    let { formData, updateFormData, atsAnalysis, validationErrors = {}, showValidationErrors = false, onSave, onAISuggest } = param;\n    var _skillCategories_find, _skillCategories_find1, _formData_skills_activeCategory, _skillCategories_find2, _formData_skills_activeCategory1, _atsAnalysis_fieldAnalysis;\n    _s();\n    const [activeCategory, setActiveCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('technical');\n    const [showAIHelper, setShowAIHelper] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [newSkill, setNewSkill] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        technical: '',\n        languages: '',\n        certifications: ''\n    });\n    const skillCategories = [\n        {\n            id: 'technical',\n            label: 'Technical Skills',\n            icon: _barrel_optimize_names_AlertTriangle_Award_Certificate_CheckCircle_Clock_Code_Globe_Info_Lightbulb_Plus_Save_Sparkles_Target_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            color: 'neural-purple'\n        },\n        {\n            id: 'languages',\n            label: 'Languages',\n            icon: _barrel_optimize_names_AlertTriangle_Award_Certificate_CheckCircle_Clock_Code_Globe_Info_Lightbulb_Plus_Save_Sparkles_Target_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            color: 'neural-blue'\n        },\n        {\n            id: 'certifications',\n            label: 'Certifications',\n            icon: _barrel_optimize_names_AlertTriangle_Award_Certificate_CheckCircle_Clock_Code_Globe_Info_Lightbulb_Plus_Save_Sparkles_Target_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__.Certificate,\n            color: 'neural-pink'\n        }\n    ];\n    const addSkill = (category)=>{\n        const skill = newSkill[category].trim();\n        if (skill && !formData.skills[category].includes(skill)) {\n            const updatedSkills = [\n                ...formData.skills[category],\n                skill\n            ];\n            updateFormData('skills', category, updatedSkills);\n            setNewSkill({\n                ...newSkill,\n                [category]: ''\n            });\n        }\n    };\n    const removeSkill = (category, skillToRemove)=>{\n        const updatedSkills = formData.skills[category].filter((skill)=>skill !== skillToRemove);\n        updateFormData('skills', category, updatedSkills);\n    };\n    const handleKeyPress = (e, category)=>{\n        if (e.key === 'Enter') {\n            e.preventDefault();\n            addSkill(category);\n        }\n    };\n    const getSkillSuggestions = (category)=>{\n        const suggestions = {\n            technical: [\n                'JavaScript',\n                'Python',\n                'React',\n                'Node.js',\n                'SQL',\n                'AWS',\n                'Docker',\n                'Git',\n                'TypeScript',\n                'MongoDB'\n            ],\n            languages: [\n                'English',\n                'Spanish',\n                'French',\n                'German',\n                'Mandarin',\n                'Japanese',\n                'Portuguese',\n                'Italian',\n                'Russian',\n                'Arabic'\n            ],\n            certifications: [\n                'AWS Certified',\n                'Google Cloud Professional',\n                'Microsoft Azure',\n                'PMP',\n                'Scrum Master',\n                'CompTIA Security+',\n                'Cisco CCNA',\n                'Oracle Certified'\n            ]\n        };\n        return suggestions[category] || [];\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n        initial: {\n            opacity: 0,\n            y: 20\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        exit: {\n            opacity: 0,\n            y: -20\n        },\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-10 h-10 bg-neural-purple/20 rounded-xl flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Award_Certificate_CheckCircle_Clock_Code_Globe_Info_Lightbulb_Plus_Save_Sparkles_Target_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"h-5 w-5 text-neural-purple\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                lineNumber: 86,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                            lineNumber: 85,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold text-white\",\n                                    children: \"Skills\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400 text-sm\",\n                                    children: \"Highlight your technical and soft skills (Optional)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                            lineNumber: 88,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                    lineNumber: 84,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                lineNumber: 83,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: -10\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                className: \"bg-blue-500/10 border border-blue-500/20 rounded-xl p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Award_Certificate_CheckCircle_Clock_Code_Globe_Info_Lightbulb_Plus_Save_Sparkles_Target_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"h-4 w-4 text-blue-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                lineNumber: 102,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-blue-400 font-medium\",\n                                children: \"Optional Section\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                lineNumber: 103,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                        lineNumber: 101,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-300 text-sm\",\n                        children: \"Skills help employers quickly identify your capabilities. You can skip this section if your experience and projects already demonstrate your skills clearly.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                        lineNumber: 105,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.AnimatePresence, {\n                children: showValidationErrors && validationErrors.skills && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: -10\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    exit: {\n                        opacity: 0,\n                        y: -10\n                    },\n                    className: \"bg-red-500/10 border border-red-500/20 rounded-xl p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 mb-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Award_Certificate_CheckCircle_Clock_Code_Globe_Info_Lightbulb_Plus_Save_Sparkles_Target_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-4 w-4 text-red-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-red-400 font-medium\",\n                                    children: \"Skills Information\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                            lineNumber: 119,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-300 text-sm\",\n                            children: validationErrors.skills\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                            lineNumber: 123,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                    lineNumber: 113,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                lineNumber: 111,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex gap-2 p-1 bg-gray-900/40 rounded-xl border border-white/10\",\n                children: skillCategories.map((category)=>{\n                    var _formData_skills_category_id;\n                    const IconComponent = category.icon;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setActiveCategory(category.id),\n                        className: \"flex items-center gap-2 px-4 py-3 rounded-lg text-sm font-medium transition-all duration-300 flex-1 \".concat(activeCategory === category.id ? \"bg-\".concat(category.color, \" text-white shadow-lg\") : 'text-gray-300 hover:bg-white/5 hover:text-white'),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                lineNumber: 142,\n                                columnNumber: 15\n                            }, undefined),\n                            category.label,\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"ml-auto bg-white/20 text-xs px-2 py-1 rounded-full\",\n                                children: ((_formData_skills_category_id = formData.skills[category.id]) === null || _formData_skills_category_id === void 0 ? void 0 : _formData_skills_category_id.length) || 0\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                lineNumber: 144,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, category.id, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                        lineNumber: 133,\n                        columnNumber: 13\n                    }, undefined);\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                lineNumber: 129,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-900/40 backdrop-blur-sm rounded-2xl p-6 border border-white/10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.AnimatePresence, {\n                    mode: \"wait\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            x: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            x: 0\n                        },\n                        exit: {\n                            opacity: 0,\n                            x: -20\n                        },\n                        transition: {\n                            duration: 0.2\n                        },\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: newSkill[activeCategory],\n                                                onChange: (e)=>setNewSkill({\n                                                        ...newSkill,\n                                                        [activeCategory]: e.target.value\n                                                    }),\n                                                onKeyPress: (e)=>handleKeyPress(e, activeCategory),\n                                                placeholder: \"Add \".concat((_skillCategories_find = skillCategories.find((c)=>c.id === activeCategory)) === null || _skillCategories_find === void 0 ? void 0 : _skillCategories_find.label.toLowerCase(), \"...\"),\n                                                className: \"flex-1 px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                                lineNumber: 166,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>addSkill(activeCategory),\n                                                disabled: !newSkill[activeCategory].trim(),\n                                                className: \"px-4 py-3 bg-neural-purple hover:bg-neural-purple/80 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-lg transition-colors\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Award_Certificate_CheckCircle_Clock_Code_Globe_Info_Lightbulb_Plus_Save_Sparkles_Target_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                                    lineNumber: 179,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: \"Popular suggestions:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap gap-2\",\n                                                children: getSkillSuggestions(activeCategory).filter((suggestion)=>!formData.skills[activeCategory].includes(suggestion)).slice(0, 8).map((suggestion)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>{\n                                                            setNewSkill({\n                                                                ...newSkill,\n                                                                [activeCategory]: suggestion\n                                                            });\n                                                            addSkill(activeCategory);\n                                                        },\n                                                        className: \"px-3 py-1.5 bg-gray-700 hover:bg-gray-600 text-gray-300 hover:text-white text-sm rounded-lg transition-colors\",\n                                                        children: suggestion\n                                                    }, suggestion, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                                        lineNumber: 191,\n                                                        columnNumber: 23\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                lineNumber: 164,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-medium text-white flex items-center gap-2\",\n                                        children: [\n                                            (()=>{\n                                                const category = skillCategories.find((c)=>c.id === activeCategory);\n                                                return (category === null || category === void 0 ? void 0 : category.icon) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(category.icon, {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                                    lineNumber: 212,\n                                                    columnNumber: 21\n                                                }, undefined) : null;\n                                            })(),\n                                            \"Your \",\n                                            (_skillCategories_find1 = skillCategories.find((c)=>c.id === activeCategory)) === null || _skillCategories_find1 === void 0 ? void 0 : _skillCategories_find1.label\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    ((_formData_skills_activeCategory = formData.skills[activeCategory]) === null || _formData_skills_activeCategory === void 0 ? void 0 : _formData_skills_activeCategory.length) > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-2\",\n                                        children: formData.skills[activeCategory].map((skill, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                                initial: {\n                                                    opacity: 0,\n                                                    scale: 0.8\n                                                },\n                                                animate: {\n                                                    opacity: 1,\n                                                    scale: 1\n                                                },\n                                                exit: {\n                                                    opacity: 0,\n                                                    scale: 0.8\n                                                },\n                                                className: \"flex items-center gap-2 px-3 py-2 bg-gradient-to-r from-neural-purple/20 to-neural-pink/20 border border-neural-purple/30 rounded-lg text-white text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: skill\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                                        lineNumber: 228,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>removeSkill(activeCategory, skill),\n                                                        className: \"text-gray-400 hover:text-red-400 transition-colors\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Award_Certificate_CheckCircle_Clock_Code_Globe_Info_Lightbulb_Plus_Save_Sparkles_Target_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                                            lineNumber: 233,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                                        lineNumber: 229,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, skill, true, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                                lineNumber: 221,\n                                                columnNumber: 21\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 17\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8 text-gray-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Award_Certificate_CheckCircle_Clock_Code_Globe_Info_Lightbulb_Plus_Save_Sparkles_Target_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-8 w-8 mx-auto mb-2 opacity-50\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                                lineNumber: 240,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"No \",\n                                                    (_skillCategories_find2 = skillCategories.find((c)=>c.id === activeCategory)) === null || _skillCategories_find2 === void 0 ? void 0 : _skillCategories_find2.label.toLowerCase(),\n                                                    \" added yet\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm\",\n                                                children: \"Start typing to add your first skill\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                lineNumber: 207,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_common_ClientOnly__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ats_FieldIndicator__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    fieldName: \"skills_\".concat(activeCategory),\n                                    value: (_formData_skills_activeCategory1 = formData.skills[activeCategory]) === null || _formData_skills_activeCategory1 === void 0 ? void 0 : _formData_skills_activeCategory1.join(', '),\n                                    analysis: atsAnalysis === null || atsAnalysis === void 0 ? void 0 : (_atsAnalysis_fieldAnalysis = atsAnalysis.fieldAnalysis) === null || _atsAnalysis_fieldAnalysis === void 0 ? void 0 : _atsAnalysis_fieldAnalysis[\"skills_\".concat(activeCategory)],\n                                    showDetails: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                    lineNumber: 248,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                lineNumber: 247,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, activeCategory, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                        lineNumber: 155,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                    lineNumber: 154,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                lineNumber: 153,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Award_Certificate_CheckCircle_Clock_Code_Globe_Info_Lightbulb_Plus_Save_Sparkles_Target_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-5 w-5 text-neural-purple\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                        lineNumber: 263,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-white\",\n                                        children: \"Skills Optimization Assistant\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                        lineNumber: 264,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                lineNumber: 262,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowAIHelper(!showAIHelper),\n                                className: \"flex items-center gap-2 px-3 py-2 bg-neural-purple/20 hover:bg-neural-purple/30 border border-neural-purple/50 rounded-lg transition-colors text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Award_Certificate_CheckCircle_Clock_Code_Globe_Info_Lightbulb_Plus_Save_Sparkles_Target_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    showAIHelper ? 'Hide' : 'Show',\n                                    \" AI Helper\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                lineNumber: 266,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                        lineNumber: 261,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.AnimatePresence, {\n                        children: showAIHelper && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                height: 0\n                            },\n                            animate: {\n                                opacity: 1,\n                                height: 'auto'\n                            },\n                            exit: {\n                                opacity: 0,\n                                height: 0\n                            },\n                            className: \"bg-gradient-to-r from-neural-purple/10 to-neural-pink/10 border border-neural-purple/30 rounded-xl p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start gap-3 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-neural-purple/20 rounded-lg flex items-center justify-center flex-shrink-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Award_Certificate_CheckCircle_Clock_Code_Globe_Info_Lightbulb_Plus_Save_Sparkles_Target_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-5 w-5 text-neural-purple\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                            lineNumber: 284,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-semibold text-white mb-2\",\n                                                    children: \"Enhance Your Skills Section\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                                    lineNumber: 288,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-300 text-sm leading-relaxed mb-3\",\n                                                    children: \"Our AI can suggest relevant skills based on your experience and industry trends:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                                    lineNumber: 289,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"text-gray-300 text-sm space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-1.5 h-1.5 bg-neural-purple rounded-full\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                                                    lineNumber: 294,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                \"Identify missing technical skills for your field\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                                            lineNumber: 293,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-1.5 h-1.5 bg-neural-purple rounded-full\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                                                    lineNumber: 298,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                \"Suggest industry-relevant certifications\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                                            lineNumber: 297,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-1.5 h-1.5 bg-neural-purple rounded-full\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                                                    lineNumber: 302,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                \"Optimize skill keywords for ATS systems\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                                            lineNumber: 301,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                                    lineNumber: 292,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                    lineNumber: 283,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>onAISuggest === null || onAISuggest === void 0 ? void 0 : onAISuggest('skills'),\n                                            className: \"flex items-center gap-2 px-4 py-2 bg-neural-purple hover:bg-neural-purple/80 text-white rounded-lg text-sm font-medium transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Award_Certificate_CheckCircle_Clock_Code_Globe_Info_Lightbulb_Plus_Save_Sparkles_Target_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                                    lineNumber: 313,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"Generate Skill Suggestions\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                            lineNumber: 309,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowAIHelper(false),\n                                            className: \"px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg text-sm font-medium transition-colors\",\n                                            children: \"Close Helper\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                            lineNumber: 316,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                    lineNumber: 308,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                            lineNumber: 277,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                        lineNumber: 275,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                lineNumber: 260,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-900/40 backdrop-blur-sm rounded-xl p-4 border border-white/10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-3 gap-4 text-center\",\n                    children: skillCategories.map((category)=>{\n                        var _formData_skills_category_id;\n                        const IconComponent = category.icon;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-\".concat(category.color, \"/20 rounded-lg flex items-center justify-center mx-auto\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                        className: \"h-4 w-4 text-\".concat(category.color)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                        lineNumber: 336,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                    lineNumber: 335,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-lg font-bold text-\".concat(category.color),\n                                            children: ((_formData_skills_category_id = formData.skills[category.id]) === null || _formData_skills_category_id === void 0 ? void 0 : _formData_skills_category_id.length) || 0\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                            lineNumber: 339,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-gray-400\",\n                                            children: category.label\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                            lineNumber: 342,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                    lineNumber: 338,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, category.id, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                            lineNumber: 334,\n                            columnNumber: 15\n                        }, undefined);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                    lineNumber: 330,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                lineNumber: 329,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n        lineNumber: 76,\n        columnNumber: 5\n    }, undefined);\n};\n_s(EnhancedSkillsForm, \"3AMfliK8dwk4WCV+B2p/IUeFBIQ=\");\n_c = EnhancedSkillsForm;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (EnhancedSkillsForm);\nvar _c;\n$RefreshReg$(_c, \"EnhancedSkillsForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/resume/forms/SkillsForm.jsx\n"));

/***/ })

});
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "default-_rsc_node_modules_next_dist_build_webpack_loaders_next-app-loader_index_js_name_app_2-3fe258";
exports.ids = ["default-_rsc_node_modules_next_dist_build_webpack_loaders_next-app-loader_index_js_name_app_2-3fe258"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F(main)%2Faboutus%2Fpage&page=%2F(main)%2Faboutus%2Fpage&appPaths=%2F(main)%2Faboutus%2Fpage&pagePath=private-next-app-dir%2F(main)%2Faboutus%2Fpage.jsx&appDir=C%3A%5CProjects%5CNewBlinkFindAI%5Cfrontend-master%5Csrc%5Capp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&rootDir=C%3A%5CProjects%5CNewBlinkFindAI%5Cfrontend-master&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F(main)%2Faboutus%2Fpage&page=%2F(main)%2Faboutus%2Fpage&appPaths=%2F(main)%2Faboutus%2Fpage&pagePath=private-next-app-dir%2F(main)%2Faboutus%2Fpage.jsx&appDir=C%3A%5CProjects%5CNewBlinkFindAI%5Cfrontend-master%5Csrc%5Capp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&rootDir=C%3A%5CProjects%5CNewBlinkFindAI%5Cfrontend-master&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.jsx */ \"(rsc)/./src/app/layout.jsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module6 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page7 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/(main)/aboutus/page.jsx */ \"(rsc)/./src/app/(main)/aboutus/page.jsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        '(main)',\n        {\n        children: [\n        'aboutus',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page7, \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'not-found': [module4, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module5, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module6, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\layout.jsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/(main)/aboutus/page\",\n        pathname: \"/aboutus\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F(main)%2Faboutus%2Fpage&page=%2F(main)%2Faboutus%2Fpage&appPaths=%2F(main)%2Faboutus%2Fpage&pagePath=private-next-app-dir%2F(main)%2Faboutus%2Fpage.jsx&appDir=C%3A%5CProjects%5CNewBlinkFindAI%5Cfrontend-master%5Csrc%5Capp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&rootDir=C%3A%5CProjects%5CNewBlinkFindAI%5Cfrontend-master&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CNewBlinkFindAI%5C%5Cfrontend-master%5C%5Csrc%5C%5Capp%5C%5C(main)%5C%5Caboutus%5C%5Cpage.jsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CNewBlinkFindAI%5C%5Cfrontend-master%5C%5Csrc%5C%5Capp%5C%5C(main)%5C%5Caboutus%5C%5Cpage.jsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/(main)/aboutus/page.jsx */ \"(rsc)/./src/app/(main)/aboutus/page.jsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNQcm9qZWN0cyU1QyU1Q05ld0JsaW5rRmluZEFJJTVDJTVDZnJvbnRlbmQtbWFzdGVyJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDKG1haW4pJTVDJTVDYWJvdXR1cyU1QyU1Q3BhZ2UuanN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSw4S0FBdUgiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFByb2plY3RzXFxcXE5ld0JsaW5rRmluZEFJXFxcXGZyb250ZW5kLW1hc3RlclxcXFxzcmNcXFxcYXBwXFxcXChtYWluKVxcXFxhYm91dHVzXFxcXHBhZ2UuanN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CNewBlinkFindAI%5C%5Cfrontend-master%5C%5Csrc%5C%5Capp%5C%5C(main)%5C%5Caboutus%5C%5Cpage.jsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./src/app/(main)/aboutus/page.jsx":
/*!*****************************************!*\
  !*** ./src/app/(main)/aboutus/page.jsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Projects\\NewBlinkFindAI\\frontend-master\\src\\app\\(main)\\aboutus\\page.jsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CNewBlinkFindAI%5C%5Cfrontend-master%5C%5Csrc%5C%5Capp%5C%5C(main)%5C%5Caboutus%5C%5Cpage.jsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CNewBlinkFindAI%5C%5Cfrontend-master%5C%5Csrc%5C%5Capp%5C%5C(main)%5C%5Caboutus%5C%5Cpage.jsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/(main)/aboutus/page.jsx */ \"(ssr)/./src/app/(main)/aboutus/page.jsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNQcm9qZWN0cyU1QyU1Q05ld0JsaW5rRmluZEFJJTVDJTVDZnJvbnRlbmQtbWFzdGVyJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDKG1haW4pJTVDJTVDYWJvdXR1cyU1QyU1Q3BhZ2UuanN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSw4S0FBdUgiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFByb2plY3RzXFxcXE5ld0JsaW5rRmluZEFJXFxcXGZyb250ZW5kLW1hc3RlclxcXFxzcmNcXFxcYXBwXFxcXChtYWluKVxcXFxhYm91dHVzXFxcXHBhZ2UuanN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CNewBlinkFindAI%5C%5Cfrontend-master%5C%5Csrc%5C%5Capp%5C%5C(main)%5C%5Caboutus%5C%5Cpage.jsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/(main)/aboutus/page.jsx":
/*!*****************************************!*\
  !*** ./src/app/(main)/aboutus/page.jsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Briefcase_Building_CheckCircle_Globe_GraduationCap_Lightbulb_Shield_Star_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Briefcase,Building,CheckCircle,Globe,GraduationCap,Lightbulb,Shield,Star,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/graduation-cap.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Briefcase_Building_CheckCircle_Globe_GraduationCap_Lightbulb_Shield_Star_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Briefcase,Building,CheckCircle,Globe,GraduationCap,Lightbulb,Shield,Star,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Briefcase_Building_CheckCircle_Globe_GraduationCap_Lightbulb_Shield_Star_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Briefcase,Building,CheckCircle,Globe,GraduationCap,Lightbulb,Shield,Star,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Briefcase_Building_CheckCircle_Globe_GraduationCap_Lightbulb_Shield_Star_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Briefcase,Building,CheckCircle,Globe,GraduationCap,Lightbulb,Shield,Star,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Briefcase_Building_CheckCircle_Globe_GraduationCap_Lightbulb_Shield_Star_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Briefcase,Building,CheckCircle,Globe,GraduationCap,Lightbulb,Shield,Star,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Briefcase_Building_CheckCircle_Globe_GraduationCap_Lightbulb_Shield_Star_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Briefcase,Building,CheckCircle,Globe,GraduationCap,Lightbulb,Shield,Star,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Briefcase_Building_CheckCircle_Globe_GraduationCap_Lightbulb_Shield_Star_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Briefcase,Building,CheckCircle,Globe,GraduationCap,Lightbulb,Shield,Star,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/lightbulb.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Briefcase_Building_CheckCircle_Globe_GraduationCap_Lightbulb_Shield_Star_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Briefcase,Building,CheckCircle,Globe,GraduationCap,Lightbulb,Shield,Star,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Briefcase_Building_CheckCircle_Globe_GraduationCap_Lightbulb_Shield_Star_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Briefcase,Building,CheckCircle,Globe,GraduationCap,Lightbulb,Shield,Star,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Briefcase_Building_CheckCircle_Globe_GraduationCap_Lightbulb_Shield_Star_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Briefcase,Building,CheckCircle,Globe,GraduationCap,Lightbulb,Shield,Star,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Briefcase_Building_CheckCircle_Globe_GraduationCap_Lightbulb_Shield_Star_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Briefcase,Building,CheckCircle,Globe,GraduationCap,Lightbulb,Shield,Star,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Briefcase_Building_CheckCircle_Globe_GraduationCap_Lightbulb_Shield_Star_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Briefcase,Building,CheckCircle,Globe,GraduationCap,Lightbulb,Shield,Star,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nconst teamMembers = [\n    {\n        name: \"Abdullah Khan\",\n        role: \"Founder & CEO (AI & Android Lead)\",\n        bio: \"Passionate about transforming productivity through AI technology to help people achieve more with less effort.\",\n        image: \"/OurTeamImages/avatar/abdullah.jpg\",\n        linkedin: \"https://www.linkedin.com/in/abdullahkhanspn/\",\n        github: \"https://github.com/Abdullahkhanspn\"\n    },\n    {\n        name: \"Zainab Fatima\",\n        role: \"Co-Founder (AI/ML Specialist)\",\n        bio: \"Specializes in developing intelligent algorithms that understand user needs and provide personalized AI solutions.\",\n        image: \"/OurTeamImages/avatar/zainab.jpg\",\n        linkedin: \"https://www.linkedin.com/in/zainaboptique?\",\n        instagram: \"https://www.instagram.com/zainaboptique?\"\n    }\n];\nconst problemPoints = [\n    \"People are intense with tasks, stress and digital distractions\",\n    \"Most tools are too scattered or complicated to actually help\",\n    \"Businesses, especially small ones, can't afford big automation teams or software\",\n    \"Students, job seekers, freelancers all want help but don't know where to begin\"\n];\nconst solutionPoints = [\n    \"A simple tool to build your own assistant, no technical knowledge required\",\n    \"A place where businesses can use digital workers to handle their routine tasks\",\n    \"Personal tools for students and job seekers, like resume help, reminders, and content creation\",\n    \"A safe space to talk, whether for support, advice, or just some company\",\n    \"All of this, in one clean dashboard\"\n];\nconst useCases = [\n    {\n        icon: _barrel_optimize_names_ArrowRight_Award_Briefcase_Building_CheckCircle_Globe_GraduationCap_Lightbulb_Shield_Star_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        title: \"Student\",\n        description: \"Logs in and builds a bot that reminds her of study hours and suggests job openings\"\n    },\n    {\n        icon: _barrel_optimize_names_ArrowRight_Award_Briefcase_Building_CheckCircle_Globe_GraduationCap_Lightbulb_Shield_Star_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        title: \"Shop Owner\",\n        description: \"Uses it to reply to customer messages and send invoices automatically\"\n    },\n    {\n        icon: _barrel_optimize_names_ArrowRight_Award_Briefcase_Building_CheckCircle_Globe_GraduationCap_Lightbulb_Shield_Star_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        title: \"Young Graduate\",\n        description: \"Uses it to write his first resume and prepare for interviews\"\n    },\n    {\n        icon: _barrel_optimize_names_ArrowRight_Award_Briefcase_Building_CheckCircle_Globe_GraduationCap_Lightbulb_Shield_Star_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        title: \"Creator\",\n        description: \"Uses it to draft social media posts and edit scripts\"\n    }\n];\nconst AboutUs = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen py-16 pt-24 px-4 sm:px-6 lg:px-8 bg-gradient-to-b from-black to-[#0A0A0A]\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                    className: \"text-center mb-20\",\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.6\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-5xl md:text-7xl font-bold text-white mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-transparent bg-clip-text bg-gradient-to-r from-neural-purple to-neural-pink\",\n                                    children: \"BlinkFind\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 13\n                                }, undefined),\n                                \" \",\n                                \"Pvt. Ltd.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                            lineNumber: 91,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl md:text-2xl text-gray-300 max-w-4xl mx-auto leading-relaxed\",\n                            children: \"One platform for AI-powered productivity, business automation and daily life solutions.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                            lineNumber: 97,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap justify-center gap-4 mt-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-r from-neural-purple/20 to-neural-pink/20 backdrop-blur-md border border-neural-purple/30 rounded-lg px-6 py-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Briefcase_Building_CheckCircle_Globe_GraduationCap_Lightbulb_Shield_Star_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-5 w-5 text-neural-purple\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-white font-medium\",\n                                            children: \"Government Recognized Startup, Startup India Certified\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                lineNumber: 103,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                            lineNumber: 102,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                    lineNumber: 85,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.section, {\n                    className: \"mb-20\",\n                    initial: {\n                        opacity: 0,\n                        y: 40\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.6\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid lg:grid-cols-2 gap-12 items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Briefcase_Building_CheckCircle_Globe_GraduationCap_Lightbulb_Shield_Star_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-8 w-8 text-red-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-bold text-white\",\n                                                children: \"The Problem\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                                lineNumber: 124,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: problemPoints.map((point, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start gap-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 rounded-full bg-red-400 mt-2 flex-shrink-0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                                        lineNumber: 129,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-300 text-lg\",\n                                                        children: point\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                                        lineNumber: 130,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                                lineNumber: 128,\n                                                columnNumber: 19\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \"            \"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                lineNumber: 121,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative h-[300px] md:h-[400px] rounded-xl overflow-hidden border border-white/10\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        src: \"/web1.jpeg\",\n                                        alt: \"Digital workplace challenges and complexity\",\n                                        layout: \"fill\",\n                                        objectFit: \"cover\",\n                                        className: \"opacity-90\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-gradient-to-t from-black/60 to-transparent\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                lineNumber: 134,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                        lineNumber: 120,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                    lineNumber: 113,\n                    columnNumber: 9\n                }, undefined),\n                \"        \",\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.section, {\n                    className: \"mb-20\",\n                    initial: {\n                        opacity: 0,\n                        y: 40\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.6\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid lg:grid-cols-2 gap-12 items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"order-2 lg:order-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative h-[400px] rounded-2xl overflow-hidden border border-neural-purple/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            src: \"/ai.jpg\",\n                                            alt: \"AI Solutions and Technology\",\n                                            layout: \"fill\",\n                                            objectFit: \"cover\",\n                                            className: \"opacity-80\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute bottom-8 left-8 right-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3 mb-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Briefcase_Building_CheckCircle_Globe_GraduationCap_Lightbulb_Shield_Star_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"h-8 w-8 text-neural-purple\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                                            lineNumber: 166,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-2xl font-bold text-white\",\n                                                            children: \"Smart Solutions\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                                            lineNumber: 167,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                                    lineNumber: 165,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-200\",\n                                                    children: \"AI-powered tools that work together seamlessly\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                                    lineNumber: 169,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                lineNumber: 154,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"order-1 lg:order-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Briefcase_Building_CheckCircle_Globe_GraduationCap_Lightbulb_Shield_Star_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-8 w-8 text-neural-purple\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                                lineNumber: 175,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-bold text-white\",\n                                                children: \"Our Solution\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                                lineNumber: 176,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: solutionPoints.map((point, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start gap-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Briefcase_Building_CheckCircle_Globe_GraduationCap_Lightbulb_Shield_Star_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"w-6 h-6 text-neural-purple mt-1 flex-shrink-0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                                        lineNumber: 181,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-300 text-lg\",\n                                                        children: point\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                                        lineNumber: 182,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 19\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                lineNumber: 173,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                        lineNumber: 153,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                    lineNumber: 146,\n                    columnNumber: 9\n                }, undefined),\n                \"        \",\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.section, {\n                    className: \"mb-20\",\n                    initial: {\n                        opacity: 0,\n                        y: 40\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.6\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center gap-3 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Briefcase_Building_CheckCircle_Globe_GraduationCap_Lightbulb_Shield_Star_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-8 w-8 text-neural-purple\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-3xl font-bold text-white\",\n                                        children: \"Why BlinkFind Matters\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                lineNumber: 197,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                            lineNumber: 196,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative mb-12 h-[300px] rounded-2xl overflow-hidden border border-neural-purple/20\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    src: \"/ai1.jpg\",\n                                    alt: \"AI Technology and Innovation\",\n                                    layout: \"fill\",\n                                    objectFit: \"cover\",\n                                    className: \"opacity-70\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 bg-gradient-to-r from-black/80 via-black/50 to-black/80\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                    lineNumber: 212,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center max-w-4xl px-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-3xl md:text-4xl font-bold text-white mb-4\",\n                                                children: [\n                                                    \"Transforming Work with \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-transparent bg-clip-text bg-gradient-to-r from-neural-purple to-neural-pink\",\n                                                        children: \"Artificial Intelligence\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                                        lineNumber: 216,\n                                                        columnNumber: 42\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xl text-gray-200\",\n                                                children: \"Making advanced AI technology accessible to everyone, everywhere\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                            lineNumber: 204,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-3 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gradient-to-br from-green-500/10 to-emerald-500/10 backdrop-blur-md border border-green-500/20 rounded-xl p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Briefcase_Building_CheckCircle_Globe_GraduationCap_Lightbulb_Shield_Star_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-12 w-12 text-green-400 mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-bold text-white mb-3\",\n                                            children: \"Accessible AI\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                            lineNumber: 228,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-300\",\n                                            children: \"Making AI tools available to everyone, not just tech companies or those who can afford expensive solutions.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                    lineNumber: 226,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gradient-to-br from-blue-500/10 to-cyan-500/10 backdrop-blur-md border border-blue-500/20 rounded-xl p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Briefcase_Building_CheckCircle_Globe_GraduationCap_Lightbulb_Shield_Star_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-12 w-12 text-blue-400 mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                            lineNumber: 232,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-bold text-white mb-3\",\n                                            children: \"Real Impact\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-300\",\n                                            children: \"Helping small businesses compete with larger ones and giving individuals the tools to succeed.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                            lineNumber: 234,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gradient-to-br from-purple-500/10 to-pink-500/10 backdrop-blur-md border border-purple-500/20 rounded-xl p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Briefcase_Building_CheckCircle_Globe_GraduationCap_Lightbulb_Shield_Star_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-12 w-12 text-purple-400 mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-bold text-white mb-3\",\n                                            children: \"Future Ready\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                            lineNumber: 238,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-300\",\n                                            children: \"Preparing people for an AI-driven world by making these technologies easy to understand and use.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                            lineNumber: 239,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                            lineNumber: 225,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                    lineNumber: 189,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.section, {\n                    className: \"mb-20\",\n                    initial: {\n                        opacity: 0,\n                        y: 40\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.6\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-white mb-4\",\n                                    children: \"Real-World Use Cases\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                    lineNumber: 253,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 text-lg max-w-3xl mx-auto\",\n                                    children: \"See how BlinkFind transforms daily workflows for different users\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                    lineNumber: 254,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                            lineNumber: 252,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                            children: useCases.map((useCase, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                    className: \"bg-gray-900/50 backdrop-blur-md border border-white/10 rounded-xl p-6 hover:border-neural-purple/30 transition-all duration-300\",\n                                    whileHover: {\n                                        y: -5\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-neural-purple mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(useCase.icon, {\n                                                className: \"h-6 w-6\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                                lineNumber: 265,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                            lineNumber: 264,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-bold text-white mb-2\",\n                                            children: useCase.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-400 text-sm\",\n                                            children: useCase.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                            lineNumber: 258,\n                            columnNumber: 11\n                        }, undefined),\n                        \"        \"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                    lineNumber: 245,\n                    columnNumber: 9\n                }, undefined),\n                \"        \",\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.section, {\n                    className: \"mb-20\",\n                    initial: {\n                        opacity: 0,\n                        y: 40\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.6\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl md:text-4xl font-bold text-white mb-4\",\n                                    children: \"Meet Our Founders\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 text-lg max-w-2xl mx-auto\",\n                                    children: \"The visionaries behind BlinkFind's mission to democratize AI technology\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                    lineNumber: 281,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                            lineNumber: 279,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-5xl mx-auto space-y-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                    className: \"bg-gray-900/30 border border-gray-700/30 rounded-2xl p-8 md:p-10\",\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.5\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid md:grid-cols-4 gap-8 items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center md:text-left\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        src: \"/OurTeamImages/avatar/abdullah.jpg\",\n                                                        alt: \"Abdullah Khan\",\n                                                        width: 120,\n                                                        height: 120,\n                                                        className: \"rounded-full mx-auto md:mx-0 mb-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                                        lineNumber: 297,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-bold text-white mb-2\",\n                                                        children: \"Abdullah Khan\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                                        lineNumber: 304,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-neural-purple font-medium mb-1\",\n                                                        children: \"Founder & CEO\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                                        lineNumber: 305,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-400 text-sm mb-4\",\n                                                        children: \"AI & Android Lead\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                                        lineNumber: 306,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-3 justify-center md:justify-start\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                href: \"https://www.linkedin.com/in/abdullahkhanspn/\",\n                                                                target: \"_blank\",\n                                                                rel: \"noopener noreferrer\",\n                                                                className: \"text-gray-400 hover:text-white transition-colors\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    width: \"20\",\n                                                                    height: \"20\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    fill: \"currentColor\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                                                        lineNumber: 315,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                                                    lineNumber: 314,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                                                lineNumber: 308,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                href: \"https://github.com/Abdullahkhanspn\",\n                                                                target: \"_blank\",\n                                                                rel: \"noopener noreferrer\",\n                                                                className: \"text-gray-400 hover:text-white transition-colors\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    width: \"20\",\n                                                                    height: \"20\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    fill: \"currentColor\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                                                        lineNumber: 325,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                                                    lineNumber: 324,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                                                lineNumber: 318,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                                        lineNumber: 307,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"md:col-span-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                                                    className: \"text-gray-300 text-lg leading-relaxed border-l-3 border-gray-600 pl-6\",\n                                                    children: '\"When I started BlinkFind, I had a simple vision: make AI technology so intuitive that anyone can harness its power. Growing up, I saw brilliant minds held back by complex tools. Today, we\\'re breaking those barriers. Every line of code we write, every feature we build, is driven by one question – how can we make this simpler for the user?\"'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                                    lineNumber: 331,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                        lineNumber: 295,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                    lineNumber: 288,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                    className: \"bg-gray-900/30 border border-gray-700/30 rounded-2xl p-8 md:p-10\",\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.5,\n                                        delay: 0.2\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid md:grid-cols-4 gap-8 items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center md:text-left md:order-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        src: \"/OurTeamImages/avatar/zainab.jpg\",\n                                                        alt: \"Zainab Fatima\",\n                                                        width: 120,\n                                                        height: 120,\n                                                        className: \"rounded-full mx-auto md:mx-0 mb-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                                        lineNumber: 348,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-bold text-white mb-2\",\n                                                        children: \"Zainab Fatima\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                                        lineNumber: 355,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-neural-pink font-medium mb-1\",\n                                                        children: \"Co-Founder\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                                        lineNumber: 356,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-400 text-sm mb-4\",\n                                                        children: \"AI/ML Specialist\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                                        lineNumber: 357,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-3 justify-center md:justify-start\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                href: \"https://www.linkedin.com/in/zainaboptique?\",\n                                                                target: \"_blank\",\n                                                                rel: \"noopener noreferrer\",\n                                                                className: \"text-gray-400 hover:text-white transition-colors\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    width: \"20\",\n                                                                    height: \"20\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    fill: \"currentColor\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                                                        lineNumber: 366,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                                                    lineNumber: 365,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                                                lineNumber: 359,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                href: \"https://www.instagram.com/zainaboptique?\",\n                                                                target: \"_blank\",\n                                                                rel: \"noopener noreferrer\",\n                                                                className: \"text-gray-400 hover:text-white transition-colors\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    width: \"20\",\n                                                                    height: \"20\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    fill: \"currentColor\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                                                        lineNumber: 376,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                                                    lineNumber: 375,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                                                lineNumber: 369,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                                        lineNumber: 358,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                                lineNumber: 347,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"md:col-span-3 md:order-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                                                    className: \"text-gray-300 text-lg leading-relaxed border-l-3 border-gray-600 pl-6\",\n                                                    children: \"\\\"AI is not about replacing human intelligence; it's about amplifying it. My passion lies in creating algorithms that understand not just what users do, but why they do it. At BlinkFind, we're building AI that feels less like a tool and more like a thoughtful assistant who truly gets you.\\\"\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                                    lineNumber: 382,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                                lineNumber: 381,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                        lineNumber: 346,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                    lineNumber: 339,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                            lineNumber: 286,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                    lineNumber: 272,\n                    columnNumber: 9\n                }, undefined),\n                \"        \",\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.section, {\n                    className: \"mb-20\",\n                    initial: {\n                        opacity: 0,\n                        y: 40\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.6\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl md:text-4xl font-bold text-white mb-4\",\n                                    children: \"Our Mission & Vision\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                    lineNumber: 398,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 text-lg max-w-3xl mx-auto\",\n                                    children: \"Driving the future of AI-powered productivity through innovation and accessibility\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                    lineNumber: 399,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                            lineNumber: 397,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-6xl mx-auto grid lg:grid-cols-2 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                    className: \"bg-gray-900/50 border border-white/10 rounded-xl p-8 hover:border-neural-purple/30 transition-all duration-300\",\n                                    initial: {\n                                        opacity: 0,\n                                        x: -20\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    transition: {\n                                        duration: 0.5\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-neural-purple/20 p-3 rounded-lg mr-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Briefcase_Building_CheckCircle_Globe_GraduationCap_Lightbulb_Shield_Star_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"h-6 w-6 text-neural-purple\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                                        lineNumber: 415,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                                    lineNumber: 414,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-2xl font-bold text-white\",\n                                                    children: \"Mission\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                                    lineNumber: 417,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                            lineNumber: 413,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-300 text-lg leading-relaxed mb-6\",\n                                            children: \"To democratize AI technology and make powerful automation tools accessible to everyone, helping individuals and businesses achieve more with intelligent solutions.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                            lineNumber: 419,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                \"Democratize AI Technology\",\n                                                \"Empower Every Individual\",\n                                                \"Transform Business Operations\"\n                                            ].map((highlight, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-neural-purple rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                                            lineNumber: 430,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-400 text-sm\",\n                                                            children: highlight\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                                            lineNumber: 431,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                                    lineNumber: 429,\n                                                    columnNumber: 19\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                            lineNumber: 423,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                    lineNumber: 406,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                    className: \"bg-gray-900/50 border border-white/10 rounded-xl p-8 hover:border-neural-pink/30 transition-all duration-300\",\n                                    initial: {\n                                        opacity: 0,\n                                        x: 20\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    transition: {\n                                        duration: 0.5,\n                                        delay: 0.2\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-neural-pink/20 p-3 rounded-lg mr-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Briefcase_Building_CheckCircle_Globe_GraduationCap_Lightbulb_Shield_Star_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-6 w-6 text-neural-pink\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                                        lineNumber: 447,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                                    lineNumber: 446,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-2xl font-bold text-white\",\n                                                    children: \"Vision\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                                    lineNumber: 449,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                            lineNumber: 445,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-300 text-lg leading-relaxed mb-6\",\n                                            children: \"To become the leading platform for AI-powered productivity tools in emerging markets, bridging the gap between complex technology and everyday users.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                            lineNumber: 451,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                \"Global AI Leadership\",\n                                                \"Bridge Technology Gaps\",\n                                                \"Emerging Market Focus\"\n                                            ].map((highlight, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-neural-pink rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                                            lineNumber: 462,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-400 text-sm\",\n                                                            children: highlight\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                                            lineNumber: 463,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                                    lineNumber: 461,\n                                                    columnNumber: 19\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                            lineNumber: 455,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                                    lineNumber: 438,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                            lineNumber: 404,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n                    lineNumber: 390,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n            lineNumber: 82,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\(main)\\\\aboutus\\\\page.jsx\",\n        lineNumber: 81,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AboutUs);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/(main)/aboutus/page.jsx\n");

/***/ })

};
;
import Link from 'next/link'
import { Gith<PERSON>, <PERSON>edin, Mail, ExternalLink } from 'lucide-react'
import { SparklesIcon } from '@heroicons/react/24/solid'

const Footer = () => {
  return (
    <footer className="relative bg-gradient-to-t from-black via-gray-950 to-black border-t border-white/10 overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0 bg-[url('/grid.svg')] [mask-image:radial-gradient(ellipse_at_center,white,transparent)]" />
      </div>
      
      {/* Floating Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-10 left-10 w-32 h-32 bg-neural-purple/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-10 right-10 w-40 h-40 bg-neural-pink/10 rounded-full blur-3xl"></div>
      </div>

      <div className="relative mx-auto max-w-6xl px-6 py-16">
        {/* Main Footer Content */}
        <div className="text-center space-y-8">
          {/* Logo and Tagline */}
          <div className="space-y-4">
            <div className="flex items-center justify-center gap-3">
              <div className="relative">
                <SparklesIcon className="h-8 w-8 text-neural-purple animate-pulse" />
                <div className="absolute inset-0 bg-neural-purple/20 rounded-full blur-md"></div>
              </div>
              <span className="font-mono font-bold text-2xl text-white">
                Blink<span className="text-neural-purple">Find</span>
              </span>
            </div>
            <p className="text-gray-400 text-lg max-w-2xl mx-auto">
              Turn hours of work into minutes with AI-powered solutions for your everyday tasks.
            </p>
          </div>

          {/* Navigation Links */}
          <div className="flex flex-wrap items-center justify-center gap-8 text-sm">
            <Link
              href="/resume-builder"
              className="flex items-center gap-2 text-gray-400 hover:text-neural-purple transition-colors duration-300 hover:scale-105"
            >
              <SparklesIcon className="h-4 w-4" />
              AI Resume Builder
            </Link>
            <Link
              href="/contact"
              className="text-gray-400 hover:text-neural-purple transition-colors duration-300 hover:scale-105"
            >
              Contact
            </Link>
            <Link
              href="/login"
              className="text-gray-400 hover:text-neural-purple transition-colors duration-300 hover:scale-105"
            >
              Sign In
            </Link>
            <Link
              href="/signup"
              className="text-gray-400 hover:text-neural-purple transition-colors duration-300 hover:scale-105"
            >
              Sign Up
            </Link>
            <Link
              href="/dashboard"
              className="text-gray-400 hover:text-neural-purple transition-colors duration-300 hover:scale-105"
            >
              Dashboard
            </Link>
            <Link
              href="/privacy-policy"
              className="text-gray-400 hover:text-neural-purple transition-colors duration-300 hover:scale-105"
            >
              Privacy Policy
            </Link>
          </div>

          {/* Social Links */}
          <div className="flex items-center justify-center gap-6">
            <Link 
              href="mailto:<EMAIL>" 
              className="group relative p-3 bg-gray-900/50 backdrop-blur-sm rounded-full border border-white/10 hover:border-neural-purple/50 transition-all duration-300"
            >
              <Mail className="h-5 w-5 text-gray-400 group-hover:text-neural-purple transition-colors" />
              <div className="absolute inset-0 bg-neural-purple/10 rounded-full opacity-0 group-hover:opacity-100 transition-opacity blur-xl"></div>
            </Link>
            <Link 
              href="#" 
              className="group relative p-3 bg-gray-900/50 backdrop-blur-sm rounded-full border border-white/10 hover:border-neural-purple/50 transition-all duration-300"
            >
              <Github className="h-5 w-5 text-gray-400 group-hover:text-neural-purple transition-colors" />
              <div className="absolute inset-0 bg-neural-purple/10 rounded-full opacity-0 group-hover:opacity-100 transition-opacity blur-xl"></div>
            </Link>
            <Link 
              href="#" 
              className="group relative p-3 bg-gray-900/50 backdrop-blur-sm rounded-full border border-white/10 hover:border-neural-purple/50 transition-all duration-300"
            >
              <Linkedin className="h-5 w-5 text-gray-400 group-hover:text-neural-purple transition-colors" />
              <div className="absolute inset-0 bg-neural-purple/10 rounded-full opacity-0 group-hover:opacity-100 transition-opacity blur-xl"></div>
            </Link>
          </div>

          {/* Divider */}
          <div className="flex items-center gap-4">
            <div className="flex-1 h-px bg-gradient-to-r from-transparent via-white/20 to-transparent"></div>
            <div className="px-4">
              <SparklesIcon className="h-4 w-4 text-neural-purple/50" />
            </div>
            <div className="flex-1 h-px bg-gradient-to-r from-transparent via-white/20 to-transparent"></div>
          </div>

          {/* Copyright */}          <div className="space-y-2">
            <p className="text-xs text-gray-500">
              &copy; {new Date().getFullYear()} BlinkFind. All rights reserved.
            </p>
            <p className="text-xs text-gray-600">
              Made By Team BlinkFind
            </p>
          </div>
        </div>
      </div>
    </footer>
  )
}

export default Footer
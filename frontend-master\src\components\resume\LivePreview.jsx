'use client';
import { motion } from 'framer-motion';
import { <PERSON>, EyeOff, Maximize2, FileText } from 'lucide-react';

const LivePreview = ({ 
  formData, 
  selectedTemplate = 'classic_ats',
  isVisible = true,
  onToggleVisibility,
  onOpenFullscreen
}) => {
  // Check if we have enough data to show a meaningful preview
  const hasPreviewData = formData && formData.personal && (
    formData.personal.firstName || 
    formData.personal.email || 
    formData.experience?.length > 0 ||
    formData.education?.length > 0
  );

  // Render minimal resume preview
  const renderPreview = () => {
    if (!hasPreviewData) {
      return (
        <div className="flex items-center justify-center h-full text-gray-500">
          <div className="text-center p-8">
            <FileText className="h-12 w-12 mx-auto mb-3 opacity-30" />
            <p className="text-sm font-medium mb-1">Live Preview</p>
            <p className="text-xs opacity-70">Start filling the form to see your resume</p>
          </div>
        </div>
      );
    }

    return (
      <div className="p-4 space-y-3 text-xs leading-tight">
        {/* Header */}
        {(formData.personal.firstName || formData.personal.lastName) && (
          <div className="text-center border-b border-gray-300 pb-3">
            <h1 className="text-sm font-bold text-gray-900 mb-1">
              {formData.personal.firstName} {formData.personal.lastName}
            </h1>
            <div className="text-xs text-gray-600 space-y-0.5">
              {formData.personal.email && <div>{formData.personal.email}</div>}
              {formData.personal.phone && <div>{formData.personal.phone}</div>}
              {formData.personal.location && <div>{formData.personal.location}</div>}
            </div>
          </div>
        )}

        {/* Summary */}
        {formData.personal.summary && (
          <div>
            <h2 className="text-xs font-bold text-gray-900 mb-1 uppercase">Summary</h2>
            <p className="text-xs text-gray-700 leading-relaxed">
              {formData.personal.summary.length > 120 
                ? `${formData.personal.summary.substring(0, 120)}...` 
                : formData.personal.summary}
            </p>
          </div>
        )}

        {/* Experience */}
        {formData.experience && formData.experience.length > 0 && (
          <div>
            <h2 className="text-xs font-bold text-gray-900 mb-1 uppercase">Experience</h2>
            <div className="space-y-2">
              {formData.experience.slice(0, 2).map((exp, index) => (
                <div key={index}>
                  <div className="flex justify-between items-start">
                    <h3 className="text-xs font-semibold text-gray-900">{exp.title}</h3>
                    <span className="text-xs text-gray-500">
                      {exp.startDate} - {exp.current ? 'Present' : exp.endDate}
                    </span>
                  </div>
                  <div className="text-xs text-gray-600 font-medium">{exp.company}</div>
                  {exp.description && (
                    <p className="text-xs text-gray-600 mt-0.5">
                      {exp.description.length > 80 
                        ? `${exp.description.substring(0, 80)}...` 
                        : exp.description}
                    </p>
                  )}
                </div>
              ))}
              {formData.experience.length > 2 && (
                <p className="text-xs text-gray-500 italic">
                  +{formData.experience.length - 2} more experience{formData.experience.length > 3 ? 's' : ''}
                </p>
              )}
            </div>
          </div>
        )}

        {/* Education */}
        {formData.education && formData.education.length > 0 && (
          <div>
            <h2 className="text-xs font-bold text-gray-900 mb-1 uppercase">Education</h2>
            <div className="space-y-1">
              {formData.education.slice(0, 2).map((edu, index) => (
                <div key={index}>
                  <div className="flex justify-between items-start">
                    <h3 className="text-xs font-semibold text-gray-900">
                      {edu.degree} {edu.field && `in ${edu.field}`}
                    </h3>
                    <span className="text-xs text-gray-500">{edu.graduationDate}</span>
                  </div>
                  <div className="text-xs text-gray-600">{edu.institution}</div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Skills */}
        {formData.skills && (formData.skills.technical?.length > 0 || formData.skills.languages?.length > 0) && (
          <div>
            <h2 className="text-xs font-bold text-gray-900 mb-1 uppercase">Skills</h2>
            <div className="space-y-1">
              {formData.skills.technical && formData.skills.technical.length > 0 && (
                <div>
                  <span className="text-xs font-semibold text-gray-900">Technical: </span>
                  <span className="text-xs text-gray-700">
                    {formData.skills.technical.slice(0, 6).join(', ')}
                    {formData.skills.technical.length > 6 && '...'}
                  </span>
                </div>
              )}
              {formData.skills.languages && formData.skills.languages.length > 0 && (
                <div>
                  <span className="text-xs font-semibold text-gray-900">Languages: </span>
                  <span className="text-xs text-gray-700">
                    {formData.skills.languages.join(', ')}
                  </span>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="bg-gray-800/50 backdrop-blur-sm rounded-xl border border-gray-700 h-full flex flex-col">
      {/* Simple Header */}
      <div className="flex items-center justify-between p-3 border-b border-gray-700 flex-shrink-0">
        <div className="flex items-center gap-2">
          <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
          <span className="text-sm font-medium text-white">Live Preview</span>
        </div>
        
        <div className="flex items-center gap-1">
          {isVisible && onOpenFullscreen && (
            <button
              onClick={onOpenFullscreen}
              className="p-1.5 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors"
              title="Fullscreen Preview"
            >
              <Maximize2 className="h-3.5 w-3.5" />
            </button>
          )}
          <button
            onClick={onToggleVisibility}
            className="p-1.5 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors"
            title={isVisible ? 'Hide Preview' : 'Show Preview'}
          >
            {isVisible ? <EyeOff className="h-3.5 w-3.5" /> : <Eye className="h-3.5 w-3.5" />}
          </button>
        </div>
      </div>

      {/* Preview Content */}
      <div className="flex-1 overflow-hidden">
        {isVisible ? (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="h-full overflow-y-auto"
          >
            <div className="bg-white m-2 rounded-lg shadow-sm min-h-[calc(100%-1rem)]">
              {renderPreview()}
            </div>
          </motion.div>
        ) : (
          <div className="flex items-center justify-center h-full text-gray-500">
            <div className="text-center">
              <Eye className="h-8 w-8 mx-auto mb-2 opacity-30" />
              <p className="text-xs">Preview hidden</p>
            </div>
          </div>
        )}
      </div>

      {/* Simple Footer */}
      {isVisible && hasPreviewData && (
        <div className="p-2 border-t border-gray-700 bg-gray-800/30 flex-shrink-0">
          <div className="flex items-center justify-between text-xs text-gray-400">
            <span>Auto-updating</span>
            <span className="capitalize">{selectedTemplate.replace('_', ' ')}</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default LivePreview;

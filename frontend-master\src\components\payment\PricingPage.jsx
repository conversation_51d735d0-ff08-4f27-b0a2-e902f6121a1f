'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Check, 
  Star, 
  Zap, 
  Crown, 
  Users, 
  Globe, 
  CreditCard,
  Smartphone,
  Shield,
  ArrowRight,
  Sparkles
} from 'lucide-react';
import { PRICING_PLANS, PRICING_PLANS_INR, ONE_TIME_PURCHASES, formatPrice, calculateYearlySavings } from '@/config/pricing';
import PaymentModal from './PaymentModal';

const PricingPage = ({ userRegion = 'US', currentPlan = 'free' }) => {
  const [billingInterval, setBillingInterval] = useState('monthly');
  const [selectedPlan, setSelectedPlan] = useState(null);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [isIndianUser, setIsIndianUser] = useState(false);

  useEffect(() => {
    setIsIndianUser(userRegion === 'IN' || userRegion === 'India');
  }, [userRegion]);

  const pricingPlans = isIndianUser ? { ...PRICING_PLANS, ...PRICING_PLANS_INR } : PRICING_PLANS;
  const currency = isIndianUser ? 'INR' : 'USD';

  const handlePlanSelect = (plan) => {
    if (plan.id === 'free') return;
    setSelectedPlan(plan);
    setShowPaymentModal(true);
  };

  const getPlanIcon = (planId) => {
    switch (planId) {
      case 'free': return <Zap className="h-6 w-6" />;
      case 'basic': return <Star className="h-6 w-6" />;
      case 'premium': return <Crown className="h-6 w-6" />;
      case 'enterprise': return <Users className="h-6 w-6" />;
      default: return <Zap className="h-6 w-6" />;
    }
  };

  const getPlanPrice = (plan) => {
    if (plan.id === 'free') return 'Free';
    
    const price = billingInterval === 'yearly' ? plan.yearlyPrice : plan.price;
    const formattedPrice = formatPrice(price, currency);
    
    if (billingInterval === 'yearly') {
      const monthlyEquivalent = formatPrice(price / 12, currency);
      return (
        <div>
          <span className="text-3xl font-bold">{formattedPrice}</span>
          <span className="text-gray-400 text-sm ml-2">/year</span>
          <div className="text-sm text-gray-500">({monthlyEquivalent}/month)</div>
        </div>
      );
    }
    
    return (
      <div>
        <span className="text-3xl font-bold">{formattedPrice}</span>
        <span className="text-gray-400 text-sm">/month</span>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 py-16 px-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="inline-flex items-center gap-2 px-4 py-2 bg-neural-purple/20 border border-neural-purple/30 rounded-full text-neural-purple text-sm font-medium mb-6"
          >
            <Sparkles className="h-4 w-4" />
            Choose Your Plan
          </motion.div>
          
          <motion.h1
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="text-4xl md:text-6xl font-bold text-white mb-6"
          >
            Unlock Your Career
            <span className="bg-gradient-to-r from-neural-purple to-neural-pink bg-clip-text text-transparent"> Potential</span>
          </motion.h1>
          
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="text-xl text-gray-300 max-w-3xl mx-auto mb-8"
          >
            Choose the perfect plan to accelerate your job search with AI-powered resume building, 
            ATS optimization, and career enhancement tools.
          </motion.p>

          {/* Billing Toggle */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="flex items-center justify-center gap-4 mb-12"
          >
            <span className={`text-sm font-medium ${billingInterval === 'monthly' ? 'text-white' : 'text-gray-400'}`}>
              Monthly
            </span>
            <button
              onClick={() => setBillingInterval(billingInterval === 'monthly' ? 'yearly' : 'monthly')}
              className={`relative w-14 h-7 rounded-full transition-colors ${
                billingInterval === 'yearly' ? 'bg-neural-purple' : 'bg-gray-600'
              }`}
            >
              <div
                className={`absolute top-1 w-5 h-5 bg-white rounded-full transition-transform ${
                  billingInterval === 'yearly' ? 'translate-x-8' : 'translate-x-1'
                }`}
              />
            </button>
            <span className={`text-sm font-medium ${billingInterval === 'yearly' ? 'text-white' : 'text-gray-400'}`}>
              Yearly
            </span>
            {billingInterval === 'yearly' && (
              <span className="px-2 py-1 bg-green-500/20 text-green-400 text-xs rounded-full border border-green-500/30">
                Save up to 17%
              </span>
            )}
          </motion.div>
        </div>

        {/* Pricing Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
          {Object.values(pricingPlans).map((plan, index) => (
            <motion.div
              key={plan.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 * index }}
              className={`relative bg-gray-800/50 backdrop-blur-sm rounded-2xl p-6 border transition-all duration-300 hover:scale-105 ${
                plan.popular 
                  ? 'border-neural-purple shadow-lg shadow-neural-purple/20' 
                  : 'border-gray-700 hover:border-gray-600'
              } ${
                currentPlan === plan.id ? 'ring-2 ring-neural-purple' : ''
              }`}
            >
              {/* Popular Badge */}
              {plan.popular && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <span className="px-3 py-1 bg-gradient-to-r from-neural-purple to-neural-pink text-white text-xs font-semibold rounded-full">
                    Most Popular
                  </span>
                </div>
              )}

              {/* Current Plan Badge */}
              {currentPlan === plan.id && (
                <div className="absolute -top-3 right-4">
                  <span className="px-3 py-1 bg-green-500 text-white text-xs font-semibold rounded-full">
                    Current Plan
                  </span>
                </div>
              )}

              {/* Plan Header */}
              <div className="text-center mb-6">
                <div className={`w-12 h-12 mx-auto mb-4 rounded-xl flex items-center justify-center ${
                  plan.color === 'gray' ? 'bg-gray-700' :
                  plan.color === 'neural-purple' ? 'bg-neural-purple/20' :
                  plan.color === 'neural-pink' ? 'bg-neural-pink/20' :
                  'bg-gradient-to-r from-neural-purple to-neural-pink'
                }`}>
                  <div className={`${
                    plan.color === 'gray' ? 'text-gray-300' :
                    plan.color === 'neural-purple' ? 'text-neural-purple' :
                    plan.color === 'neural-pink' ? 'text-neural-pink' :
                    'text-white'
                  }`}>
                    {getPlanIcon(plan.id)}
                  </div>
                </div>
                
                <h3 className="text-xl font-bold text-white mb-2">{plan.name}</h3>
                <div className="text-white">
                  {getPlanPrice(plan)}
                </div>
                
                {billingInterval === 'yearly' && plan.yearlyPrice && plan.price && (
                  <div className="mt-2">
                    {(() => {
                      const { savings, percentage } = calculateYearlySavings(plan.price, plan.yearlyPrice);
                      return (
                        <span className="text-green-400 text-sm font-medium">
                          Save {formatPrice(savings, currency)} ({percentage}%)
                        </span>
                      );
                    })()}
                  </div>
                )}
              </div>

              {/* Features */}
              <div className="space-y-3 mb-8">
                {plan.features.map((feature, featureIndex) => (
                  <div key={featureIndex} className="flex items-start gap-3">
                    <Check className="h-4 w-4 text-green-400 mt-0.5 flex-shrink-0" />
                    <span className="text-gray-300 text-sm">{feature}</span>
                  </div>
                ))}
              </div>

              {/* CTA Button */}
              <button
                onClick={() => handlePlanSelect(plan)}
                disabled={currentPlan === plan.id}
                className={`w-full py-3 px-4 rounded-xl font-semibold transition-all duration-300 ${
                  currentPlan === plan.id
                    ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                    : plan.id === 'free'
                    ? 'bg-gray-700 hover:bg-gray-600 text-white'
                    : plan.popular
                    ? 'bg-gradient-to-r from-neural-purple to-neural-pink hover:opacity-90 text-white shadow-lg'
                    : 'bg-white hover:bg-gray-100 text-gray-900'
                }`}
              >
                {currentPlan === plan.id ? 'Current Plan' : 
                 plan.id === 'free' ? 'Get Started Free' : 
                 'Upgrade Now'}
              </button>
            </motion.div>
          ))}
        </div>

        {/* One-time Purchases */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="text-center mb-12"
        >
          <h2 className="text-3xl font-bold text-white mb-4">
            Or Choose One-Time Purchases
          </h2>
          <p className="text-gray-400 mb-8">
            Perfect for occasional use or specific projects
          </p>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
            {Object.values(ONE_TIME_PURCHASES).map((purchase, index) => (
              <motion.div
                key={purchase.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.6 + 0.1 * index }}
                className={`bg-gray-800/50 backdrop-blur-sm rounded-xl p-6 border transition-all duration-300 hover:scale-105 ${
                  purchase.popular ? 'border-neural-purple' : 'border-gray-700'
                }`}
              >
                {purchase.popular && (
                  <div className="text-center mb-4">
                    <span className="px-3 py-1 bg-neural-purple/20 text-neural-purple text-xs font-semibold rounded-full border border-neural-purple/30">
                      Best Value
                    </span>
                  </div>
                )}

                <div className="text-center mb-4">
                  <h3 className="text-lg font-bold text-white mb-2">{purchase.name}</h3>
                  <div className="text-2xl font-bold text-white">
                    {formatPrice(isIndianUser ? purchase.priceINR : purchase.price, currency)}
                  </div>
                </div>

                <div className="space-y-2 mb-6">
                  {purchase.features.map((feature, featureIndex) => (
                    <div key={featureIndex} className="flex items-center gap-2">
                      <Check className="h-4 w-4 text-green-400" />
                      <span className="text-gray-300 text-sm">{feature}</span>
                    </div>
                  ))}
                </div>

                <button
                  onClick={() => {
                    setSelectedPlan(purchase);
                    setShowPaymentModal(true);
                  }}
                  className="w-full py-2 px-4 bg-neural-purple hover:bg-neural-purple/80 text-white rounded-lg font-medium transition-colors"
                >
                  Purchase Now
                </button>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Payment Methods */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7 }}
          className="text-center"
        >
          <h3 className="text-xl font-semibold text-white mb-6">Secure Payment Methods</h3>
          <div className="flex items-center justify-center gap-8 flex-wrap">
            <div className="flex items-center gap-2 text-gray-400">
              <CreditCard className="h-5 w-5" />
              <span>Credit/Debit Cards</span>
            </div>
            {isIndianUser && (
              <>
                <div className="flex items-center gap-2 text-gray-400">
                  <Smartphone className="h-5 w-5" />
                  <span>UPI</span>
                </div>
                <div className="flex items-center gap-2 text-gray-400">
                  <Globe className="h-5 w-5" />
                  <span>Net Banking</span>
                </div>
              </>
            )}
            <div className="flex items-center gap-2 text-gray-400">
              <Shield className="h-5 w-5" />
              <span>256-bit SSL Encrypted</span>
            </div>
          </div>
        </motion.div>
      </div>

      {/* Payment Modal */}
      <AnimatePresence>
        {showPaymentModal && selectedPlan && (
          <PaymentModal
            plan={selectedPlan}
            billingInterval={billingInterval}
            currency={currency}
            isIndianUser={isIndianUser}
            onClose={() => {
              setShowPaymentModal(false);
              setSelectedPlan(null);
            }}
          />
        )}
      </AnimatePresence>
    </div>
  );
};

export default PricingPage;

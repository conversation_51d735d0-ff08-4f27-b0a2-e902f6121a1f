import { useEffect, useCallback, useRef } from 'react';
import { PERFORMANCE_CONFIG } from '@/config/production';

const usePerformanceMonitoring = () => {
  const metricsRef = useRef({
    pageLoadTime: 0,
    aiResponseTimes: [],
    renderTimes: [],
    errorCount: 0,
    userInteractions: 0
  });

  // Track page load time
  useEffect(() => {
    const startTime = performance.now();
    
    const handleLoad = () => {
      const loadTime = performance.now() - startTime;
      metricsRef.current.pageLoadTime = loadTime;
      
      if (PERFORMANCE_CONFIG.performanceTracking) {
        console.log(`Page load time: ${loadTime.toFixed(2)}ms`);
      }
    };

    if (document.readyState === 'complete') {
      handleLoad();
    } else {
      window.addEventListener('load', handleLoad);
      return () => window.removeEventListener('load', handleLoad);
    }
  }, []);

  // Track AI response times
  const trackAIResponse = useCallback((startTime, endTime, operation) => {
    const responseTime = endTime - startTime;
    metricsRef.current.aiResponseTimes.push({
      operation,
      time: responseTime,
      timestamp: Date.now()
    });

    if (PERFORMANCE_CONFIG.performanceTracking) {
      console.log(`AI ${operation} response time: ${responseTime.toFixed(2)}ms`);
    }

    // Keep only last 50 entries
    if (metricsRef.current.aiResponseTimes.length > 50) {
      metricsRef.current.aiResponseTimes = metricsRef.current.aiResponseTimes.slice(-50);
    }
  }, []);

  // Track render times
  const trackRender = useCallback((componentName, renderTime) => {
    metricsRef.current.renderTimes.push({
      component: componentName,
      time: renderTime,
      timestamp: Date.now()
    });

    if (PERFORMANCE_CONFIG.performanceTracking && renderTime > 100) {
      console.warn(`Slow render detected: ${componentName} took ${renderTime.toFixed(2)}ms`);
    }

    // Keep only last 30 entries
    if (metricsRef.current.renderTimes.length > 30) {
      metricsRef.current.renderTimes = metricsRef.current.renderTimes.slice(-30);
    }
  }, []);

  // Track errors
  const trackError = useCallback((error, context = '') => {
    metricsRef.current.errorCount++;
    
    if (PERFORMANCE_CONFIG.performanceTracking) {
      console.error(`Error tracked: ${error.message} in ${context}`);
    }

    // In production, you might want to send this to an error tracking service
    if (process.env.NODE_ENV === 'production') {
      // Example: Send to error tracking service
      // errorTrackingService.captureException(error, { context });
    }
  }, []);

  // Track user interactions
  const trackInteraction = useCallback((action, details = {}) => {
    metricsRef.current.userInteractions++;
    
    if (PERFORMANCE_CONFIG.performanceTracking) {
      console.log(`User interaction: ${action}`, details);
    }
  }, []);

  // Get performance summary
  const getPerformanceSummary = useCallback(() => {
    const metrics = metricsRef.current;
    const avgAIResponseTime = metrics.aiResponseTimes.length > 0 
      ? metrics.aiResponseTimes.reduce((sum, item) => sum + item.time, 0) / metrics.aiResponseTimes.length
      : 0;
    
    const avgRenderTime = metrics.renderTimes.length > 0
      ? metrics.renderTimes.reduce((sum, item) => sum + item.time, 0) / metrics.renderTimes.length
      : 0;

    return {
      pageLoadTime: metrics.pageLoadTime,
      averageAIResponseTime: avgAIResponseTime,
      averageRenderTime: avgRenderTime,
      errorCount: metrics.errorCount,
      userInteractions: metrics.userInteractions,
      totalAIRequests: metrics.aiResponseTimes.length,
      totalRenders: metrics.renderTimes.length
    };
  }, []);

  // Performance optimization suggestions
  const getOptimizationSuggestions = useCallback(() => {
    const summary = getPerformanceSummary();
    const suggestions = [];

    if (summary.pageLoadTime > 3000) {
      suggestions.push('Consider optimizing initial bundle size - page load time is high');
    }

    if (summary.averageAIResponseTime > 5000) {
      suggestions.push('AI responses are slow - consider implementing request caching');
    }

    if (summary.averageRenderTime > 50) {
      suggestions.push('Component renders are slow - consider memoization');
    }

    if (summary.errorCount > 5) {
      suggestions.push('High error count detected - review error handling');
    }

    return suggestions;
  }, [getPerformanceSummary]);

  // Cleanup old metrics periodically
  useEffect(() => {
    const cleanup = setInterval(() => {
      const now = Date.now();
      const oneHourAgo = now - (60 * 60 * 1000);

      // Clean up old AI response times
      metricsRef.current.aiResponseTimes = metricsRef.current.aiResponseTimes.filter(
        item => item.timestamp > oneHourAgo
      );

      // Clean up old render times
      metricsRef.current.renderTimes = metricsRef.current.renderTimes.filter(
        item => item.timestamp > oneHourAgo
      );
    }, 5 * 60 * 1000); // Every 5 minutes

    return () => clearInterval(cleanup);
  }, []);

  return {
    trackAIResponse,
    trackRender,
    trackError,
    trackInteraction,
    getPerformanceSummary,
    getOptimizationSuggestions
  };
};

export default usePerformanceMonitoring;

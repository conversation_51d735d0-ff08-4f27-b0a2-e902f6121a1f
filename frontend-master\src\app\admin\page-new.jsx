'use client';

import { AdminProvider, useAdmin } from '@/contexts/AdminContext';
import AdminLogin from '@/components/admin/AdminLogin';
import AdminDashboard from '@/components/admin/AdminDashboard';
import { AlertTriangle } from 'lucide-react';

// Protected Admin Component
function ProtectedAdminPage() {
  const { user, isAdmin, loading, error } = useAdmin();

  // Loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center">
        <div className="text-center">
          <div className="w-12 h-12 border-4 border-purple-400/30 border-t-purple-400 rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-white text-lg">Authenticating...</p>
        </div>
      </div>
    );
  }

  // Not authenticated - show login
  if (!user) {
    return <AdminLogin />;
  }

  // Authenticated but not admin - show access denied
  if (user && !isAdmin) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center p-4">
        <div className="max-w-md text-center">
          <div className="bg-red-500/10 backdrop-blur-sm border border-red-500/20 rounded-xl p-8">
            <AlertTriangle className="w-16 h-16 text-red-400 mx-auto mb-4" />
            <h1 className="text-2xl font-bold text-white mb-4">Access Denied</h1>
            <p className="text-red-300 mb-6">
              You don't have administrator privileges to access this page.
            </p>
            <p className="text-gray-400 text-sm mb-6">
              Logged in as: {user.email}
            </p>
            <button
              onClick={() => window.location.href = '/'}
              className="bg-purple-500 hover:bg-purple-600 text-white px-6 py-2 rounded-lg transition-colors"
            >
              Return to Homepage
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Authenticated and admin - show dashboard
  return <AdminDashboard />;
}

export default function AdminPage() {
  return (
    <AdminProvider>
      <ProtectedAdminPage />
    </AdminProvider>
  );
}

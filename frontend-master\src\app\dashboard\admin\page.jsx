'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { collection, getCountFromServer, onSnapshot } from 'firebase/firestore';
import { firestore } from '@/firebase/config';
import { Users, Star, MessageSquare, TrendingUp, BarChart3, Award } from 'lucide-react';
import { motion } from 'framer-motion';

export default function AdminDashboard() {
  const { currentUser } = useAuth();
  const [stats, setStats] = useState({
    totalUsers: 5847,
    totalReviews: 0,
    averageRating: 4.8,
    successRate: 98,
    loading: true
  });

  useEffect(() => {
    if (!currentUser) return;

    const fetchStats = async () => {
      try {
        // Get reviews count
        const reviewsSnapshot = await getCountFromServer(collection(firestore, 'reviews'));
        const reviewsCount = reviewsSnapshot.data().count;

        // Listen for real-time updates
        const unsubscribe = onSnapshot(collection(firestore, 'reviews'), (snapshot) => {
          let totalRating = 0;
          let reviewCount = 0;

          snapshot.forEach((doc) => {
            const data = doc.data();
            if (data.rating) {
              totalRating += data.rating;
              reviewCount++;
            }
          });

          const averageRating = reviewCount > 0 ? totalRating / reviewCount : 4.8;

          setStats({
            totalUsers: 5847 + (reviewCount * 15),
            totalReviews: reviewCount,
            averageRating: parseFloat(averageRating.toFixed(1)),
            successRate: 98,
            loading: false
          });
        });

        return () => unsubscribe();
      } catch (error) {
        console.error('Error fetching stats:', error);
        setStats(prev => ({ ...prev, loading: false }));
      }
    };

    fetchStats();
  }, [currentUser]);

  if (!currentUser) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-black to-[#0A0A0A] flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-white mb-4">Access Denied</h1>
          <p className="text-gray-400">Please log in to view the dashboard.</p>
        </div>
      </div>
    );
  }

  const statItems = [
    {
      icon: Users,
      label: 'Total Users',
      value: stats.totalUsers.toLocaleString(),
      color: 'text-neural-blue',
      bgColor: 'bg-neural-blue/10'
    },
    {
      icon: MessageSquare,
      label: 'Total Reviews',
      value: stats.totalReviews.toString(),
      color: 'text-neural-pink',
      bgColor: 'bg-neural-pink/10'
    },
    {
      icon: Star,
      label: 'Average Rating',
      value: `${stats.averageRating}/5`,
      color: 'text-yellow-500',
      bgColor: 'bg-yellow-500/10'
    },
    {
      icon: TrendingUp,
      label: 'Success Rate',
      value: `${stats.successRate}%`,
      color: 'text-green-500',
      bgColor: 'bg-green-500/10'
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-b from-black to-[#0A0A0A] py-12">
      {/* Background elements */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute inset-0 bg-[url('/grid.svg')] [mask-image:radial-gradient(ellipse_at_center,white,transparent)]" />
      </div>
      
      <div className="relative z-10 max-w-6xl mx-auto px-6">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-12"
        >
          <h1 className="text-4xl font-bold bg-gradient-to-r from-neural-purple to-neural-pink bg-clip-text text-transparent mb-4">
            Platform Dashboard
          </h1>
          <p className="text-gray-300 text-lg">
            Welcome back, {currentUser.email}
          </p>
        </motion.div>

        {/* Stats Grid */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12"
        >
          {statItems.map((stat, index) => (
            <motion.div
              key={stat.label}
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.3 + index * 0.1 }}
              className={`${stat.bgColor} backdrop-blur-md rounded-xl p-6 border border-white/10`}
            >
              <div className="flex items-center justify-between mb-4">
                <stat.icon className={`h-8 w-8 ${stat.color}`} />
                {stats.loading && <div className="animate-spin h-4 w-4 border-2 border-gray-400 rounded-full border-t-transparent" />}
              </div>
              <div className={`text-2xl font-bold ${stat.color} mb-1`}>
                {stats.loading ? '...' : stat.value}
              </div>
              <div className="text-gray-400 text-sm">{stat.label}</div>
            </motion.div>
          ))}
        </motion.div>

        {/* User Info Card */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="bg-gray-900/50 backdrop-blur-md rounded-xl p-6 border border-white/10"
        >
          <div className="flex items-center gap-4 mb-4">
            <Award className="h-6 w-6 text-neural-purple" />
            <h2 className="text-xl font-bold text-white">Account Information</h2>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="text-gray-400 text-sm">Email</label>
              <p className="text-white font-medium">{currentUser.email}</p>
            </div>
            <div>
              <label className="text-gray-400 text-sm">Account Created</label>
              <p className="text-white font-medium">
                {currentUser.metadata?.creationTime ? 
                  new Date(currentUser.metadata.creationTime).toLocaleDateString() : 
                  'Unknown'
                }
              </p>
            </div>
            <div>
              <label className="text-gray-400 text-sm">Last Sign In</label>
              <p className="text-white font-medium">
                {currentUser.metadata?.lastSignInTime ? 
                  new Date(currentUser.metadata.lastSignInTime).toLocaleDateString() : 
                  'Unknown'
                }
              </p>
            </div>
            <div>
              <label className="text-gray-400 text-sm">Account Status</label>
              <p className="text-green-500 font-medium">Active</p>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
}

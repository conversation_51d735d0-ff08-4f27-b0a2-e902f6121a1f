"use client";
import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Briefcase, 
  Target, 
  Sparkles, 
  ArrowRight,
  FileText,
  Zap,
  CheckCircle
} from 'lucide-react';

const JobDescriptionInput = ({ onJobDescriptionSubmit, isLoading = false }) => {
  const [jobDescription, setJobDescription] = useState('');
  const [jobTitle, setJobTitle] = useState('');
  const [company, setCompany] = useState('');

  const handleSubmit = () => {
    if (!jobDescription.trim()) {
      return;
    }

    onJobDescriptionSubmit({
      jobTitle: jobTitle.trim(),
      company: company.trim(),
      description: jobDescription.trim()
    });
  };

  const isValid = jobDescription.trim().length > 50;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-gray-800/50 backdrop-blur-md rounded-xl p-6 border border-gray-700"
    >
      <div className="text-center mb-6">
        <div className="inline-flex items-center justify-center w-12 h-12 bg-neural-blue/20 rounded-full mb-4">
          <Target className="h-6 w-6 text-neural-blue" />
        </div>
        <h3 className="text-xl font-bold text-white mb-2">
          Target Your Resume
        </h3>
        <p className="text-gray-300 text-sm">
          Provide the job description to create an ATS-optimized resume tailored for this specific position
        </p>
      </div>

      <div className="space-y-4">
        {/* Job Title */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Job Title (Optional)
          </label>
          <input
            type="text"
            value={jobTitle}
            onChange={(e) => setJobTitle(e.target.value)}
            placeholder="e.g., Senior Software Engineer"
            className="w-full px-4 py-3 bg-gray-900/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-neural-blue focus:border-transparent transition-all duration-200"
          />
        </div>

        {/* Company */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Company (Optional)
          </label>
          <input
            type="text"
            value={company}
            onChange={(e) => setCompany(e.target.value)}
            placeholder="e.g., Google, Microsoft, Startup Inc."
            className="w-full px-4 py-3 bg-gray-900/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-neural-blue focus:border-transparent transition-all duration-200"
          />
        </div>

        {/* Job Description */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Job Description *
          </label>
          <textarea
            value={jobDescription}
            onChange={(e) => setJobDescription(e.target.value)}
            placeholder="Paste the complete job description here including requirements, responsibilities, and qualifications..."
            rows={8}
            className="w-full px-4 py-3 bg-gray-900/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-neural-blue focus:border-transparent transition-all duration-200 resize-none"
          />
          <div className="flex justify-between items-center mt-2">
            <span className={`text-xs ${jobDescription.length < 50 ? 'text-red-400' : 'text-green-400'}`}>
              {jobDescription.length} characters (minimum 50 required)
            </span>
            {isValid && (
              <span className="flex items-center gap-1 text-xs text-green-400">
                <CheckCircle className="h-3 w-3" />
                Ready to analyze
              </span>
            )}
          </div>
        </div>

        {/* Benefits Info */}
        <div className="bg-neural-purple/10 rounded-lg p-4 border border-neural-purple/20">
          <h4 className="text-sm font-semibold text-neural-purple mb-2 flex items-center gap-2">
            <Sparkles className="h-4 w-4" />
            AI Enhancement Benefits
          </h4>
          <ul className="text-xs text-gray-300 space-y-1">
            <li className="flex items-center gap-2">
              <div className="w-1 h-1 bg-neural-purple rounded-full" />
              Keyword optimization based on job requirements
            </li>
            <li className="flex items-center gap-2">
              <div className="w-1 h-1 bg-neural-purple rounded-full" />
              Skills alignment with job description
            </li>
            <li className="flex items-center gap-2">
              <div className="w-1 h-1 bg-neural-purple rounded-full" />
              Experience highlighting for relevant achievements
            </li>
            <li className="flex items-center gap-2">
              <div className="w-1 h-1 bg-neural-purple rounded-full" />
              ATS-friendly formatting and structure
            </li>
          </ul>
        </div>

        {/* Submit Button */}
        <motion.button
          onClick={handleSubmit}
          disabled={!isValid || isLoading}
          className={`w-full py-4 px-6 rounded-xl font-semibold transition-all duration-300 flex items-center justify-center gap-2 ${
            isValid && !isLoading
              ? 'bg-gradient-to-r from-neural-blue to-neural-purple text-white hover:opacity-90 shadow-lg hover:shadow-xl'
              : 'bg-gray-700 text-gray-400 cursor-not-allowed'
          }`}
          whileHover={isValid && !isLoading ? { scale: 1.02 } : {}}
          whileTap={isValid && !isLoading ? { scale: 0.98 } : {}}
        >
          {isLoading ? (
            <>
              <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin" />
              Generating Targeted Resume...
            </>
          ) : (
            <>
              <Zap className="h-5 w-5" />
              Generate ATS-Optimized Resume
              <ArrowRight className="h-4 w-4" />
            </>
          )}
        </motion.button>
      </div>
    </motion.div>
  );
};

export default JobDescriptionInput;

"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/resume-builder/page",{

/***/ "(app-pages-browser)/./src/components/resume/ResumeBuilder.jsx":
/*!*************************************************!*\
  !*** ./src/components/resume/ResumeBuilder.jsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Briefcase,CheckCircle,Clock,Download,Eye,FileText,GraduationCap,Lightbulb,RotateCcw,Save,Sparkles,Target,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Briefcase,CheckCircle,Clock,Download,Eye,FileText,GraduationCap,Lightbulb,RotateCcw,Save,Sparkles,Target,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/graduation-cap.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Briefcase,CheckCircle,Clock,Download,Eye,FileText,GraduationCap,Lightbulb,RotateCcw,Save,Sparkles,Target,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Briefcase,CheckCircle,Clock,Download,Eye,FileText,GraduationCap,Lightbulb,RotateCcw,Save,Sparkles,Target,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Briefcase,CheckCircle,Clock,Download,Eye,FileText,GraduationCap,Lightbulb,RotateCcw,Save,Sparkles,Target,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Briefcase,CheckCircle,Clock,Download,Eye,FileText,GraduationCap,Lightbulb,RotateCcw,Save,Sparkles,Target,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Briefcase,CheckCircle,Clock,Download,Eye,FileText,GraduationCap,Lightbulb,RotateCcw,Save,Sparkles,Target,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Briefcase,CheckCircle,Clock,Download,Eye,FileText,GraduationCap,Lightbulb,RotateCcw,Save,Sparkles,Target,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _StepNavigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./StepNavigation */ \"(app-pages-browser)/./src/components/resume/StepNavigation.jsx\");\n/* harmony import */ var _layout_SmartNavigationBar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../layout/SmartNavigationBar */ \"(app-pages-browser)/./src/components/layout/SmartNavigationBar.jsx\");\n/* harmony import */ var _forms_ResumeFormComponents__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./forms/ResumeFormComponents */ \"(app-pages-browser)/./src/components/resume/forms/ResumeFormComponents.jsx\");\n/* harmony import */ var _forms_ExperienceForm__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./forms/ExperienceForm */ \"(app-pages-browser)/./src/components/resume/forms/ExperienceForm.jsx\");\n/* harmony import */ var _forms_SkillsProjectsForm__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./forms/SkillsProjectsForm */ \"(app-pages-browser)/./src/components/resume/forms/SkillsProjectsForm.jsx\");\n/* harmony import */ var _forms_ProjectsForm__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./forms/ProjectsForm */ \"(app-pages-browser)/./src/components/resume/forms/ProjectsForm.jsx\");\n/* harmony import */ var _forms_SkillsForm__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./forms/SkillsForm */ \"(app-pages-browser)/./src/components/resume/forms/SkillsForm.jsx\");\n/* harmony import */ var _forms_ReviewForm__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./forms/ReviewForm */ \"(app-pages-browser)/./src/components/resume/forms/ReviewForm.jsx\");\n/* harmony import */ var _common_ProgressBar__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../common/ProgressBar */ \"(app-pages-browser)/./src/components/common/ProgressBar.jsx\");\n/* harmony import */ var _ResumePreview__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./ResumePreview */ \"(app-pages-browser)/./src/components/resume/ResumePreview.jsx\");\n/* harmony import */ var _SuccessScreen__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./SuccessScreen */ \"(app-pages-browser)/./src/components/resume/SuccessScreen.jsx\");\n/* harmony import */ var _hooks_useATSAnalysis__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/hooks/useATSAnalysis */ \"(app-pages-browser)/./src/hooks/useATSAnalysis.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst EnhancedResumeBuilder = (param)=>{\n    let { hideHeader = false } = param;\n    var _atsAnalysis_recommendations;\n    _s();\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [selectedTemplate, setSelectedTemplate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('modern');\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        personal: {\n            firstName: \"\",\n            lastName: \"\",\n            email: \"\",\n            phone: \"\",\n            location: \"\",\n            linkedin: \"\",\n            portfolio: \"\",\n            summary: \"\",\n            profileImage: \"\"\n        },\n        education: [\n            {\n                id: 1,\n                degree: \"\",\n                institution: \"\",\n                location: \"\",\n                startDate: \"\",\n                endDate: \"\",\n                gpa: \"\",\n                relevant: \"\"\n            }\n        ],\n        experience: [\n            {\n                id: 1,\n                title: \"\",\n                company: \"\",\n                location: \"\",\n                startDate: \"\",\n                endDate: \"\",\n                current: false,\n                description: \"\"\n            }\n        ],\n        skills: {\n            technical: [],\n            languages: [],\n            certifications: []\n        },\n        projects: [\n            {\n                id: 1,\n                name: \"\",\n                description: \"\",\n                technologies: \"\",\n                link: \"\"\n            }\n        ],\n        jobDescription: \"\"\n    });\n    const [showPreview, setShowPreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showProgressBar, setShowProgressBar] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [resumeGenerated, setResumeGenerated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [validationErrors, setValidationErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [showValidationErrors, setShowValidationErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [completedSteps, setCompletedSteps] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [autoSaveEnabled, setAutoSaveEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [lastSaved, setLastSaved] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [resumeData, setResumeData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [resumeUrl, setResumeUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [atsScore, setAtsScore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [suggestions, setSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [aiSuggestionsEnabled, setAiSuggestionsEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [smartValidation, setSmartValidation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showHints, setShowHints] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const steps = [\n        {\n            id: 0,\n            title: \"Personal Information\",\n            icon: _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n            description: \"Tell us about yourself\",\n            estimatedTime: 3,\n            required: true\n        },\n        {\n            id: 1,\n            title: \"Education\",\n            icon: _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n            description: \"Your academic background\",\n            estimatedTime: 5,\n            required: true\n        },\n        {\n            id: 2,\n            title: \"Experience\",\n            icon: _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n            description: \"Your work experience\",\n            estimatedTime: 8,\n            required: true\n        },\n        {\n            id: 3,\n            title: \"Projects\",\n            icon: _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n            description: \"Showcase your projects\",\n            estimatedTime: 4,\n            required: false\n        },\n        {\n            id: 4,\n            title: \"Skills\",\n            icon: _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n            description: \"Your technical & soft skills\",\n            estimatedTime: 3,\n            required: false\n        },\n        {\n            id: 5,\n            title: \"Review & Generate\",\n            icon: _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n            description: \"Finalize your resume\",\n            estimatedTime: 2,\n            required: false\n        }\n    ];\n    const atsAnalysis = (0,_hooks_useATSAnalysis__WEBPACK_IMPORTED_MODULE_14__[\"default\"])(formData);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EnhancedResumeBuilder.useEffect\": ()=>{\n            // Check for backup data from auth flow first\n            const backupData = localStorage.getItem('resumeFormDataBackup');\n            const authTimestamp = localStorage.getItem('authFlowTimestamp');\n            const savedData = localStorage.getItem('resumeFormData');\n            // If backup data exists and is recent (within 10 minutes), use it\n            if (backupData && authTimestamp) {\n                const timeDiff = Date.now() - parseInt(authTimestamp);\n                if (timeDiff < 10 * 60 * 1000) {\n                    try {\n                        const parsed = JSON.parse(backupData);\n                        setFormData(parsed);\n                        setLastSaved(new Date());\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success('Your work has been restored after sign-in!');\n                        // Clean up backup data\n                        localStorage.removeItem('resumeFormDataBackup');\n                        localStorage.removeItem('authFlowTimestamp');\n                        // Update main storage\n                        localStorage.setItem('resumeFormData', backupData);\n                        localStorage.setItem('resumeLastSaved', new Date().toISOString());\n                        return;\n                    } catch (error) {\n                        console.error('Failed to load backup data:', error);\n                    }\n                }\n            }\n            // Fallback to regular saved data\n            if (savedData) {\n                try {\n                    const parsed = JSON.parse(savedData);\n                    setFormData(parsed);\n                    setLastSaved(new Date(localStorage.getItem('resumeLastSaved') || Date.now()));\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success('Previous work restored!');\n                } catch (error) {\n                    console.error('Failed to load saved data:', error);\n                }\n            }\n            // Clean up old backup data\n            localStorage.removeItem('resumeFormDataBackup');\n            localStorage.removeItem('authFlowTimestamp');\n        }\n    }[\"EnhancedResumeBuilder.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EnhancedResumeBuilder.useEffect\": ()=>{\n            if (autoSaveEnabled && formData) {\n                // Make form data globally accessible for auth flow\n                if (true) {\n                    window.formData = formData;\n                }\n                const timeoutId = setTimeout({\n                    \"EnhancedResumeBuilder.useEffect.timeoutId\": ()=>{\n                        localStorage.setItem('resumeFormData', JSON.stringify(formData));\n                        localStorage.setItem('resumeLastSaved', new Date().toISOString());\n                        setLastSaved(new Date());\n                    }\n                }[\"EnhancedResumeBuilder.useEffect.timeoutId\"], 2000);\n                return ({\n                    \"EnhancedResumeBuilder.useEffect\": ()=>clearTimeout(timeoutId)\n                })[\"EnhancedResumeBuilder.useEffect\"];\n            }\n        }\n    }[\"EnhancedResumeBuilder.useEffect\"], [\n        formData,\n        autoSaveEnabled\n    ]);\n    const validateStep = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[validateStep]\": (stepIndex)=>{\n            const errors = {};\n            switch(stepIndex){\n                case 0:\n                    var _formData_personal_firstName, _formData_personal_lastName, _formData_personal_email;\n                    if (!((_formData_personal_firstName = formData.personal.firstName) === null || _formData_personal_firstName === void 0 ? void 0 : _formData_personal_firstName.trim())) errors.firstName = 'First name is required';\n                    if (!((_formData_personal_lastName = formData.personal.lastName) === null || _formData_personal_lastName === void 0 ? void 0 : _formData_personal_lastName.trim())) errors.lastName = 'Last name is required';\n                    if (!((_formData_personal_email = formData.personal.email) === null || _formData_personal_email === void 0 ? void 0 : _formData_personal_email.trim())) errors.email = 'Email is required';\n                    else if (!/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(formData.personal.email)) errors.email = 'Please enter a valid email address';\n                    break;\n                case 1:\n                    var _formData_education;\n                    const validEducation = (_formData_education = formData.education) === null || _formData_education === void 0 ? void 0 : _formData_education.filter({\n                        \"EnhancedResumeBuilder.useCallback[validateStep]\": (edu)=>{\n                            var _edu_degree, _edu_institution;\n                            return ((_edu_degree = edu.degree) === null || _edu_degree === void 0 ? void 0 : _edu_degree.trim()) && ((_edu_institution = edu.institution) === null || _edu_institution === void 0 ? void 0 : _edu_institution.trim());\n                        }\n                    }[\"EnhancedResumeBuilder.useCallback[validateStep]\"]);\n                    if (!validEducation || validEducation.length === 0) errors.education = 'At least one education entry with degree and institution is required';\n                    break;\n                case 2:\n                    var _formData_experience;\n                    const validExperience = (_formData_experience = formData.experience) === null || _formData_experience === void 0 ? void 0 : _formData_experience.filter({\n                        \"EnhancedResumeBuilder.useCallback[validateStep]\": (exp)=>{\n                            var _exp_title, _exp_company;\n                            return ((_exp_title = exp.title) === null || _exp_title === void 0 ? void 0 : _exp_title.trim()) && ((_exp_company = exp.company) === null || _exp_company === void 0 ? void 0 : _exp_company.trim());\n                        }\n                    }[\"EnhancedResumeBuilder.useCallback[validateStep]\"]);\n                    if (!validExperience || validExperience.length === 0) errors.experience = 'At least one work experience entry with job title and company is required';\n                    break;\n                case 3:\n                    break;\n                case 4:\n                    break;\n                default:\n                    break;\n            }\n            return errors;\n        }\n    }[\"EnhancedResumeBuilder.useCallback[validateStep]\"], [\n        formData\n    ]);\n    const canProceedToNextStep = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[canProceedToNextStep]\": (stepIndex)=>{\n            const errors = validateStep(stepIndex);\n            return Object.keys(errors).length === 0;\n        }\n    }[\"EnhancedResumeBuilder.useCallback[canProceedToNextStep]\"], [\n        validateStep\n    ]);\n    const canGenerateResume = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[canGenerateResume]\": ()=>{\n            for(let i = 0; i < steps.length - 1; i++){\n                if (!canProceedToNextStep(i)) return false;\n            }\n            return true;\n        }\n    }[\"EnhancedResumeBuilder.useCallback[canGenerateResume]\"], [\n        canProceedToNextStep,\n        steps.length\n    ]);\n    const updateFormData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[updateFormData]\": function(section, field, value) {\n            let index = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : null;\n            setFormData({\n                \"EnhancedResumeBuilder.useCallback[updateFormData]\": (prev)=>{\n                    let newData = {\n                        ...prev\n                    };\n                    if (index !== null && Array.isArray(prev[section])) {\n                        const newArray = [\n                            ...prev[section]\n                        ];\n                        newArray[index] = {\n                            ...newArray[index],\n                            [field]: value\n                        };\n                        newData[section] = newArray;\n                    } else if (typeof prev[section] === 'object' && !Array.isArray(prev[section])) {\n                        newData[section] = {\n                            ...prev[section],\n                            [field]: value\n                        };\n                    } else {\n                        newData[field] = value;\n                    }\n                    return newData;\n                }\n            }[\"EnhancedResumeBuilder.useCallback[updateFormData]\"]);\n            if (validationErrors[field]) {\n                setValidationErrors({\n                    \"EnhancedResumeBuilder.useCallback[updateFormData]\": (prev)=>{\n                        const newErrors = {\n                            ...prev\n                        };\n                        delete newErrors[field];\n                        return newErrors;\n                    }\n                }[\"EnhancedResumeBuilder.useCallback[updateFormData]\"]);\n            }\n        }\n    }[\"EnhancedResumeBuilder.useCallback[updateFormData]\"], [\n        validationErrors\n    ]);\n    const addArrayItem = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[addArrayItem]\": (section, template)=>{\n            setFormData({\n                \"EnhancedResumeBuilder.useCallback[addArrayItem]\": (prev)=>({\n                        ...prev,\n                        [section]: [\n                            ...prev[section],\n                            {\n                                ...template,\n                                id: Math.random().toString(36).substring(2, 11)\n                            }\n                        ]\n                    })\n            }[\"EnhancedResumeBuilder.useCallback[addArrayItem]\"]);\n        }\n    }[\"EnhancedResumeBuilder.useCallback[addArrayItem]\"], []);\n    const removeArrayItem = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[removeArrayItem]\": (section, id)=>{\n            setFormData({\n                \"EnhancedResumeBuilder.useCallback[removeArrayItem]\": (prev)=>({\n                        ...prev,\n                        [section]: prev[section].filter({\n                            \"EnhancedResumeBuilder.useCallback[removeArrayItem]\": (item)=>item.id !== id\n                        }[\"EnhancedResumeBuilder.useCallback[removeArrayItem]\"])\n                    })\n            }[\"EnhancedResumeBuilder.useCallback[removeArrayItem]\"]);\n        }\n    }[\"EnhancedResumeBuilder.useCallback[removeArrayItem]\"], []);\n    const nextStep = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[nextStep]\": ()=>{\n            const errors = validateStep(currentStep);\n            if (Object.keys(errors).length > 0) {\n                setValidationErrors(errors);\n                setShowValidationErrors(true);\n                const errorMessages = Object.values(errors);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(errorMessages[0]);\n                return;\n            }\n            setCompletedSteps({\n                \"EnhancedResumeBuilder.useCallback[nextStep]\": (prev)=>[\n                        ...new Set([\n                            ...prev,\n                            currentStep\n                        ])\n                    ]\n            }[\"EnhancedResumeBuilder.useCallback[nextStep]\"]);\n            setValidationErrors({});\n            setShowValidationErrors(false);\n            if (currentStep < steps.length - 1) {\n                setCurrentStep(currentStep + 1);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"\".concat(steps[currentStep].title, \" completed!\"));\n            }\n        }\n    }[\"EnhancedResumeBuilder.useCallback[nextStep]\"], [\n        currentStep,\n        validateStep,\n        steps\n    ]);\n    const prevStep = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[prevStep]\": ()=>{\n            setValidationErrors({});\n            setShowValidationErrors(false);\n            if (currentStep > 0) setCurrentStep(currentStep - 1);\n        }\n    }[\"EnhancedResumeBuilder.useCallback[prevStep]\"], [\n        currentStep\n    ]);\n    const handleStepClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[handleStepClick]\": (stepIndex)=>{\n            if (stepIndex <= currentStep || completedSteps.includes(stepIndex)) {\n                setCurrentStep(stepIndex);\n                setValidationErrors({});\n                setShowValidationErrors(false);\n            }\n        }\n    }[\"EnhancedResumeBuilder.useCallback[handleStepClick]\"], [\n        currentStep,\n        completedSteps\n    ]);\n    const generateResume = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[generateResume]\": async ()=>{\n            setValidationErrors({});\n            setShowValidationErrors(false);\n            if (!canGenerateResume()) {\n                const allErrors = {};\n                for(let i = 0; i < steps.length - 1; i++){\n                    const stepErrors = validateStep(i);\n                    Object.assign(allErrors, stepErrors);\n                }\n                setValidationErrors(allErrors);\n                setShowValidationErrors(true);\n                for(let i = 0; i < steps.length - 1; i++){\n                    const stepErrors = validateStep(i);\n                    if (Object.keys(stepErrors).length > 0) {\n                        setCurrentStep(i);\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Please complete all required fields in \".concat(steps[i].title));\n                        return;\n                    }\n                }\n                return;\n            }\n            try {\n                setIsGenerating(true);\n                setShowProgressBar(true);\n                setResumeGenerated(false);\n                const response = await fetch('/api/generate-resume', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        formData,\n                        templateId: selectedTemplate\n                    })\n                });\n                const contentType = response.headers.get('content-type');\n                if (!contentType || !contentType.includes('application/json')) throw new Error('Server returned non-JSON response');\n                const data = await response.json();\n                if (!response.ok) throw new Error(data.error || 'Failed to generate resume');\n                if (!data.resumeData || !data.downloadUrl) throw new Error('Generated resume data is incomplete');\n                setResumeUrl(data.downloadUrl);\n                setResumeData(data.resumeData);\n                setAtsScore(data.atsScore || 75);\n                setSuggestions(data.suggestions || []);\n                setResumeGenerated(true);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success('Resume generated successfully!');\n            } catch (error) {\n                console.error('Resume generation error:', error);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(error.message || 'Failed to generate resume');\n            } finally{\n                setIsGenerating(false);\n                setShowProgressBar(false);\n            }\n        }\n    }[\"EnhancedResumeBuilder.useCallback[generateResume]\"], [\n        canGenerateResume,\n        formData,\n        steps,\n        validateStep,\n        selectedTemplate\n    ]);\n    const handleSave = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[handleSave]\": ()=>{\n            localStorage.setItem('resumeFormData', JSON.stringify(formData));\n            localStorage.setItem('resumeLastSaved', new Date().toISOString());\n            setLastSaved(new Date());\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success('Progress saved!');\n        }\n    }[\"EnhancedResumeBuilder.useCallback[handleSave]\"], [\n        formData\n    ]);\n    const handleAISuggest = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[handleAISuggest]\": async (section)=>{\n            if (!aiSuggestionsEnabled) return;\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.loading('Getting AI suggestions...');\n            try {\n                await new Promise({\n                    \"EnhancedResumeBuilder.useCallback[handleAISuggest]\": (resolve)=>setTimeout(resolve, 1500)\n                }[\"EnhancedResumeBuilder.useCallback[handleAISuggest]\"]);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.dismiss();\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success('AI suggestions applied!');\n            } catch (error) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.dismiss();\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error('Failed to get AI suggestions');\n            }\n        }\n    }[\"EnhancedResumeBuilder.useCallback[handleAISuggest]\"], [\n        aiSuggestionsEnabled\n    ]);\n    // Set up event listener for resume generation from ReviewForm\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EnhancedResumeBuilder.useEffect\": ()=>{\n            const handleGenerateResume = {\n                \"EnhancedResumeBuilder.useEffect.handleGenerateResume\": ()=>{\n                    // Check if we can generate resume and call the function\n                    if (canGenerateResume()) {\n                        generateResume();\n                    } else {\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error('Please complete all required sections before generating your resume.');\n                    }\n                }\n            }[\"EnhancedResumeBuilder.useEffect.handleGenerateResume\"];\n            if (true) {\n                window.addEventListener('generateResume', handleGenerateResume);\n                return ({\n                    \"EnhancedResumeBuilder.useEffect\": ()=>window.removeEventListener('generateResume', handleGenerateResume)\n                })[\"EnhancedResumeBuilder.useEffect\"];\n            }\n        }\n    }[\"EnhancedResumeBuilder.useEffect\"], [\n        canGenerateResume,\n        generateResume\n    ]);\n    const getStepValidation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[getStepValidation]\": ()=>{\n            const validation = {};\n            steps.forEach({\n                \"EnhancedResumeBuilder.useCallback[getStepValidation]\": (step, index)=>{\n                    const errors = validateStep(index);\n                    validation[index] = Object.keys(errors).length === 0;\n                }\n            }[\"EnhancedResumeBuilder.useCallback[getStepValidation]\"]);\n            return validation;\n        }\n    }[\"EnhancedResumeBuilder.useCallback[getStepValidation]\"], [\n        steps,\n        validateStep\n    ]);\n    const renderStepContent = ()=>{\n        const commonProps = {\n            formData,\n            updateFormData,\n            addArrayItem,\n            removeArrayItem,\n            atsAnalysis,\n            validationErrors,\n            showValidationErrors,\n            onSave: handleSave,\n            onAISuggest: handleAISuggest\n        };\n        switch(currentStep){\n            case 0:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_ResumeFormComponents__WEBPACK_IMPORTED_MODULE_5__.PersonalInfoForm, {\n                    ...commonProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                    lineNumber: 360,\n                    columnNumber: 16\n                }, undefined);\n            case 1:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_ResumeFormComponents__WEBPACK_IMPORTED_MODULE_5__.EducationForm, {\n                    ...commonProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                    lineNumber: 362,\n                    columnNumber: 16\n                }, undefined);\n            case 2:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_ExperienceForm__WEBPACK_IMPORTED_MODULE_6__.EnhancedExperienceForm, {\n                    ...commonProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                    lineNumber: 364,\n                    columnNumber: 16\n                }, undefined);\n            case 3:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_ProjectsForm__WEBPACK_IMPORTED_MODULE_8__.EnhancedProjectsForm, {\n                    ...commonProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                    lineNumber: 366,\n                    columnNumber: 16\n                }, undefined);\n            case 4:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_SkillsForm__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    ...commonProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                    lineNumber: 368,\n                    columnNumber: 16\n                }, undefined);\n            case 5:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_ReviewForm__WEBPACK_IMPORTED_MODULE_10__.EnhancedReviewForm, {\n                    ...commonProps,\n                    selectedTemplate: selectedTemplate,\n                    onTemplateSelect: setSelectedTemplate\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                    lineNumber: 370,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return null;\n        }\n    };\n    if (resumeGenerated && resumeData && resumeUrl) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SuccessScreen__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n            formData: formData,\n            resumeData: resumeData,\n            onStartOver: ()=>{\n                setResumeGenerated(false);\n                setResumeData(null);\n                setResumeUrl(\"\");\n                setCurrentStep(0);\n            },\n            onEditResume: ()=>{\n                setResumeGenerated(false);\n                setCurrentStep(4); // Go back to review step\n            }\n        }, void 0, false, {\n            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n            lineNumber: 378,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-b from-black to-[#0A0A0A] text-white relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 opacity-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 bg-[url('/grid.svg')] [mask-image:radial-gradient(ellipse_at_center,white,transparent)]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                    lineNumber: 398,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                lineNumber: 397,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_common_ProgressBar__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                isVisible: showProgressBar,\n                onComplete: ()=>setShowProgressBar(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                lineNumber: 400,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 pb-32\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 py-8\",\n                    children: [\n                        !hideHeader && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_20__.motion.div, {\n                            className: \"text-center mb-8\",\n                            initial: {\n                                opacity: 0,\n                                y: -20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center gap-3 mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"h-10 w-10 text-neural-pink animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                    lineNumber: 407,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-neural-pink opacity-20 rounded-full blur-md\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                    lineNumber: 408,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                            lineNumber: 406,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-4xl lg:text-5xl font-bold text-white\",\n                                            children: \"Enhanced Resume Builder\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                            lineNumber: 410,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                    lineNumber: 405,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 text-lg max-w-2xl mx-auto\",\n                                    children: \"Create professional, ATS-friendly resumes with our enhanced AI-powered builder\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                    lineNumber: 412,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                            lineNumber: 404,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StepNavigation__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            currentStep: currentStep,\n                            totalSteps: steps.length,\n                            steps: steps,\n                            onStepClick: handleStepClick,\n                            allowClickNavigation: true,\n                            completedSteps: completedSteps,\n                            stepValidation: getStepValidation(),\n                            estimatedTime: steps.reduce((acc, step, index)=>{\n                                acc[index] = step.estimatedTime;\n                                return acc;\n                            }, {})\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                            lineNumber: 415,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 xl:grid-cols-1 gap-8 max-w-4xl xl:max-w-5xl mx-auto xl:mr-96\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_22__.AnimatePresence, {\n                                        mode: \"wait\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_20__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                x: 20\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                x: 0\n                                            },\n                                            exit: {\n                                                opacity: 0,\n                                                x: -20\n                                            },\n                                            transition: {\n                                                duration: 0.3\n                                            },\n                                            children: renderStepContent()\n                                        }, currentStep, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                            lineNumber: 419,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                        lineNumber: 418,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                    lineNumber: 417,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden xl:block\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"xl:fixed xl:top-24 xl:right-8 xl:w-80 xl:h-[calc(100vh-8rem)] xl:overflow-hidden\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_20__.motion.div, {\n                                                className: \"bg-gray-900/60 backdrop-blur-md rounded-2xl border border-white/10 h-full flex flex-col\",\n                                                initial: {\n                                                    opacity: 0,\n                                                    x: 20\n                                                },\n                                                animate: {\n                                                    opacity: 1,\n                                                    x: 0\n                                                },\n                                                transition: {\n                                                    delay: 0.2\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between p-4 border-b border-white/10\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-semibold flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-neural-blue\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                                        lineNumber: 436,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    \"Live Preview\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                                lineNumber: 435,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>setShowPreview(!showPreview),\n                                                                    className: \"px-3 py-1.5 rounded-lg text-sm font-medium transition-colors \".concat(showPreview ? 'bg-neural-purple text-white' : 'bg-gray-800 text-gray-300 hover:bg-gray-700'),\n                                                                    children: showPreview ? 'Hide' : 'Show'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                                    lineNumber: 440,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                                lineNumber: 439,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                        lineNumber: 434,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 overflow-hidden\",\n                                                        children: showPreview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-full overflow-y-auto p-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"transform scale-[0.65] origin-top-left w-[154%] h-[154%]\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-white rounded-lg shadow-lg\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ResumePreview__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                        formData: formData,\n                                                                        selectedTemplate: selectedTemplate\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                                        lineNumber: 457,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                                    lineNumber: 456,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                                lineNumber: 455,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                            lineNumber: 454,\n                                                            columnNumber: 23\n                                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-center h-full text-gray-400\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                        className: \"h-16 w-16 mx-auto mb-4 opacity-30\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                                        lineNumber: 464,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm\",\n                                                                        children: 'Click \"Show\" to preview'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                                        lineNumber: 465,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-500 mt-1\",\n                                                                        children: \"Live updates as you type\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                                        lineNumber: 466,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                                lineNumber: 463,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                            lineNumber: 462,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                        lineNumber: 452,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    showPreview && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-3 border-t border-white/10 bg-gray-800/30\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between text-xs text-gray-400\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        \"Template: \",\n                                                                        selectedTemplate\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                                    lineNumber: 476,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Auto-updating\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                                    lineNumber: 477,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                            lineNumber: 475,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                        lineNumber: 474,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                lineNumber: 427,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                            lineNumber: 426,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"xl:hidden\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_20__.motion.div, {\n                                                className: \"bg-gray-900/60 backdrop-blur-md rounded-2xl p-6 border border-white/10\",\n                                                initial: {\n                                                    opacity: 0,\n                                                    x: 20\n                                                },\n                                                animate: {\n                                                    opacity: 1,\n                                                    x: 0\n                                                },\n                                                transition: {\n                                                    delay: 0.2\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between mb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-semibold flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-neural-blue\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                                        lineNumber: 489,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    \"Resume Preview\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                                lineNumber: 488,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>setShowPreview(!showPreview),\n                                                                className: \"px-3 py-1.5 rounded-lg text-sm font-medium transition-colors \".concat(showPreview ? 'bg-neural-purple text-white' : 'bg-gray-800 text-gray-300 hover:bg-gray-700'),\n                                                                children: showPreview ? 'Hide' : 'Show'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                                lineNumber: 492,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                        lineNumber: 487,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    showPreview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"max-h-[600px] overflow-y-auto border border-white/10 rounded-lg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ResumePreview__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            formData: formData,\n                                                            selectedTemplate: selectedTemplate\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                            lineNumber: 503,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                        lineNumber: 502,\n                                                        columnNumber: 21\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center py-12 text-gray-400\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                className: \"h-12 w-12 mx-auto mb-3 opacity-30\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                                lineNumber: 507,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm\",\n                                                                children: 'Click \"Show\" to preview your resume'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                                lineNumber: 508,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                        lineNumber: 506,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                lineNumber: 486,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                            lineNumber: 485,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_20__.motion.div, {\n                                            className: \"bg-gray-900/60 backdrop-blur-md rounded-2xl p-6 border border-white/10\",\n                                            initial: {\n                                                opacity: 0,\n                                                x: 20\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                x: 0\n                                            },\n                                            transition: {\n                                                delay: 0.3\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                            className: \"h-5 w-5 text-neural-purple\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                            lineNumber: 515,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-semibold\",\n                                                            children: \"ATS Score\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                            lineNumber: 516,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                    lineNumber: 514,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-3xl font-bold \".concat((atsAnalysis.overallScore || 0) >= 80 ? 'text-green-400' : (atsAnalysis.overallScore || 0) >= 60 ? 'text-yellow-400' : 'text-red-400'),\n                                                            children: [\n                                                                atsAnalysis.overallScore || 0,\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                            lineNumber: 519,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-400\",\n                                                            children: (atsAnalysis.overallScore || 0) >= 80 ? 'Excellent' : (atsAnalysis.overallScore || 0) >= 60 ? 'Good' : 'Needs Work'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                            lineNumber: 525,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                    lineNumber: 518,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                ((_atsAnalysis_recommendations = atsAnalysis.recommendations) === null || _atsAnalysis_recommendations === void 0 ? void 0 : _atsAnalysis_recommendations.length) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-500 mb-2\",\n                                                            children: \"Top Suggestions:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                            lineNumber: 532,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        atsAnalysis.recommendations.slice(0, 2).map((rec, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-start gap-2 text-sm\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-1.5 h-1.5 bg-yellow-400 rounded-full mt-2 flex-shrink-0\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                                        lineNumber: 535,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-300\",\n                                                                        children: rec.message\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                                        lineNumber: 536,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                                lineNumber: 534,\n                                                                columnNumber: 23\n                                                            }, undefined))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                    lineNumber: 531,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                            lineNumber: 513,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                    lineNumber: 424,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                            lineNumber: 416,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                    lineNumber: 402,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                lineNumber: 401,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_layout_SmartNavigationBar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                currentStep: currentStep,\n                totalSteps: steps.length,\n                onPrevious: prevStep,\n                onNext: nextStep,\n                onGenerate: generateResume,\n                onSave: handleSave,\n                onPreview: ()=>setShowPreview(!showPreview),\n                isGenerating: isGenerating,\n                canProceed: currentStep === steps.length - 1 ? canGenerateResume() : canProceedToNextStep(currentStep),\n                showPreview: showPreview,\n                steps: steps,\n                formData: formData,\n                atsScore: atsAnalysis.overallScore,\n                autoSaveEnabled: autoSaveEnabled\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                lineNumber: 547,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n        lineNumber: 396,\n        columnNumber: 5\n    }, undefined);\n};\n_s(EnhancedResumeBuilder, \"kL0tPIp7ubCuYi96RKPg68CdXC4=\", false, function() {\n    return [\n        _hooks_useATSAnalysis__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n    ];\n});\n_c = EnhancedResumeBuilder;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (EnhancedResumeBuilder);\nvar _c;\n$RefreshReg$(_c, \"EnhancedResumeBuilder\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/resume/ResumeBuilder.jsx\n"));

/***/ })

});
"use client";
import React from 'react';
import { motion } from 'framer-motion';
import { 
  CheckCircle, 
  AlertTriangle, 
  XCircle, 
  Info,
  TrendingUp,
  Lightbulb
} from 'lucide-react';

const ATSFieldIndicator = ({ 
  fieldName, 
  value, 
  analysis, 
  showDetails = false,
  className = "" 
}) => {
  if (!analysis) return null;

  const getStatusIcon = (status) => {
    switch (status) {
      case 'excellent':
        return CheckCircle;
      case 'good':
        return CheckCircle;
      case 'warning':
        return AlertTriangle;
      case 'incomplete':
        return XCircle;
      default:
        return Info;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'excellent':
        return 'text-green-400 border-green-400 bg-green-400/10';
      case 'good':
        return 'text-blue-400 border-blue-400 bg-blue-400/10';
      case 'warning':
        return 'text-yellow-400 border-yellow-400 bg-yellow-400/10';
      case 'incomplete':
        return 'text-red-400 border-red-400 bg-red-400/10';
      default:
        return 'text-gray-400 border-gray-400 bg-gray-400/10';
    }
  };

  const getScoreColor = (score) => {
    if (score >= 90) return 'text-green-400';
    if (score >= 70) return 'text-blue-400';
    if (score >= 50) return 'text-yellow-400';
    return 'text-red-400';
  };

  const StatusIcon = getStatusIcon(analysis.status);
  const statusColor = getStatusColor(analysis.status);
  const scoreColor = getScoreColor(analysis.score);

  return (
    <div className={`relative ${className}`}>
      {/* Inline Status Indicator */}
      <div className="flex items-center gap-2 mt-1">
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          className={`flex items-center gap-1 px-2 py-1 rounded-md border text-xs font-medium ${statusColor}`}
        >
          <StatusIcon className="h-3 w-3" />
          <span className={scoreColor}>{analysis.score}%</span>
        </motion.div>

        {analysis.keywords && analysis.keywords.length > 0 && (
          <div className="flex items-center gap-1">
            {analysis.keywords.map((keyword, index) => (
              <span
                key={index}
                className="px-1.5 py-0.5 bg-neural-purple/20 text-neural-purple text-xs rounded border border-neural-purple/30"
              >
                {keyword}
              </span>
            ))}
          </div>
        )}
      </div>

      {/* Detailed Analysis (when expanded) */}
      {showDetails && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          className="mt-2 p-3 bg-gray-800/50 rounded-lg border border-gray-700"
        >
          {/* Score Breakdown */}
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-300">ATS Score</span>
            <span className={`text-sm font-bold ${scoreColor}`}>{analysis.score}/100</span>
          </div>
          
          <div className="w-full bg-gray-700 rounded-full h-2 mb-3">
            <motion.div
              initial={{ width: 0 }}
              animate={{ width: `${analysis.score}%` }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className={`h-2 rounded-full ${
                analysis.score >= 90 ? 'bg-green-500' :
                analysis.score >= 70 ? 'bg-blue-500' :
                analysis.score >= 50 ? 'bg-yellow-500' : 'bg-red-500'
              }`}
            />
          </div>

          {/* Suggestions */}
          {analysis.suggestions && analysis.suggestions.length > 0 && (
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-sm font-medium text-gray-300">
                <Lightbulb className="h-4 w-4 text-neural-purple" />
                Suggestions
              </div>
              {analysis.suggestions.map((suggestion, index) => (
                <div key={index} className="flex items-start gap-2 text-xs text-gray-400">
                  <div className="w-1 h-1 bg-neural-purple rounded-full mt-1.5 flex-shrink-0" />
                  <span>{suggestion}</span>
                </div>
              ))}
            </div>
          )}

          {/* Keywords Found */}
          {analysis.keywords && analysis.keywords.length > 0 && (
            <div className="mt-3 pt-3 border-t border-gray-700">
              <div className="flex items-center gap-2 text-sm font-medium text-gray-300 mb-2">
                <TrendingUp className="h-4 w-4 text-green-400" />
                ATS Keywords Found
              </div>
              <div className="flex flex-wrap gap-1">
                {analysis.keywords.map((keyword, index) => (
                  <span
                    key={index}
                    className="px-2 py-1 bg-green-900/20 text-green-400 text-xs rounded border border-green-500/30"
                  >
                    {keyword}
                  </span>
                ))}
              </div>
            </div>
          )}
        </motion.div>
      )}
    </div>
  );
};

// Simplified version for inline display
export const ATSFieldBadge = ({ analysis, className = "" }) => {
  if (!analysis) return null;

  const getStatusColor = (status) => {
    switch (status) {
      case 'excellent':
        return 'bg-green-500';
      case 'good':
        return 'bg-blue-500';
      case 'warning':
        return 'bg-yellow-500';
      case 'incomplete':
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'excellent':
      case 'good':
        return CheckCircle;
      case 'warning':
        return AlertTriangle;
      case 'incomplete':
        return XCircle;
      default:
        return Info;
    }
  };

  const StatusIcon = getStatusIcon(analysis.status);
  const statusColor = getStatusColor(analysis.status);

  return (
    <motion.div
      initial={{ scale: 0 }}
      animate={{ scale: 1 }}
      className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-white text-xs font-medium ${statusColor} ${className}`}
    >
      <StatusIcon className="h-3 w-3" />
      <span>{analysis.score}%</span>
    </motion.div>
  );
};

// Real-time field analysis component
export const ATSFieldAnalysis = ({ fieldName, value, context, className = "" }) => {
  // This would use the analysis hook
  const analysis = {
    score: value ? Math.min(100, value.length * 2) : 0,
    status: value ? (value.length > 50 ? 'excellent' : 'warning') : 'incomplete',
    suggestions: value ? [] : ['This field is required for ATS optimization'],
    keywords: []
  };

  return (
    <ATSFieldIndicator
      fieldName={fieldName}
      value={value}
      analysis={analysis}
      className={className}
    />
  );
};

export default ATSFieldIndicator;

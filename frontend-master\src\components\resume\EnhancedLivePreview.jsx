'use client';
import { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Eye, EyeOff, FileText, Target, TrendingUp, 
  AlertCircle, CheckCircle, Clock, Zap
} from 'lucide-react';
import { analyzeResumeATS } from '@/services/geminiService';
import { renderMultiIndustryTemplate } from './templates/MultiIndustryTemplates';
import { ATSScoreLoading } from './LoadingStates';

const EnhancedLivePreview = ({ 
  formData, 
  selectedTemplate = 'professional'
}) => {
  const [isVisible, setIsVisible] = useState(true);
  const [atsAnalysis, setAtsAnalysis] = useState(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [lastAnalyzedData, setLastAnalyzedData] = useState(null);

  // Check if we have meaningful data to preview
  const hasContent = formData && (
    formData.personal?.firstName ||
    formData.personal?.email ||
    formData.experience?.some(exp => exp.title || exp.company) ||
    formData.education?.some(edu => edu.degree || edu.institution)
  );

  // Debounced ATS analysis
  const analyzeResume = useCallback(async () => {
    if (!hasContent || isAnalyzing) return;
    
    const currentDataString = JSON.stringify(formData);
    if (currentDataString === lastAnalyzedData) return;
    
    setIsAnalyzing(true);
    try {
      const analysis = await analyzeResumeATS(formData);
      setAtsAnalysis(analysis);
      setLastAnalyzedData(currentDataString);
    } catch (error) {
      console.error('ATS Analysis failed:', error);
      // Set fallback analysis
      setAtsAnalysis({
        overallScore: 75,
        scores: { formatting: 80, keywords: 70, sections: 75, content: 75 },
        recommendations: [
          {
            category: "content",
            issue: "Analysis temporarily unavailable",
            suggestion: "Continue building your resume",
            priority: "low"
          }
        ],
        strengths: ["Professional structure"]
      });
    } finally {
      setIsAnalyzing(false);
    }
  }, [formData, hasContent, isAnalyzing, lastAnalyzedData]);

  // Trigger analysis when content changes
  useEffect(() => {
    if (!hasContent) return;
    
    const timeoutId = setTimeout(() => {
      analyzeResume();
    }, 2000); // Debounce for 2 seconds
    
    return () => clearTimeout(timeoutId);
  }, [analyzeResume, hasContent]);

  const getScoreColor = (score) => {
    if (score >= 80) return 'text-green-400';
    if (score >= 60) return 'text-yellow-400';
    return 'text-red-400';
  };

  const getScoreBgColor = (score) => {
    if (score >= 80) return 'bg-green-500';
    if (score >= 60) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  const renderEmptyState = () => (
    <div className="flex flex-col items-center justify-center h-full text-gray-400 p-6">
      <div className="relative mb-3">
        <FileText className="h-12 w-12 opacity-50" />
        <div className="absolute -top-1 -right-1 w-4 h-4 bg-neural-pink rounded-full flex items-center justify-center">
          <span className="text-xs">✨</span>
        </div>
      </div>
      <h3 className="text-sm font-medium mb-1 text-white">AI Resume Preview</h3>
      <p className="text-xs text-center opacity-75">
        Watch your professional resume build in real-time as you type
      </p>
      <p className="text-xs text-center opacity-50 mt-1">
        ⚡ Powered by AI for instant results
      </p>
    </div>
  );

  const renderATSScore = () => {
    if (!atsAnalysis) return null;

    return (
      <div className="border-t border-gray-700/30 p-4 bg-gray-800/30">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-2">
            <Target className="h-4 w-4 text-neural-purple" />
            <span className="text-sm font-medium text-white">ATS Score</span>
            {isAnalyzing && (
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
              >
                <Clock className="h-3 w-3 text-neural-blue" />
              </motion.div>
            )}
          </div>
          <div className={`text-lg font-bold ${getScoreColor(atsAnalysis.overallScore)}`}>
            {atsAnalysis.overallScore}%
          </div>
        </div>

        {/* Score Breakdown */}
        <div className="space-y-2 mb-3">
          {Object.entries(atsAnalysis.scores || {}).map(([category, score]) => (
            <div key={category} className="flex items-center justify-between text-xs">
              <span className="text-gray-400 capitalize">{category}</span>
              <div className="flex items-center gap-2">
                <div className="w-16 h-1 bg-gray-700 rounded-full overflow-hidden">
                  <motion.div
                    className={`h-full ${getScoreBgColor(score)}`}
                    initial={{ width: 0 }}
                    animate={{ width: `${score}%` }}
                    transition={{ duration: 0.5 }}
                  />
                </div>
                <span className={`${getScoreColor(score)} font-medium`}>{score}%</span>
              </div>
            </div>
          ))}
        </div>

        {/* Top Recommendations */}
        {atsAnalysis.recommendations && atsAnalysis.recommendations.length > 0 && (
          <div className="space-y-1">
            <div className="text-xs text-gray-500 mb-1">Top Suggestions:</div>
            {atsAnalysis.recommendations.slice(0, 2).map((rec, index) => (
              <div key={index} className="flex items-start gap-2 text-xs">
                <div className={`w-1 h-1 rounded-full mt-1.5 flex-shrink-0 ${
                  rec.priority === 'high' ? 'bg-red-400' : 
                  rec.priority === 'medium' ? 'bg-yellow-400' : 'bg-blue-400'
                }`}></div>
                <span className="text-gray-300 leading-relaxed">{rec.suggestion}</span>
              </div>
            ))}
          </div>
        )}

        {/* Strengths */}
        {atsAnalysis.strengths && atsAnalysis.strengths.length > 0 && (
          <div className="mt-3 pt-2 border-t border-gray-700/30">
            <div className="text-xs text-gray-500 mb-1">Strengths:</div>
            <div className="flex flex-wrap gap-1">
              {atsAnalysis.strengths.slice(0, 3).map((strength, index) => (
                <span 
                  key={index}
                  className="text-xs px-2 py-0.5 bg-green-500/20 text-green-400 rounded"
                >
                  {strength}
                </span>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  };

  const renderPreviewContent = () => {
    if (!hasContent) return renderEmptyState();

    return (
      <div className="h-full overflow-y-auto">
        {/* Resume Preview */}
        <div className="bg-white m-2 rounded-lg shadow-sm min-h-[400px] transform scale-75 origin-top">
          <div 
            className="w-full"
            style={{
              fontFamily: "'Inter', 'Helvetica Neue', Arial, sans-serif",
              fontSize: '11pt',
              lineHeight: '1.4',
              color: '#000000',
              backgroundColor: '#ffffff',
              minHeight: '400px',
              padding: '24px'
            }}
          >
            {renderMultiIndustryTemplate(selectedTemplate, formData)}
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="bg-gray-900/60 backdrop-blur-md border border-gray-700/50 rounded-2xl shadow-xl h-full flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-3 border-b border-gray-700/30 bg-gray-800/30">
        <div className="flex items-center gap-2">
          <div className="w-2 h-2 bg-neural-pink rounded-full animate-pulse"></div>
          <span className="text-sm font-medium text-white">Live Preview</span>
          {isAnalyzing && (
            <div className="flex items-center gap-1 text-xs text-neural-blue">
              <Zap className="h-3 w-3" />
              <span>Analyzing...</span>
            </div>
          )}
        </div>
        
        <div className="flex items-center gap-1">
          <button
            onClick={() => setIsVisible(!isVisible)}
            className="p-1.5 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors"
            title={isVisible ? 'Hide Preview' : 'Show Preview'}
          >
            {isVisible ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
          </button>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-hidden flex flex-col">
        <AnimatePresence mode="wait">
          {isVisible ? (
            <motion.div
              key="preview"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="flex-1 overflow-hidden flex flex-col"
            >
              <div className="flex-1 overflow-hidden">
                {renderPreviewContent()}
              </div>
              
              {/* ATS Score Section */}
              {hasContent && renderATSScore()}
            </motion.div>
          ) : (
            <motion.div
              key="hidden"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="flex items-center justify-center h-full text-gray-500"
            >
              <div className="text-center">
                <Eye className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p className="text-sm">Preview Hidden</p>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Footer */}
      {isVisible && hasContent && (
        <div className="p-2 border-t border-gray-700/30 bg-gray-800/30">
          <div className="flex items-center justify-between text-xs text-gray-400">
            <span>Updates automatically</span>
            <span className="capitalize">{selectedTemplate.replace('_', ' ')}</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default EnhancedLivePreview;

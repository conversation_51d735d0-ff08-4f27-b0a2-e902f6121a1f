'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  X, 
  Lock, 
  UserPlus, 
  LogIn, 
  Shield,
  Star,
  Download,
  CheckCircle,
  ArrowRight
} from 'lucide-react';
import Link from 'next/link';

const AuthGuardModal = ({ isOpen, onClose, onProceedWithAuth, featureName = "download your resume" }) => {
  const [showBenefits, setShowBenefits] = useState(false);

  const benefits = [
    {
      icon: <Download className="h-5 w-5 text-neural-blue" />,
      title: "Save & Download Resumes",
      description: "Download unlimited professional resumes in PDF format"
    },
    {
      icon: <Star className="h-5 w-5 text-yellow-400" />,
      title: "Access Premium Templates",
      description: "Choose from our collection of ATS-optimized resume templates"
    },
    {
      icon: <Shield className="h-5 w-5 text-green-400" />,
      title: "Secure Data Storage",
      description: "Your resume data is safely stored and easily accessible"
    },
    {
      icon: <CheckCircle className="h-5 w-5 text-neural-purple" />,
      title: "Track Your Applications",
      description: "Keep track of your job applications and resume versions"
    }
  ];

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 bg-black/70 backdrop-blur-sm"
            onClick={onClose}
          />
          
          {/* Modal */}
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            className="relative bg-gray-900 border border-white/10 rounded-2xl p-8 max-w-lg w-full mx-4 shadow-2xl max-h-[90vh] overflow-y-auto"
          >
            {/* Header */}
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center gap-3">
                <div className="bg-gradient-to-r from-neural-purple to-neural-pink p-2 rounded-lg">
                  <Lock className="h-5 w-5 text-white" />
                </div>
                <h2 className="text-xl font-bold text-white">Account Required</h2>
              </div>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-white transition-colors"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            {/* Main Message */}
            <div className="text-center mb-6">
              <p className="text-gray-300 text-lg mb-4">
                To {featureName}, you need to create a free account or sign in.
              </p>
              <p className="text-gray-400 text-sm">
                Join thousands of job seekers who trust BlinkFind for their career success!
              </p>
            </div>

            {/* Benefits Section */}
            <div className="space-y-4 mb-8">
              <button
                onClick={() => setShowBenefits(!showBenefits)}
                className="w-full flex items-center justify-between p-3 bg-gray-800/50 rounded-lg hover:bg-gray-800 transition-colors"
              >
                <span className="text-white font-medium">Why create an account?</span>
                <motion.div
                  animate={{ rotate: showBenefits ? 90 : 0 }}
                  transition={{ duration: 0.2 }}
                >
                  <ArrowRight className="h-4 w-4 text-gray-400" />
                </motion.div>
              </button>

              <AnimatePresence>
                {showBenefits && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: "auto" }}
                    exit={{ opacity: 0, height: 0 }}
                    className="space-y-3"
                  >
                    {benefits.map((benefit, index) => (
                      <motion.div
                        key={index}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: index * 0.1 }}
                        className="flex items-start gap-3 p-3 bg-gray-800/30 rounded-lg"
                      >
                        <div className="flex-shrink-0 mt-0.5">
                          {benefit.icon}
                        </div>
                        <div>
                          <h4 className="text-white font-medium text-sm">{benefit.title}</h4>
                          <p className="text-gray-400 text-xs">{benefit.description}</p>
                        </div>
                      </motion.div>
                    ))}
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            {/* Action Buttons */}
            <div className="space-y-3">
              <Link
                href="/signup"
                className="w-full flex items-center justify-center gap-2 px-6 py-3 bg-gradient-to-r from-neural-purple to-neural-pink text-white font-semibold rounded-lg hover:opacity-90 transition-opacity"
                onClick={() => {
                  onClose();
                  // Store the intent to continue after signup
                  localStorage.setItem('redirectAfterAuth', window.location.pathname);
                  localStorage.setItem('pendingAction', 'download');
                }}
              >
                <UserPlus className="h-4 w-4" />
                Create Free Account
              </Link>

              <Link
                href="/login"
                className="w-full flex items-center justify-center gap-2 px-6 py-3 bg-gray-800 hover:bg-gray-700 text-gray-300 font-medium rounded-lg transition-colors"
                onClick={() => {
                  onClose();
                  // Store the intent to continue after login
                  localStorage.setItem('redirectAfterAuth', window.location.pathname);
                  localStorage.setItem('pendingAction', 'download');
                }}
              >
                <LogIn className="h-4 w-4" />
                Sign In to Existing Account
              </Link>

              <button
                onClick={onClose}
                className="w-full px-6 py-2 text-gray-400 hover:text-gray-300 text-sm transition-colors"
              >
                Maybe later
              </button>
            </div>

            {/* Footer */}
            <div className="mt-6 pt-4 border-t border-gray-800">
              <div className="flex items-center justify-center gap-2 text-xs text-gray-500">
                <Shield className="h-3 w-3" />
                <span>Your data is secure and your account is always free</span>
              </div>
            </div>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );
};

export default AuthGuardModal;

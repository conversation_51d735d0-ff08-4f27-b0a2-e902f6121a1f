'use client';

import React, { useContext, useState, useEffect, createContext } from 'react';

const AuthContext = createContext();

export function useAuth() {
  return useContext(AuthContext);
}

export function AuthProvider({ children }) {
  const [currentUser, setCurrentUser] = useState(null);
  const [loading, setLoading] = useState(false); // Set to false for demo mode

  // Mock authentication functions for development
  function signup(email, password) {
    return new Promise((resolve) => {
      setTimeout(() => {
        const mockUser = { uid: 'mock-uid', email, metadata: { creationTime: new Date().toISOString() } };
        setCurrentUser(mockUser);
        resolve({ user: mockUser });
      }, 1000);
    });
  }

  function login(email, password) {
    return new Promise((resolve) => {
      setTimeout(() => {
        const mockUser = { 
          uid: 'mock-uid', 
          email, 
          metadata: { 
            creationTime: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
            lastSignInTime: new Date().toISOString()
          } 
        };
        setCurrentUser(mockUser);
        resolve({ user: mockUser });
      }, 1000);
    });
  }

  function logout() {
    return new Promise((resolve) => {
      setTimeout(() => {
        setCurrentUser(null);
        resolve();
      }, 500);
    });
  }

  function loginWithGoogle() {
    return new Promise((resolve) => {
      setTimeout(() => {
        const mockUser = { 
          uid: 'google-mock-uid', 
          email: '<EMAIL>',
          displayName: 'Demo User',
          metadata: { 
            creationTime: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000).toISOString(),
            lastSignInTime: new Date().toISOString()
          }
        };
        setCurrentUser(mockUser);
        resolve({ user: mockUser });
      }, 1000);
    });
  }

  const value = {
    currentUser,
    signup,
    login,
    logout,
    loginWithGoogle,
    loading
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

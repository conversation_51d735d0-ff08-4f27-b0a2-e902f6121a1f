'use client';
import React from 'react';
import { motion } from 'framer-motion';
import { AlertTriangle, RefreshCw, Home } from 'lucide-react';

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null, errorInfo: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    this.setState({
      error: error,
      errorInfo: errorInfo
    });
    
    // Log error to console in development
    if (process.env.NODE_ENV === 'development') {
      console.error('Resume Builder Error:', error, errorInfo);
    }
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen bg-gradient-to-b from-black to-[#0A0A0A] flex items-center justify-center p-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-gray-900/60 backdrop-blur-md rounded-2xl border border-gray-700/50 p-8 max-w-md w-full text-center"
          >
            <div className="flex items-center justify-center mb-6">
              <div className="relative">
                <AlertTriangle className="h-12 w-12 text-red-400" />
                <div className="absolute inset-0 bg-red-400 opacity-20 rounded-full blur-md"></div>
              </div>
            </div>
            
            <h2 className="text-xl font-semibold text-white mb-4">
              Something went wrong
            </h2>
            
            <p className="text-gray-300 mb-6">
              We encountered an unexpected error while building your resume. 
              Don't worry - your progress is saved!
            </p>
            
            <div className="space-y-3">
              <button
                onClick={() => window.location.reload()}
                className="w-full flex items-center justify-center gap-2 px-4 py-3 bg-gradient-to-r from-neural-purple to-neural-pink text-white rounded-lg hover:opacity-90 transition-opacity"
              >
                <RefreshCw className="h-4 w-4" />
                Try Again
              </button>
              
              <button
                onClick={() => window.location.href = '/'}
                className="w-full flex items-center justify-center gap-2 px-4 py-3 bg-gray-800 text-gray-300 rounded-lg hover:bg-gray-700 transition-colors border border-gray-600"
              >
                <Home className="h-4 w-4" />
                Go Home
              </button>
            </div>
            
            {process.env.NODE_ENV === 'development' && this.state.error && (
              <details className="mt-6 text-left">
                <summary className="text-sm text-gray-400 cursor-pointer hover:text-gray-300">
                  Error Details (Development)
                </summary>
                <div className="mt-2 p-3 bg-gray-800 rounded text-xs text-red-300 overflow-auto max-h-32">
                  <div className="font-mono">
                    {this.state.error && this.state.error.toString()}
                  </div>
                  <div className="mt-2 font-mono text-gray-400">
                    {this.state.errorInfo.componentStack}
                  </div>
                </div>
              </details>
            )}
          </motion.div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;

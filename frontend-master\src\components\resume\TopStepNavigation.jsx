'use client';
import { motion } from 'framer-motion';
import { 
  ArrowLeft, ArrowRight, Save, Home, Check,
  User, GraduationCap, Briefcase, Award, FileText
} from 'lucide-react';

const TopStepNavigation = ({
  currentStep,
  totalSteps,
  onPrevious,
  onNext,
  onSave,
  onHome,
  canProceed = true,
  isSaving = false,
  completionPercentage = 0,
  stepTitles = [],
  completedSteps = []
}) => {
  const isFirstStep = currentStep === 0;
  const isLastStep = currentStep === totalSteps - 1;

  const getStepIcon = (stepIndex) => {
    const icons = [User, GraduationCap, Briefcase, Award, FileText];
    const Icon = icons[stepIndex] || FileText;
    return Icon;
  };

  const getStepStatus = (stepIndex) => {
    if (completedSteps.includes(stepIndex)) return 'completed';
    if (stepIndex === currentStep) return 'current';
    return 'upcoming';
  };

  return (
    <div className="bg-gray-900/60 backdrop-blur-md rounded-2xl border border-gray-700/50 mb-8">
      {/* Mobile Progress Header */}
      <div className="p-4 border-b border-gray-700/30">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-3">
            <button
              onClick={onHome}
              className="p-2 text-gray-400 hover:text-white transition-colors rounded-lg hover:bg-gray-700/50"
              title="Back to Home"
            >
              <Home className="h-4 w-4" />
            </button>
            <div>
              <h3 className="text-lg font-semibold text-white">
                {stepTitles[currentStep] || `Step ${currentStep + 1}`}
              </h3>
              <p className="text-sm text-gray-400">
                Step {currentStep + 1} of {totalSteps} • {Math.round(completionPercentage)}% Complete
              </p>
            </div>
          </div>
          
          <button
            onClick={onSave}
            disabled={isSaving}
            className="flex items-center gap-2 px-3 py-2 bg-gray-800 hover:bg-gray-700 text-gray-300 rounded-lg font-medium transition-colors disabled:opacity-50 border border-gray-600"
          >
            <Save className="h-4 w-4" />
            <span className="hidden sm:inline">
              {isSaving ? 'Saving...' : 'Save'}
            </span>
          </button>
        </div>

        {/* Progress Bar */}
        <div className="w-full bg-gray-700 rounded-full h-2 mb-4">
          <motion.div
            className="bg-gradient-to-r from-neural-purple to-neural-pink h-2 rounded-full"
            initial={{ width: 0 }}
            animate={{ width: `${completionPercentage}%` }}
            transition={{ duration: 0.5 }}
          />
        </div>

        {/* Step Indicators - Mobile Horizontal Scroll */}
        <div className="overflow-x-auto">
          <div className="flex items-center gap-3 min-w-max pb-2">
            {stepTitles.map((title, index) => {
              const Icon = getStepIcon(index);
              const status = getStepStatus(index);
              
              return (
                <div key={index} className="flex items-center">
                  <div className="flex flex-col items-center min-w-[80px]">
                    <div className={`
                      w-8 h-8 rounded-full border-2 flex items-center justify-center transition-all duration-200 mb-1
                      ${status === 'completed' 
                        ? 'bg-neural-pink border-neural-pink text-white' 
                        : status === 'current'
                        ? 'bg-neural-purple border-neural-purple text-white'
                        : 'bg-gray-800 border-gray-600 text-gray-400'
                      }
                    `}>
                      {status === 'completed' ? (
                        <Check className="h-4 w-4" />
                      ) : (
                        <Icon className="h-4 w-4" />
                      )}
                    </div>
                    <span className={`
                      text-xs font-medium text-center
                      ${status === 'current' ? 'text-neural-purple' : 'text-gray-400'}
                    `}>
                      {title}
                    </span>
                  </div>
                  
                  {/* Connector Line */}
                  {index < stepTitles.length - 1 && (
                    <div className="w-8 h-0.5 mx-2 bg-gray-700 relative">
                      <motion.div
                        className="h-full bg-neural-pink"
                        initial={{ width: 0 }}
                        animate={{ 
                          width: index < currentStep ? '100%' : '0%' 
                        }}
                        transition={{ duration: 0.3 }}
                      />
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Navigation Buttons */}
      <div className="p-4">
        <div className="flex items-center justify-between gap-4">
          {/* Previous Button */}
          <button
            onClick={onPrevious}
            disabled={isFirstStep}
            className={`
              flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-all duration-200 min-w-[100px]
              ${isFirstStep 
                ? 'bg-gray-800 text-gray-500 cursor-not-allowed' 
                : 'bg-gray-800 text-gray-300 hover:bg-gray-700 border border-gray-600'
              }
            `}
          >
            <ArrowLeft className="h-4 w-4" />
            <span>Previous</span>
          </button>

          {/* Step Info - Desktop Only */}
          <div className="hidden md:flex flex-col items-center text-center">
            <p className="text-sm text-gray-400">
              {isLastStep ? 'Your AI-powered resume is ready' : 'Building your professional resume with AI'}
            </p>
          </div>

          {/* Next Button */}
          <button
            onClick={onNext}
            disabled={!canProceed}
            className={`
              flex items-center gap-2 px-6 py-2 rounded-lg font-medium transition-all duration-200 min-w-[100px]
              ${canProceed
                ? 'bg-gradient-to-r from-neural-purple to-neural-pink text-white shadow-lg hover:opacity-90'
                : 'bg-gray-800 text-gray-500 cursor-not-allowed border border-gray-700'
              }
            `}
          >
            <span>{isLastStep ? 'Complete' : 'Next'}</span>
            <ArrowRight className="h-4 w-4" />
          </button>
        </div>

        {/* Mobile Step Description */}
        <div className="md:hidden mt-3 text-center">
          <p className="text-sm text-gray-400">
            {isLastStep ? 'Your AI-powered resume is ready' : 'Building your professional resume with AI'}
          </p>
        </div>
      </div>
    </div>
  );
};

export default TopStepNavigation;

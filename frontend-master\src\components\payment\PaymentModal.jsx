'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  X, 
  CreditCard, 
  Smartphone, 
  Globe, 
  Shield, 
  Check,
  Loader2,
  AlertTriangle,
  ArrowLeft
} from 'lucide-react';
import { formatPrice } from '@/config/pricing';
import StripePayment from './StripePayment';
import RazorpayPayment from './RazorpayPayment';

const PaymentModal = ({ 
  plan, 
  billingInterval = 'monthly', 
  currency = 'USD', 
  isIndianUser = false,
  onClose,
  onSuccess 
}) => {
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState(null);
  const [step, setStep] = useState('method-selection'); // method-selection, payment-processing, success

  useEffect(() => {
    // Auto-select payment method based on region
    if (isIndianUser) {
      setSelectedPaymentMethod('razorpay');
    } else {
      setSelectedPaymentMethod('stripe');
    }
  }, [isIndianUser]);

  const getPrice = () => {
    if (plan.price === undefined) return plan.priceINR || plan.price;
    
    if (billingInterval === 'yearly' && plan.yearlyPrice) {
      return plan.yearlyPrice;
    }
    
    if (isIndianUser && plan.priceINR) {
      return plan.priceINR;
    }
    
    return plan.price;
  };

  const paymentMethods = [
    {
      id: 'stripe',
      name: 'Credit/Debit Card',
      description: 'Visa, Mastercard, American Express',
      icon: <CreditCard className="h-6 w-6" />,
      available: !isIndianUser || currency !== 'INR',
      recommended: !isIndianUser
    },
    {
      id: 'razorpay',
      name: 'Razorpay',
      description: 'UPI, Net Banking, Cards, Wallets',
      icon: <Smartphone className="h-6 w-6" />,
      available: isIndianUser,
      recommended: isIndianUser
    }
  ];

  const handlePaymentSuccess = (paymentData) => {
    setStep('success');
    setTimeout(() => {
      onSuccess?.(paymentData);
      onClose();
    }, 2000);
  };

  const handlePaymentError = (error) => {
    setError(error.message || 'Payment failed. Please try again.');
    setIsProcessing(false);
  };

  const renderMethodSelection = () => (
    <div className="space-y-6">
      {/* Plan Summary */}
      <div className="bg-gray-800/50 rounded-xl p-4 border border-gray-700">
        <div className="flex items-center justify-between mb-2">
          <h3 className="font-semibold text-white">{plan.name}</h3>
          <span className="text-2xl font-bold text-white">
            {formatPrice(getPrice(), currency)}
            {billingInterval && plan.interval !== 'forever' && (
              <span className="text-sm text-gray-400">
                /{billingInterval === 'yearly' ? 'year' : plan.interval}
              </span>
            )}
          </span>
        </div>
        
        {billingInterval === 'yearly' && plan.yearlyPrice && plan.price && (
          <div className="text-sm text-green-400">
            Save {formatPrice((plan.price * 12) - plan.yearlyPrice, currency)} annually
          </div>
        )}
      </div>

      {/* Payment Methods */}
      <div className="space-y-3">
        <h4 className="font-medium text-white">Choose Payment Method</h4>
        {paymentMethods.filter(method => method.available).map((method) => (
          <button
            key={method.id}
            onClick={() => setSelectedPaymentMethod(method.id)}
            className={`w-full p-4 rounded-xl border transition-all duration-200 text-left ${
              selectedPaymentMethod === method.id
                ? 'border-neural-purple bg-neural-purple/10'
                : 'border-gray-700 bg-gray-800/30 hover:border-gray-600'
            }`}
          >
            <div className="flex items-center gap-3">
              <div className={`${
                selectedPaymentMethod === method.id ? 'text-neural-purple' : 'text-gray-400'
              }`}>
                {method.icon}
              </div>
              <div className="flex-1">
                <div className="flex items-center gap-2">
                  <span className="font-medium text-white">{method.name}</span>
                  {method.recommended && (
                    <span className="px-2 py-1 bg-neural-purple/20 text-neural-purple text-xs rounded-full">
                      Recommended
                    </span>
                  )}
                </div>
                <p className="text-sm text-gray-400">{method.description}</p>
              </div>
              <div className={`w-4 h-4 rounded-full border-2 ${
                selectedPaymentMethod === method.id
                  ? 'border-neural-purple bg-neural-purple'
                  : 'border-gray-600'
              }`}>
                {selectedPaymentMethod === method.id && (
                  <Check className="h-3 w-3 text-white" />
                )}
              </div>
            </div>
          </button>
        ))}
      </div>

      {/* Security Notice */}
      <div className="flex items-center gap-2 text-sm text-gray-400 bg-gray-800/30 p-3 rounded-lg">
        <Shield className="h-4 w-4" />
        <span>Your payment information is encrypted and secure</span>
      </div>

      {/* Action Buttons */}
      <div className="flex gap-3">
        <button
          onClick={onClose}
          className="flex-1 py-3 px-4 bg-gray-700 hover:bg-gray-600 text-white rounded-xl transition-colors"
        >
          Cancel
        </button>
        <button
          onClick={() => setStep('payment-processing')}
          disabled={!selectedPaymentMethod}
          className="flex-1 py-3 px-4 bg-neural-purple hover:bg-neural-purple/80 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-xl transition-colors"
        >
          Continue to Payment
        </button>
      </div>
    </div>
  );

  const renderPaymentProcessing = () => (
    <div className="space-y-6">
      {/* Back Button */}
      <button
        onClick={() => setStep('method-selection')}
        className="flex items-center gap-2 text-gray-400 hover:text-white transition-colors"
      >
        <ArrowLeft className="h-4 w-4" />
        Back to payment methods
      </button>

      {/* Payment Component */}
      {selectedPaymentMethod === 'stripe' && (
        <StripePayment
          plan={plan}
          amount={getPrice()}
          currency={currency}
          billingInterval={billingInterval}
          onSuccess={handlePaymentSuccess}
          onError={handlePaymentError}
          isProcessing={isProcessing}
          setIsProcessing={setIsProcessing}
        />
      )}

      {selectedPaymentMethod === 'razorpay' && (
        <RazorpayPayment
          plan={plan}
          amount={getPrice()}
          currency={currency}
          billingInterval={billingInterval}
          onSuccess={handlePaymentSuccess}
          onError={handlePaymentError}
          isProcessing={isProcessing}
          setIsProcessing={setIsProcessing}
        />
      )}

      {/* Error Display */}
      {error && (
        <div className="bg-red-500/10 border border-red-500/20 rounded-xl p-4">
          <div className="flex items-center gap-2 text-red-400">
            <AlertTriangle className="h-4 w-4" />
            <span className="font-medium">Payment Error</span>
          </div>
          <p className="text-red-300 text-sm mt-1">{error}</p>
        </div>
      )}
    </div>
  );

  const renderSuccess = () => (
    <div className="text-center space-y-6">
      <div className="w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mx-auto">
        <Check className="h-8 w-8 text-green-400" />
      </div>
      
      <div>
        <h3 className="text-xl font-bold text-white mb-2">Payment Successful!</h3>
        <p className="text-gray-400">
          Welcome to {plan.name}! Your account has been upgraded successfully.
        </p>
      </div>

      <div className="bg-gray-800/50 rounded-xl p-4 border border-gray-700">
        <div className="text-sm text-gray-400 mb-1">Plan Activated</div>
        <div className="font-semibold text-white">{plan.name}</div>
        <div className="text-neural-purple font-medium">
          {formatPrice(getPrice(), currency)}
          {billingInterval && plan.interval !== 'forever' && (
            <span className="text-gray-400">
              /{billingInterval === 'yearly' ? 'year' : plan.interval}
            </span>
          )}
        </div>
      </div>

      <div className="flex items-center justify-center gap-2 text-sm text-gray-400">
        <Loader2 className="h-4 w-4 animate-spin" />
        <span>Redirecting to dashboard...</span>
      </div>
    </div>
  );

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
      onClick={(e) => e.target === e.currentTarget && onClose()}
    >
      <motion.div
        initial={{ opacity: 0, scale: 0.95, y: 20 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        exit={{ opacity: 0, scale: 0.95, y: 20 }}
        className="bg-gray-900 rounded-2xl border border-gray-700 p-6 w-full max-w-md max-h-[90vh] overflow-y-auto"
      >
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-bold text-white">
            {step === 'method-selection' && 'Complete Purchase'}
            {step === 'payment-processing' && 'Payment Details'}
            {step === 'success' && 'Payment Complete'}
          </h2>
          {step !== 'success' && (
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-white transition-colors"
            >
              <X className="h-5 w-5" />
            </button>
          )}
        </div>

        {/* Content */}
        <AnimatePresence mode="wait">
          <motion.div
            key={step}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.2 }}
          >
            {step === 'method-selection' && renderMethodSelection()}
            {step === 'payment-processing' && renderPaymentProcessing()}
            {step === 'success' && renderSuccess()}
          </motion.div>
        </AnimatePresence>
      </motion.div>
    </motion.div>
  );
};

export default PaymentModal;

'use client';
import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Sparkles, Wand2, RefreshCw, Check, X, 
  Loader2, ChevronDown, ChevronUp, Zap
} from 'lucide-react';
import { enhanceContent } from '@/services/geminiService';

const AIContentEnhancer = ({ 
  content, 
  type, 
  context = {}, 
  onContentSelect, 
  placeholder = "Enter your content here..."
}) => {
  const [isEnhancing, setIsEnhancing] = useState(false);
  const [enhancements, setEnhancements] = useState(null);
  const [showEnhancements, setShowEnhancements] = useState(false);
  const [selectedVariation, setSelectedVariation] = useState(null);

  const handleEnhance = async () => {
    if (!content?.trim()) return;
    
    setIsEnhancing(true);
    try {
      const result = await enhanceContent(content, type, context);
      setEnhancements(result);
      setShowEnhancements(true);
    } catch (error) {
      console.error('Enhancement failed:', error);
      // Show fallback enhancements
      setEnhancements({
        variations: [
          {
            title: "Enhanced Version",
            content: content
          }
        ]
      });
      setShowEnhancements(true);
    } finally {
      setIsEnhancing(false);
    }
  };

  const handleSelectVariation = (variation) => {
    setSelectedVariation(variation);
    onContentSelect(variation.content);
    setShowEnhancements(false);
    setEnhancements(null);
  };

  const handleReject = () => {
    setShowEnhancements(false);
    setEnhancements(null);
    setSelectedVariation(null);
  };

  const getTypeLabel = () => {
    switch (type) {
      case 'summary': return 'Professional Summary';
      case 'experience': return 'Job Description';
      case 'skills': return 'Skills';
      default: return 'Content';
    }
  };

  const getTypeIcon = () => {
    switch (type) {
      case 'summary': return '📝';
      case 'experience': return '💼';
      case 'skills': return '🎯';
      default: return '✨';
    }
  };

  return (
    <div className="space-y-3">
      {/* AI Enhancement Button */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <span className="text-xs text-gray-400">
            {getTypeIcon()} {getTypeLabel()}
          </span>
          {selectedVariation && (
            <span className="text-xs px-2 py-0.5 bg-neural-purple/20 text-neural-purple rounded">
              AI Enhanced
            </span>
          )}
        </div>
        
        <button
          onClick={handleEnhance}
          disabled={!content?.trim() || isEnhancing}
          className={`
            flex items-center gap-2 px-3 py-1.5 rounded-lg text-xs font-medium transition-all duration-200
            ${content?.trim() && !isEnhancing
              ? 'bg-gradient-to-r from-neural-purple to-neural-pink text-white hover:opacity-90 shadow-sm'
              : 'bg-gray-800 text-gray-500 cursor-not-allowed'
            }
          `}
        >
          {isEnhancing ? (
            <>
              <Loader2 className="h-3 w-3 animate-spin" />
              <span>Enhancing...</span>
            </>
          ) : (
            <>
              <Wand2 className="h-3 w-3" />
              <span>Enhance with AI</span>
            </>
          )}
        </button>
      </div>

      {/* Enhancement Results */}
      <AnimatePresence>
        {showEnhancements && enhancements && (
          <motion.div
            initial={{ opacity: 0, y: -10, height: 0 }}
            animate={{ opacity: 1, y: 0, height: 'auto' }}
            exit={{ opacity: 0, y: -10, height: 0 }}
            className="bg-gray-800/50 border border-gray-700/50 rounded-xl p-4 space-y-3"
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Sparkles className="h-4 w-4 text-neural-pink" />
                <span className="text-sm font-medium text-white">AI Suggestions</span>
              </div>
              <div className="flex items-center gap-1">
                <button
                  onClick={handleReject}
                  className="p-1 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors"
                  title="Close"
                >
                  <X className="h-3 w-3" />
                </button>
              </div>
            </div>

            <div className="space-y-3">
              {enhancements.variations?.map((variation, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="bg-gray-900/50 border border-gray-600/30 rounded-lg p-3 hover:border-neural-purple/50 transition-colors cursor-pointer group"
                  onClick={() => handleSelectVariation(variation)}
                >
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-neural-blue rounded-full"></div>
                      <span className="text-xs font-medium text-neural-blue">
                        {variation.title}
                      </span>
                    </div>
                    <button className="opacity-0 group-hover:opacity-100 p-1 text-green-400 hover:bg-green-400/20 rounded transition-all">
                      <Check className="h-3 w-3" />
                    </button>
                  </div>
                  
                  <div className="text-sm text-gray-300 leading-relaxed">
                    {type === 'experience' ? (
                      <div className="space-y-1">
                        {variation.content.split('\n').map((line, i) => (
                          <div key={i} className="flex items-start gap-2">
                            <span className="text-neural-purple mt-1">•</span>
                            <span>{line.trim()}</span>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <p>{variation.content}</p>
                    )}
                  </div>
                  
                  <div className="mt-2 flex items-center gap-2 text-xs text-gray-500">
                    <Zap className="h-3 w-3" />
                    <span>ATS Optimized</span>
                    <span>•</span>
                    <span>Click to use</span>
                  </div>
                </motion.div>
              ))}
            </div>

            <div className="flex items-center justify-between pt-2 border-t border-gray-700/30">
              <div className="text-xs text-gray-500">
                💡 These suggestions are optimized for ATS systems
              </div>
              <button
                onClick={handleEnhance}
                disabled={isEnhancing}
                className="flex items-center gap-1 text-xs text-neural-blue hover:text-neural-purple transition-colors"
              >
                <RefreshCw className="h-3 w-3" />
                <span>Generate new</span>
              </button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default AIContentEnhancer;

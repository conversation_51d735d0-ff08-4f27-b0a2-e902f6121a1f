'use client';

import { useState, useEffect } from 'react';
import { collection, getCountFromServer } from 'firebase/firestore';
import { firestore } from '@/firebase/config';
import { Users, TrendingUp, Shield, Zap } from 'lucide-react';
import { motion } from 'framer-motion';

const UserCount = () => {
  const [userCount, setUserCount] = useState(5000); // Starting with a base count
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const getUserCount = async () => {
      try {
        // Check if firestore is properly initialized
        if (!firestore) {
          console.log('Firestore not initialized, using default count');
          setUserCount(5847); // Default count
          setLoading(false);
          return;
        }

        // Get count from reviews collection as a proxy for active users
        const reviewsSnapshot = await getCountFromServer(collection(firestore, 'reviews'));
        const reviewsCount = reviewsSnapshot.data().count;
        
        // Base user count plus active reviewers, with some estimation
        const estimatedUsers = 5000 + (reviewsCount * 10); // Estimate 10 users per reviewer
        setUserCount(estimatedUsers);
      } catch (error) {
        console.error('Error fetching user count:', error);
        // Keep the default count if there's an error
        setUserCount(5847);
      } finally {
        setLoading(false);
      }
    };

    getUserCount();
    
    // Update count every 30 seconds
    const interval = setInterval(getUserCount, 30000);
    return () => clearInterval(interval);
  }, []);

  const stats = [
    {
      icon: Users,
      label: 'Active Users',
      value: userCount.toLocaleString(),
      color: 'text-neural-blue'
    },
    {
      icon: TrendingUp,
      label: 'Success Rate',
      value: '98%',
      color: 'text-neural-pink'
    },
    {
      icon: Shield,
      label: 'Platform Security',
      value: '100%',
      color: 'text-green-500'
    },
    {
      icon: Zap,
      label: 'Speed Boost',
      value: '10x',
      color: 'text-neural-purple'
    }
  ];

  return (
    <motion.div 
      className="mt-8 flex flex-wrap items-center justify-center gap-6 text-sm text-gray-400"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.5 }}
    >
      {stats.map((stat, index) => (
        <motion.div
          key={stat.label}
          className="flex items-center gap-2"
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.6 + index * 0.1 }}
        >
          <stat.icon className={`h-4 w-4 ${stat.color}`} />
          <span className={`font-semibold ${stat.color}`}>
            {loading && stat.label === 'Active Users' ? '...' : stat.value}
          </span>
          <span className="text-gray-400">{stat.label}</span>
        </motion.div>
      ))}
    </motion.div>
  );
};

export default UserCount;

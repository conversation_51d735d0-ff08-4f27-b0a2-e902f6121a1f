"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/resume-builder/page",{

/***/ "(app-pages-browser)/./src/components/resume/SimpleResumePreview.jsx":
/*!*******************************************************!*\
  !*** ./src/components/resume/SimpleResumePreview.jsx ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_EyeOff_FileText_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,EyeOff,FileText!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_EyeOff_FileText_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,EyeOff,FileText!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n// import { renderEnhancedTemplate } from './templates/EnhancedTemplateSystem';\nconst SimpleResumePreview = (param)=>{\n    let { formData, selectedTemplate = 'classic_ats', showPreview = false, onTogglePreview } = param;\n    _s();\n    const [zoom] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0.4); // Fixed zoom for sidebar\n    // Render the resume content\n    const renderResumeContent = ()=>{\n        if (!formData) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-full text-gray-400\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_EyeOff_FileText_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            className: \"h-8 w-8 mx-auto mb-2 opacity-50\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimpleResumePreview.jsx\",\n                            lineNumber: 21,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs\",\n                            children: \"Fill out the form to see preview\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimpleResumePreview.jsx\",\n                            lineNumber: 22,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimpleResumePreview.jsx\",\n                    lineNumber: 20,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimpleResumePreview.jsx\",\n                lineNumber: 19,\n                columnNumber: 9\n            }, undefined);\n        }\n        return renderEnhancedTemplate(selectedTemplate, formData);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gray-900/60 backdrop-blur-md rounded-2xl border border-white/10 h-full flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between p-4 border-b border-white/10 flex-shrink-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold flex items-center gap-2 text-white\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_EyeOff_FileText_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"h-5 w-5 text-neural-purple\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimpleResumePreview.jsx\",\n                                lineNumber: 36,\n                                columnNumber: 11\n                            }, undefined),\n                            \"Live Preview\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimpleResumePreview.jsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onTogglePreview,\n                            className: \"px-3 py-1.5 rounded-lg text-sm font-medium transition-colors \".concat(showPreview ? 'bg-neural-purple text-white' : 'bg-gray-800 text-gray-300 hover:bg-gray-700'),\n                            children: showPreview ? 'Hide' : 'Show'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimpleResumePreview.jsx\",\n                            lineNumber: 40,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimpleResumePreview.jsx\",\n                        lineNumber: 39,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimpleResumePreview.jsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-hidden\",\n                children: showPreview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-full overflow-y-auto p-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            scale: 0.95\n                        },\n                        animate: {\n                            opacity: 1,\n                            scale: 1\n                        },\n                        transition: {\n                            duration: 0.3\n                        },\n                        className: \"bg-white rounded-lg shadow-lg\",\n                        style: {\n                            transform: \"scale(\".concat(zoom, \")\"),\n                            transformOrigin: 'top left',\n                            width: \"\".concat(100 / zoom, \"%\"),\n                            height: \"\".concat(100 / zoom, \"%\")\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full h-full\",\n                            style: {\n                                fontFamily: \"'Inter', 'Helvetica Neue', Arial, sans-serif\",\n                                fontSize: '11pt',\n                                lineHeight: '1.4',\n                                color: '#000000',\n                                padding: '0.75in',\n                                backgroundColor: '#ffffff',\n                                minHeight: '11in'\n                            },\n                            children: renderResumeContent()\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimpleResumePreview.jsx\",\n                            lineNumber: 67,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimpleResumePreview.jsx\",\n                        lineNumber: 55,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimpleResumePreview.jsx\",\n                    lineNumber: 54,\n                    columnNumber: 11\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full text-gray-400\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_EyeOff_FileText_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"h-12 w-12 mx-auto mb-3 opacity-30\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimpleResumePreview.jsx\",\n                                lineNumber: 86,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm\",\n                                children: 'Click \"Show\" to preview'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimpleResumePreview.jsx\",\n                                lineNumber: 87,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-gray-500 mt-1\",\n                                children: \"Live updates as you type\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimpleResumePreview.jsx\",\n                                lineNumber: 88,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimpleResumePreview.jsx\",\n                        lineNumber: 85,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimpleResumePreview.jsx\",\n                    lineNumber: 84,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimpleResumePreview.jsx\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, undefined),\n            showPreview && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-3 border-t border-white/10 bg-gray-800/30 flex-shrink-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between text-xs text-gray-400\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: [\n                                \"Template: \",\n                                (selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.replace('_', ' ')) || 'Classic ATS'\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimpleResumePreview.jsx\",\n                            lineNumber: 98,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"Auto-updating\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimpleResumePreview.jsx\",\n                            lineNumber: 99,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimpleResumePreview.jsx\",\n                    lineNumber: 97,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimpleResumePreview.jsx\",\n                lineNumber: 96,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimpleResumePreview.jsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SimpleResumePreview, \"1BsvysT4e5bkMo9U9orGIJ06XPE=\");\n_c = SimpleResumePreview;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SimpleResumePreview);\nvar _c;\n$RefreshReg$(_c, \"SimpleResumePreview\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3Jlc3VtZS9TaW1wbGVSZXN1bWVQcmV2aWV3LmpzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFDaUM7QUFDTTtBQUN3QjtBQUMvRCwrRUFBK0U7QUFFL0UsTUFBTU0sc0JBQXNCO1FBQUMsRUFDM0JDLFFBQVEsRUFDUkMsbUJBQW1CLGFBQWEsRUFDaENDLGNBQWMsS0FBSyxFQUNuQkMsZUFBZSxFQUNoQjs7SUFDQyxNQUFNLENBQUNDLEtBQUssR0FBR1gsK0NBQVFBLENBQUMsTUFBTSx5QkFBeUI7SUFFdkQsNEJBQTRCO0lBQzVCLE1BQU1ZLHNCQUFzQjtRQUMxQixJQUFJLENBQUNMLFVBQVU7WUFDYixxQkFDRSw4REFBQ007Z0JBQUlDLFdBQVU7MEJBQ2IsNEVBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ1Qsd0dBQVFBOzRCQUFDUyxXQUFVOzs7Ozs7c0NBQ3BCLDhEQUFDQzs0QkFBRUQsV0FBVTtzQ0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7UUFJL0I7UUFFQSxPQUFPRSx1QkFBdUJSLGtCQUFrQkQ7SUFDbEQ7SUFFQSxxQkFDRSw4REFBQ007UUFBSUMsV0FBVTs7MEJBRWIsOERBQUNEO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0c7d0JBQUdILFdBQVU7OzBDQUNaLDhEQUFDWix3R0FBR0E7Z0NBQUNZLFdBQVU7Ozs7Ozs0QkFBK0I7Ozs7Ozs7a0NBR2hELDhEQUFDRDt3QkFBSUMsV0FBVTtrQ0FDYiw0RUFBQ0k7NEJBQ0NDLFNBQVNUOzRCQUNUSSxXQUFXLGdFQUVWLE9BRENMLGNBQWMsZ0NBQWdDO3NDQUcvQ0EsY0FBYyxTQUFTOzs7Ozs7Ozs7Ozs7Ozs7OzswQkFNOUIsOERBQUNJO2dCQUFJQyxXQUFVOzBCQUNaTCw0QkFDQyw4REFBQ0k7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUNiLGlEQUFNQSxDQUFDWSxHQUFHO3dCQUNUTyxTQUFTOzRCQUFFQyxTQUFTOzRCQUFHQyxPQUFPO3dCQUFLO3dCQUNuQ0MsU0FBUzs0QkFBRUYsU0FBUzs0QkFBR0MsT0FBTzt3QkFBRTt3QkFDaENFLFlBQVk7NEJBQUVDLFVBQVU7d0JBQUk7d0JBQzVCWCxXQUFVO3dCQUNWWSxPQUFPOzRCQUNMQyxXQUFXLFNBQWMsT0FBTGhCLE1BQUs7NEJBQ3pCaUIsaUJBQWlCOzRCQUNqQkMsT0FBTyxHQUFjLE9BQVgsTUFBTWxCLE1BQUs7NEJBQ3JCbUIsUUFBUSxHQUFjLE9BQVgsTUFBTW5CLE1BQUs7d0JBQ3hCO2tDQUVBLDRFQUFDRTs0QkFDQ0MsV0FBVTs0QkFDVlksT0FBTztnQ0FDTEssWUFBWTtnQ0FDWkMsVUFBVTtnQ0FDVkMsWUFBWTtnQ0FDWkMsT0FBTztnQ0FDUEMsU0FBUztnQ0FDVEMsaUJBQWlCO2dDQUNqQkMsV0FBVzs0QkFDYjtzQ0FFQ3pCOzs7Ozs7Ozs7Ozs7Ozs7OENBS1AsOERBQUNDO29CQUFJQyxXQUFVOzhCQUNiLDRFQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNaLHdHQUFHQTtnQ0FBQ1ksV0FBVTs7Ozs7OzBDQUNmLDhEQUFDQztnQ0FBRUQsV0FBVTswQ0FBVTs7Ozs7OzBDQUN2Qiw4REFBQ0M7Z0NBQUVELFdBQVU7MENBQTZCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O1lBT2pETCw2QkFDQyw4REFBQ0k7Z0JBQUlDLFdBQVU7MEJBQ2IsNEVBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ3dCOztnQ0FBSztnQ0FBVzlCLENBQUFBLDZCQUFBQSx1Q0FBQUEsaUJBQWtCK0IsT0FBTyxDQUFDLEtBQUssU0FBUTs7Ozs7OztzQ0FDeEQsOERBQUNEO3NDQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU1sQjtHQWxHTWhDO0tBQUFBO0FBb0dOLGlFQUFlQSxtQkFBbUJBLEVBQUMiLCJzb3VyY2VzIjpbIkM6XFxQcm9qZWN0c1xcTmV3QmxpbmtGaW5kQUlcXGZyb250ZW5kLW1hc3Rlclxcc3JjXFxjb21wb25lbnRzXFxyZXN1bWVcXFNpbXBsZVJlc3VtZVByZXZpZXcuanN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcbmltcG9ydCB7IHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgbW90aW9uIH0gZnJvbSAnZnJhbWVyLW1vdGlvbic7XG5pbXBvcnQgeyBFeWUsIEV5ZU9mZiwgRG93bmxvYWQsIEZpbGVUZXh0IH0gZnJvbSAnbHVjaWRlLXJlYWN0Jztcbi8vIGltcG9ydCB7IHJlbmRlckVuaGFuY2VkVGVtcGxhdGUgfSBmcm9tICcuL3RlbXBsYXRlcy9FbmhhbmNlZFRlbXBsYXRlU3lzdGVtJztcblxuY29uc3QgU2ltcGxlUmVzdW1lUHJldmlldyA9ICh7IFxuICBmb3JtRGF0YSwgXG4gIHNlbGVjdGVkVGVtcGxhdGUgPSAnY2xhc3NpY19hdHMnLFxuICBzaG93UHJldmlldyA9IGZhbHNlLFxuICBvblRvZ2dsZVByZXZpZXdcbn0pID0+IHtcbiAgY29uc3QgW3pvb21dID0gdXNlU3RhdGUoMC40KTsgLy8gRml4ZWQgem9vbSBmb3Igc2lkZWJhclxuXG4gIC8vIFJlbmRlciB0aGUgcmVzdW1lIGNvbnRlbnRcbiAgY29uc3QgcmVuZGVyUmVzdW1lQ29udGVudCA9ICgpID0+IHtcbiAgICBpZiAoIWZvcm1EYXRhKSB7XG4gICAgICByZXR1cm4gKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGgtZnVsbCB0ZXh0LWdyYXktNDAwXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgPEZpbGVUZXh0IGNsYXNzTmFtZT1cImgtOCB3LTggbXgtYXV0byBtYi0yIG9wYWNpdHktNTBcIiAvPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14c1wiPkZpbGwgb3V0IHRoZSBmb3JtIHRvIHNlZSBwcmV2aWV3PC9wPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgICk7XG4gICAgfVxuXG4gICAgcmV0dXJuIHJlbmRlckVuaGFuY2VkVGVtcGxhdGUoc2VsZWN0ZWRUZW1wbGF0ZSwgZm9ybURhdGEpO1xuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmF5LTkwMC82MCBiYWNrZHJvcC1ibHVyLW1kIHJvdW5kZWQtMnhsIGJvcmRlciBib3JkZXItd2hpdGUvMTAgaC1mdWxsIGZsZXggZmxleC1jb2xcIj5cbiAgICAgIHsvKiBQcmV2aWV3IEhlYWRlciAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHAtNCBib3JkZXItYiBib3JkZXItd2hpdGUvMTAgZmxleC1zaHJpbmstMFwiPlxuICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIGZsZXggaXRlbXMtY2VudGVyIGdhcC0yIHRleHQtd2hpdGVcIj5cbiAgICAgICAgICA8RXllIGNsYXNzTmFtZT1cImgtNSB3LTUgdGV4dC1uZXVyYWwtcHVycGxlXCIgLz5cbiAgICAgICAgICBMaXZlIFByZXZpZXdcbiAgICAgICAgPC9oMz5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgIG9uQ2xpY2s9e29uVG9nZ2xlUHJldmlld31cbiAgICAgICAgICAgIGNsYXNzTmFtZT17YHB4LTMgcHktMS41IHJvdW5kZWQtbGcgdGV4dC1zbSBmb250LW1lZGl1bSB0cmFuc2l0aW9uLWNvbG9ycyAke1xuICAgICAgICAgICAgICBzaG93UHJldmlldyA/ICdiZy1uZXVyYWwtcHVycGxlIHRleHQtd2hpdGUnIDogJ2JnLWdyYXktODAwIHRleHQtZ3JheS0zMDAgaG92ZXI6YmctZ3JheS03MDAnXG4gICAgICAgICAgICB9YH1cbiAgICAgICAgICA+XG4gICAgICAgICAgICB7c2hvd1ByZXZpZXcgPyAnSGlkZScgOiAnU2hvdyd9XG4gICAgICAgICAgPC9idXR0b24+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBQcmV2aWV3IENvbnRlbnQgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSBvdmVyZmxvdy1oaWRkZW5cIj5cbiAgICAgICAge3Nob3dQcmV2aWV3ID8gKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC1mdWxsIG92ZXJmbG93LXktYXV0byBwLTJcIj5cbiAgICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgc2NhbGU6IDAuOTUgfX1cbiAgICAgICAgICAgICAgYW5pbWF0ZT17eyBvcGFjaXR5OiAxLCBzY2FsZTogMSB9fVxuICAgICAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjMgfX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC1sZyBzaGFkb3ctbGdcIlxuICAgICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICAgIHRyYW5zZm9ybTogYHNjYWxlKCR7em9vbX0pYCxcbiAgICAgICAgICAgICAgICB0cmFuc2Zvcm1PcmlnaW46ICd0b3AgbGVmdCcsXG4gICAgICAgICAgICAgICAgd2lkdGg6IGAkezEwMCAvIHpvb219JWAsXG4gICAgICAgICAgICAgICAgaGVpZ2h0OiBgJHsxMDAgLyB6b29tfSVgXG4gICAgICAgICAgICAgIH19XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxkaXYgXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGgtZnVsbFwiXG4gICAgICAgICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICAgIGZvbnRGYW1pbHk6IFwiJ0ludGVyJywgJ0hlbHZldGljYSBOZXVlJywgQXJpYWwsIHNhbnMtc2VyaWZcIixcbiAgICAgICAgICAgICAgICAgIGZvbnRTaXplOiAnMTFwdCcsXG4gICAgICAgICAgICAgICAgICBsaW5lSGVpZ2h0OiAnMS40JyxcbiAgICAgICAgICAgICAgICAgIGNvbG9yOiAnIzAwMDAwMCcsXG4gICAgICAgICAgICAgICAgICBwYWRkaW5nOiAnMC43NWluJyxcbiAgICAgICAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogJyNmZmZmZmYnLFxuICAgICAgICAgICAgICAgICAgbWluSGVpZ2h0OiAnMTFpbidcbiAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAge3JlbmRlclJlc3VtZUNvbnRlbnQoKX1cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L21vdGlvbi5kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICkgOiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBoLWZ1bGwgdGV4dC1ncmF5LTQwMFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICA8RXllIGNsYXNzTmFtZT1cImgtMTIgdy0xMiBteC1hdXRvIG1iLTMgb3BhY2l0eS0zMFwiIC8+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc21cIj5DbGljayBcIlNob3dcIiB0byBwcmV2aWV3PC9wPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDAgbXQtMVwiPkxpdmUgdXBkYXRlcyBhcyB5b3UgdHlwZTwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApfVxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBQcmV2aWV3IEZvb3RlciAqL31cbiAgICAgIHtzaG93UHJldmlldyAmJiAoXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC0zIGJvcmRlci10IGJvcmRlci13aGl0ZS8xMCBiZy1ncmF5LTgwMC8zMCBmbGV4LXNocmluay0wXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gdGV4dC14cyB0ZXh0LWdyYXktNDAwXCI+XG4gICAgICAgICAgICA8c3Bhbj5UZW1wbGF0ZToge3NlbGVjdGVkVGVtcGxhdGU/LnJlcGxhY2UoJ18nLCAnICcpIHx8ICdDbGFzc2ljIEFUUyd9PC9zcGFuPlxuICAgICAgICAgICAgPHNwYW4+QXV0by11cGRhdGluZzwvc3Bhbj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuICAgIDwvZGl2PlxuICApO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgU2ltcGxlUmVzdW1lUHJldmlldztcbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsIm1vdGlvbiIsIkV5ZSIsIkV5ZU9mZiIsIkRvd25sb2FkIiwiRmlsZVRleHQiLCJTaW1wbGVSZXN1bWVQcmV2aWV3IiwiZm9ybURhdGEiLCJzZWxlY3RlZFRlbXBsYXRlIiwic2hvd1ByZXZpZXciLCJvblRvZ2dsZVByZXZpZXciLCJ6b29tIiwicmVuZGVyUmVzdW1lQ29udGVudCIsImRpdiIsImNsYXNzTmFtZSIsInAiLCJyZW5kZXJFbmhhbmNlZFRlbXBsYXRlIiwiaDMiLCJidXR0b24iLCJvbkNsaWNrIiwiaW5pdGlhbCIsIm9wYWNpdHkiLCJzY2FsZSIsImFuaW1hdGUiLCJ0cmFuc2l0aW9uIiwiZHVyYXRpb24iLCJzdHlsZSIsInRyYW5zZm9ybSIsInRyYW5zZm9ybU9yaWdpbiIsIndpZHRoIiwiaGVpZ2h0IiwiZm9udEZhbWlseSIsImZvbnRTaXplIiwibGluZUhlaWdodCIsImNvbG9yIiwicGFkZGluZyIsImJhY2tncm91bmRDb2xvciIsIm1pbkhlaWdodCIsInNwYW4iLCJyZXBsYWNlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/resume/SimpleResumePreview.jsx\n"));

/***/ })

});
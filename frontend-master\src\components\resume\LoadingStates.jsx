'use client';
import { motion } from 'framer-motion';
import { <PERSON>ader<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, Target } from 'lucide-react';

// AI Analysis Loading
export const AIAnalysisLoading = ({ message = "Analyzing your resume with AI..." }) => (
  <div className="flex items-center justify-center p-6">
    <div className="text-center">
      <motion.div
        className="relative mb-4"
        animate={{ rotate: 360 }}
        transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
      >
        <Brain className="h-8 w-8 text-neural-purple mx-auto" />
        <div className="absolute inset-0 bg-neural-purple opacity-20 rounded-full blur-md"></div>
      </motion.div>
      <p className="text-sm text-gray-300">{message}</p>
      <div className="flex items-center justify-center gap-1 mt-2">
        {[0, 1, 2].map((i) => (
          <motion.div
            key={i}
            className="w-1 h-1 bg-neural-blue rounded-full"
            animate={{ opacity: [0.3, 1, 0.3] }}
            transition={{ duration: 1.5, repeat: Infinity, delay: i * 0.2 }}
          />
        ))}
      </div>
    </div>
  </div>
);

// Content Enhancement Loading
export const ContentEnhancementLoading = ({ message = "Enhancing content with AI..." }) => (
  <div className="flex items-center justify-center p-4">
    <div className="text-center">
      <motion.div
        className="relative mb-3"
        animate={{ scale: [1, 1.1, 1] }}
        transition={{ duration: 1.5, repeat: Infinity }}
      >
        <Sparkles className="h-6 w-6 text-neural-pink mx-auto" />
        <div className="absolute inset-0 bg-neural-pink opacity-20 rounded-full blur-md"></div>
      </motion.div>
      <p className="text-xs text-gray-400">{message}</p>
    </div>
  </div>
);

// ATS Score Loading
export const ATSScoreLoading = () => (
  <div className="flex items-center justify-center p-3">
    <div className="text-center">
      <motion.div
        className="relative mb-2"
        animate={{ rotate: 360 }}
        transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
      >
        <Target className="h-5 w-5 text-neural-blue mx-auto" />
      </motion.div>
      <p className="text-xs text-gray-400">Calculating ATS score...</p>
    </div>
  </div>
);

// Resume Generation Loading
export const ResumeGenerationLoading = ({ progress = 0 }) => (
  <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center">
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      className="bg-gray-900/90 backdrop-blur-md rounded-2xl border border-gray-700/50 p-8 max-w-sm w-full mx-4 text-center"
    >
      <motion.div
        className="relative mb-6"
        animate={{ rotate: 360 }}
        transition={{ duration: 3, repeat: Infinity, ease: "linear" }}
      >
        <Zap className="h-12 w-12 text-neural-pink mx-auto" />
        <div className="absolute inset-0 bg-neural-pink opacity-20 rounded-full blur-md"></div>
      </motion.div>
      
      <h3 className="text-lg font-semibold text-white mb-2">
        Creating Your Resume
      </h3>
      
      <p className="text-gray-300 mb-6">
        AI is crafting your professional resume with optimized formatting and content...
      </p>
      
      {/* Progress Bar */}
      <div className="w-full bg-gray-700 rounded-full h-2 mb-4">
        <motion.div
          className="bg-gradient-to-r from-neural-purple to-neural-pink h-2 rounded-full"
          initial={{ width: 0 }}
          animate={{ width: `${progress}%` }}
          transition={{ duration: 0.5 }}
        />
      </div>
      
      <p className="text-sm text-gray-400">
        {progress}% Complete
      </p>
    </motion.div>
  </div>
);

// Simple Loading Spinner
export const LoadingSpinner = ({ size = 'md', color = 'neural-purple' }) => {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-6 w-6',
    lg: 'h-8 w-8'
  };
  
  const colorClasses = {
    'neural-purple': 'text-neural-purple',
    'neural-pink': 'text-neural-pink',
    'neural-blue': 'text-neural-blue',
    white: 'text-white',
    gray: 'text-gray-400'
  };
  
  return (
    <motion.div
      animate={{ rotate: 360 }}
      transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
      className={`${sizeClasses[size]} ${colorClasses[color]}`}
    >
      <Loader2 className="h-full w-full" />
    </motion.div>
  );
};

// Page Loading
export const PageLoading = () => (
  <div className="min-h-screen bg-gradient-to-b from-black to-[#0A0A0A] flex items-center justify-center">
    <div className="text-center">
      <motion.div
        className="relative mb-6"
        animate={{ 
          rotate: 360,
          scale: [1, 1.1, 1]
        }}
        transition={{ 
          rotate: { duration: 2, repeat: Infinity, ease: "linear" },
          scale: { duration: 1.5, repeat: Infinity }
        }}
      >
        <Sparkles className="h-12 w-12 text-neural-pink mx-auto" />
        <div className="absolute inset-0 bg-neural-pink opacity-20 rounded-full blur-md"></div>
      </motion.div>
      
      <h2 className="text-xl font-semibold text-white mb-2">
        Loading Resume Builder
      </h2>
      
      <p className="text-gray-400">
        Preparing your AI-powered resume experience...
      </p>
      
      <div className="flex items-center justify-center gap-1 mt-4">
        {[0, 1, 2, 3, 4].map((i) => (
          <motion.div
            key={i}
            className="w-2 h-2 bg-neural-blue rounded-full"
            animate={{ opacity: [0.3, 1, 0.3] }}
            transition={{ duration: 1.5, repeat: Infinity, delay: i * 0.1 }}
          />
        ))}
      </div>
    </div>
  </div>
);

export default {
  AIAnalysisLoading,
  ContentEnhancementLoading,
  ATSScoreLoading,
  ResumeGenerationLoading,
  LoadingSpinner,
  PageLoading
};

"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["default-_app-pages-browser_src_contexts_AuthContext_jsx"],{

/***/ "(app-pages-browser)/./src/contexts/AuthContext.jsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.jsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/auth */ \"(app-pages-browser)/./node_modules/firebase/auth/dist/esm/index.esm.js\");\n/* harmony import */ var _firebase_config__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../firebase/config */ \"(app-pages-browser)/./src/firebase/config.js\");\n/* __next_internal_client_entry_do_not_use__ useAuth,AuthProvider auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n // Adjusted path to Firebase config file\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)();\nfunction useAuth() {\n    _s();\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n}\n_s(useAuth, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\nfunction AuthProvider(param) {\n    let { children } = param;\n    _s1();\n    const [currentUser, setCurrentUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    function signup(email, password) {\n        if (!_firebase_config__WEBPACK_IMPORTED_MODULE_3__.auth) {\n            return Promise.reject(new Error('Authentication service not available'));\n        }\n        return (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.createUserWithEmailAndPassword)(_firebase_config__WEBPACK_IMPORTED_MODULE_3__.auth, email, password);\n    }\n    function login(email, password) {\n        if (!_firebase_config__WEBPACK_IMPORTED_MODULE_3__.auth) {\n            return Promise.reject(new Error('Authentication service not available'));\n        }\n        return (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.signInWithEmailAndPassword)(_firebase_config__WEBPACK_IMPORTED_MODULE_3__.auth, email, password);\n    }\n    function logout() {\n        if (!_firebase_config__WEBPACK_IMPORTED_MODULE_3__.auth) {\n            return Promise.reject(new Error('Authentication service not available'));\n        }\n        return (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.signOut)(_firebase_config__WEBPACK_IMPORTED_MODULE_3__.auth);\n    }\n    function loginWithGoogle() {\n        if (!_firebase_config__WEBPACK_IMPORTED_MODULE_3__.auth) {\n            return Promise.reject(new Error('Authentication service not available'));\n        }\n        const provider = new firebase_auth__WEBPACK_IMPORTED_MODULE_2__.GoogleAuthProvider();\n        return (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.signInWithPopup)(_firebase_config__WEBPACK_IMPORTED_MODULE_3__.auth, provider);\n    }\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            if (!_firebase_config__WEBPACK_IMPORTED_MODULE_3__.auth || !_firebase_config__WEBPACK_IMPORTED_MODULE_3__.auth.onAuthStateChanged) {\n                console.warn('Auth service not available, setting loading to false');\n                setLoading(false);\n                return;\n            }\n            const unsubscribe = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.onAuthStateChanged)(_firebase_config__WEBPACK_IMPORTED_MODULE_3__.auth, {\n                \"AuthProvider.useEffect.unsubscribe\": (user)=>{\n                    setCurrentUser(user);\n                    setLoading(false);\n                }\n            }[\"AuthProvider.useEffect.unsubscribe\"]);\n            return unsubscribe; // Cleanup subscription on unmount\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const value = {\n        currentUser,\n        signup,\n        login,\n        logout,\n        loginWithGoogle,\n        loading\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: !loading && children\n    }, void 0, false, {\n        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\contexts\\\\AuthContext.jsx\",\n        lineNumber: 76,\n        columnNumber: 5\n    }, this);\n}\n_s1(AuthProvider, \"+Bt7EujFHjK6mRV3YX1iAtSqXvQ=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/AuthContext.jsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/firebase/config.js":
/*!********************************!*\
  !*** ./src/firebase/config.js ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   database: () => (/* binding */ database),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   firestore: () => (/* binding */ firestore),\n/* harmony export */   storage: () => (/* binding */ storage)\n/* harmony export */ });\n/* harmony import */ var firebase_app__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/app */ \"(app-pages-browser)/./node_modules/firebase/app/dist/esm/index.esm.js\");\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/auth */ \"(app-pages-browser)/./node_modules/firebase/auth/dist/esm/index.esm.js\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* harmony import */ var firebase_storage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/storage */ \"(app-pages-browser)/./node_modules/firebase/storage/dist/esm/index.esm.js\");\n/* harmony import */ var firebase_database__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! firebase/database */ \"(app-pages-browser)/./node_modules/firebase/database/dist/esm/index.esm.js\");\n\n\n\n\n\n// Production Firebase configuration\nconst firebaseConfig = {\n    apiKey: \"AIzaSyAzqqKdReH-D0Vjg84kTHeSxFDfRo-Mi-k\",\n    authDomain: \"blinkfind-a8517.firebaseapp.com\",\n    projectId: \"blinkfind-a8517\",\n    storageBucket: \"blinkfind-a8517.firebasestorage.app\",\n    messagingSenderId: \"257986295350\",\n    appId: \"1:257986295350:web:60de4e79b9b8316e0f0a97\",\n    databaseURL: \"https://blinkfind-a8517-default-rtdb.firebaseio.com\",\n    measurementId: \"G-VD3CRECNBK\"\n};\n// Initialize Firebase services\nlet app;\nlet auth;\nlet firestore;\nlet storage;\nlet database;\ntry {\n    // Validate that required environment variables are present\n    if (!firebaseConfig.apiKey || !firebaseConfig.projectId) {\n        throw new Error('Missing required Firebase configuration. Please check your environment variables.');\n    }\n    // Initialize Firebase\n    app = (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.initializeApp)(firebaseConfig);\n    auth = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_1__.getAuth)(app);\n    firestore = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getFirestore)(app);\n    storage = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.getStorage)(app);\n    database = (0,firebase_database__WEBPACK_IMPORTED_MODULE_4__.getDatabase)(app);\n    console.log('🔥 Firebase initialized successfully with project:', firebaseConfig.projectId);\n} catch (error) {\n    console.error('❌ Firebase initialization failed:', error);\n    throw error; // Re-throw error to prevent silent failures\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (app);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9maXJlYmFzZS9jb25maWcuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUE2QztBQUNMO0FBQ1U7QUFDSjtBQUNFO0FBRWhELG9DQUFvQztBQUNwQyxNQUFNSyxpQkFBaUI7SUFDckJDLFFBQVFDLHlDQUF3QztJQUNoREcsWUFBWUgsaUNBQTRDO0lBQ3hESyxXQUFXTCxpQkFBMkM7SUFDdERPLGVBQWVQLHFDQUErQztJQUM5RFMsbUJBQW1CVCxjQUFvRDtJQUN2RVcsT0FBT1gsMkNBQXVDO0lBQzlDYSxhQUFhYixxREFBNkM7SUFDMURlLGVBQWVmLGNBQStDO0FBQ2hFO0FBRUEsK0JBQStCO0FBQy9CLElBQUlpQjtBQUNKLElBQUlDO0FBQ0osSUFBSUM7QUFDSixJQUFJQztBQUNKLElBQUlDO0FBRUosSUFBSTtJQUNGLDJEQUEyRDtJQUMzRCxJQUFJLENBQUN2QixlQUFlQyxNQUFNLElBQUksQ0FBQ0QsZUFBZU8sU0FBUyxFQUFFO1FBQ3ZELE1BQU0sSUFBSWlCLE1BQU07SUFDbEI7SUFFQSxzQkFBc0I7SUFDdEJMLE1BQU14QiwyREFBYUEsQ0FBQ0s7SUFDcEJvQixPQUFPeEIsc0RBQU9BLENBQUN1QjtJQUNmRSxZQUFZeEIsZ0VBQVlBLENBQUNzQjtJQUN6QkcsVUFBVXhCLDREQUFVQSxDQUFDcUI7SUFDckJJLFdBQVd4Qiw4REFBV0EsQ0FBQ29CO0lBRXZCTSxRQUFRQyxHQUFHLENBQUMsc0RBQXNEMUIsZUFBZU8sU0FBUztBQUM1RixFQUFFLE9BQU9vQixPQUFPO0lBQ2RGLFFBQVFFLEtBQUssQ0FBQyxxQ0FBcUNBO0lBQ25ELE1BQU1BLE9BQU8sNENBQTRDO0FBQzNEO0FBRThDO0FBQzlDLGlFQUFlUixHQUFHQSxFQUFDIiwic291cmNlcyI6WyJDOlxcUHJvamVjdHNcXE5ld0JsaW5rRmluZEFJXFxmcm9udGVuZC1tYXN0ZXJcXHNyY1xcZmlyZWJhc2VcXGNvbmZpZy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBpbml0aWFsaXplQXBwIH0gZnJvbSAnZmlyZWJhc2UvYXBwJztcbmltcG9ydCB7IGdldEF1dGggfSBmcm9tICdmaXJlYmFzZS9hdXRoJztcbmltcG9ydCB7IGdldEZpcmVzdG9yZSB9IGZyb20gJ2ZpcmViYXNlL2ZpcmVzdG9yZSc7XG5pbXBvcnQgeyBnZXRTdG9yYWdlIH0gZnJvbSAnZmlyZWJhc2Uvc3RvcmFnZSc7XG5pbXBvcnQgeyBnZXREYXRhYmFzZSB9IGZyb20gJ2ZpcmViYXNlL2RhdGFiYXNlJztcblxuLy8gUHJvZHVjdGlvbiBGaXJlYmFzZSBjb25maWd1cmF0aW9uXG5jb25zdCBmaXJlYmFzZUNvbmZpZyA9IHtcbiAgYXBpS2V5OiBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19GSVJFQkFTRV9BUElfS0VZLFxuICBhdXRoRG9tYWluOiBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19GSVJFQkFTRV9BVVRIX0RPTUFJTixcbiAgcHJvamVjdElkOiBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19GSVJFQkFTRV9QUk9KRUNUX0lELFxuICBzdG9yYWdlQnVja2V0OiBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19GSVJFQkFTRV9TVE9SQUdFX0JVQ0tFVCxcbiAgbWVzc2FnaW5nU2VuZGVySWQ6IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0ZJUkVCQVNFX01FU1NBR0lOR19TRU5ERVJfSUQsXG4gIGFwcElkOiBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19GSVJFQkFTRV9BUFBfSUQsXG4gIGRhdGFiYXNlVVJMOiBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19GSVJFQkFTRV9EQVRBQkFTRV9VUkwsXG4gIG1lYXN1cmVtZW50SWQ6IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0ZJUkVCQVNFX01FQVNVUkVNRU5UX0lEXG59O1xuXG4vLyBJbml0aWFsaXplIEZpcmViYXNlIHNlcnZpY2VzXG5sZXQgYXBwO1xubGV0IGF1dGg7XG5sZXQgZmlyZXN0b3JlO1xubGV0IHN0b3JhZ2U7XG5sZXQgZGF0YWJhc2U7XG5cbnRyeSB7XG4gIC8vIFZhbGlkYXRlIHRoYXQgcmVxdWlyZWQgZW52aXJvbm1lbnQgdmFyaWFibGVzIGFyZSBwcmVzZW50XG4gIGlmICghZmlyZWJhc2VDb25maWcuYXBpS2V5IHx8ICFmaXJlYmFzZUNvbmZpZy5wcm9qZWN0SWQpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ01pc3NpbmcgcmVxdWlyZWQgRmlyZWJhc2UgY29uZmlndXJhdGlvbi4gUGxlYXNlIGNoZWNrIHlvdXIgZW52aXJvbm1lbnQgdmFyaWFibGVzLicpO1xuICB9XG5cbiAgLy8gSW5pdGlhbGl6ZSBGaXJlYmFzZVxuICBhcHAgPSBpbml0aWFsaXplQXBwKGZpcmViYXNlQ29uZmlnKTtcbiAgYXV0aCA9IGdldEF1dGgoYXBwKTtcbiAgZmlyZXN0b3JlID0gZ2V0RmlyZXN0b3JlKGFwcCk7XG4gIHN0b3JhZ2UgPSBnZXRTdG9yYWdlKGFwcCk7XG4gIGRhdGFiYXNlID0gZ2V0RGF0YWJhc2UoYXBwKTtcbiAgXG4gIGNvbnNvbGUubG9nKCfwn5SlIEZpcmViYXNlIGluaXRpYWxpemVkIHN1Y2Nlc3NmdWxseSB3aXRoIHByb2plY3Q6JywgZmlyZWJhc2VDb25maWcucHJvamVjdElkKTtcbn0gY2F0Y2ggKGVycm9yKSB7XG4gIGNvbnNvbGUuZXJyb3IoJ+KdjCBGaXJlYmFzZSBpbml0aWFsaXphdGlvbiBmYWlsZWQ6JywgZXJyb3IpO1xuICB0aHJvdyBlcnJvcjsgLy8gUmUtdGhyb3cgZXJyb3IgdG8gcHJldmVudCBzaWxlbnQgZmFpbHVyZXNcbn1cblxuZXhwb3J0IHsgYXV0aCwgZmlyZXN0b3JlLCBzdG9yYWdlLCBkYXRhYmFzZSB9O1xuZXhwb3J0IGRlZmF1bHQgYXBwO1xuIl0sIm5hbWVzIjpbImluaXRpYWxpemVBcHAiLCJnZXRBdXRoIiwiZ2V0RmlyZXN0b3JlIiwiZ2V0U3RvcmFnZSIsImdldERhdGFiYXNlIiwiZmlyZWJhc2VDb25maWciLCJhcGlLZXkiLCJwcm9jZXNzIiwiZW52IiwiTkVYVF9QVUJMSUNfRklSRUJBU0VfQVBJX0tFWSIsImF1dGhEb21haW4iLCJORVhUX1BVQkxJQ19GSVJFQkFTRV9BVVRIX0RPTUFJTiIsInByb2plY3RJZCIsIk5FWFRfUFVCTElDX0ZJUkVCQVNFX1BST0pFQ1RfSUQiLCJzdG9yYWdlQnVja2V0IiwiTkVYVF9QVUJMSUNfRklSRUJBU0VfU1RPUkFHRV9CVUNLRVQiLCJtZXNzYWdpbmdTZW5kZXJJZCIsIk5FWFRfUFVCTElDX0ZJUkVCQVNFX01FU1NBR0lOR19TRU5ERVJfSUQiLCJhcHBJZCIsIk5FWFRfUFVCTElDX0ZJUkVCQVNFX0FQUF9JRCIsImRhdGFiYXNlVVJMIiwiTkVYVF9QVUJMSUNfRklSRUJBU0VfREFUQUJBU0VfVVJMIiwibWVhc3VyZW1lbnRJZCIsIk5FWFRfUFVCTElDX0ZJUkVCQVNFX01FQVNVUkVNRU5UX0lEIiwiYXBwIiwiYXV0aCIsImZpcmVzdG9yZSIsInN0b3JhZ2UiLCJkYXRhYmFzZSIsIkVycm9yIiwiY29uc29sZSIsImxvZyIsImVycm9yIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/firebase/config.js\n"));

/***/ })

}]);
'use client';

import { useAdmin } from '@/contexts/AdminContext';
import { Shield, AlertTriangle } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

const AdminGuard = ({ children, redirectTo = '/' }) => {
  const { user, isAdmin, loading } = useAdmin();
  const router = useRouter();

  useEffect(() => {
    // If not loading and not admin, redirect
    if (!loading && !isAdmin && redirectTo !== '/admin') {
      router.push(redirectTo);
    }
  }, [loading, isAdmin, redirectTo, router]);

  // Loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center">
        <div className="text-center">
          <Shield className="w-12 h-12 text-purple-400 mx-auto mb-4 animate-pulse" />
          <div className="w-8 h-8 border-4 border-purple-400/30 border-t-purple-400 rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-white text-lg">Verifying admin access...</p>
        </div>
      </div>
    );
  }

  // Not authenticated
  if (!user) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center p-4">
        <div className="max-w-md text-center">
          <div className="bg-yellow-500/10 backdrop-blur-sm border border-yellow-500/20 rounded-xl p-8">
            <AlertTriangle className="w-16 h-16 text-yellow-400 mx-auto mb-4" />
            <h1 className="text-2xl font-bold text-white mb-4">Authentication Required</h1>
            <p className="text-yellow-300 mb-6">
              Please log in to access this administrative area.
            </p>
            <div className="space-y-3">
              <button
                onClick={() => router.push('/admin')}
                className="w-full bg-purple-500 hover:bg-purple-600 text-white px-6 py-2 rounded-lg transition-colors"
              >
                Go to Admin Login
              </button>
              <button
                onClick={() => router.push('/')}
                className="w-full bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-lg transition-colors"
              >
                Return to Homepage
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Authenticated but not admin
  if (user && !isAdmin) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center p-4">
        <div className="max-w-md text-center">
          <div className="bg-red-500/10 backdrop-blur-sm border border-red-500/20 rounded-xl p-8">
            <AlertTriangle className="w-16 h-16 text-red-400 mx-auto mb-4" />
            <h1 className="text-2xl font-bold text-white mb-4">Access Denied</h1>
            <p className="text-red-300 mb-4">
              You don't have administrator privileges to access this area.
            </p>
            <p className="text-gray-400 text-sm mb-6">
              Logged in as: {user.email}
            </p>
            <div className="space-y-3">
              <button
                onClick={() => router.push('/dashboard')}
                className="w-full bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg transition-colors"
              >
                Go to User Dashboard
              </button>
              <button
                onClick={() => router.push('/')}
                className="w-full bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-lg transition-colors"
              >
                Return to Homepage
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // User is admin - render children
  return children;
};

export default AdminGuard;

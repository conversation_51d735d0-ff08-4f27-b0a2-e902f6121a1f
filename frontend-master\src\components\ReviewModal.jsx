'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  X, 
  Star, 
  Send, 
  Heart,
  MessageCircle,
  ThumbsUp,
  User
} from 'lucide-react';
import { toast } from 'react-hot-toast';

const ReviewModal = ({ isOpen, onClose, onSubmitReview, userInfo }) => {
  const [rating, setRating] = useState(0);
  const [hoverRating, setHoverRating] = useState(0);
  const [review, setReview] = useState('');
  const [name, setName] = useState(userInfo?.displayName || '');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (rating === 0) {
      toast.error('Please provide a rating');
      return;
    }
    
    if (review.trim().length < 10) {
      toast.error('Please write a review with at least 10 characters');
      return;
    }

    setIsSubmitting(true);
    
    try {
      const reviewData = {
        rating,
        review: review.trim(),
        name: name.trim() || 'Anonymous User',
        date: new Date().toISOString(),
        userId: userInfo?.uid || 'anonymous'
      };

      // Call the API to save the review
      const response = await fetch('/api/reviews', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(reviewData),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to submit review');
      }      await onSubmitReview(reviewData);
      
      toast.success('🎉 Thank you for your review! You can now download your resume.');
      
      // Reset form
      setRating(0);
      setReview('');
      setName(userInfo?.displayName || '');
      
      // Close modal after short delay to show success message
      setTimeout(() => {
        onClose();
      }, 1000);
        } catch (error) {
      console.error('Error submitting review:', error);
      if (error.message.includes('already submitted')) {
        toast.error('You have already submitted a review. Thank you!');
        onClose(); // Close modal since user already reviewed
      } else if (error.message.includes('network') || error.message.includes('fetch')) {
        toast.error('Network error. Please check your connection and try again.');
      } else {
        toast.error(error.message || 'Failed to submit review. Please try again.');
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 bg-black/70 backdrop-blur-sm"
            onClick={onClose}
          />
          
          {/* Modal */}
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            className="relative bg-gray-900 border border-white/10 rounded-2xl p-8 max-w-md w-full mx-4 shadow-2xl"
          >
            {/* Header */}
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center gap-3">
                <div className="bg-gradient-to-r from-neural-purple to-neural-pink p-2 rounded-lg">
                  <Heart className="h-5 w-5 text-white" />
                </div>
                <h2 className="text-xl font-bold text-white">Share Your Experience</h2>
              </div>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-white transition-colors"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            {/* Subtitle */}
            <p className="text-gray-300 mb-6 text-sm">
              Help others discover our AI-powered resume builder by sharing your experience!
            </p>

            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Rating */}
              <div>
                <label className="block text-sm font-medium text-white mb-3">
                  Rate your experience
                </label>
                <div className="flex items-center gap-1">
                  {[1, 2, 3, 4, 5].map((star) => (
                    <button
                      key={star}
                      type="button"
                      onClick={() => setRating(star)}
                      onMouseEnter={() => setHoverRating(star)}
                      onMouseLeave={() => setHoverRating(0)}
                      className="p-1 transition-transform hover:scale-110"
                    >
                      <Star
                        className={`h-8 w-8 transition-colors ${
                          star <= (hoverRating || rating)
                            ? 'text-yellow-400 fill-yellow-400'
                            : 'text-gray-600 hover:text-gray-400'
                        }`}
                      />
                    </button>
                  ))}
                </div>
                {rating > 0 && (
                  <p className="text-sm text-gray-400 mt-2">
                    {rating === 1 && "We'd love to improve!"}
                    {rating === 2 && "Thanks for the feedback!"}
                    {rating === 3 && "Good to know!"}
                    {rating === 4 && "Great to hear!"}
                    {rating === 5 && "Awesome! Thank you!"}
                  </p>
                )}
              </div>

              {/* Review Text */}
              <div>
                <label className="block text-sm font-medium text-white mb-2">
                  Write your review
                </label>
                <textarea
                  value={review}
                  onChange={(e) => setReview(e.target.value)}
                  placeholder="Share your thoughts about the resume builder..."
                  className="w-full h-24 px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-neural-purple focus:border-transparent resize-none text-sm"
                  maxLength={500}
                />
                <div className="flex justify-between items-center mt-1">
                  <p className="text-xs text-gray-500">
                    {review.length}/500 characters
                  </p>
                  {review.length >= 10 && (
                    <p className="text-xs text-green-400 flex items-center gap-1">
                      <ThumbsUp className="h-3 w-3" />
                      Looks good!
                    </p>
                  )}
                </div>
              </div>

              {/* Name */}
              <div>
                <label className="block text-sm font-medium text-white mb-2">
                  Display name (optional)
                </label>
                <div className="relative">
                  <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    value={name}
                    onChange={(e) => setName(e.target.value)}
                    placeholder="Your name"
                    className="w-full pl-10 pr-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-neural-purple focus:border-transparent text-sm"
                    maxLength={50}
                  />
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  Leave empty to post as "Anonymous User"
                </p>
              </div>

              {/* Submit Button */}
              <div className="flex gap-3 pt-2">
                <button
                  type="button"
                  onClick={onClose}
                  className="flex-1 px-4 py-2 bg-gray-800 hover:bg-gray-700 text-gray-300 font-medium rounded-lg transition-colors text-sm"
                >
                  Maybe Later
                </button>
                <button
                  type="submit"
                  disabled={isSubmitting || rating === 0 || review.trim().length < 10}
                  className="flex-1 flex items-center justify-center gap-2 px-4 py-2 bg-gradient-to-r from-neural-purple to-neural-pink text-white font-medium rounded-lg hover:opacity-90 transition-opacity disabled:opacity-50 text-sm"
                >
                  {isSubmitting ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                      Submitting...
                    </>
                  ) : (
                    <>
                      <Send className="h-4 w-4" />
                      Submit Review
                    </>
                  )}
                </button>
              </div>
            </form>

            {/* Footer */}
            <div className="mt-6 pt-4 border-t border-gray-800">
              <div className="flex items-center gap-2 text-xs text-gray-500">
                <MessageCircle className="h-3 w-3" />
                <span>Your review helps us improve and helps others make informed decisions</span>
              </div>
            </div>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );
};

export default ReviewModal;

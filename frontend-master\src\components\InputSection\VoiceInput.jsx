import { Button } from "@/components/ui/button";
import { Keyboard, Mic, MicOff } from "lucide-react";


export const VoiceInput = ({
  transcript,
  currentValue,
  isRecording,
  isProcessing,
  language,
  onSwitchToText,
  onToggleRecording,
}) => (
  <div className="bg-gray-800/50 border border-gray-700 rounded-lg p-4 min-h-[100px] relative">
    {isRecording && (
      <div className="absolute top-2 right-2 flex items-center gap-1">
        <div className="h-2 w-2 bg-red-500 rounded-full animate-pulse" />
        <span className="text-xs text-red-400">
          {language === "hindi" ? "रिकॉर्डिंग..." : "Recording..."}
          {isProcessing && (
            <span className="ml-2 text-yellow-400">
              {language === "hindi" ? "प्रोसेसिंग..." : "Processing..."}
            </span>
          )}
        </span>
      </div>
    )}

    {transcript ? (
      <p>{transcript}</p>
    ) : currentValue ? (
      <p>{currentValue}</p>
    ) : (
      <span className="text-gray-500">
        {language === "hindi"
          ? isRecording
            ? "बोलना शुरू करें..."
            : "रिकॉर्डिंग शुरू करने के लिए माइक बटन दबाएं"
          : isRecording
          ? "Start speaking..."
          : "Press the mic button to start recording"}
      </span>
    )}

    <div className="absolute right-2 bottom-2 flex gap-2">
      <Button
        variant="outline"
        size="icon"
        onClick={onSwitchToText}
        title={language === "hindi" ? "टेक्स्ट इनपुट" : "Text input"}
        className="hover:opacity-80  bg-gradient-to-r from-purple-500 to-pink-500"
      >
        <Keyboard className="h-4 w-4 " />
      </Button>
      <Button
        variant={isRecording ? "default" : "outline"}
        size="icon"
        onClick={onToggleRecording}
        title={language === "hindi" ? "वॉइस इनपुट" : "Voice input"}
        className={` hover:opacity-80 opacity-100 ${
          isRecording ? "bg-red-500 hover:bg-red-600 text-white" : "  bg-gradient-to-r from-purple-500 to-pink-500"
        }`}
        disabled={isProcessing}
      >
        {isRecording ? (
          <MicOff className="h-4 w-4" />
        ) : (
          <Mic className="h-4 w-4" />
        )}
      </Button>
    </div>
  </div>
);
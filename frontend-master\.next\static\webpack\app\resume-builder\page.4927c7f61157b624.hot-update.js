"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/resume-builder/page",{

/***/ "(app-pages-browser)/./src/components/resume/MobileActionBar.jsx":
/*!***************************************************!*\
  !*** ./src/components/resume/MobileActionBar.jsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Eye_Save_lucide_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Eye,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Eye_Save_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Eye,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Eye_Save_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Eye,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Eye_Save_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Eye,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst MobileActionBar = (param)=>{\n    let { currentStep, totalSteps, onPrevious, onNext, onSave, onTogglePreview, canProceed = true, isSaving = false, showPreview = false } = param;\n    const isFirstStep = currentStep === 0;\n    const isLastStep = currentStep === totalSteps - 1;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed bottom-0 left-0 right-0 z-40 bg-gray-900/95 backdrop-blur-sm border-t border-gray-700/50 p-4 xl:hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between gap-3 max-w-lg mx-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onPrevious,\n                        disabled: isFirstStep,\n                        className: \"\\n            flex items-center gap-2 px-4 py-3 rounded-xl font-medium transition-all duration-200 flex-1\\n            \".concat(isFirstStep ? 'bg-gray-800 text-gray-500 cursor-not-allowed' : 'bg-gray-800 text-gray-300 hover:bg-gray-700 border border-gray-600', \"\\n          \"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Eye_Save_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobileActionBar.jsx\",\n                                lineNumber: 34,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm\",\n                                children: \"Previous\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobileActionBar.jsx\",\n                                lineNumber: 35,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobileActionBar.jsx\",\n                        lineNumber: 23,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onSave,\n                                disabled: isSaving,\n                                className: \"p-3 bg-gray-800 hover:bg-gray-700 text-gray-300 rounded-xl transition-colors disabled:opacity-50 border border-gray-600\",\n                                title: \"Save Progress\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Eye_Save_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobileActionBar.jsx\",\n                                    lineNumber: 47,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobileActionBar.jsx\",\n                                lineNumber: 41,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onTogglePreview,\n                                className: \"\\n              p-3 rounded-xl transition-colors border\\n              \".concat(showPreview ? 'bg-neural-purple text-white border-neural-purple' : 'bg-gray-800 text-gray-300 hover:bg-gray-700 border-gray-600', \"\\n            \"),\n                                title: \"Toggle Preview\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Eye_Save_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobileActionBar.jsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobileActionBar.jsx\",\n                                lineNumber: 51,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobileActionBar.jsx\",\n                        lineNumber: 39,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onNext,\n                        disabled: !canProceed,\n                        className: \"\\n            flex items-center gap-2 px-4 py-3 rounded-xl font-medium transition-all duration-200 flex-1\\n            \".concat(canProceed ? 'bg-gradient-to-r from-neural-purple to-neural-pink text-white shadow-lg hover:opacity-90' : 'bg-gray-800 text-gray-500 cursor-not-allowed border border-gray-700', \"\\n          \"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm\",\n                                children: isLastStep ? 'Complete' : 'Next'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobileActionBar.jsx\",\n                                lineNumber: 78,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Eye_Save_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobileActionBar.jsx\",\n                                lineNumber: 79,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobileActionBar.jsx\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobileActionBar.jsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-3 max-w-lg mx-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center gap-1\",\n                    children: Array.from({\n                        length: totalSteps\n                    }, (_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"\\n                h-1 rounded-full transition-all duration-300\\n                \".concat(index <= currentStep ? 'bg-gradient-to-r from-neural-purple to-neural-pink w-8' : 'bg-gray-700 w-4', \"\\n              \")\n                        }, index, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobileActionBar.jsx\",\n                            lineNumber: 87,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobileActionBar.jsx\",\n                    lineNumber: 85,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobileActionBar.jsx\",\n                lineNumber: 84,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobileActionBar.jsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, undefined);\n};\n_c = MobileActionBar;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MobileActionBar);\nvar _c;\n$RefreshReg$(_c, \"MobileActionBar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/resume/MobileActionBar.jsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/resume/MobilePreviewModal.jsx":
/*!******************************************************!*\
  !*** ./src/components/resume/MobilePreviewModal.jsx ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Eye_Maximize2_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Maximize2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_Maximize2_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Maximize2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/maximize-2.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_Maximize2_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Maximize2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst MobilePreviewModal = (param)=>{\n    let { isOpen, onClose, formData, selectedTemplate, onOpenFullscreen } = param;\n    var _formData_personal, _formData_personal1, _formData_experience, _formData_education;\n    _s();\n    // Handle escape key and body scroll\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MobilePreviewModal.useEffect\": ()=>{\n            if (isOpen) {\n                const handleEscape = {\n                    \"MobilePreviewModal.useEffect.handleEscape\": (e)=>{\n                        if (e.key === 'Escape') onClose();\n                    }\n                }[\"MobilePreviewModal.useEffect.handleEscape\"];\n                document.addEventListener('keydown', handleEscape);\n                document.body.style.overflow = 'hidden';\n                return ({\n                    \"MobilePreviewModal.useEffect\": ()=>{\n                        document.removeEventListener('keydown', handleEscape);\n                        document.body.style.overflow = 'unset';\n                    }\n                })[\"MobilePreviewModal.useEffect\"];\n            }\n        }\n    }[\"MobilePreviewModal.useEffect\"], [\n        isOpen,\n        onClose\n    ]);\n    // Check if we have meaningful data to preview\n    const hasContent = formData && (((_formData_personal = formData.personal) === null || _formData_personal === void 0 ? void 0 : _formData_personal.firstName) || ((_formData_personal1 = formData.personal) === null || _formData_personal1 === void 0 ? void 0 : _formData_personal1.email) || ((_formData_experience = formData.experience) === null || _formData_experience === void 0 ? void 0 : _formData_experience.some((exp)=>exp.title || exp.company)) || ((_formData_education = formData.education) === null || _formData_education === void 0 ? void 0 : _formData_education.some((edu)=>edu.degree || edu.institution)));\n    const renderPreviewContent = ()=>{\n        var _formData_personal, _formData_personal1, _formData_personal2, _formData_personal3, _formData_personal4, _formData_personal5, _formData_experience, _formData_education, _formData_skills_technical, _formData_skills, _formData_skills_languages, _formData_skills1, _formData_skills_technical1, _formData_skills_languages1;\n        if (!hasContent) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-center justify-center h-full text-gray-400 p-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Maximize2_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                className: \"h-16 w-16 opacity-50\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobilePreviewModal.jsx\",\n                                lineNumber: 42,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute -top-1 -right-1 w-6 h-6 bg-neural-pink rounded-full flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm\",\n                                    children: \"✨\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobilePreviewModal.jsx\",\n                                    lineNumber: 44,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobilePreviewModal.jsx\",\n                                lineNumber: 43,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobilePreviewModal.jsx\",\n                        lineNumber: 41,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium mb-2 text-white\",\n                        children: \"AI Resume Preview\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobilePreviewModal.jsx\",\n                        lineNumber: 47,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-center opacity-75 mb-2\",\n                        children: \"Your professional resume will appear here as you fill out the form\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobilePreviewModal.jsx\",\n                        lineNumber: 48,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-center opacity-50 text-sm\",\n                        children: \"⚡ Powered by AI for instant results\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobilePreviewModal.jsx\",\n                        lineNumber: 51,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobilePreviewModal.jsx\",\n                lineNumber: 40,\n                columnNumber: 9\n            }, undefined);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6 space-y-6 text-sm bg-white text-black min-h-full\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center border-b-2 border-gray-300 pb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900 mb-3\",\n                            children: [\n                                ((_formData_personal = formData.personal) === null || _formData_personal === void 0 ? void 0 : _formData_personal.firstName) || 'Your Name',\n                                \" \",\n                                ((_formData_personal1 = formData.personal) === null || _formData_personal1 === void 0 ? void 0 : _formData_personal1.lastName) || ''\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobilePreviewModal.jsx\",\n                            lineNumber: 62,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap justify-center gap-3 text-sm text-gray-600\",\n                            children: [\n                                ((_formData_personal2 = formData.personal) === null || _formData_personal2 === void 0 ? void 0 : _formData_personal2.email) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"\\uD83D\\uDCE7\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobilePreviewModal.jsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: formData.personal.email\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobilePreviewModal.jsx\",\n                                            lineNumber: 70,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobilePreviewModal.jsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 15\n                                }, undefined),\n                                ((_formData_personal3 = formData.personal) === null || _formData_personal3 === void 0 ? void 0 : _formData_personal3.phone) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"\\uD83D\\uDCF1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobilePreviewModal.jsx\",\n                                            lineNumber: 75,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: formData.personal.phone\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobilePreviewModal.jsx\",\n                                            lineNumber: 76,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobilePreviewModal.jsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 15\n                                }, undefined),\n                                ((_formData_personal4 = formData.personal) === null || _formData_personal4 === void 0 ? void 0 : _formData_personal4.location) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"\\uD83D\\uDCCD\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobilePreviewModal.jsx\",\n                                            lineNumber: 81,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: formData.personal.location\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobilePreviewModal.jsx\",\n                                            lineNumber: 82,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobilePreviewModal.jsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobilePreviewModal.jsx\",\n                            lineNumber: 66,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobilePreviewModal.jsx\",\n                    lineNumber: 61,\n                    columnNumber: 9\n                }, undefined),\n                ((_formData_personal5 = formData.personal) === null || _formData_personal5 === void 0 ? void 0 : _formData_personal5.summary) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-lg font-semibold text-gray-900 mb-3\",\n                            children: \"Professional Summary\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobilePreviewModal.jsx\",\n                            lineNumber: 91,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-700 leading-relaxed text-justify\",\n                            children: formData.personal.summary\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobilePreviewModal.jsx\",\n                            lineNumber: 92,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobilePreviewModal.jsx\",\n                    lineNumber: 90,\n                    columnNumber: 11\n                }, undefined),\n                ((_formData_experience = formData.experience) === null || _formData_experience === void 0 ? void 0 : _formData_experience.some((exp)=>exp.title || exp.company)) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-lg font-semibold text-gray-900 mb-3\",\n                            children: \"Experience\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobilePreviewModal.jsx\",\n                            lineNumber: 101,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: formData.experience.filter((exp)=>exp.title || exp.company).map((exp, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-l-4 border-neural-purple pl-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-start mb-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-medium text-gray-900\",\n                                                    children: exp.title || 'Job Title'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobilePreviewModal.jsx\",\n                                                    lineNumber: 108,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: [\n                                                        exp.startDate,\n                                                        \" \",\n                                                        exp.startDate && (exp.endDate || exp.current) && ' - ',\n                                                        exp.current ? 'Present' : exp.endDate\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobilePreviewModal.jsx\",\n                                                    lineNumber: 111,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobilePreviewModal.jsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 mb-2\",\n                                            children: [\n                                                exp.company || 'Company Name',\n                                                exp.location && \" • \".concat(exp.location)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobilePreviewModal.jsx\",\n                                            lineNumber: 116,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        exp.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 leading-relaxed\",\n                                            children: exp.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobilePreviewModal.jsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 23\n                                        }, undefined)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobilePreviewModal.jsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 19\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobilePreviewModal.jsx\",\n                            lineNumber: 102,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobilePreviewModal.jsx\",\n                    lineNumber: 100,\n                    columnNumber: 11\n                }, undefined),\n                ((_formData_education = formData.education) === null || _formData_education === void 0 ? void 0 : _formData_education.some((edu)=>edu.degree || edu.institution)) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-lg font-semibold text-gray-900 mb-3\",\n                            children: \"Education\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobilePreviewModal.jsx\",\n                            lineNumber: 134,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: formData.education.filter((edu)=>edu.degree || edu.institution).map((edu, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-l-4 border-neural-blue pl-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-start\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-medium text-gray-900\",\n                                                        children: [\n                                                            edu.degree || 'Degree',\n                                                            \" \",\n                                                            edu.field && \"in \".concat(edu.field)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobilePreviewModal.jsx\",\n                                                        lineNumber: 142,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: edu.institution || 'Institution'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobilePreviewModal.jsx\",\n                                                        lineNumber: 145,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobilePreviewModal.jsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: edu.graduationDate\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobilePreviewModal.jsx\",\n                                                lineNumber: 149,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobilePreviewModal.jsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 21\n                                    }, undefined)\n                                }, index, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobilePreviewModal.jsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 19\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobilePreviewModal.jsx\",\n                            lineNumber: 135,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobilePreviewModal.jsx\",\n                    lineNumber: 133,\n                    columnNumber: 11\n                }, undefined),\n                (((_formData_skills = formData.skills) === null || _formData_skills === void 0 ? void 0 : (_formData_skills_technical = _formData_skills.technical) === null || _formData_skills_technical === void 0 ? void 0 : _formData_skills_technical.length) > 0 || ((_formData_skills1 = formData.skills) === null || _formData_skills1 === void 0 ? void 0 : (_formData_skills_languages = _formData_skills1.languages) === null || _formData_skills_languages === void 0 ? void 0 : _formData_skills_languages.length) > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-lg font-semibold text-gray-900 mb-3\",\n                            children: \"Skills\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobilePreviewModal.jsx\",\n                            lineNumber: 162,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                ((_formData_skills_technical1 = formData.skills.technical) === null || _formData_skills_technical1 === void 0 ? void 0 : _formData_skills_technical1.length) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium text-gray-700\",\n                                            children: \"Technical: \"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobilePreviewModal.jsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-600\",\n                                            children: formData.skills.technical.join(', ')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobilePreviewModal.jsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobilePreviewModal.jsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 17\n                                }, undefined),\n                                ((_formData_skills_languages1 = formData.skills.languages) === null || _formData_skills_languages1 === void 0 ? void 0 : _formData_skills_languages1.length) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium text-gray-700\",\n                                            children: \"Languages: \"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobilePreviewModal.jsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-600\",\n                                            children: formData.skills.languages.join(', ')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobilePreviewModal.jsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobilePreviewModal.jsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobilePreviewModal.jsx\",\n                            lineNumber: 163,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobilePreviewModal.jsx\",\n                    lineNumber: 161,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobilePreviewModal.jsx\",\n            lineNumber: 59,\n            columnNumber: 7\n        }, undefined);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.AnimatePresence, {\n        children: isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n            initial: {\n                opacity: 0\n            },\n            animate: {\n                opacity: 1\n            },\n            exit: {\n                opacity: 0\n            },\n            className: \"fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex flex-col\",\n            onClick: onClose,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between p-4 bg-gray-900/95 backdrop-blur-sm border-b border-gray-700/50\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-2 h-2 bg-neural-pink rounded-full animate-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobilePreviewModal.jsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-white font-medium\",\n                                    children: \"Resume Preview\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobilePreviewModal.jsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobilePreviewModal.jsx\",\n                            lineNumber: 199,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                hasContent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        onOpenFullscreen();\n                                        onClose();\n                                    },\n                                    className: \"p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors\",\n                                    title: \"Fullscreen Preview\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Maximize2_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobilePreviewModal.jsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobilePreviewModal.jsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onClose,\n                                    className: \"p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors\",\n                                    title: \"Close\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Maximize2_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobilePreviewModal.jsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobilePreviewModal.jsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobilePreviewModal.jsx\",\n                            lineNumber: 204,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobilePreviewModal.jsx\",\n                    lineNumber: 198,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    exit: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    className: \"flex-1 overflow-auto\",\n                    onClick: (e)=>e.stopPropagation(),\n                    children: renderPreviewContent()\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobilePreviewModal.jsx\",\n                    lineNumber: 229,\n                    columnNumber: 11\n                }, undefined),\n                hasContent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-3 bg-gray-900/95 backdrop-blur-sm border-t border-gray-700/50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center text-sm text-gray-400\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: [\n                                \"Template: \",\n                                (selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.replace('_', ' ')) || 'Professional',\n                                \" • ATS Optimized\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobilePreviewModal.jsx\",\n                            lineNumber: 243,\n                            columnNumber: 17\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobilePreviewModal.jsx\",\n                        lineNumber: 242,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobilePreviewModal.jsx\",\n                    lineNumber: 241,\n                    columnNumber: 13\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobilePreviewModal.jsx\",\n            lineNumber: 190,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\MobilePreviewModal.jsx\",\n        lineNumber: 188,\n        columnNumber: 5\n    }, undefined);\n};\n_s(MobilePreviewModal, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c = MobilePreviewModal;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MobilePreviewModal);\nvar _c;\n$RefreshReg$(_c, \"MobilePreviewModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/resume/MobilePreviewModal.jsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/resume/RedesignedResumeBuilder.jsx":
/*!***********************************************************!*\
  !*** ./src/components/resume/RedesignedResumeBuilder.jsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _TopStepNavigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./TopStepNavigation */ \"(app-pages-browser)/./src/components/resume/TopStepNavigation.jsx\");\n/* harmony import */ var _MobileActionBar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./MobileActionBar */ \"(app-pages-browser)/./src/components/resume/MobileActionBar.jsx\");\n/* harmony import */ var _MobilePreviewModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./MobilePreviewModal */ \"(app-pages-browser)/./src/components/resume/MobilePreviewModal.jsx\");\n/* harmony import */ var _StreamlinedPreview__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./StreamlinedPreview */ \"(app-pages-browser)/./src/components/resume/StreamlinedPreview.jsx\");\n/* harmony import */ var _FullscreenPreviewModal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./FullscreenPreviewModal */ \"(app-pages-browser)/./src/components/resume/FullscreenPreviewModal.jsx\");\n/* harmony import */ var _UniversalTemplateSelector__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./UniversalTemplateSelector */ \"(app-pages-browser)/./src/components/resume/UniversalTemplateSelector.jsx\");\n/* harmony import */ var _forms_UniversalFormFields__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./forms/UniversalFormFields */ \"(app-pages-browser)/./src/components/resume/forms/UniversalFormFields.jsx\");\n/* harmony import */ var _templates_MultiIndustryTemplates__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./templates/MultiIndustryTemplates */ \"(app-pages-browser)/./src/components/resume/templates/MultiIndustryTemplates.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n// Import new components\n\n\n\n\n\n\n\n// Import template system\n\nconst RedesignedResumeBuilder = (param)=>{\n    let { hideHeader = false } = param;\n    _s();\n    // Core state\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [selectedTemplate, setSelectedTemplate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('business_executive');\n    const [showTemplateSelector, setShowTemplateSelector] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showFullscreenPreview, setShowFullscreenPreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Form data with universal structure\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        personal: {\n            firstName: \"\",\n            lastName: \"\",\n            email: \"\",\n            phone: \"\",\n            location: \"\",\n            summary: \"\"\n        },\n        experience: [\n            {\n                id: 1,\n                title: \"\",\n                company: \"\",\n                location: \"\",\n                startDate: \"\",\n                endDate: \"\",\n                current: false,\n                description: \"\"\n            }\n        ],\n        education: [\n            {\n                id: 1,\n                degree: \"\",\n                field: \"\",\n                institution: \"\",\n                location: \"\",\n                graduationDate: \"\",\n                gpa: \"\"\n            }\n        ],\n        skills: {\n            technical: [],\n            languages: [],\n            certifications: []\n        }\n    });\n    // UI state\n    const [validationErrors, setValidationErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isSaving, setIsSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [completedSteps, setCompletedSteps] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Step configuration - simplified and universal\n    const steps = [\n        {\n            id: 0,\n            title: \"Personal Info\",\n            description: \"Your contact information and professional summary\"\n        },\n        {\n            id: 1,\n            title: \"Experience\",\n            description: \"Your work history and achievements\"\n        },\n        {\n            id: 2,\n            title: \"Education\",\n            description: \"Your educational background and qualifications\"\n        },\n        {\n            id: 3,\n            title: \"Skills\",\n            description: \"Your core competencies and abilities\"\n        },\n        {\n            id: 4,\n            title: \"Review\",\n            description: \"Review and download your resume\"\n        }\n    ];\n    // Auto-save functionality\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RedesignedResumeBuilder.useEffect\": ()=>{\n            const timeoutId = setTimeout({\n                \"RedesignedResumeBuilder.useEffect.timeoutId\": ()=>{\n                    localStorage.setItem('universalResumeData', JSON.stringify(formData));\n                    localStorage.setItem('selectedTemplate', selectedTemplate);\n                }\n            }[\"RedesignedResumeBuilder.useEffect.timeoutId\"], 2000);\n            return ({\n                \"RedesignedResumeBuilder.useEffect\": ()=>clearTimeout(timeoutId)\n            })[\"RedesignedResumeBuilder.useEffect\"];\n        }\n    }[\"RedesignedResumeBuilder.useEffect\"], [\n        formData,\n        selectedTemplate\n    ]);\n    // Load saved data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RedesignedResumeBuilder.useEffect\": ()=>{\n            const savedData = localStorage.getItem('universalResumeData');\n            const savedTemplate = localStorage.getItem('selectedTemplate');\n            if (savedData) {\n                try {\n                    setFormData(JSON.parse(savedData));\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success('Previous work restored!');\n                } catch (error) {\n                    console.error('Failed to load saved data:', error);\n                }\n            }\n            if (savedTemplate && _templates_MultiIndustryTemplates__WEBPACK_IMPORTED_MODULE_10__.INDUSTRY_TEMPLATES[savedTemplate]) {\n                setSelectedTemplate(savedTemplate);\n            }\n        }\n    }[\"RedesignedResumeBuilder.useEffect\"], []);\n    // Validation logic\n    const validateStep = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"RedesignedResumeBuilder.useCallback[validateStep]\": (stepIndex)=>{\n            const errors = {};\n            switch(stepIndex){\n                case 0:\n                    var _formData_personal_firstName, _formData_personal_lastName, _formData_personal_email;\n                    if (!((_formData_personal_firstName = formData.personal.firstName) === null || _formData_personal_firstName === void 0 ? void 0 : _formData_personal_firstName.trim())) errors.firstName = 'First name is required';\n                    if (!((_formData_personal_lastName = formData.personal.lastName) === null || _formData_personal_lastName === void 0 ? void 0 : _formData_personal_lastName.trim())) errors.lastName = 'Last name is required';\n                    if (!((_formData_personal_email = formData.personal.email) === null || _formData_personal_email === void 0 ? void 0 : _formData_personal_email.trim())) errors.email = 'Email is required';\n                    else if (!/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(formData.personal.email)) {\n                        errors.email = 'Please enter a valid email address';\n                    }\n                    break;\n                case 1:\n                    var _formData_experience;\n                    const validExperience = (_formData_experience = formData.experience) === null || _formData_experience === void 0 ? void 0 : _formData_experience.filter({\n                        \"RedesignedResumeBuilder.useCallback[validateStep]\": (exp)=>{\n                            var _exp_title, _exp_company;\n                            return ((_exp_title = exp.title) === null || _exp_title === void 0 ? void 0 : _exp_title.trim()) && ((_exp_company = exp.company) === null || _exp_company === void 0 ? void 0 : _exp_company.trim());\n                        }\n                    }[\"RedesignedResumeBuilder.useCallback[validateStep]\"]);\n                    if (!validExperience || validExperience.length === 0) {\n                        errors.experience = 'At least one work experience entry is required';\n                    }\n                    break;\n                case 2:\n                    var _formData_education;\n                    const validEducation = (_formData_education = formData.education) === null || _formData_education === void 0 ? void 0 : _formData_education.filter({\n                        \"RedesignedResumeBuilder.useCallback[validateStep]\": (edu)=>{\n                            var _edu_degree, _edu_institution;\n                            return ((_edu_degree = edu.degree) === null || _edu_degree === void 0 ? void 0 : _edu_degree.trim()) && ((_edu_institution = edu.institution) === null || _edu_institution === void 0 ? void 0 : _edu_institution.trim());\n                        }\n                    }[\"RedesignedResumeBuilder.useCallback[validateStep]\"]);\n                    if (!validEducation || validEducation.length === 0) {\n                        errors.education = 'At least one education entry is required';\n                    }\n                    break;\n                case 3:\n                    break;\n                default:\n                    break;\n            }\n            return errors;\n        }\n    }[\"RedesignedResumeBuilder.useCallback[validateStep]\"], [\n        formData\n    ]);\n    // Navigation logic\n    const canProceedToNextStep = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"RedesignedResumeBuilder.useCallback[canProceedToNextStep]\": (stepIndex)=>{\n            const errors = validateStep(stepIndex);\n            return Object.keys(errors).length === 0;\n        }\n    }[\"RedesignedResumeBuilder.useCallback[canProceedToNextStep]\"], [\n        validateStep\n    ]);\n    const nextStep = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"RedesignedResumeBuilder.useCallback[nextStep]\": ()=>{\n            const errors = validateStep(currentStep);\n            if (Object.keys(errors).length > 0) {\n                setValidationErrors(errors);\n                const errorMessages = Object.values(errors);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(errorMessages[0]);\n                return;\n            }\n            setCompletedSteps({\n                \"RedesignedResumeBuilder.useCallback[nextStep]\": (prev)=>[\n                        ...new Set([\n                            ...prev,\n                            currentStep\n                        ])\n                    ]\n            }[\"RedesignedResumeBuilder.useCallback[nextStep]\"]);\n            setValidationErrors({});\n            if (currentStep < steps.length - 1) {\n                setCurrentStep(currentStep + 1);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"\".concat(steps[currentStep].title, \" completed!\"));\n            }\n        }\n    }[\"RedesignedResumeBuilder.useCallback[nextStep]\"], [\n        currentStep,\n        validateStep,\n        steps\n    ]);\n    const prevStep = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"RedesignedResumeBuilder.useCallback[prevStep]\": ()=>{\n            setValidationErrors({});\n            if (currentStep > 0) setCurrentStep(currentStep - 1);\n        }\n    }[\"RedesignedResumeBuilder.useCallback[prevStep]\"], [\n        currentStep\n    ]);\n    // Form data management\n    const updateFormData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"RedesignedResumeBuilder.useCallback[updateFormData]\": function(section, field, value) {\n            let index = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : null;\n            setFormData({\n                \"RedesignedResumeBuilder.useCallback[updateFormData]\": (prev)=>{\n                    let newData = {\n                        ...prev\n                    };\n                    if (index !== null && Array.isArray(prev[section])) {\n                        const newArray = [\n                            ...prev[section]\n                        ];\n                        newArray[index] = {\n                            ...newArray[index],\n                            [field]: value\n                        };\n                        newData[section] = newArray;\n                    } else if (typeof prev[section] === 'object' && !Array.isArray(prev[section])) {\n                        newData[section] = {\n                            ...prev[section],\n                            [field]: value\n                        };\n                    } else {\n                        newData[field] = value;\n                    }\n                    return newData;\n                }\n            }[\"RedesignedResumeBuilder.useCallback[updateFormData]\"]);\n            // Clear validation errors for this field\n            if (validationErrors[field]) {\n                setValidationErrors({\n                    \"RedesignedResumeBuilder.useCallback[updateFormData]\": (prev)=>{\n                        const newErrors = {\n                            ...prev\n                        };\n                        delete newErrors[field];\n                        return newErrors;\n                    }\n                }[\"RedesignedResumeBuilder.useCallback[updateFormData]\"]);\n            }\n        }\n    }[\"RedesignedResumeBuilder.useCallback[updateFormData]\"], [\n        validationErrors\n    ]);\n    const addArrayItem = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"RedesignedResumeBuilder.useCallback[addArrayItem]\": (section, template)=>{\n            setFormData({\n                \"RedesignedResumeBuilder.useCallback[addArrayItem]\": (prev)=>({\n                        ...prev,\n                        [section]: [\n                            ...prev[section],\n                            {\n                                ...template,\n                                id: Math.random().toString(36).substring(2, 11)\n                            }\n                        ]\n                    })\n            }[\"RedesignedResumeBuilder.useCallback[addArrayItem]\"]);\n        }\n    }[\"RedesignedResumeBuilder.useCallback[addArrayItem]\"], []);\n    const removeArrayItem = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"RedesignedResumeBuilder.useCallback[removeArrayItem]\": (section, id)=>{\n            setFormData({\n                \"RedesignedResumeBuilder.useCallback[removeArrayItem]\": (prev)=>({\n                        ...prev,\n                        [section]: prev[section].filter({\n                            \"RedesignedResumeBuilder.useCallback[removeArrayItem]\": (item)=>item.id !== id\n                        }[\"RedesignedResumeBuilder.useCallback[removeArrayItem]\"])\n                    })\n            }[\"RedesignedResumeBuilder.useCallback[removeArrayItem]\"]);\n        }\n    }[\"RedesignedResumeBuilder.useCallback[removeArrayItem]\"], []);\n    // Save functionality\n    const handleSave = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"RedesignedResumeBuilder.useCallback[handleSave]\": ()=>{\n            setIsSaving(true);\n            localStorage.setItem('universalResumeData', JSON.stringify(formData));\n            localStorage.setItem('selectedTemplate', selectedTemplate);\n            setTimeout({\n                \"RedesignedResumeBuilder.useCallback[handleSave]\": ()=>{\n                    setIsSaving(false);\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success('Progress saved!');\n                }\n            }[\"RedesignedResumeBuilder.useCallback[handleSave]\"], 1000);\n        }\n    }[\"RedesignedResumeBuilder.useCallback[handleSave]\"], [\n        formData,\n        selectedTemplate\n    ]);\n    // Download functionality\n    const handleDownload = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"RedesignedResumeBuilder.useCallback[handleDownload]\": ()=>{\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success('Download feature coming soon!');\n        }\n    }[\"RedesignedResumeBuilder.useCallback[handleDownload]\"], []);\n    // Calculate completion percentage\n    const getCompletionPercentage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"RedesignedResumeBuilder.useCallback[getCompletionPercentage]\": ()=>{\n            var _formData_experience, _formData_education, _formData_skills_technical, _formData_skills;\n            let totalFields = 0;\n            let completedFields = 0;\n            // Personal info (4 required fields)\n            totalFields += 4;\n            if (formData.personal.firstName) completedFields++;\n            if (formData.personal.lastName) completedFields++;\n            if (formData.personal.email) completedFields++;\n            if (formData.personal.summary) completedFields++;\n            // Experience (at least 1 entry with title and company)\n            totalFields += 2;\n            const validExp = (_formData_experience = formData.experience) === null || _formData_experience === void 0 ? void 0 : _formData_experience.filter({\n                \"RedesignedResumeBuilder.useCallback[getCompletionPercentage]\": (exp)=>exp.title && exp.company\n            }[\"RedesignedResumeBuilder.useCallback[getCompletionPercentage]\"]);\n            if ((validExp === null || validExp === void 0 ? void 0 : validExp.length) > 0) completedFields += 2;\n            // Education (at least 1 entry with degree and institution)\n            totalFields += 2;\n            const validEdu = (_formData_education = formData.education) === null || _formData_education === void 0 ? void 0 : _formData_education.filter({\n                \"RedesignedResumeBuilder.useCallback[getCompletionPercentage]\": (edu)=>edu.degree && edu.institution\n            }[\"RedesignedResumeBuilder.useCallback[getCompletionPercentage]\"]);\n            if ((validEdu === null || validEdu === void 0 ? void 0 : validEdu.length) > 0) completedFields += 2;\n            // Skills (optional but counts if present)\n            if (((_formData_skills = formData.skills) === null || _formData_skills === void 0 ? void 0 : (_formData_skills_technical = _formData_skills.technical) === null || _formData_skills_technical === void 0 ? void 0 : _formData_skills_technical.length) > 0) {\n                totalFields += 1;\n                completedFields += 1;\n            }\n            return Math.round(completedFields / totalFields * 100);\n        }\n    }[\"RedesignedResumeBuilder.useCallback[getCompletionPercentage]\"], [\n        formData\n    ]);\n    // Render step content\n    const renderStepContent = ()=>{\n        const commonProps = {\n            formData,\n            updateFormData,\n            addArrayItem,\n            removeArrayItem,\n            validationErrors\n        };\n        switch(currentStep){\n            case 0:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_UniversalFormFields__WEBPACK_IMPORTED_MODULE_9__.UniversalPersonalForm, {\n                    ...commonProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                    lineNumber: 308,\n                    columnNumber: 16\n                }, undefined);\n            case 1:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_UniversalFormFields__WEBPACK_IMPORTED_MODULE_9__.UniversalExperienceForm, {\n                    ...commonProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                    lineNumber: 310,\n                    columnNumber: 16\n                }, undefined);\n            case 2:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_UniversalFormFields__WEBPACK_IMPORTED_MODULE_9__.UniversalEducationForm, {\n                    ...commonProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                    lineNumber: 312,\n                    columnNumber: 16\n                }, undefined);\n            case 3:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_UniversalFormFields__WEBPACK_IMPORTED_MODULE_9__.UniversalSkillsForm, {\n                    ...commonProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                    lineNumber: 314,\n                    columnNumber: 16\n                }, undefined);\n            case 4:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                        className: \"bg-gray-900/60 backdrop-blur-md rounded-2xl border border-gray-700/50 p-8 text-center\",\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center gap-3 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                                className: \"h-8 w-8 text-neural-pink\",\n                                                animate: {\n                                                    scale: [\n                                                        1,\n                                                        1.2,\n                                                        1\n                                                    ]\n                                                },\n                                                transition: {\n                                                    duration: 2,\n                                                    repeat: Infinity\n                                                },\n                                                children: \"⚡\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                                lineNumber: 325,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-neural-pink opacity-20 rounded-full blur-md\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                                lineNumber: 332,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                        lineNumber: 324,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-semibold text-white\",\n                                        children: \"Resume Created in Minutes!\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                        lineNumber: 334,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                lineNumber: 323,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-300 mb-4 max-w-md mx-auto\",\n                                children: \"You've turned hours of work into minutes with AI. Your professional resume is ready for job applications.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                lineNumber: 339,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center gap-4 text-sm text-gray-400 mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"flex items-center gap-1\",\n                                        children: \"✅ ATS-Optimized\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                        lineNumber: 344,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"flex items-center gap-1\",\n                                        children: \"✅ Job-Ready Format\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                        lineNumber: 347,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"flex items-center gap-1\",\n                                        children: \"✅ Professional Design\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                        lineNumber: 350,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                lineNumber: 343,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowTemplateSelector(true),\n                                        className: \"px-6 py-3 bg-gray-800 hover:bg-gray-700 text-white rounded-lg transition-colors border border-gray-600\",\n                                        children: \"Change Template\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                        lineNumber: 356,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowFullscreenPreview(true),\n                                        className: \"px-6 py-3 bg-gradient-to-r from-neural-purple to-neural-pink text-white rounded-lg hover:opacity-90 transition-opacity\",\n                                        children: \"Preview Fullscreen\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                        lineNumber: 362,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.info('Upgrade to Pro to download your resume!'),\n                                        className: \"px-6 py-3 bg-gradient-to-r from-yellow-500 to-orange-500 text-white rounded-lg hover:opacity-90 transition-opacity\",\n                                        children: \"Upgrade to Download\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                        lineNumber: 368,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                lineNumber: 355,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 p-4 bg-gradient-to-r from-neural-purple/10 to-neural-pink/10 border border-neural-purple/20 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-neural-blue text-sm font-medium\",\n                                        children: \"\\uD83D\\uDE80 Upgrade to Pro: Download instantly, access 50+ premium templates, and get AI-powered job matching\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                        lineNumber: 377,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400 text-xs mt-1\",\n                                        children: \"Join thousands who've landed their dream jobs faster with our AI tools\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                        lineNumber: 380,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                lineNumber: 376,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                        lineNumber: 318,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                    lineNumber: 317,\n                    columnNumber: 11\n                }, undefined);\n            default:\n                return null;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-b from-black to-[#0A0A0A] text-white relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 opacity-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 bg-[url('/grid.svg')] [mask-image:radial-gradient(ellipse_at_center,white,transparent)]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                    lineNumber: 396,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                lineNumber: 395,\n                columnNumber: 7\n            }, undefined),\n            !hideHeader && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 pt-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                        className: \"text-center\",\n                        initial: {\n                            opacity: 0,\n                            y: -20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center gap-2 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                                className: \"h-8 w-8 text-neural-pink\",\n                                                animate: {\n                                                    rotate: 360\n                                                },\n                                                transition: {\n                                                    duration: 20,\n                                                    repeat: Infinity,\n                                                    ease: \"linear\"\n                                                },\n                                                children: \"✨\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                                lineNumber: 410,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-neural-pink opacity-20 rounded-full blur-md\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                                lineNumber: 417,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                        lineNumber: 409,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-semibold text-neural-blue\",\n                                        children: \"Turn Hours Into Minutes — With AI\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                        lineNumber: 419,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                lineNumber: 408,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl lg:text-4xl font-bold text-white mb-4\",\n                                children: \"AI Resume Builder\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                lineNumber: 424,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-300 text-lg max-w-2xl mx-auto mb-2\",\n                                children: \"Create ATS-optimized resumes in minutes with AI-powered suggestions\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                lineNumber: 428,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap items-center justify-center gap-4 text-sm text-gray-400\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"flex items-center gap-1\",\n                                        children: \"⚡ Create Resume in 5 minutes\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                        lineNumber: 433,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"flex items-center gap-1\",\n                                        children: \"\\uD83C\\uDFAF ATS Scoring\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                        lineNumber: 436,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"flex items-center gap-1\",\n                                        children: \"\\uD83D\\uDE80 Job-Specific Content\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                        lineNumber: 439,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                lineNumber: 432,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                        lineNumber: 403,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                    lineNumber: 402,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                lineNumber: 401,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 pb-32\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col xl:flex-row gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 max-w-4xl\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.AnimatePresence, {\n                                    mode: \"wait\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            x: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        exit: {\n                                            opacity: 0,\n                                            x: -20\n                                        },\n                                        transition: {\n                                            duration: 0.3\n                                        },\n                                        children: renderStepContent()\n                                    }, currentStep, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                        lineNumber: 455,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                    lineNumber: 454,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                lineNumber: 453,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden xl:block xl:w-80 xl:flex-shrink-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"sticky top-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StreamlinedPreview__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        formData: formData,\n                                        selectedTemplate: selectedTemplate,\n                                        onOpenFullscreen: ()=>setShowFullscreenPreview(true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                        lineNumber: 470,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                    lineNumber: 469,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                lineNumber: 468,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                        lineNumber: 451,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                    lineNumber: 450,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                lineNumber: 449,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SimplifiedNavigation, {\n                currentStep: currentStep,\n                totalSteps: steps.length,\n                onPrevious: prevStep,\n                onNext: nextStep,\n                onSave: handleSave,\n                onDownload: handleDownload,\n                onHome: ()=>window.location.href = '/',\n                canProceed: canProceedToNextStep(currentStep),\n                isSaving: isSaving,\n                completionPercentage: getCompletionPercentage(),\n                stepTitles: steps.map((step)=>step.title)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                lineNumber: 482,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.AnimatePresence, {\n                children: showTemplateSelector && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_UniversalTemplateSelector__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    selectedTemplate: selectedTemplate,\n                    onTemplateSelect: setSelectedTemplate,\n                    onClose: ()=>setShowTemplateSelector(false),\n                    formData: formData\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                    lineNumber: 499,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                lineNumber: 497,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.AnimatePresence, {\n                children: showFullscreenPreview && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FullscreenPreviewModal__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    isOpen: showFullscreenPreview,\n                    onClose: ()=>setShowFullscreenPreview(false),\n                    formData: formData,\n                    selectedTemplate: selectedTemplate,\n                    onTemplateChange: setSelectedTemplate\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                    lineNumber: 510,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                lineNumber: 508,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n        lineNumber: 393,\n        columnNumber: 5\n    }, undefined);\n};\n_s(RedesignedResumeBuilder, \"DLu6Gwuw9dLx2aADXayQf6m5jMU=\");\n_c = RedesignedResumeBuilder;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (RedesignedResumeBuilder);\nvar _c;\n$RefreshReg$(_c, \"RedesignedResumeBuilder\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/resume/RedesignedResumeBuilder.jsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/resume/TopStepNavigation.jsx":
/*!*****************************************************!*\
  !*** ./src/components/resume/TopStepNavigation.jsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Check_FileText_GraduationCap_Home_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Check,FileText,GraduationCap,Home,Save,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Check_FileText_GraduationCap_Home_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Check,FileText,GraduationCap,Home,Save,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/graduation-cap.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Check_FileText_GraduationCap_Home_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Check,FileText,GraduationCap,Home,Save,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Check_FileText_GraduationCap_Home_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Check,FileText,GraduationCap,Home,Save,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Check_FileText_GraduationCap_Home_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Check,FileText,GraduationCap,Home,Save,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Check_FileText_GraduationCap_Home_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Check,FileText,GraduationCap,Home,Save,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Check_FileText_GraduationCap_Home_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Check,FileText,GraduationCap,Home,Save,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Check_FileText_GraduationCap_Home_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Check,FileText,GraduationCap,Home,Save,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Check_FileText_GraduationCap_Home_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Check,FileText,GraduationCap,Home,Save,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Check_FileText_GraduationCap_Home_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Check,FileText,GraduationCap,Home,Save,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst TopStepNavigation = (param)=>{\n    let { currentStep, totalSteps, onPrevious, onNext, onSave, onHome, canProceed = true, isSaving = false, completionPercentage = 0, stepTitles = [], completedSteps = [] } = param;\n    const isFirstStep = currentStep === 0;\n    const isLastStep = currentStep === totalSteps - 1;\n    const getStepIcon = (stepIndex)=>{\n        const icons = [\n            _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Check_FileText_GraduationCap_Home_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n            _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Check_FileText_GraduationCap_Home_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n            _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Check_FileText_GraduationCap_Home_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Check_FileText_GraduationCap_Home_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Check_FileText_GraduationCap_Home_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n        ];\n        const Icon = icons[stepIndex] || _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Check_FileText_GraduationCap_Home_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\n        return Icon;\n    };\n    const getStepStatus = (stepIndex)=>{\n        if (completedSteps.includes(stepIndex)) return 'completed';\n        if (stepIndex === currentStep) return 'current';\n        return 'upcoming';\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gray-900/60 backdrop-blur-md rounded-2xl border border-gray-700/50 mb-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-b border-gray-700/30\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: onHome,\n                                        className: \"p-2 text-gray-400 hover:text-white transition-colors rounded-lg hover:bg-gray-700/50\",\n                                        title: \"Back to Home\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Check_FileText_GraduationCap_Home_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\TopStepNavigation.jsx\",\n                                            lineNumber: 47,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\TopStepNavigation.jsx\",\n                                        lineNumber: 42,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-white\",\n                                                children: stepTitles[currentStep] || \"Step \".concat(currentStep + 1)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\TopStepNavigation.jsx\",\n                                                lineNumber: 50,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: [\n                                                    \"Step \",\n                                                    currentStep + 1,\n                                                    \" of \",\n                                                    totalSteps,\n                                                    \" • \",\n                                                    Math.round(completionPercentage),\n                                                    \"% Complete\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\TopStepNavigation.jsx\",\n                                                lineNumber: 53,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\TopStepNavigation.jsx\",\n                                        lineNumber: 49,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\TopStepNavigation.jsx\",\n                                lineNumber: 41,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onSave,\n                                disabled: isSaving,\n                                className: \"flex items-center gap-2 px-3 py-2 bg-gray-800 hover:bg-gray-700 text-gray-300 rounded-lg font-medium transition-colors disabled:opacity-50 border border-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Check_FileText_GraduationCap_Home_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\TopStepNavigation.jsx\",\n                                        lineNumber: 64,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"hidden sm:inline\",\n                                        children: isSaving ? 'Saving...' : 'Save'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\TopStepNavigation.jsx\",\n                                        lineNumber: 65,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\TopStepNavigation.jsx\",\n                                lineNumber: 59,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\TopStepNavigation.jsx\",\n                        lineNumber: 40,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full bg-gray-700 rounded-full h-2 mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                            className: \"bg-gradient-to-r from-neural-purple to-neural-pink h-2 rounded-full\",\n                            initial: {\n                                width: 0\n                            },\n                            animate: {\n                                width: \"\".concat(completionPercentage, \"%\")\n                            },\n                            transition: {\n                                duration: 0.5\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\TopStepNavigation.jsx\",\n                            lineNumber: 73,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\TopStepNavigation.jsx\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-x-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3 min-w-max pb-2\",\n                            children: stepTitles.map((title, index)=>{\n                                const Icon = getStepIcon(index);\n                                const status = getStepStatus(index);\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col items-center min-w-[80px]\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"\\n                      w-8 h-8 rounded-full border-2 flex items-center justify-center transition-all duration-200 mb-1\\n                      \".concat(status === 'completed' ? 'bg-neural-pink border-neural-pink text-white' : status === 'current' ? 'bg-neural-purple border-neural-purple text-white' : 'bg-gray-800 border-gray-600 text-gray-400', \"\\n                    \"),\n                                                    children: status === 'completed' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Check_FileText_GraduationCap_Home_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\TopStepNavigation.jsx\",\n                                                        lineNumber: 101,\n                                                        columnNumber: 25\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\TopStepNavigation.jsx\",\n                                                        lineNumber: 103,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\TopStepNavigation.jsx\",\n                                                    lineNumber: 91,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"\\n                      text-xs font-medium text-center\\n                      \".concat(status === 'current' ? 'text-neural-purple' : 'text-gray-400', \"\\n                    \"),\n                                                    children: title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\TopStepNavigation.jsx\",\n                                                    lineNumber: 106,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\TopStepNavigation.jsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        index < stepTitles.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-0.5 mx-2 bg-gray-700 relative\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                                className: \"h-full bg-neural-pink\",\n                                                initial: {\n                                                    width: 0\n                                                },\n                                                animate: {\n                                                    width: index < currentStep ? '100%' : '0%'\n                                                },\n                                                transition: {\n                                                    duration: 0.3\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\TopStepNavigation.jsx\",\n                                                lineNumber: 117,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\TopStepNavigation.jsx\",\n                                            lineNumber: 116,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\TopStepNavigation.jsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 17\n                                }, undefined);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\TopStepNavigation.jsx\",\n                            lineNumber: 83,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\TopStepNavigation.jsx\",\n                        lineNumber: 82,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\TopStepNavigation.jsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onPrevious,\n                                disabled: isFirstStep,\n                                className: \"\\n              flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-all duration-200 min-w-[100px]\\n              \".concat(isFirstStep ? 'bg-gray-800 text-gray-500 cursor-not-allowed' : 'bg-gray-800 text-gray-300 hover:bg-gray-700 border border-gray-600', \"\\n            \"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Check_FileText_GraduationCap_Home_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\TopStepNavigation.jsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Previous\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\TopStepNavigation.jsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\TopStepNavigation.jsx\",\n                                lineNumber: 138,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden md:flex flex-col items-center text-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-400\",\n                                    children: isLastStep ? 'Your AI-powered resume is ready' : 'Building your professional resume with AI'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\TopStepNavigation.jsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\TopStepNavigation.jsx\",\n                                lineNumber: 154,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onNext,\n                                disabled: !canProceed,\n                                className: \"\\n              flex items-center gap-2 px-6 py-2 rounded-lg font-medium transition-all duration-200 min-w-[100px]\\n              \".concat(canProceed ? 'bg-gradient-to-r from-neural-purple to-neural-pink text-white shadow-lg hover:opacity-90' : 'bg-gray-800 text-gray-500 cursor-not-allowed border border-gray-700', \"\\n            \"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: isLastStep ? 'Complete' : 'Next'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\TopStepNavigation.jsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Check_FileText_GraduationCap_Home_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\TopStepNavigation.jsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\TopStepNavigation.jsx\",\n                                lineNumber: 161,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\TopStepNavigation.jsx\",\n                        lineNumber: 136,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"md:hidden mt-3 text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-400\",\n                            children: isLastStep ? 'Your AI-powered resume is ready' : 'Building your professional resume with AI'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\TopStepNavigation.jsx\",\n                            lineNumber: 179,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\TopStepNavigation.jsx\",\n                        lineNumber: 178,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\TopStepNavigation.jsx\",\n                lineNumber: 135,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\TopStepNavigation.jsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, undefined);\n};\n_c = TopStepNavigation;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TopStepNavigation);\nvar _c;\n$RefreshReg$(_c, \"TopStepNavigation\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/resume/TopStepNavigation.jsx\n"));

/***/ })

});
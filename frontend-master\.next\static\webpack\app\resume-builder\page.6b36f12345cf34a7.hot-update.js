"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/resume-builder/page",{

/***/ "(app-pages-browser)/./src/components/resume/ResumeBuilder.jsx":
/*!*************************************************!*\
  !*** ./src/components/resume/ResumeBuilder.jsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Briefcase,CheckCircle,Clock,Download,Eye,FileText,GraduationCap,Lightbulb,RotateCcw,Save,Sparkles,Target,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Briefcase,CheckCircle,Clock,Download,Eye,FileText,GraduationCap,Lightbulb,RotateCcw,Save,Sparkles,Target,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/graduation-cap.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Briefcase,CheckCircle,Clock,Download,Eye,FileText,GraduationCap,Lightbulb,RotateCcw,Save,Sparkles,Target,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Briefcase,CheckCircle,Clock,Download,Eye,FileText,GraduationCap,Lightbulb,RotateCcw,Save,Sparkles,Target,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Briefcase,CheckCircle,Clock,Download,Eye,FileText,GraduationCap,Lightbulb,RotateCcw,Save,Sparkles,Target,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Briefcase,CheckCircle,Clock,Download,Eye,FileText,GraduationCap,Lightbulb,RotateCcw,Save,Sparkles,Target,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Briefcase,CheckCircle,Clock,Download,Eye,FileText,GraduationCap,Lightbulb,RotateCcw,Save,Sparkles,Target,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Briefcase,CheckCircle,Clock,Download,Eye,FileText,GraduationCap,Lightbulb,RotateCcw,Save,Sparkles,Target,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _StepNavigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./StepNavigation */ \"(app-pages-browser)/./src/components/resume/StepNavigation.jsx\");\n/* harmony import */ var _layout_SmartNavigationBar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../layout/SmartNavigationBar */ \"(app-pages-browser)/./src/components/layout/SmartNavigationBar.jsx\");\n/* harmony import */ var _forms_ResumeFormComponents__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./forms/ResumeFormComponents */ \"(app-pages-browser)/./src/components/resume/forms/ResumeFormComponents.jsx\");\n/* harmony import */ var _forms_ExperienceForm__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./forms/ExperienceForm */ \"(app-pages-browser)/./src/components/resume/forms/ExperienceForm.jsx\");\n/* harmony import */ var _forms_SkillsProjectsForm__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./forms/SkillsProjectsForm */ \"(app-pages-browser)/./src/components/resume/forms/SkillsProjectsForm.jsx\");\n/* harmony import */ var _forms_ProjectsForm__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./forms/ProjectsForm */ \"(app-pages-browser)/./src/components/resume/forms/ProjectsForm.jsx\");\n/* harmony import */ var _forms_SkillsForm__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./forms/SkillsForm */ \"(app-pages-browser)/./src/components/resume/forms/SkillsForm.jsx\");\n/* harmony import */ var _forms_ReviewForm__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./forms/ReviewForm */ \"(app-pages-browser)/./src/components/resume/forms/ReviewForm.jsx\");\n/* harmony import */ var _common_ProgressBar__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../common/ProgressBar */ \"(app-pages-browser)/./src/components/common/ProgressBar.jsx\");\n/* harmony import */ var _ResumePreview__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./ResumePreview */ \"(app-pages-browser)/./src/components/resume/ResumePreview.jsx\");\n/* harmony import */ var _SuccessScreen__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./SuccessScreen */ \"(app-pages-browser)/./src/components/resume/SuccessScreen.jsx\");\n/* harmony import */ var _hooks_useATSAnalysis__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/hooks/useATSAnalysis */ \"(app-pages-browser)/./src/hooks/useATSAnalysis.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst EnhancedResumeBuilder = (param)=>{\n    let { hideHeader = false } = param;\n    var _atsAnalysis_recommendations;\n    _s();\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [selectedTemplate, setSelectedTemplate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('modern');\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        personal: {\n            firstName: \"\",\n            lastName: \"\",\n            email: \"\",\n            phone: \"\",\n            location: \"\",\n            linkedin: \"\",\n            portfolio: \"\",\n            summary: \"\",\n            profileImage: \"\"\n        },\n        education: [\n            {\n                id: 1,\n                degree: \"\",\n                institution: \"\",\n                location: \"\",\n                startDate: \"\",\n                endDate: \"\",\n                gpa: \"\",\n                relevant: \"\"\n            }\n        ],\n        experience: [\n            {\n                id: 1,\n                title: \"\",\n                company: \"\",\n                location: \"\",\n                startDate: \"\",\n                endDate: \"\",\n                current: false,\n                description: \"\"\n            }\n        ],\n        skills: {\n            technical: [],\n            languages: [],\n            certifications: []\n        },\n        projects: [\n            {\n                id: 1,\n                name: \"\",\n                description: \"\",\n                technologies: \"\",\n                link: \"\"\n            }\n        ],\n        jobDescription: \"\"\n    });\n    const [showPreview, setShowPreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showProgressBar, setShowProgressBar] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [resumeGenerated, setResumeGenerated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [validationErrors, setValidationErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [showValidationErrors, setShowValidationErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [completedSteps, setCompletedSteps] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [autoSaveEnabled, setAutoSaveEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [lastSaved, setLastSaved] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [resumeData, setResumeData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [resumeUrl, setResumeUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [atsScore, setAtsScore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [suggestions, setSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [aiSuggestionsEnabled, setAiSuggestionsEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [smartValidation, setSmartValidation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showHints, setShowHints] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const steps = [\n        {\n            id: 0,\n            title: \"Personal Information\",\n            icon: _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n            description: \"Tell us about yourself\",\n            estimatedTime: 3,\n            required: true\n        },\n        {\n            id: 1,\n            title: \"Education\",\n            icon: _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n            description: \"Your academic background\",\n            estimatedTime: 5,\n            required: true\n        },\n        {\n            id: 2,\n            title: \"Experience\",\n            icon: _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n            description: \"Your work experience\",\n            estimatedTime: 8,\n            required: true\n        },\n        {\n            id: 3,\n            title: \"Projects\",\n            icon: _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n            description: \"Showcase your projects\",\n            estimatedTime: 4,\n            required: false\n        },\n        {\n            id: 4,\n            title: \"Skills\",\n            icon: _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n            description: \"Your technical & soft skills\",\n            estimatedTime: 3,\n            required: false\n        },\n        {\n            id: 5,\n            title: \"Review & Generate\",\n            icon: _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n            description: \"Finalize your resume\",\n            estimatedTime: 2,\n            required: false\n        }\n    ];\n    const atsAnalysis = (0,_hooks_useATSAnalysis__WEBPACK_IMPORTED_MODULE_14__[\"default\"])(formData);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EnhancedResumeBuilder.useEffect\": ()=>{\n            // Check for backup data from auth flow first\n            const backupData = localStorage.getItem('resumeFormDataBackup');\n            const authTimestamp = localStorage.getItem('authFlowTimestamp');\n            const savedData = localStorage.getItem('resumeFormData');\n            // If backup data exists and is recent (within 10 minutes), use it\n            if (backupData && authTimestamp) {\n                const timeDiff = Date.now() - parseInt(authTimestamp);\n                if (timeDiff < 10 * 60 * 1000) {\n                    try {\n                        const parsed = JSON.parse(backupData);\n                        setFormData(parsed);\n                        setLastSaved(new Date());\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success('Your work has been restored after sign-in!');\n                        // Clean up backup data\n                        localStorage.removeItem('resumeFormDataBackup');\n                        localStorage.removeItem('authFlowTimestamp');\n                        // Update main storage\n                        localStorage.setItem('resumeFormData', backupData);\n                        localStorage.setItem('resumeLastSaved', new Date().toISOString());\n                        return;\n                    } catch (error) {\n                        console.error('Failed to load backup data:', error);\n                    }\n                }\n            }\n            // Fallback to regular saved data\n            if (savedData) {\n                try {\n                    const parsed = JSON.parse(savedData);\n                    setFormData(parsed);\n                    setLastSaved(new Date(localStorage.getItem('resumeLastSaved') || Date.now()));\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success('Previous work restored!');\n                } catch (error) {\n                    console.error('Failed to load saved data:', error);\n                }\n            }\n            // Clean up old backup data\n            localStorage.removeItem('resumeFormDataBackup');\n            localStorage.removeItem('authFlowTimestamp');\n            // Listen for resume generation event from ReviewForm\n            const handleGenerateResume = {\n                \"EnhancedResumeBuilder.useEffect.handleGenerateResume\": ()=>{\n                    // We'll call the generation function directly here to avoid dependency issues\n                    if (typeof generateResume === 'function') {\n                        generateResume();\n                    }\n                }\n            }[\"EnhancedResumeBuilder.useEffect.handleGenerateResume\"];\n            if (true) {\n                window.addEventListener('generateResume', handleGenerateResume);\n                return ({\n                    \"EnhancedResumeBuilder.useEffect\": ()=>window.removeEventListener('generateResume', handleGenerateResume)\n                })[\"EnhancedResumeBuilder.useEffect\"];\n            }\n        }\n    }[\"EnhancedResumeBuilder.useEffect\"], []); // Remove generateResume from dependencies to avoid circular reference\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EnhancedResumeBuilder.useEffect\": ()=>{\n            if (autoSaveEnabled && formData) {\n                // Make form data globally accessible for auth flow\n                if (true) {\n                    window.formData = formData;\n                }\n                const timeoutId = setTimeout({\n                    \"EnhancedResumeBuilder.useEffect.timeoutId\": ()=>{\n                        localStorage.setItem('resumeFormData', JSON.stringify(formData));\n                        localStorage.setItem('resumeLastSaved', new Date().toISOString());\n                        setLastSaved(new Date());\n                    }\n                }[\"EnhancedResumeBuilder.useEffect.timeoutId\"], 2000);\n                return ({\n                    \"EnhancedResumeBuilder.useEffect\": ()=>clearTimeout(timeoutId)\n                })[\"EnhancedResumeBuilder.useEffect\"];\n            }\n        }\n    }[\"EnhancedResumeBuilder.useEffect\"], [\n        formData,\n        autoSaveEnabled\n    ]);\n    const validateStep = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[validateStep]\": (stepIndex)=>{\n            const errors = {};\n            switch(stepIndex){\n                case 0:\n                    var _formData_personal_firstName, _formData_personal_lastName, _formData_personal_email;\n                    if (!((_formData_personal_firstName = formData.personal.firstName) === null || _formData_personal_firstName === void 0 ? void 0 : _formData_personal_firstName.trim())) errors.firstName = 'First name is required';\n                    if (!((_formData_personal_lastName = formData.personal.lastName) === null || _formData_personal_lastName === void 0 ? void 0 : _formData_personal_lastName.trim())) errors.lastName = 'Last name is required';\n                    if (!((_formData_personal_email = formData.personal.email) === null || _formData_personal_email === void 0 ? void 0 : _formData_personal_email.trim())) errors.email = 'Email is required';\n                    else if (!/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(formData.personal.email)) errors.email = 'Please enter a valid email address';\n                    break;\n                case 1:\n                    var _formData_education;\n                    const validEducation = (_formData_education = formData.education) === null || _formData_education === void 0 ? void 0 : _formData_education.filter({\n                        \"EnhancedResumeBuilder.useCallback[validateStep]\": (edu)=>{\n                            var _edu_degree, _edu_institution;\n                            return ((_edu_degree = edu.degree) === null || _edu_degree === void 0 ? void 0 : _edu_degree.trim()) && ((_edu_institution = edu.institution) === null || _edu_institution === void 0 ? void 0 : _edu_institution.trim());\n                        }\n                    }[\"EnhancedResumeBuilder.useCallback[validateStep]\"]);\n                    if (!validEducation || validEducation.length === 0) errors.education = 'At least one education entry with degree and institution is required';\n                    break;\n                case 2:\n                    var _formData_experience;\n                    const validExperience = (_formData_experience = formData.experience) === null || _formData_experience === void 0 ? void 0 : _formData_experience.filter({\n                        \"EnhancedResumeBuilder.useCallback[validateStep]\": (exp)=>{\n                            var _exp_title, _exp_company;\n                            return ((_exp_title = exp.title) === null || _exp_title === void 0 ? void 0 : _exp_title.trim()) && ((_exp_company = exp.company) === null || _exp_company === void 0 ? void 0 : _exp_company.trim());\n                        }\n                    }[\"EnhancedResumeBuilder.useCallback[validateStep]\"]);\n                    if (!validExperience || validExperience.length === 0) errors.experience = 'At least one work experience entry with job title and company is required';\n                    break;\n                case 3:\n                    break;\n                case 4:\n                    break;\n                default:\n                    break;\n            }\n            return errors;\n        }\n    }[\"EnhancedResumeBuilder.useCallback[validateStep]\"], [\n        formData\n    ]);\n    const canProceedToNextStep = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[canProceedToNextStep]\": (stepIndex)=>{\n            const errors = validateStep(stepIndex);\n            return Object.keys(errors).length === 0;\n        }\n    }[\"EnhancedResumeBuilder.useCallback[canProceedToNextStep]\"], [\n        validateStep\n    ]);\n    const canGenerateResume = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[canGenerateResume]\": ()=>{\n            for(let i = 0; i < steps.length - 1; i++){\n                if (!canProceedToNextStep(i)) return false;\n            }\n            return true;\n        }\n    }[\"EnhancedResumeBuilder.useCallback[canGenerateResume]\"], [\n        canProceedToNextStep,\n        steps.length\n    ]);\n    const updateFormData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[updateFormData]\": function(section, field, value) {\n            let index = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : null;\n            setFormData({\n                \"EnhancedResumeBuilder.useCallback[updateFormData]\": (prev)=>{\n                    let newData = {\n                        ...prev\n                    };\n                    if (index !== null && Array.isArray(prev[section])) {\n                        const newArray = [\n                            ...prev[section]\n                        ];\n                        newArray[index] = {\n                            ...newArray[index],\n                            [field]: value\n                        };\n                        newData[section] = newArray;\n                    } else if (typeof prev[section] === 'object' && !Array.isArray(prev[section])) {\n                        newData[section] = {\n                            ...prev[section],\n                            [field]: value\n                        };\n                    } else {\n                        newData[field] = value;\n                    }\n                    return newData;\n                }\n            }[\"EnhancedResumeBuilder.useCallback[updateFormData]\"]);\n            if (validationErrors[field]) {\n                setValidationErrors({\n                    \"EnhancedResumeBuilder.useCallback[updateFormData]\": (prev)=>{\n                        const newErrors = {\n                            ...prev\n                        };\n                        delete newErrors[field];\n                        return newErrors;\n                    }\n                }[\"EnhancedResumeBuilder.useCallback[updateFormData]\"]);\n            }\n        }\n    }[\"EnhancedResumeBuilder.useCallback[updateFormData]\"], [\n        validationErrors\n    ]);\n    const addArrayItem = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[addArrayItem]\": (section, template)=>{\n            setFormData({\n                \"EnhancedResumeBuilder.useCallback[addArrayItem]\": (prev)=>({\n                        ...prev,\n                        [section]: [\n                            ...prev[section],\n                            {\n                                ...template,\n                                id: Math.random().toString(36).substring(2, 11)\n                            }\n                        ]\n                    })\n            }[\"EnhancedResumeBuilder.useCallback[addArrayItem]\"]);\n        }\n    }[\"EnhancedResumeBuilder.useCallback[addArrayItem]\"], []);\n    const removeArrayItem = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[removeArrayItem]\": (section, id)=>{\n            setFormData({\n                \"EnhancedResumeBuilder.useCallback[removeArrayItem]\": (prev)=>({\n                        ...prev,\n                        [section]: prev[section].filter({\n                            \"EnhancedResumeBuilder.useCallback[removeArrayItem]\": (item)=>item.id !== id\n                        }[\"EnhancedResumeBuilder.useCallback[removeArrayItem]\"])\n                    })\n            }[\"EnhancedResumeBuilder.useCallback[removeArrayItem]\"]);\n        }\n    }[\"EnhancedResumeBuilder.useCallback[removeArrayItem]\"], []);\n    const nextStep = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[nextStep]\": ()=>{\n            const errors = validateStep(currentStep);\n            if (Object.keys(errors).length > 0) {\n                setValidationErrors(errors);\n                setShowValidationErrors(true);\n                const errorMessages = Object.values(errors);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(errorMessages[0]);\n                return;\n            }\n            setCompletedSteps({\n                \"EnhancedResumeBuilder.useCallback[nextStep]\": (prev)=>[\n                        ...new Set([\n                            ...prev,\n                            currentStep\n                        ])\n                    ]\n            }[\"EnhancedResumeBuilder.useCallback[nextStep]\"]);\n            setValidationErrors({});\n            setShowValidationErrors(false);\n            if (currentStep < steps.length - 1) {\n                setCurrentStep(currentStep + 1);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"\".concat(steps[currentStep].title, \" completed!\"));\n            }\n        }\n    }[\"EnhancedResumeBuilder.useCallback[nextStep]\"], [\n        currentStep,\n        validateStep,\n        steps\n    ]);\n    const prevStep = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[prevStep]\": ()=>{\n            setValidationErrors({});\n            setShowValidationErrors(false);\n            if (currentStep > 0) setCurrentStep(currentStep - 1);\n        }\n    }[\"EnhancedResumeBuilder.useCallback[prevStep]\"], [\n        currentStep\n    ]);\n    const handleStepClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[handleStepClick]\": (stepIndex)=>{\n            if (stepIndex <= currentStep || completedSteps.includes(stepIndex)) {\n                setCurrentStep(stepIndex);\n                setValidationErrors({});\n                setShowValidationErrors(false);\n            }\n        }\n    }[\"EnhancedResumeBuilder.useCallback[handleStepClick]\"], [\n        currentStep,\n        completedSteps\n    ]);\n    const generateResume = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[generateResume]\": async ()=>{\n            setValidationErrors({});\n            setShowValidationErrors(false);\n            if (!canGenerateResume()) {\n                const allErrors = {};\n                for(let i = 0; i < steps.length - 1; i++){\n                    const stepErrors = validateStep(i);\n                    Object.assign(allErrors, stepErrors);\n                }\n                setValidationErrors(allErrors);\n                setShowValidationErrors(true);\n                for(let i = 0; i < steps.length - 1; i++){\n                    const stepErrors = validateStep(i);\n                    if (Object.keys(stepErrors).length > 0) {\n                        setCurrentStep(i);\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Please complete all required fields in \".concat(steps[i].title));\n                        return;\n                    }\n                }\n                return;\n            }\n            try {\n                setIsGenerating(true);\n                setShowProgressBar(true);\n                setResumeGenerated(false);\n                const response = await fetch('/api/generate-resume', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        formData,\n                        templateId: selectedTemplate\n                    })\n                });\n                const contentType = response.headers.get('content-type');\n                if (!contentType || !contentType.includes('application/json')) throw new Error('Server returned non-JSON response');\n                const data = await response.json();\n                if (!response.ok) throw new Error(data.error || 'Failed to generate resume');\n                if (!data.resumeData || !data.downloadUrl) throw new Error('Generated resume data is incomplete');\n                setResumeUrl(data.downloadUrl);\n                setResumeData(data.resumeData);\n                setAtsScore(data.atsScore || 75);\n                setSuggestions(data.suggestions || []);\n                setResumeGenerated(true);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success('Resume generated successfully!');\n            } catch (error) {\n                console.error('Resume generation error:', error);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(error.message || 'Failed to generate resume');\n            } finally{\n                setIsGenerating(false);\n                setShowProgressBar(false);\n            }\n        }\n    }[\"EnhancedResumeBuilder.useCallback[generateResume]\"], [\n        canGenerateResume,\n        formData,\n        steps,\n        validateStep,\n        selectedTemplate\n    ]);\n    const handleSave = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[handleSave]\": ()=>{\n            localStorage.setItem('resumeFormData', JSON.stringify(formData));\n            localStorage.setItem('resumeLastSaved', new Date().toISOString());\n            setLastSaved(new Date());\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success('Progress saved!');\n        }\n    }[\"EnhancedResumeBuilder.useCallback[handleSave]\"], [\n        formData\n    ]);\n    const handleAISuggest = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[handleAISuggest]\": async (section)=>{\n            if (!aiSuggestionsEnabled) return;\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.loading('Getting AI suggestions...');\n            try {\n                await new Promise({\n                    \"EnhancedResumeBuilder.useCallback[handleAISuggest]\": (resolve)=>setTimeout(resolve, 1500)\n                }[\"EnhancedResumeBuilder.useCallback[handleAISuggest]\"]);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.dismiss();\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success('AI suggestions applied!');\n            } catch (error) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.dismiss();\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error('Failed to get AI suggestions');\n            }\n        }\n    }[\"EnhancedResumeBuilder.useCallback[handleAISuggest]\"], [\n        aiSuggestionsEnabled\n    ]);\n    const getStepValidation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[getStepValidation]\": ()=>{\n            const validation = {};\n            steps.forEach({\n                \"EnhancedResumeBuilder.useCallback[getStepValidation]\": (step, index)=>{\n                    const errors = validateStep(index);\n                    validation[index] = Object.keys(errors).length === 0;\n                }\n            }[\"EnhancedResumeBuilder.useCallback[getStepValidation]\"]);\n            return validation;\n        }\n    }[\"EnhancedResumeBuilder.useCallback[getStepValidation]\"], [\n        steps,\n        validateStep\n    ]);\n    const renderStepContent = ()=>{\n        const commonProps = {\n            formData,\n            updateFormData,\n            addArrayItem,\n            removeArrayItem,\n            atsAnalysis,\n            validationErrors,\n            showValidationErrors,\n            onSave: handleSave,\n            onAISuggest: handleAISuggest\n        };\n        switch(currentStep){\n            case 0:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_ResumeFormComponents__WEBPACK_IMPORTED_MODULE_5__.PersonalInfoForm, {\n                    ...commonProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                    lineNumber: 353,\n                    columnNumber: 16\n                }, undefined);\n            case 1:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_ResumeFormComponents__WEBPACK_IMPORTED_MODULE_5__.EducationForm, {\n                    ...commonProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                    lineNumber: 355,\n                    columnNumber: 16\n                }, undefined);\n            case 2:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_ExperienceForm__WEBPACK_IMPORTED_MODULE_6__.EnhancedExperienceForm, {\n                    ...commonProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                    lineNumber: 357,\n                    columnNumber: 16\n                }, undefined);\n            case 3:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_ProjectsForm__WEBPACK_IMPORTED_MODULE_8__.EnhancedProjectsForm, {\n                    ...commonProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                    lineNumber: 359,\n                    columnNumber: 16\n                }, undefined);\n            case 4:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_SkillsForm__WEBPACK_IMPORTED_MODULE_9__.EnhancedSkillsForm, {\n                    ...commonProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                    lineNumber: 361,\n                    columnNumber: 16\n                }, undefined);\n            case 5:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_ReviewForm__WEBPACK_IMPORTED_MODULE_10__.EnhancedReviewForm, {\n                    ...commonProps,\n                    selectedTemplate: selectedTemplate,\n                    onTemplateSelect: setSelectedTemplate\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                    lineNumber: 363,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return null;\n        }\n    };\n    if (resumeGenerated && resumeData && resumeUrl) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SuccessScreen__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n            formData: formData,\n            resumeData: resumeData,\n            onStartOver: ()=>{\n                setResumeGenerated(false);\n                setResumeData(null);\n                setResumeUrl(\"\");\n                setCurrentStep(0);\n            },\n            onEditResume: ()=>{\n                setResumeGenerated(false);\n                setCurrentStep(4); // Go back to review step\n            }\n        }, void 0, false, {\n            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n            lineNumber: 371,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-b from-black to-[#0A0A0A] text-white relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 opacity-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 bg-[url('/grid.svg')] [mask-image:radial-gradient(ellipse_at_center,white,transparent)]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                    lineNumber: 391,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                lineNumber: 390,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_common_ProgressBar__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                isVisible: showProgressBar,\n                onComplete: ()=>setShowProgressBar(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                lineNumber: 393,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 pb-32\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 py-8\",\n                    children: [\n                        !hideHeader && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_20__.motion.div, {\n                            className: \"text-center mb-8\",\n                            initial: {\n                                opacity: 0,\n                                y: -20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center gap-3 mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"h-10 w-10 text-neural-pink animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                    lineNumber: 400,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-neural-pink opacity-20 rounded-full blur-md\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                    lineNumber: 401,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                            lineNumber: 399,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-4xl lg:text-5xl font-bold text-white\",\n                                            children: \"Enhanced Resume Builder\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                            lineNumber: 403,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                    lineNumber: 398,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 text-lg max-w-2xl mx-auto\",\n                                    children: \"Create professional, ATS-friendly resumes with our enhanced AI-powered builder\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                    lineNumber: 405,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                            lineNumber: 397,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StepNavigation__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            currentStep: currentStep,\n                            totalSteps: steps.length,\n                            steps: steps,\n                            onStepClick: handleStepClick,\n                            allowClickNavigation: true,\n                            completedSteps: completedSteps,\n                            stepValidation: getStepValidation(),\n                            estimatedTime: steps.reduce((acc, step, index)=>{\n                                acc[index] = step.estimatedTime;\n                                return acc;\n                            }, {})\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                            lineNumber: 408,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 xl:grid-cols-1 gap-8 max-w-4xl xl:max-w-5xl mx-auto xl:mr-96\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_22__.AnimatePresence, {\n                                        mode: \"wait\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_20__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                x: 20\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                x: 0\n                                            },\n                                            exit: {\n                                                opacity: 0,\n                                                x: -20\n                                            },\n                                            transition: {\n                                                duration: 0.3\n                                            },\n                                            children: renderStepContent()\n                                        }, currentStep, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                            lineNumber: 412,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                        lineNumber: 411,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                    lineNumber: 410,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden xl:block\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"xl:fixed xl:top-24 xl:right-8 xl:w-80 xl:h-[calc(100vh-8rem)] xl:overflow-hidden\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_20__.motion.div, {\n                                                className: \"bg-gray-900/60 backdrop-blur-md rounded-2xl border border-white/10 h-full flex flex-col\",\n                                                initial: {\n                                                    opacity: 0,\n                                                    x: 20\n                                                },\n                                                animate: {\n                                                    opacity: 1,\n                                                    x: 0\n                                                },\n                                                transition: {\n                                                    delay: 0.2\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between p-4 border-b border-white/10\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-semibold flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-neural-blue\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                                        lineNumber: 429,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    \"Live Preview\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                                lineNumber: 428,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>setShowPreview(!showPreview),\n                                                                    className: \"px-3 py-1.5 rounded-lg text-sm font-medium transition-colors \".concat(showPreview ? 'bg-neural-purple text-white' : 'bg-gray-800 text-gray-300 hover:bg-gray-700'),\n                                                                    children: showPreview ? 'Hide' : 'Show'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                                    lineNumber: 433,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                                lineNumber: 432,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                        lineNumber: 427,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 overflow-hidden\",\n                                                        children: showPreview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-full overflow-y-auto p-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"transform scale-[0.65] origin-top-left w-[154%] h-[154%]\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-white rounded-lg shadow-lg\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ResumePreview__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                        formData: formData,\n                                                                        selectedTemplate: selectedTemplate\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                                        lineNumber: 450,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                                    lineNumber: 449,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                                lineNumber: 448,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                            lineNumber: 447,\n                                                            columnNumber: 23\n                                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-center h-full text-gray-400\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                        className: \"h-16 w-16 mx-auto mb-4 opacity-30\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                                        lineNumber: 457,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm\",\n                                                                        children: 'Click \"Show\" to preview'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                                        lineNumber: 458,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-500 mt-1\",\n                                                                        children: \"Live updates as you type\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                                        lineNumber: 459,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                                lineNumber: 456,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                            lineNumber: 455,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                        lineNumber: 445,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    showPreview && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-3 border-t border-white/10 bg-gray-800/30\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between text-xs text-gray-400\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        \"Template: \",\n                                                                        selectedTemplate\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                                    lineNumber: 469,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Auto-updating\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                                    lineNumber: 470,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                            lineNumber: 468,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                        lineNumber: 467,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                lineNumber: 420,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                            lineNumber: 419,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"xl:hidden\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_20__.motion.div, {\n                                                className: \"bg-gray-900/60 backdrop-blur-md rounded-2xl p-6 border border-white/10\",\n                                                initial: {\n                                                    opacity: 0,\n                                                    x: 20\n                                                },\n                                                animate: {\n                                                    opacity: 1,\n                                                    x: 0\n                                                },\n                                                transition: {\n                                                    delay: 0.2\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between mb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-semibold flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-neural-blue\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                                        lineNumber: 482,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    \"Resume Preview\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                                lineNumber: 481,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>setShowPreview(!showPreview),\n                                                                className: \"px-3 py-1.5 rounded-lg text-sm font-medium transition-colors \".concat(showPreview ? 'bg-neural-purple text-white' : 'bg-gray-800 text-gray-300 hover:bg-gray-700'),\n                                                                children: showPreview ? 'Hide' : 'Show'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                                lineNumber: 485,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                        lineNumber: 480,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    showPreview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"max-h-[600px] overflow-y-auto border border-white/10 rounded-lg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ResumePreview__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            formData: formData,\n                                                            selectedTemplate: selectedTemplate\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                            lineNumber: 496,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                        lineNumber: 495,\n                                                        columnNumber: 21\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center py-12 text-gray-400\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                className: \"h-12 w-12 mx-auto mb-3 opacity-30\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                                lineNumber: 500,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm\",\n                                                                children: 'Click \"Show\" to preview your resume'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                                lineNumber: 501,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                        lineNumber: 499,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                lineNumber: 479,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                            lineNumber: 478,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_20__.motion.div, {\n                                            className: \"bg-gray-900/60 backdrop-blur-md rounded-2xl p-6 border border-white/10\",\n                                            initial: {\n                                                opacity: 0,\n                                                x: 20\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                x: 0\n                                            },\n                                            transition: {\n                                                delay: 0.3\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                            className: \"h-5 w-5 text-neural-purple\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                            lineNumber: 508,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-semibold\",\n                                                            children: \"ATS Score\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                            lineNumber: 509,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                    lineNumber: 507,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-3xl font-bold \".concat((atsAnalysis.overallScore || 0) >= 80 ? 'text-green-400' : (atsAnalysis.overallScore || 0) >= 60 ? 'text-yellow-400' : 'text-red-400'),\n                                                            children: [\n                                                                atsAnalysis.overallScore || 0,\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                            lineNumber: 512,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-400\",\n                                                            children: (atsAnalysis.overallScore || 0) >= 80 ? 'Excellent' : (atsAnalysis.overallScore || 0) >= 60 ? 'Good' : 'Needs Work'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                            lineNumber: 518,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                    lineNumber: 511,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                ((_atsAnalysis_recommendations = atsAnalysis.recommendations) === null || _atsAnalysis_recommendations === void 0 ? void 0 : _atsAnalysis_recommendations.length) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-500 mb-2\",\n                                                            children: \"Top Suggestions:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                            lineNumber: 525,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        atsAnalysis.recommendations.slice(0, 2).map((rec, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-start gap-2 text-sm\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-1.5 h-1.5 bg-yellow-400 rounded-full mt-2 flex-shrink-0\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                                        lineNumber: 528,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-300\",\n                                                                        children: rec.message\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                                        lineNumber: 529,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                                lineNumber: 527,\n                                                                columnNumber: 23\n                                                            }, undefined))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                    lineNumber: 524,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                            lineNumber: 506,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                    lineNumber: 417,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                            lineNumber: 409,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                    lineNumber: 395,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                lineNumber: 394,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_layout_SmartNavigationBar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                currentStep: currentStep,\n                totalSteps: steps.length,\n                onPrevious: prevStep,\n                onNext: nextStep,\n                onGenerate: generateResume,\n                onSave: handleSave,\n                onPreview: ()=>setShowPreview(!showPreview),\n                isGenerating: isGenerating,\n                canProceed: currentStep === steps.length - 1 ? canGenerateResume() : canProceedToNextStep(currentStep),\n                showPreview: showPreview,\n                steps: steps,\n                formData: formData,\n                atsScore: atsAnalysis.overallScore,\n                autoSaveEnabled: autoSaveEnabled\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                lineNumber: 540,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n        lineNumber: 389,\n        columnNumber: 5\n    }, undefined);\n};\n_s(EnhancedResumeBuilder, \"KYGTDyYsit5HpL0G19ZmDX/jYJ8=\", false, function() {\n    return [\n        _hooks_useATSAnalysis__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n    ];\n});\n_c = EnhancedResumeBuilder;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (EnhancedResumeBuilder);\nvar _c;\n$RefreshReg$(_c, \"EnhancedResumeBuilder\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/resume/ResumeBuilder.jsx\n"));

/***/ })

});
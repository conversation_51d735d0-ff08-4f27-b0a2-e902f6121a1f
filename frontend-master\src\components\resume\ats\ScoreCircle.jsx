"use client";
import { motion } from 'framer-motion';
import { useState, useEffect } from 'react';

const ATSScoreCircle = ({ score = 0, size = 120, strokeWidth = 8, showAnimation = true }) => {
  const [animatedScore, setAnimatedScore] = useState(0);
  const radius = (size - strokeWidth) / 2;
  const circumference = radius * 2 * Math.PI;
  
  // Calculate stroke dash offset for the progress circle
  const strokeDashoffset = circumference - (animatedScore / 100) * circumference;

  // Determine color based on score
  const getScoreColor = (score) => {
    if (score >= 81) return '#10B981'; // Green
    if (score >= 61) return '#F59E0B'; // Yellow/Orange
    return '#EF4444'; // Red
  };

  const getScoreLabel = (score) => {
    if (score >= 81) return 'Excellent';
    if (score >= 61) return 'Good';
    return 'Needs Improvement';
  };

  // Animate score on mount
  useEffect(() => {
    if (showAnimation) {
      const timer = setTimeout(() => {
        setAnimatedScore(score);
      }, 500);
      return () => clearTimeout(timer);
    } else {
      setAnimatedScore(score);
    }
  }, [score, showAnimation]);

  const scoreColor = getScoreColor(animatedScore);
  const scoreLabel = getScoreLabel(animatedScore);

  return (
    <div className="flex flex-col items-center">
      <div className="relative" style={{ width: size, height: size }}>
        {/* Background circle */}
        <svg
          className="transform -rotate-90"
          width={size}
          height={size}
        >
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke="currentColor"
            strokeWidth={strokeWidth}
            fill="transparent"
            className="text-gray-700"
          />
          
          {/* Progress circle */}
          <motion.circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke={scoreColor}
            strokeWidth={strokeWidth}
            fill="transparent"
            strokeLinecap="round"
            strokeDasharray={circumference}
            strokeDashoffset={showAnimation ? circumference : strokeDashoffset}
            animate={{
              strokeDashoffset: strokeDashoffset
            }}
            transition={{
              duration: 2,
              ease: "easeInOut",
              delay: showAnimation ? 0.5 : 0
            }}
            style={{
              filter: `drop-shadow(0 0 6px ${scoreColor}40)`
            }}
          />
        </svg>

        {/* Score text in center */}
        <div className="absolute inset-0 flex flex-col items-center justify-center">
          <motion.div
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.5, delay: showAnimation ? 1 : 0 }}
            className="text-center"
          >
            <div 
              className="text-2xl md:text-3xl font-bold"
              style={{ color: scoreColor }}
            >
              {Math.round(animatedScore)}%
            </div>
            <div className="text-xs text-gray-400 font-medium">
              ATS Score
            </div>
          </motion.div>
        </div>
      </div>

      {/* Score label */}
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: showAnimation ? 1.5 : 0 }}
        className="mt-3 text-center"
      >
        <div 
          className="text-sm font-semibold"
          style={{ color: scoreColor }}
        >
          {scoreLabel}
        </div>
        <div className="text-xs text-gray-500 mt-1">
          Applicant Tracking System Compatibility
        </div>
      </motion.div>
    </div>
  );
};

// Loading version of the ATS Score Circle
export const ATSScoreLoading = ({ size = 120, strokeWidth = 8 }) => {
  const radius = (size - strokeWidth) / 2;
  const circumference = radius * 2 * Math.PI;

  return (
    <div className="flex flex-col items-center">
      <div className="relative" style={{ width: size, height: size }}>
        <svg
          className="transform -rotate-90"
          width={size}
          height={size}
        >
          {/* Background circle */}
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke="currentColor"
            strokeWidth={strokeWidth}
            fill="transparent"
            className="text-gray-700"
          />
          
          {/* Animated loading circle */}
          <motion.circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke="#832ED3"
            strokeWidth={strokeWidth}
            fill="transparent"
            strokeLinecap="round"
            strokeDasharray={circumference * 0.25}
            animate={{
              rotate: 360
            }}
            transition={{
              duration: 1.5,
              repeat: Infinity,
              ease: "linear"
            }}
            style={{
              filter: 'drop-shadow(0 0 6px #832ED340)'
            }}
          />
        </svg>

        {/* Loading text in center */}
        <div className="absolute inset-0 flex flex-col items-center justify-center">
          <motion.div
            animate={{ 
              scale: [1, 1.1, 1],
              opacity: [0.7, 1, 0.7]
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: "easeInOut"
            }}
            className="text-center"
          >
            <div className="text-lg md:text-xl font-bold text-neural-purple">
              ...
            </div>
            <div className="text-xs text-gray-400 font-medium">
              Analyzing
            </div>
          </motion.div>
        </div>
      </div>

      <motion.div
        animate={{ opacity: [0.5, 1, 0.5] }}
        transition={{ duration: 2, repeat: Infinity }}
        className="mt-3 text-center"
      >
        <div className="text-sm font-semibold text-neural-purple">
          Calculating ATS Score...
        </div>
        <div className="text-xs text-gray-500 mt-1">
          AI is analyzing your resume
        </div>
      </motion.div>
    </div>
  );
};

export default ATSScoreCircle;

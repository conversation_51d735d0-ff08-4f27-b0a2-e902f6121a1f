'use client';
import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Eye, Star, Crown, Zap, Filter, Search, Grid, List,
  CheckCircle, Award, Globe, Code, Palette, Heart,
  TrendingUp, Shield, Users
} from 'lucide-react';
import { ENHANCED_TEMPLATES } from './templates/EnhancedTemplateSystem';

const EnhancedTemplateSelector = ({ 
  selectedTemplate, 
  onTemplateSelect, 
  formData,
  showPreview = true,
  variant = 'grid' // 'grid', 'list', 'carousel'
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [sortBy, setSortBy] = useState('ats-score'); // 'ats-score', 'name', 'category'
  const [viewMode, setViewMode] = useState(variant);
  const [previewTemplate, setPreviewTemplate] = useState(null);

  const categories = [
    { id: 'all', name: 'All Templates', icon: Grid },
    { id: 'ats-optimized', name: 'ATS Optimized', icon: Shield },
    { id: 'modern', name: 'Modern', icon: TrendingUp },
    { id: 'executive', name: 'Executive', icon: Crown },
    { id: 'tech', name: 'Technology', icon: Code },
    { id: 'creative', name: 'Creative', icon: Palette },
    { id: 'healthcare', name: 'Healthcare', icon: Heart },
    { id: 'regional', name: 'Regional', icon: Globe },
    { id: 'minimal', name: 'Minimal', icon: Zap }
  ];

  const filteredTemplates = Object.values(ENHANCED_TEMPLATES).filter(template => {
    const matchesSearch = template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         template.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || template.category === selectedCategory;
    return matchesSearch && matchesCategory;
  }).sort((a, b) => {
    switch (sortBy) {
      case 'ats-score':
        return b.atsScore - a.atsScore;
      case 'name':
        return a.name.localeCompare(b.name);
      case 'category':
        return a.category.localeCompare(b.category);
      default:
        return 0;
    }
  });

  const getCategoryIcon = (categoryId) => {
    const category = categories.find(cat => cat.id === categoryId);
    return category ? category.icon : Grid;
  };

  const getATSScoreColor = (score) => {
    if (score >= 95) return 'text-green-400 bg-green-400/20';
    if (score >= 90) return 'text-yellow-400 bg-yellow-400/20';
    return 'text-red-400 bg-red-400/20';
  };

  const renderTemplateCard = (template) => (
    <motion.div
      key={template.id}
      layout
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.9 }}
      className={`relative bg-gray-800/50 backdrop-blur-sm rounded-xl border transition-all duration-300 cursor-pointer group hover:scale-105 ${
        selectedTemplate === template.id 
          ? 'border-neural-purple shadow-lg shadow-neural-purple/20' 
          : 'border-gray-700 hover:border-gray-600'
      }`}
      onClick={() => onTemplateSelect(template.id)}
    >
      {/* Premium Badge */}
      {template.isPremium && (
        <div className="absolute -top-2 -right-2 z-10">
          <div className="bg-gradient-to-r from-neural-purple to-neural-pink p-2 rounded-full">
            <Crown className="h-3 w-3 text-white" />
          </div>
        </div>
      )}

      {/* Selected Badge */}
      {selectedTemplate === template.id && (
        <div className="absolute -top-2 -left-2 z-10">
          <div className="bg-green-500 p-2 rounded-full">
            <CheckCircle className="h-3 w-3 text-white" />
          </div>
        </div>
      )}

      {/* Template Preview */}
      <div className="aspect-[8.5/11] bg-white rounded-t-xl overflow-hidden relative">
        {/* Placeholder preview - in real implementation, this would be a rendered template */}
        <div className="w-full h-full bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
          <div className="text-center text-gray-600">
            <div className="text-xs font-medium mb-1">{template.name}</div>
            <div className="text-xs opacity-60">Preview</div>
          </div>
        </div>
        
        {/* Preview Overlay */}
        <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors flex items-center justify-center opacity-0 group-hover:opacity-100">
          <button
            onClick={(e) => {
              e.stopPropagation();
              setPreviewTemplate(template);
            }}
            className="bg-white/90 text-gray-900 px-3 py-2 rounded-lg font-medium flex items-center gap-2 transform scale-90 group-hover:scale-100 transition-transform"
          >
            <Eye className="h-4 w-4" />
            Preview
          </button>
        </div>
      </div>

      {/* Template Info */}
      <div className="p-4">
        <div className="flex items-start justify-between mb-2">
          <h3 className="font-semibold text-white text-sm">{template.name}</h3>
          <div className={`px-2 py-1 rounded-full text-xs font-medium ${getATSScoreColor(template.atsScore)}`}>
            {template.atsScore}%
          </div>
        </div>
        
        <p className="text-gray-400 text-xs mb-3 line-clamp-2">{template.description}</p>
        
        {/* Features */}
        <div className="flex flex-wrap gap-1 mb-3">
          {template.features.slice(0, 2).map((feature, index) => (
            <span
              key={index}
              className="px-2 py-1 bg-gray-700/50 text-gray-300 text-xs rounded"
            >
              {feature}
            </span>
          ))}
          {template.features.length > 2 && (
            <span className="px-2 py-1 bg-gray-700/50 text-gray-400 text-xs rounded">
              +{template.features.length - 2}
            </span>
          )}
        </div>

        {/* Category */}
        <div className="flex items-center gap-2">
          {(() => {
            const CategoryIcon = getCategoryIcon(template.category);
            return <CategoryIcon className="h-3 w-3 text-gray-400" />;
          })()}
          <span className="text-gray-400 text-xs capitalize">{template.category}</span>
        </div>
      </div>
    </motion.div>
  );

  const renderListView = () => (
    <div className="space-y-4">
      {filteredTemplates.map((template) => (
        <motion.div
          key={template.id}
          layout
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: -20 }}
          className={`flex items-center gap-4 p-4 bg-gray-800/50 backdrop-blur-sm rounded-xl border transition-all duration-300 cursor-pointer hover:bg-gray-800/70 ${
            selectedTemplate === template.id 
              ? 'border-neural-purple shadow-lg shadow-neural-purple/20' 
              : 'border-gray-700 hover:border-gray-600'
          }`}
          onClick={() => onTemplateSelect(template.id)}
        >
          {/* Mini Preview */}
          <div className="w-16 h-20 bg-white rounded flex-shrink-0 flex items-center justify-center">
            <div className="text-xs text-gray-600 text-center">
              <div className="font-medium">{template.name.split(' ')[0]}</div>
            </div>
          </div>

          {/* Template Info */}
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-1">
              <h3 className="font-semibold text-white">{template.name}</h3>
              {template.isPremium && <Crown className="h-4 w-4 text-neural-purple" />}
              <div className={`px-2 py-1 rounded-full text-xs font-medium ${getATSScoreColor(template.atsScore)}`}>
                {template.atsScore}% ATS
              </div>
            </div>
            <p className="text-gray-400 text-sm mb-2">{template.description}</p>
            <div className="flex items-center gap-4 text-xs text-gray-500">
              <span className="capitalize">{template.category}</span>
              <span>{template.features.length} features</span>
            </div>
          </div>

          {/* Actions */}
          <div className="flex items-center gap-2">
            <button
              onClick={(e) => {
                e.stopPropagation();
                setPreviewTemplate(template);
              }}
              className="p-2 text-gray-400 hover:text-white transition-colors"
            >
              <Eye className="h-4 w-4" />
            </button>
            {selectedTemplate === template.id && (
              <CheckCircle className="h-5 w-5 text-green-400" />
            )}
          </div>
        </motion.div>
      ))}
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white mb-2">Choose Your Template</h2>
          <p className="text-gray-400">Select a professional template optimized for ATS systems</p>
        </div>
        
        {/* View Mode Toggle */}
        <div className="flex items-center gap-2 bg-gray-800/50 rounded-lg p-1">
          <button
            onClick={() => setViewMode('grid')}
            className={`p-2 rounded transition-colors ${
              viewMode === 'grid' ? 'bg-neural-purple text-white' : 'text-gray-400 hover:text-white'
            }`}
          >
            <Grid className="h-4 w-4" />
          </button>
          <button
            onClick={() => setViewMode('list')}
            className={`p-2 rounded transition-colors ${
              viewMode === 'list' ? 'bg-neural-purple text-white' : 'text-gray-400 hover:text-white'
            }`}
          >
            <List className="h-4 w-4" />
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="flex flex-col lg:flex-row gap-4">
        {/* Search */}
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="text"
            placeholder="Search templates..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 bg-gray-800/50 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-neural-purple focus:border-transparent"
          />
        </div>

        {/* Category Filter */}
        <select
          value={selectedCategory}
          onChange={(e) => setSelectedCategory(e.target.value)}
          className="px-4 py-2 bg-gray-800/50 border border-gray-700 rounded-lg text-white focus:ring-2 focus:ring-neural-purple focus:border-transparent"
        >
          {categories.map((category) => (
            <option key={category.id} value={category.id}>
              {category.name}
            </option>
          ))}
        </select>

        {/* Sort */}
        <select
          value={sortBy}
          onChange={(e) => setSortBy(e.target.value)}
          className="px-4 py-2 bg-gray-800/50 border border-gray-700 rounded-lg text-white focus:ring-2 focus:ring-neural-purple focus:border-transparent"
        >
          <option value="ats-score">ATS Score</option>
          <option value="name">Name</option>
          <option value="category">Category</option>
        </select>
      </div>

      {/* Results Count */}
      <div className="flex items-center justify-between">
        <span className="text-gray-400 text-sm">
          {filteredTemplates.length} template{filteredTemplates.length !== 1 ? 's' : ''} found
        </span>
        {selectedCategory !== 'all' && (
          <button
            onClick={() => setSelectedCategory('all')}
            className="text-neural-purple hover:text-neural-purple/80 text-sm transition-colors"
          >
            Clear filters
          </button>
        )}
      </div>

      {/* Templates */}
      <AnimatePresence mode="wait">
        {viewMode === 'grid' ? (
          <motion.div
            key="grid"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
          >
            {filteredTemplates.map(renderTemplateCard)}
          </motion.div>
        ) : (
          <motion.div
            key="list"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            {renderListView()}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Empty State */}
      {filteredTemplates.length === 0 && (
        <div className="text-center py-12">
          <Filter className="h-12 w-12 text-gray-600 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-400 mb-2">No templates found</h3>
          <p className="text-gray-500">Try adjusting your search or filter criteria</p>
        </div>
      )}

      {/* Template Preview Modal */}
      <AnimatePresence>
        {previewTemplate && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            onClick={() => setPreviewTemplate(null)}
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.9 }}
              className="bg-gray-900 rounded-2xl border border-gray-700 p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="flex items-center justify-between mb-6">
                <div>
                  <h3 className="text-xl font-bold text-white">{previewTemplate.name}</h3>
                  <p className="text-gray-400">{previewTemplate.description}</p>
                </div>
                <button
                  onClick={() => setPreviewTemplate(null)}
                  className="text-gray-400 hover:text-white transition-colors"
                >
                  ✕
                </button>
              </div>
              
              {/* Full template preview would go here */}
              <div className="bg-white rounded-lg p-8 min-h-[600px] flex items-center justify-center">
                <div className="text-center text-gray-600">
                  <h4 className="text-lg font-medium mb-2">{previewTemplate.name} Preview</h4>
                  <p>Full template preview would be rendered here</p>
                </div>
              </div>
              
              <div className="flex items-center justify-between mt-6">
                <div className="flex items-center gap-4">
                  <div className={`px-3 py-1 rounded-full text-sm font-medium ${getATSScoreColor(previewTemplate.atsScore)}`}>
                    {previewTemplate.atsScore}% ATS Score
                  </div>
                  {previewTemplate.isPremium && (
                    <div className="flex items-center gap-1 text-neural-purple">
                      <Crown className="h-4 w-4" />
                      <span className="text-sm">Premium</span>
                    </div>
                  )}
                </div>
                <button
                  onClick={() => {
                    onTemplateSelect(previewTemplate.id);
                    setPreviewTemplate(null);
                  }}
                  className="px-6 py-2 bg-neural-purple hover:bg-neural-purple/80 text-white rounded-lg transition-colors"
                >
                  Use This Template
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default EnhancedTemplateSelector;

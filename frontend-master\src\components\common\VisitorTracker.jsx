'use client';

import { useEffect } from 'react';
import { usePathname } from 'next/navigation';
import visitorService from '@/services/VisitorService';

const VisitorTracker = () => {
  const pathname = usePathname();

  useEffect(() => {
    // Track visitor on mount (only once per session)
    visitorService.trackVisitor();
  }, []);

  useEffect(() => {
    // Track page view on route change
    if (pathname) {
      visitorService.trackPageView(pathname);
    }
  }, [pathname]);

  // This component doesn't render anything
  return null;
};

export default VisitorTracker;

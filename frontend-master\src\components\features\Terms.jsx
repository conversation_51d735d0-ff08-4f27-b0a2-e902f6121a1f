'use client'
import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Trigger } from "@/components/ui/dialog";

const Terms = () => {
  return (
    <Dialog>
      <DialogTrigger className="font-medium text-neural-purple hover:underline">
        Terms & Conditions
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto bg-gray-900 border-gray-800">
        <DialogHeader>
          <DialogTitle className="text-2xl font-bold text-white mb-4">Terms & Conditions</DialogTitle>
        </DialogHeader>
        <div className="text-gray-300 space-y-4">
          {[
            {
              title: "1. Acceptance of Terms",
              content: "By accessing and using BlinkFind's services, you agree to be bound by these Terms and Conditions. If you do not agree to these terms, please do not use our services."
            },
            {
              title: "2. User Responsibilities",
              items: [
                "Provide accurate information",
                "Maintain account security",
                "Comply with applicable laws",
                "Respect intellectual property rights"
              ]
            },
            {
              title: "3. Service Usage",
              content: 'Our services are provided "as is" and may be modified at any time. We reserve the right to:',
              items: [
                "Modify or discontinue services",
                "Restrict access to certain features",
                "Remove inappropriate content",
                "Update these terms as needed"
              ]
            },
            {
              title: "4. Intellectual Property",
              content: "All content, features, and functionality are owned by BlinkFind and protected by international copyright laws. Users may not copy, modify, or distribute our content without permission."
            },
            {
              title: "5. Limitation of Liability",
              content: "BlinkFind is not liable for any indirect, incidental, or consequential damages arising from your use of our services. This includes data loss, profit loss, or business interruption."
            },
            {
              title: "6. Contact Information",
              content: "For questions about these terms, contact us at:\nEmail: <EMAIL>\nPhone: +****************"
            },
            {
              title: "7. Acceptance",
              content: 'Click "Accept" to continue using our services.'
            }
          ].map((section, index) => (
            <section key={index}>
              <h3 className="text-lg font-semibold text-white mb-2">{section.title}</h3>
              {section.content && <p className="mb-4 whitespace-pre-line">{section.content}</p>}
              {section.items && (
                <ul className="list-disc pl-5 mt-2 space-y-1">
                  {section.items.map((item, i) => (
                    <li key={i}>{item}</li>
                  ))}
                </ul>
              )}
            </section>
          ))}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default Terms;
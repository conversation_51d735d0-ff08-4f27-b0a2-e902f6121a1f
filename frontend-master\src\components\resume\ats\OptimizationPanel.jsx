"use client";
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Target,
  TrendingUp,
  CheckCircle,
  AlertTriangle,
  Zap,
  FileText,
  Award,
  BarChart3,
  Lightbulb,
  ArrowUp,
  XCircle,
  Info
} from 'lucide-react';
import ATSScoreCircle from './ScoreCircle';

const ATSOptimizationPanel = ({ formData, atsScore, suggestions, realTimeAnalysis }) => {
  const [optimizationTips, setOptimizationTips] = useState([]);
  const [scoreBreakdown, setScoreBreakdown] = useState({
    keywords: 0,
    formatting: 0,
    achievements: 0,
    skills: 0
  });

  useEffect(() => {
    // Use real-time analysis if available, otherwise calculate from form data
    if (realTimeAnalysis) {
      setOptimizationTips(realTimeAnalysis.recommendations || []);
      setScoreBreakdown(realTimeAnalysis.categoryScores || calculateEstimatedScores(formData));
    } else {
      // Fallback to manual calculation
      const tips = generateOptimizationTips(formData);
      setOptimizationTips(tips);
      const estimated = calculateEstimatedScores(formData);
      setScoreBreakdown(estimated);
    }
  }, [formData, atsScore, realTimeAnalysis]);

  const generateOptimizationTips = (data) => {
    const tips = [];

    // Personal Information Analysis
    if (!data.personal.firstName || !data.personal.lastName) {
      tips.push({
        type: 'error',
        category: 'Contact',
        field: 'name',
        message: 'Complete your full name (first and last name required)',
        impact: '+5 ATS points',
        icon: Target,
        priority: 'high'
      });
    }

    if (!data.personal.email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.personal.email)) {
      tips.push({
        type: 'error',
        category: 'Contact',
        field: 'email',
        message: 'Add a valid professional email address',
        impact: '+8 ATS points',
        icon: Target,
        priority: 'high'
      });
    }

    if (!data.personal.phone || data.personal.phone.length < 10) {
      tips.push({
        type: 'warning',
        category: 'Contact',
        field: 'phone',
        message: 'Add a complete phone number for better reachability',
        impact: '+3 ATS points',
        icon: Target,
        priority: 'medium'
      });
    }

    if (!data.personal.location) {
      tips.push({
        type: 'info',
        category: 'Contact',
        field: 'location',
        message: 'Add your location (city, state) for local job matching',
        impact: '+4 ATS points',
        icon: Target,
        priority: 'medium'
      });
    }

    if (!data.personal.linkedin) {
      tips.push({
        type: 'info',
        category: 'Contact',
        field: 'linkedin',
        message: 'Add LinkedIn profile URL for professional networking',
        impact: '+6 ATS points',
        icon: Target,
        priority: 'medium'
      });
    }

    // Professional Summary Analysis
    if (!data.personal.summary) {
      tips.push({
        type: 'error',
        category: 'Summary',
        field: 'summary',
        message: 'Add a professional summary to introduce yourself',
        impact: '+15 ATS points',
        icon: FileText,
        priority: 'high'
      });
    } else if (data.personal.summary.length < 50) {
      tips.push({
        type: 'warning',
        category: 'Summary',
        field: 'summary',
        message: 'Expand your summary to 50+ characters for better impact',
        impact: '+8 ATS points',
        icon: FileText,
        priority: 'medium'
      });
    } else if (data.personal.summary.length > 300) {
      tips.push({
        type: 'info',
        category: 'Summary',
        field: 'summary',
        message: 'Consider shortening summary to under 300 characters',
        impact: '+2 ATS points',
        icon: FileText,
        priority: 'low'
      });
    }

    // Experience Analysis
    const validExperience = data.experience.filter(exp => exp.title && exp.company);
    if (validExperience.length === 0) {
      tips.push({
        type: 'error',
        category: 'Experience',
        field: 'experience',
        message: 'Add at least one work experience entry',
        impact: '+20 ATS points',
        icon: TrendingUp,
        priority: 'high'
      });
    } else {
      // Check for quantified achievements
      const hasQuantifiedAchievements = validExperience.some(exp =>
        exp.description && (exp.description.includes('%') || exp.description.includes('$') || /\d+/.test(exp.description))
      );
      if (!hasQuantifiedAchievements) {
        tips.push({
          type: 'warning',
          category: 'Experience',
          field: 'experience_metrics',
          message: 'Add quantified achievements with numbers, percentages, or metrics',
          impact: '+12 ATS points',
          icon: TrendingUp,
          priority: 'high'
        });
      }

      // Check for action verbs
      const hasActionVerbs = validExperience.some(exp =>
        exp.description && /^(Led|Developed|Implemented|Managed|Created|Built|Designed|Optimized|Achieved|Delivered|Increased|Reduced|Improved)/i.test(exp.description)
      );
      if (!hasActionVerbs) {
        tips.push({
          type: 'warning',
          category: 'Experience',
          field: 'experience_verbs',
          message: 'Start experience bullets with strong action verbs',
          impact: '+8 ATS points',
          icon: Award,
          priority: 'medium'
        });
      }

      // Check for missing descriptions
      const missingDescriptions = validExperience.filter(exp => !exp.description || exp.description.length < 20);
      if (missingDescriptions.length > 0) {
        tips.push({
          type: 'warning',
          category: 'Experience',
          field: 'experience_descriptions',
          message: `${missingDescriptions.length} experience entries need detailed descriptions`,
          impact: '+10 ATS points',
          icon: TrendingUp,
          priority: 'medium'
        });
      }
    }

    // Education Analysis
    const validEducation = data.education.filter(edu => edu.degree && edu.institution);
    if (validEducation.length === 0) {
      tips.push({
        type: 'warning',
        category: 'Education',
        field: 'education',
        message: 'Add your educational background',
        impact: '+10 ATS points',
        icon: Award,
        priority: 'medium'
      });
    }

    // Skills Analysis
    if (data.skills.technical.length < 3) {
      tips.push({
        type: 'warning',
        category: 'Skills',
        field: 'technical_skills',
        message: 'Add more technical skills (aim for 6-12 relevant skills)',
        impact: '+12 ATS points',
        icon: Zap,
        priority: 'high'
      });
    } else if (data.skills.technical.length > 15) {
      tips.push({
        type: 'info',
        category: 'Skills',
        field: 'technical_skills',
        message: 'Consider focusing on your most relevant skills (6-12 is optimal)',
        impact: '+2 ATS points',
        icon: Zap,
        priority: 'low'
      });
    }

    // Projects Analysis
    const validProjects = data.projects.filter(proj => proj.name && proj.description);
    if (validProjects.length === 0) {
      tips.push({
        type: 'info',
        category: 'Projects',
        field: 'projects',
        message: 'Add relevant projects to showcase your skills',
        impact: '+8 ATS points',
        icon: Lightbulb,
        priority: 'medium'
      });
    }

    // Sort tips by priority and return top recommendations
    const priorityOrder = { high: 3, medium: 2, low: 1 };
    return tips
      .sort((a, b) => priorityOrder[b.priority] - priorityOrder[a.priority])
      .slice(0, 8); // Show top 8 tips
  };

  const calculateEstimatedScores = (data) => {
    // Contact Information Score (0-100)
    let contactScore = 0;
    if (data.personal.firstName && data.personal.lastName) contactScore += 20;
    if (data.personal.email && /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.personal.email)) contactScore += 20;
    if (data.personal.phone && data.personal.phone.length >= 10) contactScore += 15;
    if (data.personal.location) contactScore += 15;
    if (data.personal.linkedin) contactScore += 15;
    if (data.personal.portfolio) contactScore += 10;
    contactScore = Math.min(contactScore, 100);

    // Professional Summary Score (0-100)
    let summaryScore = 0;
    if (data.personal.summary) {
      summaryScore += 30; // Base score for having a summary
      if (data.personal.summary.length >= 50) summaryScore += 20;
      if (data.personal.summary.length >= 100) summaryScore += 15;
      if (data.personal.summary.length <= 300) summaryScore += 10; // Not too long

      // Check for keywords and professional language
      const professionalWords = ['experienced', 'skilled', 'proficient', 'expertise', 'accomplished', 'results-driven', 'dedicated'];
      const hasKeywords = professionalWords.some(word =>
        data.personal.summary.toLowerCase().includes(word)
      );
      if (hasKeywords) summaryScore += 15;

      // Check for industry-specific terms
      const techWords = ['software', 'development', 'programming', 'technology', 'engineering', 'data', 'systems'];
      const hasTechTerms = techWords.some(word =>
        data.personal.summary.toLowerCase().includes(word)
      );
      if (hasTechTerms) summaryScore += 10;
    }
    summaryScore = Math.min(summaryScore, 100);

    // Experience Score (0-100)
    let experienceScore = 0;
    const validExperience = data.experience.filter(exp => exp.title && exp.company);

    if (validExperience.length > 0) {
      experienceScore += 20; // Base score for having experience

      // Score based on number of experiences
      if (validExperience.length >= 2) experienceScore += 10;
      if (validExperience.length >= 3) experienceScore += 10;

      // Check for detailed descriptions
      const withDescriptions = validExperience.filter(exp => exp.description && exp.description.length > 50);
      experienceScore += Math.min(withDescriptions.length * 10, 30);

      // Check for quantified achievements
      const withMetrics = validExperience.filter(exp =>
        exp.description && (exp.description.includes('%') || exp.description.includes('$') || /\d+/.test(exp.description))
      );
      experienceScore += Math.min(withMetrics.length * 15, 30);

      // Check for action verbs
      const actionVerbs = ['led', 'developed', 'implemented', 'managed', 'created', 'built', 'designed', 'optimized', 'achieved', 'delivered', 'increased', 'reduced', 'improved'];
      const withActionVerbs = validExperience.filter(exp =>
        exp.description && actionVerbs.some(verb => exp.description.toLowerCase().includes(verb))
      );
      experienceScore += Math.min(withActionVerbs.length * 10, 20);
    }
    experienceScore = Math.min(experienceScore, 100);

    // Skills Score (0-100)
    let skillsScore = 0;

    // Technical skills
    if (data.skills.technical.length > 0) {
      skillsScore += 30; // Base score
      if (data.skills.technical.length >= 6) skillsScore += 20;
      if (data.skills.technical.length >= 10) skillsScore += 15;
      if (data.skills.technical.length <= 15) skillsScore += 10; // Not too many
    }

    // Additional skills
    if (data.skills.languages.length > 0) skillsScore += 10;
    if (data.skills.certifications.length > 0) skillsScore += 15;

    skillsScore = Math.min(skillsScore, 100);

    // Education Score (0-100)
    let educationScore = 60; // Base score
    const validEducation = data.education.filter(edu => edu.degree && edu.institution);

    if (validEducation.length > 0) {
      educationScore += 20;
      if (validEducation.some(edu => edu.gpa && parseFloat(edu.gpa) >= 3.5)) educationScore += 10;
      if (validEducation.some(edu => edu.relevant)) educationScore += 10;
    }
    educationScore = Math.min(educationScore, 100);

    // Projects Score (0-100)
    let projectsScore = 50; // Base score (projects are optional but valuable)
    const validProjects = data.projects.filter(proj => proj.name && proj.description);

    if (validProjects.length > 0) {
      projectsScore += 20;
      if (validProjects.length >= 2) projectsScore += 15;
      if (validProjects.some(proj => proj.technologies)) projectsScore += 10;
      if (validProjects.some(proj => proj.link)) projectsScore += 5;
    }
    projectsScore = Math.min(projectsScore, 100);

    return {
      contact: contactScore,
      summary: summaryScore,
      experience: experienceScore,
      skills: skillsScore,
      education: educationScore,
      projects: projectsScore
    };
  };

  const getScoreColor = (score) => {
    if (score >= 80) return 'text-green-400';
    if (score >= 60) return 'text-yellow-400';
    return 'text-red-400';
  };

  const getScoreBarColor = (score) => {
    if (score >= 80) return 'bg-green-500';
    if (score >= 60) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  const overallScore = realTimeAnalysis?.overallScore || atsScore?.overall || Math.round(
    (scoreBreakdown.contact + scoreBreakdown.summary + scoreBreakdown.experience +
     scoreBreakdown.skills + scoreBreakdown.education + scoreBreakdown.projects) / 6
  );

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-gray-800/50 backdrop-blur-md rounded-xl p-6 border border-gray-700"
    >
      {/* Header */}
      <div className="flex items-center gap-3 mb-6">
        <div className="w-10 h-10 bg-gradient-to-r from-neural-blue to-neural-purple rounded-full flex items-center justify-center">
          <Target className="h-5 w-5 text-white" />
        </div>
        <div>
          <h3 className="text-xl font-bold text-white">ATS Optimization</h3>
          <p className="text-gray-400 text-sm">Real-time resume analysis and suggestions</p>
        </div>
      </div>

      {/* Overall Score */}
      <div className="flex items-center justify-center mb-6">
        <ATSScoreCircle score={overallScore} size={120} />
      </div>

      {/* Score Breakdown */}
      <div className="grid grid-cols-2 lg:grid-cols-3 gap-3 mb-6">
        {Object.entries(scoreBreakdown).map(([category, score]) => {
          const categoryLabels = {
            contact: 'Contact Info',
            summary: 'Summary',
            experience: 'Experience',
            skills: 'Skills',
            education: 'Education',
            projects: 'Projects'
          };

          return (
            <div key={category} className="bg-gray-900/50 rounded-lg p-3">
              <div className="flex items-center justify-between mb-2">
                <span className="text-xs font-medium text-gray-300">
                  {categoryLabels[category] || category}
                </span>
                <span className={`text-xs font-bold ${getScoreColor(score)}`}>{score}%</span>
              </div>
              <div className="w-full bg-gray-700 rounded-full h-1.5">
                <div
                  className={`h-1.5 rounded-full transition-all duration-500 ${getScoreBarColor(score)}`}
                  style={{ width: `${score}%` }}
                />
              </div>
            </div>
          );
        })}
      </div>

      {/* Optimization Tips */}
      <div className="space-y-4">
        <h4 className="text-lg font-semibold text-white flex items-center gap-2">
          <Lightbulb className="h-5 w-5 text-neural-purple" />
          Optimization Tips
        </h4>
        
        {optimizationTips.length > 0 ? (
          <div className="space-y-3">
            {optimizationTips.map((tip, index) => {
              // Provide fallback icon based on tip type if icon is missing
              const getDefaultIcon = (type, category) => {
                if (type === 'error') return XCircle;
                if (type === 'warning') return AlertTriangle;
                if (category === 'Contact') return Target;
                if (category === 'Summary') return FileText;
                if (category === 'Experience') return TrendingUp;
                if (category === 'Skills') return Zap;
                if (category === 'Education') return Award;
                if (category === 'Projects') return Lightbulb;
                return Info; // Default fallback
              };

              const Icon = tip.icon || getDefaultIcon(tip.type, tip.category);

              return (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className={`p-3 rounded-lg border ${
                    tip.type === 'error'
                      ? 'bg-red-900/20 border-red-500/30'
                      : tip.type === 'warning'
                      ? 'bg-yellow-900/20 border-yellow-500/30'
                      : 'bg-blue-900/20 border-blue-500/30'
                  }`}
                >
                  <div className="flex items-start gap-3">
                    {Icon && (
                      <Icon className={`h-4 w-4 mt-0.5 ${
                        tip.type === 'error' ? 'text-red-400'
                        : tip.type === 'warning' ? 'text-yellow-400'
                        : 'text-blue-400'
                      }`} />
                    )}
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-1">
                        <span className="text-xs font-medium text-gray-400 uppercase tracking-wide">
                          {tip.category}
                        </span>
                        <span className="text-xs font-semibold text-green-400 flex items-center gap-1">
                          <ArrowUp className="h-3 w-3" />
                          {tip.impact}
                        </span>
                      </div>
                      <p className="text-sm text-gray-300">{tip.message}</p>
                    </div>
                  </div>
                </motion.div>
              );
            })}
          </div>
        ) : (
          <div className="text-center py-4">
            <CheckCircle className="h-8 w-8 text-green-400 mx-auto mb-2" />
            <p className="text-sm text-green-400 font-medium">Great job! Your resume is well optimized.</p>
            <p className="text-xs text-gray-400 mt-1">Continue filling out sections for even better results.</p>
          </div>
        )}
      </div>

      {/* Additional Suggestions */}
      {suggestions && suggestions.length > 0 && (
        <div className="mt-6 pt-6 border-t border-gray-700">
          <h4 className="text-lg font-semibold text-white mb-3 flex items-center gap-2">
            <BarChart3 className="h-5 w-5 text-neural-blue" />
            AI Recommendations
          </h4>
          <div className="space-y-2">
            {suggestions.slice(0, 3).map((suggestion, index) => (
              <div key={index} className="flex items-start gap-2">
                <div className="w-1.5 h-1.5 bg-neural-blue rounded-full mt-2" />
                <p className="text-sm text-gray-300">
                  {typeof suggestion === 'string' ? suggestion : suggestion.message || suggestion}
                </p>
              </div>
            ))}
          </div>
        </div>
      )}
    </motion.div>
  );
};

export default ATSOptimizationPanel;

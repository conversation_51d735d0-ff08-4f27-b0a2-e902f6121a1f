import { NextResponse } from 'next/server';

// Admin email addresses (from environment variables or fallback)
const getAdminEmails = () => {
  const adminEmailsEnv = process.env.ADMIN_EMAILS;
  if (adminEmailsEnv) {
    return adminEmailsEnv.split(',').map(email => email.trim());
  }
  // Fallback emails
  return [
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>'
  ];
};

const ADMIN_EMAILS = getAdminEmails();

export async function POST(request) {
  try {
    const { email, action, uid } = await request.json();

    // Validate request
    if (!email || !action) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Check admin status
    const isAdmin = ADMIN_EMAILS.includes(email.toLowerCase());

    switch (action) {
      case 'checkAdmin':
        return NextResponse.json({
          isAdmin,
          message: isAdmin ? 'Admin access granted' : 'Access denied'
        });

      case 'validateSession':
        if (!uid) {
          return NextResponse.json(
            { error: 'User ID required for session validation' },
            { status: 400 }
          );
        }
        
        // Here you could add additional server-side validation
        // like checking against a database, rate limiting, etc.
        
        return NextResponse.json({
          isAdmin,
          sessionValid: true,
          uid,
          email,
          permissions: isAdmin ? ['read', 'write', 'delete', 'admin'] : []
        });

      case 'logAccess':
        // Log admin access attempts (in production, save to database)
        console.log(`Admin access attempt: ${email} - ${isAdmin ? 'GRANTED' : 'DENIED'} at ${new Date().toISOString()}`);
        
        return NextResponse.json({
          logged: true,
          timestamp: new Date().toISOString()
        });

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('Admin API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET(request) {
  // Return admin configuration (without sensitive data)
  return NextResponse.json({
    adminRoutes: ['/admin'],
    authRequired: true,
    serverTime: new Date().toISOString(),
    version: '1.0.0'
  });
}

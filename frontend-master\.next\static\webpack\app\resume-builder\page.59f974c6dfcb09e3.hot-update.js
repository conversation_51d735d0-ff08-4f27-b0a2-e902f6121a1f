"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/resume-builder/page",{

/***/ "(app-pages-browser)/./src/app/resume-builder/page.jsx":
/*!*****************************************!*\
  !*** ./src/app/resume-builder/page.jsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Settings_Sparkles_Trash2_Upload_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Palette,Phone,Plus,PlusCircle,Settings,Sparkles,Trash2,Upload,User,Wand2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Settings_Sparkles_Trash2_Upload_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,Calendar,Eye,FileText,Globe,GraduationCap,Link,Mail,MapPin,Palette,Phone,Plus,PlusCircle,Settings,Sparkles,Trash2,Upload,User,Wand2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_SparklesIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=SparklesIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/SparklesIcon.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _components_common_ProgressBar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/common/ProgressBar */ \"(app-pages-browser)/./src/components/common/ProgressBar.jsx\");\n/* harmony import */ var _components_resume_ats_ScoreCircle__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/resume/ats/ScoreCircle */ \"(app-pages-browser)/./src/components/resume/ats/ScoreCircle.jsx\");\n/* harmony import */ var _components_resume_ResumePreview__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/resume/ResumePreview */ \"(app-pages-browser)/./src/components/resume/ResumePreview.jsx\");\n/* harmony import */ var _components_resume_PDFDownload__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/resume/PDFDownload */ \"(app-pages-browser)/./src/components/resume/PDFDownload.jsx\");\n/* harmony import */ var _components_resume_ResumeBuilder__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/resume/ResumeBuilder */ \"(app-pages-browser)/./src/components/resume/ResumeBuilder.jsx\");\n/* harmony import */ var _components_resume_ResumeUpload__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/resume/ResumeUpload */ \"(app-pages-browser)/./src/components/resume/ResumeUpload.jsx\");\n/* harmony import */ var _components_resume_ResumeUpload__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(_components_resume_ResumeUpload__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _components_resume_ats_AnalysisDisplay__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/resume/ats/AnalysisDisplay */ \"(app-pages-browser)/./src/components/resume/ats/AnalysisDisplay.jsx\");\n/* harmony import */ var _components_resume_JobDescriptionInput__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/resume/JobDescriptionInput */ \"(app-pages-browser)/./src/components/resume/JobDescriptionInput.jsx\");\n/* harmony import */ var _components_resume_BeforeAfterComparison__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/resume/BeforeAfterComparison */ \"(app-pages-browser)/./src/components/resume/BeforeAfterComparison.jsx\");\n/* harmony import */ var _components_resume_AIContentSuggestions__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/resume/AIContentSuggestions */ \"(app-pages-browser)/./src/components/resume/AIContentSuggestions.jsx\");\n/* harmony import */ var _components_resume_ats_OptimizationPanel__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/resume/ats/OptimizationPanel */ \"(app-pages-browser)/./src/components/resume/ats/OptimizationPanel.jsx\");\n/* harmony import */ var _components_resume_TemplateSelector__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/resume/TemplateSelector */ \"(app-pages-browser)/./src/components/resume/TemplateSelector.jsx\");\n/* harmony import */ var _components_resume_UploadWorkflow__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/resume/UploadWorkflow */ \"(app-pages-browser)/./src/components/resume/UploadWorkflow.jsx\");\n/* harmony import */ var _components_resume_UploadWorkflow__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(_components_resume_UploadWorkflow__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var _hooks_useATSAnalysis__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/hooks/useATSAnalysis */ \"(app-pages-browser)/./src/hooks/useATSAnalysis.js\");\n/* harmony import */ var _components_resume_ats_FieldIndicator__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/resume/ats/FieldIndicator */ \"(app-pages-browser)/./src/components/resume/ats/FieldIndicator.jsx\");\n/* harmony import */ var _components_common_ClientOnly__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/components/common/ClientOnly */ \"(app-pages-browser)/./src/components/common/ClientOnly.jsx\");\n/* harmony import */ var _components_resume_ats_Tooltip__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/components/resume/ats/Tooltip */ \"(app-pages-browser)/./src/components/resume/ats/Tooltip.jsx\");\n/* harmony import */ var _components_resume_StepNavigation__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/components/resume/StepNavigation */ \"(app-pages-browser)/./src/components/resume/StepNavigation.jsx\");\n/* harmony import */ var _components_resume_forms_ResumeFormComponents__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/components/resume/forms/ResumeFormComponents */ \"(app-pages-browser)/./src/components/resume/forms/ResumeFormComponents.jsx\");\n/* harmony import */ var _components_resume_ClassicMode__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/components/resume/ClassicMode */ \"(app-pages-browser)/./src/components/resume/ClassicMode.jsx\");\n/* harmony import */ var _components_resume_RedesignedResumeBuilder__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/components/resume/RedesignedResumeBuilder */ \"(app-pages-browser)/./src/components/resume/RedesignedResumeBuilder.jsx\");\n/* harmony import */ var _components_resume_ErrorBoundary__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/components/resume/ErrorBoundary */ \"(app-pages-browser)/./src/components/resume/ErrorBoundary.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst ResumeBuilder = ()=>{\n    _s();\n    // Mode selection: 'classic', 'enhanced', 'universal'\n    const [currentMode, setCurrentMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('universal');\n    // Universal Mode - Show redesigned version by default\n    if (currentMode === 'universal') {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_resume_RedesignedResumeBuilder__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n            lineNumber: 62,\n            columnNumber: 12\n        }, undefined);\n    }\n    // Enhanced Mode Toggle - Show enhanced version\n    if (currentMode === 'enhanced') {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-b from-black to-[#0A0A0A] text-white relative\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 opacity-20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-[url('/grid.svg')] [mask-image:radial-gradient(ellipse_at_center,white,transparent)]\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                        lineNumber: 71,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 70,\n                    columnNumber: 9\n                }, undefined),\n                \"        \",\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative z-10\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_25__.motion.div, {\n                            className: \"text-center pt-24 pb-16 px-4 sm:px-6 lg:px-8 relative z-20\",\n                            initial: {\n                                opacity: 0,\n                                y: -20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.6\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center gap-3 mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_SparklesIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                    className: \"h-8 w-8 sm:h-10 sm:w-10 text-neural-pink animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 82,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-neural-pink opacity-20 rounded-full blur-md\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 83,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 81,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-3xl sm:text-4xl lg:text-5xl font-extrabold text-white\",\n                                            children: \"Supercharge Your Job Hunt with AI-Powered Resumes\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 85,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 text-base sm:text-lg max-w-2xl mx-auto mb-8 px-4\",\n                                    children: \"Effortlessly craft ATS-optimized resumes in minutes using smart AI suggestions, real-time feedback, and one-click downloads.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_25__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    className: \"flex items-center justify-center\",\n                                    children: [\n                                        \"              \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gray-900/60 backdrop-blur-md rounded-2xl p-3 border border-white/10 max-w-lg mx-auto\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 flex-wrap justify-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setCurrentMode('classic'),\n                                                            className: \"px-3 sm:px-4 py-2 rounded-lg text-xs sm:text-sm font-medium transition-colors bg-gray-800 text-gray-300 hover:bg-gray-700\",\n                                                            children: \"Classic\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                            lineNumber: 100,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setCurrentMode('enhanced'),\n                                                            className: \"px-3 sm:px-4 py-2 rounded-lg text-xs sm:text-sm font-medium transition-colors bg-gradient-to-r from-neural-purple to-neural-pink text-white\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-1 sm:gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Settings_Sparkles_Trash2_Upload_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                        className: \"h-3 w-3 sm:h-4 sm:w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                                        lineNumber: 111,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"hidden sm:inline\",\n                                                                        children: \"AI Enhanced\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                                        lineNumber: 112,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"sm:hidden\",\n                                                                        children: \"AI\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                                        lineNumber: 113,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                                lineNumber: 110,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                            lineNumber: 106,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setCurrentMode('universal'),\n                                                            className: \"px-3 sm:px-4 py-2 rounded-lg text-xs sm:text-sm font-medium transition-colors bg-gradient-to-r from-blue-500 to-green-500 text-white\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-1 sm:gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Settings_Sparkles_Trash2_Upload_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                                        className: \"h-3 w-3 sm:h-4 sm:w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                                        lineNumber: 121,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"hidden sm:inline\",\n                                                                        children: \"Universal\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                                        lineNumber: 122,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"sm:hidden\",\n                                                                        children: \"New\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                                        lineNumber: 123,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                                lineNumber: 120,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                            lineNumber: 116,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 99,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                \"                \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-3 pt-3 border-t border-gray-700\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2 text-xs sm:text-sm text-blue-400 justify-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Settings_Sparkles_Trash2_Upload_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                                className: \"h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                                lineNumber: 128,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-center\",\n                                                                children: \"Universal design for all industries • Simplified UX • Multi-professional templates\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                                lineNumber: 129,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                        lineNumber: 127,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                    lineNumber: 126,\n                                                    columnNumber: 39\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 28\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 74,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_resume_ResumeBuilder__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            hideHeader: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 137,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 72,\n                    columnNumber: 23\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n            lineNumber: 68,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Classic mode\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-b from-black to-[#0A0A0A] text-white relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 opacity-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 bg-[url('/grid.svg')] [mask-image:radial-gradient(ellipse_at_center,white,transparent)]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 147,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 146,\n                columnNumber: 7\n            }, undefined),\n            \"      \",\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 container mx-auto px-4 pt-24 pb-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_25__.motion.div, {\n                    className: \"text-center\",\n                    initial: {\n                        opacity: 0,\n                        y: -20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.6\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center gap-3 mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_SparklesIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                            className: \"h-8 w-8 sm:h-10 sm:w-10 text-neural-pink animate-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-neural-pink opacity-20 rounded-full blur-md\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl sm:text-4xl lg:text-5xl font-bold text-white\",\n                                    children: \"Resume Builder\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 155,\n                            columnNumber: 11\n                        }, undefined),\n                        \"          \",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-300 text-lg max-w-2xl mx-auto mb-8\",\n                            children: [\n                                \"Create professional, ATS-optimized resumes with our AI-powered builder.\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"block mt-2 text-green-400 text-base font-semibold\",\n                                    children: \"✅ Updated with 2024/2025 ATS best practices for maximum compatibility\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 163,\n                            columnNumber: 27\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_25__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            className: \"flex items-center justify-center mb-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-900/60 backdrop-blur-md rounded-2xl p-3 border border-white/10\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 flex-wrap justify-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setCurrentMode('classic'),\n                                            className: \"px-4 py-2 rounded-lg text-sm font-medium transition-colors bg-neural-purple text-white\",\n                                            children: \"Classic Mode\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setCurrentMode('enhanced'),\n                                            className: \"px-4 py-2 rounded-lg text-sm font-medium transition-colors bg-gray-800 text-gray-300 hover:bg-gray-700\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Settings_Sparkles_Trash2_Upload_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                        lineNumber: 189,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    \"AI Enhanced\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setCurrentMode('universal'),\n                                            className: \"px-4 py-2 rounded-lg text-sm font-medium transition-colors bg-gradient-to-r from-blue-500 to-green-500 text-white\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_Calendar_Eye_FileText_Globe_GraduationCap_Link_Mail_MapPin_Palette_Phone_Plus_PlusCircle_Settings_Sparkles_Trash2_Upload_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                        lineNumber: 198,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    \"Universal\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                                lineNumber: 176,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 171,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_resume_ClassicMode__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                            lineNumber: 206,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                    lineNumber: 149,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n                lineNumber: 148,\n                columnNumber: 19\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\resume-builder\\\\page.jsx\",\n        lineNumber: 145,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ResumeBuilder, \"twMU8uPcqb0v6mpO3ylZSIo2Y0k=\");\n_c = ResumeBuilder;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ResumeBuilder);\nvar _c;\n$RefreshReg$(_c, \"ResumeBuilder\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/resume-builder/page.jsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/resume/ErrorBoundary.jsx":
/*!*************************************************!*\
  !*** ./src/components/resume/ErrorBoundary.jsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Home,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Home,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Home,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nclass ErrorBoundary extends (react__WEBPACK_IMPORTED_MODULE_1___default().Component) {\n    static getDerivedStateFromError(error) {\n        return {\n            hasError: true\n        };\n    }\n    componentDidCatch(error, errorInfo) {\n        this.setState({\n            error: error,\n            errorInfo: errorInfo\n        });\n        // Log error to console in development\n        if (true) {\n            console.error('Resume Builder Error:', error, errorInfo);\n        }\n    }\n    render() {\n        if (this.state.hasError) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-gradient-to-b from-black to-[#0A0A0A] flex items-center justify-center p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    className: \"bg-gray-900/60 backdrop-blur-md rounded-2xl border border-gray-700/50 p-8 max-w-md w-full text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center mb-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"h-12 w-12 text-red-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ErrorBoundary.jsx\",\n                                        lineNumber: 39,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-red-400 opacity-20 rounded-full blur-md\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ErrorBoundary.jsx\",\n                                        lineNumber: 40,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ErrorBoundary.jsx\",\n                                lineNumber: 38,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ErrorBoundary.jsx\",\n                            lineNumber: 37,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-white mb-4\",\n                            children: \"Something went wrong\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ErrorBoundary.jsx\",\n                            lineNumber: 44,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-300 mb-6\",\n                            children: \"We encountered an unexpected error while building your resume. Don't worry - your progress is saved!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ErrorBoundary.jsx\",\n                            lineNumber: 48,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>window.location.reload(),\n                                    className: \"w-full flex items-center justify-center gap-2 px-4 py-3 bg-gradient-to-r from-neural-purple to-neural-pink text-white rounded-lg hover:opacity-90 transition-opacity\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ErrorBoundary.jsx\",\n                                            lineNumber: 58,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Try Again\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ErrorBoundary.jsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>window.location.href = '/',\n                                    className: \"w-full flex items-center justify-center gap-2 px-4 py-3 bg-gray-800 text-gray-300 rounded-lg hover:bg-gray-700 transition-colors border border-gray-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ErrorBoundary.jsx\",\n                                            lineNumber: 66,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Go Home\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ErrorBoundary.jsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ErrorBoundary.jsx\",\n                            lineNumber: 53,\n                            columnNumber: 13\n                        }, this),\n                         true && this.state.error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                            className: \"mt-6 text-left\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                                    className: \"text-sm text-gray-400 cursor-pointer hover:text-gray-300\",\n                                    children: \"Error Details (Development)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ErrorBoundary.jsx\",\n                                    lineNumber: 73,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-2 p-3 bg-gray-800 rounded text-xs text-red-300 overflow-auto max-h-32\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"font-mono\",\n                                            children: this.state.error && this.state.error.toString()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ErrorBoundary.jsx\",\n                                            lineNumber: 77,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-2 font-mono text-gray-400\",\n                                            children: this.state.errorInfo.componentStack\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ErrorBoundary.jsx\",\n                                            lineNumber: 80,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ErrorBoundary.jsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ErrorBoundary.jsx\",\n                            lineNumber: 72,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ErrorBoundary.jsx\",\n                    lineNumber: 32,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ErrorBoundary.jsx\",\n                lineNumber: 31,\n                columnNumber: 9\n            }, this);\n        }\n        return this.props.children;\n    }\n    constructor(props){\n        super(props);\n        this.state = {\n            hasError: false,\n            error: null,\n            errorInfo: null\n        };\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ErrorBoundary);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/resume/ErrorBoundary.jsx\n"));

/***/ })

});
"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/resume-builder/page",{

/***/ "(app-pages-browser)/./src/services/geminiService.js":
/*!***************************************!*\
  !*** ./src/services/geminiService.js ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   analyzeResumeATS: () => (/* binding */ analyzeResumeATS),\n/* harmony export */   enhanceContent: () => (/* binding */ enhanceContent),\n/* harmony export */   getJobMatchScore: () => (/* binding */ getJobMatchScore)\n/* harmony export */ });\n/* harmony import */ var _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @google/generative-ai */ \"(app-pages-browser)/./node_modules/@google/generative-ai/dist/index.mjs\");\n\n// Initialize Gemini AI with error handling\nlet genAI = null;\nlet model = null;\ntry {\n    if (true) {\n        genAI = new _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__.GoogleGenerativeAI(\"AIzaSyBpbTY-HKaAjgel4sjfYyXdB3eA2VUwEo4\");\n        model = genAI.getGenerativeModel({\n            model: 'gemini-pro'\n        });\n    } else {}\n} catch (error) {\n    console.error('Failed to initialize Gemini AI:', error);\n}\n// Rate limiting\nconst API_CALLS = new Map();\nconst RATE_LIMIT = 10; // calls per minute\nconst RATE_WINDOW = 60000; // 1 minute\nconst checkRateLimit = function() {\n    let userId = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 'anonymous';\n    const now = Date.now();\n    const userCalls = API_CALLS.get(userId) || [];\n    // Remove calls older than rate window\n    const recentCalls = userCalls.filter((time)=>now - time < RATE_WINDOW);\n    if (recentCalls.length >= RATE_LIMIT) {\n        throw new Error('Rate limit exceeded. Please wait a moment before trying again.');\n    }\n    recentCalls.push(now);\n    API_CALLS.set(userId, recentCalls);\n};\n// ATS Scoring Service\nconst analyzeResumeATS = async (resumeData)=>{\n    try {\n        const prompt = \"\\n    Analyze this resume data for ATS (Applicant Tracking System) compatibility and provide a detailed score:\\n\\n    Resume Data:\\n    \".concat(JSON.stringify(resumeData, null, 2), '\\n\\n    Please provide a JSON response with the following structure:\\n    {\\n      \"overallScore\": number (0-100),\\n      \"scores\": {\\n        \"formatting\": number (0-100),\\n        \"keywords\": number (0-100),\\n        \"sections\": number (0-100),\\n        \"content\": number (0-100)\\n      },\\n      \"recommendations\": [\\n        {\\n          \"category\": \"string\",\\n          \"issue\": \"string\",\\n          \"suggestion\": \"string\",\\n          \"priority\": \"high|medium|low\"\\n        }\\n      ],\\n      \"strengths\": [\"string\"],\\n      \"keywordDensity\": {\\n        \"total\": number,\\n        \"relevant\": number,\\n        \"missing\": [\"string\"]\\n      }\\n    }\\n\\n    Focus on:\\n    1. ATS-friendly formatting\\n    2. Keyword optimization\\n    3. Section completeness\\n    4. Content quality and relevance\\n    5. Industry-specific requirements\\n    ');\n        const result = await model.generateContent(prompt);\n        const response = await result.response;\n        const text = response.text();\n        // Parse JSON response\n        try {\n            const jsonMatch = text.match(/\\{[\\s\\S]*\\}/);\n            if (jsonMatch) {\n                return JSON.parse(jsonMatch[0]);\n            }\n        } catch (parseError) {\n            console.error('Error parsing ATS analysis:', parseError);\n        }\n        // Fallback response if parsing fails\n        return {\n            overallScore: 75,\n            scores: {\n                formatting: 80,\n                keywords: 70,\n                sections: 75,\n                content: 75\n            },\n            recommendations: [\n                {\n                    category: \"keywords\",\n                    issue: \"Could use more industry-specific keywords\",\n                    suggestion: \"Add relevant technical skills and industry terms\",\n                    priority: \"medium\"\n                }\n            ],\n            strengths: [\n                \"Clear structure\",\n                \"Professional formatting\"\n            ],\n            keywordDensity: {\n                total: 0,\n                relevant: 0,\n                missing: [\n                    \"industry-specific terms\"\n                ]\n            }\n        };\n    } catch (error) {\n        console.error('Error analyzing resume with ATS:', error);\n        throw new Error('Failed to analyze resume. Please try again.');\n    }\n};\n// Content Enhancement Service\nconst enhanceContent = async function(content, type) {\n    let context = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {};\n    try {\n        let prompt = '';\n        switch(type){\n            case 'summary':\n                prompt = '\\n        Enhance this professional summary for maximum ATS impact:\\n        \\n        Original: \"'.concat(content, '\"\\n        \\n        Context:\\n        - Industry: ').concat(context.industry || 'General', \"\\n        - Experience Level: \").concat(context.experienceLevel || 'Mid-level', \"\\n        - Target Role: \").concat(context.targetRole || 'Professional', '\\n        \\n        Provide 3 enhanced versions that are:\\n        1. ATS-optimized with relevant keywords\\n        2. Compelling and professional\\n        3. 2-3 sentences each\\n        4. Tailored to the industry and role\\n        \\n        Return as JSON:\\n        {\\n          \"variations\": [\\n            {\\n              \"title\": \"Professional & Direct\",\\n              \"content\": \"enhanced summary text\"\\n            },\\n            {\\n              \"title\": \"Achievement-Focused\",\\n              \"content\": \"enhanced summary text\"\\n            },\\n            {\\n              \"title\": \"Skills-Emphasized\",\\n              \"content\": \"enhanced summary text\"\\n            }\\n          ]\\n        }\\n        ');\n                break;\n            case 'experience':\n                prompt = '\\n        Enhance this job description for ATS optimization:\\n        \\n        Original: \"'.concat(content, '\"\\n        \\n        Context:\\n        - Job Title: ').concat(context.title || 'Professional', \"\\n        - Company: \").concat(context.company || 'Company', \"\\n        - Industry: \").concat(context.industry || 'General', '\\n        \\n        Provide 3 enhanced versions with:\\n        1. Strong action verbs\\n        2. Quantified achievements where possible\\n        3. Industry-relevant keywords\\n        4. ATS-friendly formatting\\n        \\n        Return as JSON:\\n        {\\n          \"variations\": [\\n            {\\n              \"title\": \"Achievement-Focused\",\\n              \"content\": \"enhanced bullet points\"\\n            },\\n            {\\n              \"title\": \"Skills-Emphasized\",\\n              \"content\": \"enhanced bullet points\"\\n            },\\n            {\\n              \"title\": \"Results-Driven\",\\n              \"content\": \"enhanced bullet points\"\\n            }\\n          ]\\n        }\\n        ');\n                break;\n            case 'skills':\n                prompt = \"\\n        Suggest relevant skills for this professional profile:\\n        \\n        Current Skills: \".concat(Array.isArray(content) ? content.join(', ') : content, \"\\n        \\n        Context:\\n        - Industry: \").concat(context.industry || 'General', \"\\n        - Role: \").concat(context.role || 'Professional', \"\\n        - Experience Level: \").concat(context.experienceLevel || 'Mid-level', '\\n        \\n        Provide skill suggestions in categories:\\n        \\n        Return as JSON:\\n        {\\n          \"suggestions\": {\\n            \"technical\": [\"skill1\", \"skill2\"],\\n            \"soft\": [\"skill1\", \"skill2\"],\\n            \"industry\": [\"skill1\", \"skill2\"],\\n            \"trending\": [\"skill1\", \"skill2\"]\\n          }\\n        }\\n        ');\n                break;\n            default:\n                throw new Error('Invalid content type');\n        }\n        const result = await model.generateContent(prompt);\n        const response = await result.response;\n        const text = response.text();\n        // Parse JSON response\n        try {\n            const jsonMatch = text.match(/\\{[\\s\\S]*\\}/);\n            if (jsonMatch) {\n                return JSON.parse(jsonMatch[0]);\n            }\n        } catch (parseError) {\n            console.error('Error parsing content enhancement:', parseError);\n        }\n        // Fallback response\n        return {\n            variations: [\n                {\n                    title: \"Enhanced Version\",\n                    content: content\n                }\n            ]\n        };\n    } catch (error) {\n        console.error('Error enhancing content:', error);\n        throw new Error('Failed to enhance content. Please try again.');\n    }\n};\n// Job Matching Service\nconst getJobMatchScore = async (resumeData, jobDescription)=>{\n    try {\n        const prompt = \"\\n    Analyze how well this resume matches the job description:\\n    \\n    Resume Data:\\n    \".concat(JSON.stringify(resumeData, null, 2), '\\n    \\n    Job Description:\\n    \"').concat(jobDescription, '\"\\n    \\n    Provide a JSON response:\\n    {\\n      \"matchScore\": number (0-100),\\n      \"matchedSkills\": [\"skill1\", \"skill2\"],\\n      \"missingSkills\": [\"skill1\", \"skill2\"],\\n      \"recommendations\": [\\n        {\\n          \"action\": \"string\",\\n          \"description\": \"string\",\\n          \"impact\": \"high|medium|low\"\\n        }\\n      ],\\n      \"keywordAlignment\": number (0-100)\\n    }\\n    ');\n        const result = await model.generateContent(prompt);\n        const response = await result.response;\n        const text = response.text();\n        try {\n            const jsonMatch = text.match(/\\{[\\s\\S]*\\}/);\n            if (jsonMatch) {\n                return JSON.parse(jsonMatch[0]);\n            }\n        } catch (parseError) {\n            console.error('Error parsing job match analysis:', parseError);\n        }\n        return {\n            matchScore: 75,\n            matchedSkills: [],\n            missingSkills: [],\n            recommendations: [],\n            keywordAlignment: 75\n        };\n    } catch (error) {\n        console.error('Error analyzing job match:', error);\n        throw new Error('Failed to analyze job match. Please try again.');\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/geminiService.js\n"));

/***/ })

});
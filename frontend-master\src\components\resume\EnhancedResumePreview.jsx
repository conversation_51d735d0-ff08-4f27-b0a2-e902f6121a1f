'use client';
import { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Download, Eye, EyeOff, ZoomIn, ZoomOut, RotateCcw,
  FileText, Share2, <PERSON>er, Settings, Maximize2
} from 'lucide-react';
import { renderEnhancedTemplate } from './templates/EnhancedTemplateSystem';
import { downloadPDF, convertHTMLToPDF } from '@/utils/enhancedPdfGenerator';

const EnhancedResumePreview = ({ 
  formData, 
  selectedTemplate = 'classic_ats',
  isVisible = true,
  onToggleVisibility,
  className = ''
}) => {
  const [zoom, setZoom] = useState(0.6);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [previewMode, setPreviewMode] = useState('live'); // 'live', 'pdf'
  const previewRef = useRef(null);
  const containerRef = useRef(null);

  // Zoom controls
  const handleZoomIn = () => setZoom(prev => Math.min(prev + 0.1, 2));
  const handleZoomOut = () => setZoom(prev => Math.max(prev - 0.1, 0.3));
  const handleResetZoom = () => setZoom(0.6);

  // Download handlers
  const handleDownloadPDF = async () => {
    if (!formData || !previewRef.current) return;
    
    setIsGenerating(true);
    try {
      const filename = `${formData.personal.firstName}_${formData.personal.lastName}_Resume.pdf`;
      await convertHTMLToPDF(previewRef.current, filename);
    } catch (error) {
      console.error('Error generating PDF:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  const handlePrint = () => {
    if (!previewRef.current) return;
    
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
      <html>
        <head>
          <title>Resume - ${formData.personal.firstName} ${formData.personal.lastName}</title>
          <style>
            body { margin: 0; padding: 20px; font-family: Arial, sans-serif; }
            @media print { body { padding: 0; } }
            .resume-content { max-width: 8.5in; margin: 0 auto; }
          </style>
        </head>
        <body>
          <div class="resume-content">
            ${previewRef.current.innerHTML}
          </div>
        </body>
      </html>
    `);
    printWindow.document.close();
    printWindow.print();
  };

  // Render the resume template
  const renderResumeContent = () => {
    if (!formData) {
      return (
        <div className="flex items-center justify-center h-full text-gray-400">
          <div className="text-center">
            <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>Fill out the form to see your resume preview</p>
          </div>
        </div>
      );
    }

    return renderEnhancedTemplate(selectedTemplate, formData);
  };

  if (!isVisible) {
    return (
      <motion.button
        onClick={onToggleVisibility}
        className="fixed bottom-6 right-6 bg-neural-purple hover:bg-neural-purple/80 text-white p-3 rounded-full shadow-lg z-40 transition-colors"
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.95 }}
      >
        <Eye className="h-5 w-5" />
      </motion.button>
    );
  }

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, x: 300 }}
        animate={{ opacity: 1, x: 0 }}
        exit={{ opacity: 0, x: 300 }}
        className={`${
          isFullscreen 
            ? 'fixed inset-0 z-50 bg-gray-900' 
            : 'relative'
        } ${className}`}
      >
        {/* Preview Header */}
        <div className="bg-gray-800/90 backdrop-blur-sm border-b border-gray-700 p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <h3 className="text-lg font-semibold text-white flex items-center gap-2">
                <FileText className="h-5 w-5 text-neural-purple" />
                Resume Preview
              </h3>
              
              {/* Preview Mode Toggle */}
              <div className="flex items-center bg-gray-700/50 rounded-lg p-1">
                <button
                  onClick={() => setPreviewMode('live')}
                  className={`px-3 py-1 text-sm rounded transition-colors ${
                    previewMode === 'live' 
                      ? 'bg-neural-purple text-white' 
                      : 'text-gray-400 hover:text-white'
                  }`}
                >
                  Live
                </button>
                <button
                  onClick={() => setPreviewMode('pdf')}
                  className={`px-3 py-1 text-sm rounded transition-colors ${
                    previewMode === 'pdf' 
                      ? 'bg-neural-purple text-white' 
                      : 'text-gray-400 hover:text-white'
                  }`}
                >
                  PDF
                </button>
              </div>
            </div>

            <div className="flex items-center gap-2">
              {/* Zoom Controls */}
              <div className="flex items-center gap-1 bg-gray-700/50 rounded-lg p-1">
                <button
                  onClick={handleZoomOut}
                  className="p-1 text-gray-400 hover:text-white transition-colors"
                  title="Zoom Out"
                >
                  <ZoomOut className="h-4 w-4" />
                </button>
                <span className="px-2 text-sm text-gray-300 min-w-[3rem] text-center">
                  {Math.round(zoom * 100)}%
                </span>
                <button
                  onClick={handleZoomIn}
                  className="p-1 text-gray-400 hover:text-white transition-colors"
                  title="Zoom In"
                >
                  <ZoomIn className="h-4 w-4" />
                </button>
                <button
                  onClick={handleResetZoom}
                  className="p-1 text-gray-400 hover:text-white transition-colors"
                  title="Reset Zoom"
                >
                  <RotateCcw className="h-4 w-4" />
                </button>
              </div>

              {/* Action Buttons */}
              <button
                onClick={handlePrint}
                className="p-2 text-gray-400 hover:text-white transition-colors"
                title="Print"
              >
                <Printer className="h-4 w-4" />
              </button>

              <button
                onClick={handleDownloadPDF}
                disabled={isGenerating}
                className="flex items-center gap-2 px-3 py-2 bg-green-600 hover:bg-green-700 disabled:opacity-50 text-white rounded-lg transition-colors"
                title="Download PDF"
              >
                {isGenerating ? (
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                  >
                    <RotateCcw className="h-4 w-4" />
                  </motion.div>
                ) : (
                  <Download className="h-4 w-4" />
                )}
                <span className="hidden sm:inline">
                  {isGenerating ? 'Generating...' : 'Download'}
                </span>
              </button>

              <button
                onClick={() => setIsFullscreen(!isFullscreen)}
                className="p-2 text-gray-400 hover:text-white transition-colors"
                title={isFullscreen ? 'Exit Fullscreen' : 'Fullscreen'}
              >
                <Maximize2 className="h-4 w-4" />
              </button>

              {onToggleVisibility && (
                <button
                  onClick={onToggleVisibility}
                  className="p-2 text-gray-400 hover:text-white transition-colors"
                  title="Hide Preview"
                >
                  <EyeOff className="h-4 w-4" />
                </button>
              )}
            </div>
          </div>
        </div>

        {/* Preview Content */}
        <div 
          ref={containerRef}
          className="flex-1 overflow-auto bg-gray-100 p-4"
          style={{ 
            height: isFullscreen ? 'calc(100vh - 80px)' : '600px'
          }}
        >
          <div className="flex justify-center">
            <motion.div
              className="bg-white shadow-2xl"
              style={{
                transform: `scale(${zoom})`,
                transformOrigin: 'top center',
                width: '8.5in',
                minHeight: '11in',
                maxWidth: '8.5in'
              }}
              animate={{ scale: zoom }}
              transition={{ duration: 0.2 }}
            >
              {/* Resume Content */}
              <div 
                ref={previewRef}
                className="w-full h-full"
                style={{
                  fontFamily: "'Inter', 'Helvetica Neue', Arial, sans-serif",
                  fontSize: '11pt',
                  lineHeight: '1.4',
                  color: '#000000',
                  padding: '0.75in',
                  backgroundColor: '#ffffff'
                }}
              >
                {renderResumeContent()}
              </div>
            </motion.div>
          </div>
        </div>

        {/* Preview Footer */}
        <div className="bg-gray-800/90 backdrop-blur-sm border-t border-gray-700 p-3">
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center gap-4 text-gray-400">
              <span>Template: {selectedTemplate.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}</span>
              <span>•</span>
              <span>A4 Format</span>
              <span>•</span>
              <span>ATS Optimized</span>
            </div>
            
            <div className="flex items-center gap-2 text-gray-400">
              <span>Last updated: {new Date().toLocaleTimeString()}</span>
            </div>
          </div>
        </div>

        {/* Loading Overlay */}
        <AnimatePresence>
          {isGenerating && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="absolute inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-10"
            >
              <div className="bg-gray-900 rounded-xl p-6 text-center">
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                  className="w-8 h-8 border-4 border-neural-purple border-t-transparent rounded-full mx-auto mb-4"
                />
                <p className="text-white font-medium">Generating PDF...</p>
                <p className="text-gray-400 text-sm mt-1">This may take a few seconds</p>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>
    </AnimatePresence>
  );
};

export default EnhancedResumePreview;

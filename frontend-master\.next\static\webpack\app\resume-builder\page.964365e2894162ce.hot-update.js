"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/resume-builder/page",{

/***/ "(app-pages-browser)/./src/services/geminiService.js":
/*!***************************************!*\
  !*** ./src/services/geminiService.js ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   analyzeResumeATS: () => (/* binding */ analyzeResumeATS),\n/* harmony export */   enhanceContent: () => (/* binding */ enhanceContent),\n/* harmony export */   getJobMatchScore: () => (/* binding */ getJobMatchScore)\n/* harmony export */ });\n/* harmony import */ var _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @google/generative-ai */ \"(app-pages-browser)/./node_modules/@google/generative-ai/dist/index.mjs\");\n\n// Initialize Gemini AI with error handling\nlet genAI = null;\nlet model = null;\ntry {\n    if (true) {\n        genAI = new _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__.GoogleGenerativeAI(\"AIzaSyBpbTY-HKaAjgel4sjfYyXdB3eA2VUwEo4\");\n        model = genAI.getGenerativeModel({\n            model: 'gemini-pro'\n        });\n    } else {}\n} catch (error) {\n    console.error('Failed to initialize Gemini AI:', error);\n}\n// Rate limiting\nconst API_CALLS = new Map();\nconst RATE_LIMIT = 10; // calls per minute\nconst RATE_WINDOW = 60000; // 1 minute\nconst checkRateLimit = function() {\n    let userId = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 'anonymous';\n    const now = Date.now();\n    const userCalls = API_CALLS.get(userId) || [];\n    // Remove calls older than rate window\n    const recentCalls = userCalls.filter((time)=>now - time < RATE_WINDOW);\n    if (recentCalls.length >= RATE_LIMIT) {\n        throw new Error('Rate limit exceeded. Please wait a moment before trying again.');\n    }\n    recentCalls.push(now);\n    API_CALLS.set(userId, recentCalls);\n};\n// ATS Scoring Service\nconst analyzeResumeATS = async function(resumeData) {\n    let userId = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'anonymous';\n    try {\n        // Check if AI is available\n        if (!model) {\n            return getFallbackATSAnalysis();\n        }\n        // Rate limiting\n        checkRateLimit(userId);\n        const prompt = \"\\n    Analyze this resume data for ATS (Applicant Tracking System) compatibility and provide a detailed score:\\n\\n    Resume Data:\\n    \".concat(JSON.stringify(resumeData, null, 2), '\\n\\n    Please provide a JSON response with the following structure:\\n    {\\n      \"overallScore\": number (0-100),\\n      \"scores\": {\\n        \"formatting\": number (0-100),\\n        \"keywords\": number (0-100),\\n        \"sections\": number (0-100),\\n        \"content\": number (0-100)\\n      },\\n      \"recommendations\": [\\n        {\\n          \"category\": \"string\",\\n          \"issue\": \"string\",\\n          \"suggestion\": \"string\",\\n          \"priority\": \"high|medium|low\"\\n        }\\n      ],\\n      \"strengths\": [\"string\"],\\n      \"keywordDensity\": {\\n        \"total\": number,\\n        \"relevant\": number,\\n        \"missing\": [\"string\"]\\n      }\\n    }\\n\\n    Focus on:\\n    1. ATS-friendly formatting\\n    2. Keyword optimization\\n    3. Section completeness\\n    4. Content quality and relevance\\n    5. Industry-specific requirements\\n    ');\n        const result = await model.generateContent(prompt);\n        const response = await result.response;\n        const text = response.text();\n        // Parse JSON response\n        try {\n            const jsonMatch = text.match(/\\{[\\s\\S]*\\}/);\n            if (jsonMatch) {\n                return JSON.parse(jsonMatch[0]);\n            }\n        } catch (parseError) {\n            console.error('Error parsing ATS analysis:', parseError);\n        }\n        // Fallback response if parsing fails\n        return {\n            overallScore: 75,\n            scores: {\n                formatting: 80,\n                keywords: 70,\n                sections: 75,\n                content: 75\n            },\n            recommendations: [\n                {\n                    category: \"keywords\",\n                    issue: \"Could use more industry-specific keywords\",\n                    suggestion: \"Add relevant technical skills and industry terms\",\n                    priority: \"medium\"\n                }\n            ],\n            strengths: [\n                \"Clear structure\",\n                \"Professional formatting\"\n            ],\n            keywordDensity: {\n                total: 0,\n                relevant: 0,\n                missing: [\n                    \"industry-specific terms\"\n                ]\n            }\n        };\n    } catch (error) {\n        console.error('Error analyzing resume with ATS:', error);\n        throw new Error('Failed to analyze resume. Please try again.');\n    }\n};\n// Content Enhancement Service\nconst enhanceContent = async function(content, type) {\n    let context = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {};\n    try {\n        let prompt = '';\n        switch(type){\n            case 'summary':\n                prompt = '\\n        Enhance this professional summary for maximum ATS impact:\\n        \\n        Original: \"'.concat(content, '\"\\n        \\n        Context:\\n        - Industry: ').concat(context.industry || 'General', \"\\n        - Experience Level: \").concat(context.experienceLevel || 'Mid-level', \"\\n        - Target Role: \").concat(context.targetRole || 'Professional', '\\n        \\n        Provide 3 enhanced versions that are:\\n        1. ATS-optimized with relevant keywords\\n        2. Compelling and professional\\n        3. 2-3 sentences each\\n        4. Tailored to the industry and role\\n        \\n        Return as JSON:\\n        {\\n          \"variations\": [\\n            {\\n              \"title\": \"Professional & Direct\",\\n              \"content\": \"enhanced summary text\"\\n            },\\n            {\\n              \"title\": \"Achievement-Focused\",\\n              \"content\": \"enhanced summary text\"\\n            },\\n            {\\n              \"title\": \"Skills-Emphasized\",\\n              \"content\": \"enhanced summary text\"\\n            }\\n          ]\\n        }\\n        ');\n                break;\n            case 'experience':\n                prompt = '\\n        Enhance this job description for ATS optimization:\\n        \\n        Original: \"'.concat(content, '\"\\n        \\n        Context:\\n        - Job Title: ').concat(context.title || 'Professional', \"\\n        - Company: \").concat(context.company || 'Company', \"\\n        - Industry: \").concat(context.industry || 'General', '\\n        \\n        Provide 3 enhanced versions with:\\n        1. Strong action verbs\\n        2. Quantified achievements where possible\\n        3. Industry-relevant keywords\\n        4. ATS-friendly formatting\\n        \\n        Return as JSON:\\n        {\\n          \"variations\": [\\n            {\\n              \"title\": \"Achievement-Focused\",\\n              \"content\": \"enhanced bullet points\"\\n            },\\n            {\\n              \"title\": \"Skills-Emphasized\",\\n              \"content\": \"enhanced bullet points\"\\n            },\\n            {\\n              \"title\": \"Results-Driven\",\\n              \"content\": \"enhanced bullet points\"\\n            }\\n          ]\\n        }\\n        ');\n                break;\n            case 'skills':\n                prompt = \"\\n        Suggest relevant skills for this professional profile:\\n        \\n        Current Skills: \".concat(Array.isArray(content) ? content.join(', ') : content, \"\\n        \\n        Context:\\n        - Industry: \").concat(context.industry || 'General', \"\\n        - Role: \").concat(context.role || 'Professional', \"\\n        - Experience Level: \").concat(context.experienceLevel || 'Mid-level', '\\n        \\n        Provide skill suggestions in categories:\\n        \\n        Return as JSON:\\n        {\\n          \"suggestions\": {\\n            \"technical\": [\"skill1\", \"skill2\"],\\n            \"soft\": [\"skill1\", \"skill2\"],\\n            \"industry\": [\"skill1\", \"skill2\"],\\n            \"trending\": [\"skill1\", \"skill2\"]\\n          }\\n        }\\n        ');\n                break;\n            default:\n                throw new Error('Invalid content type');\n        }\n        const result = await model.generateContent(prompt);\n        const response = await result.response;\n        const text = response.text();\n        // Parse JSON response\n        try {\n            const jsonMatch = text.match(/\\{[\\s\\S]*\\}/);\n            if (jsonMatch) {\n                return JSON.parse(jsonMatch[0]);\n            }\n        } catch (parseError) {\n            console.error('Error parsing content enhancement:', parseError);\n        }\n        // Fallback response\n        return {\n            variations: [\n                {\n                    title: \"Enhanced Version\",\n                    content: content\n                }\n            ]\n        };\n    } catch (error) {\n        console.error('Error enhancing content:', error);\n        throw new Error('Failed to enhance content. Please try again.');\n    }\n};\n// Job Matching Service\nconst getJobMatchScore = async (resumeData, jobDescription)=>{\n    try {\n        const prompt = \"\\n    Analyze how well this resume matches the job description:\\n    \\n    Resume Data:\\n    \".concat(JSON.stringify(resumeData, null, 2), '\\n    \\n    Job Description:\\n    \"').concat(jobDescription, '\"\\n    \\n    Provide a JSON response:\\n    {\\n      \"matchScore\": number (0-100),\\n      \"matchedSkills\": [\"skill1\", \"skill2\"],\\n      \"missingSkills\": [\"skill1\", \"skill2\"],\\n      \"recommendations\": [\\n        {\\n          \"action\": \"string\",\\n          \"description\": \"string\",\\n          \"impact\": \"high|medium|low\"\\n        }\\n      ],\\n      \"keywordAlignment\": number (0-100)\\n    }\\n    ');\n        const result = await model.generateContent(prompt);\n        const response = await result.response;\n        const text = response.text();\n        try {\n            const jsonMatch = text.match(/\\{[\\s\\S]*\\}/);\n            if (jsonMatch) {\n                return JSON.parse(jsonMatch[0]);\n            }\n        } catch (parseError) {\n            console.error('Error parsing job match analysis:', parseError);\n        }\n        return {\n            matchScore: 75,\n            matchedSkills: [],\n            missingSkills: [],\n            recommendations: [],\n            keywordAlignment: 75\n        };\n    } catch (error) {\n        console.error('Error analyzing job match:', error);\n        throw new Error('Failed to analyze job match. Please try again.');\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/geminiService.js\n"));

/***/ })

});
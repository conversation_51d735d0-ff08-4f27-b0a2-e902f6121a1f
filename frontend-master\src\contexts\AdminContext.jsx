'use client';

import { createContext, useContext, useState, useEffect } from 'react';
import { auth } from '@/firebase/config';
import { signInWithEmailAndPassword, signOut, onAuthStateChanged } from 'firebase/auth';

const AdminContext = createContext();

// Admin email addresses (from environment variables or fallback)
const getAdminEmails = () => {
  if (typeof window !== 'undefined') {
    // Client-side: use public env var
    const adminEmailsEnv = process.env.NEXT_PUBLIC_ADMIN_EMAILS;
    if (adminEmailsEnv) {
      return adminEmailsEnv.split(',').map(email => email.trim());
    }
    const adminDomain = process.env.NEXT_PUBLIC_ADMIN_DOMAIN || 'blinkfind.com';
    return [
      `admin@${adminDomain}`,
      `superadmin@${adminDomain}`,
      `dev@${adminDomain}`
    ];
  } else {
    // Server-side: use private env var
    const adminEmailsEnv = process.env.ADMIN_EMAILS;
    if (adminEmailsEnv) {
      return adminEmailsEnv.split(',').map(email => email.trim());
    }
    return ['<EMAIL>', '<EMAIL>', '<EMAIL>'];
  }
};

const ADMIN_EMAILS = getAdminEmails();

export const useAdmin = () => {
  const context = useContext(AdminContext);
  if (!context) {
    throw new Error('useAdmin must be used within an AdminProvider');
  }
  return context;
};

export const AdminProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [isAdmin, setIsAdmin] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (currentUser) => {
      setUser(currentUser);
      
      if (currentUser && currentUser.email) {
        // Check if user email is in admin list
        const isUserAdmin = ADMIN_EMAILS.includes(currentUser.email.toLowerCase());
        setIsAdmin(isUserAdmin);
        
        if (!isUserAdmin) {
          setError('Access denied. Admin privileges required.');
        } else {
          setError(null);
        }
      } else {
        setIsAdmin(false);
        setError(null);
      }
      
      setLoading(false);
    });

    return unsubscribe;
  }, []);

  const adminLogin = async (email, password) => {
    try {
      setError(null);
      setLoading(true);
      
      // Check if email is in admin list before attempting login
      if (!ADMIN_EMAILS.includes(email.toLowerCase())) {
        throw new Error('Access denied. Admin privileges required.');
      }
      
      const result = await signInWithEmailAndPassword(auth, email, password);
      
      // Double-check admin status after login
      if (!ADMIN_EMAILS.includes(result.user.email.toLowerCase())) {
        await signOut(auth);
        throw new Error('Access denied. Admin privileges required.');
      }
      
      return result;
    } catch (error) {
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const adminLogout = async () => {
    try {
      await signOut(auth);
      setError(null);
    } catch (error) {
      setError(error.message);
      throw error;
    }
  };

  const value = {
    user,
    isAdmin,
    loading,
    error,
    adminLogin,
    adminLogout
  };

  return (
    <AdminContext.Provider value={value}>
      {children}
    </AdminContext.Provider>
  );
};

export default AdminContext;

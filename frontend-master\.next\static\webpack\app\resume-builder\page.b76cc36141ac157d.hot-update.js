"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/resume-builder/page",{

/***/ "(app-pages-browser)/./src/components/resume/ResumeBuilder.jsx":
/*!*************************************************!*\
  !*** ./src/components/resume/ResumeBuilder.jsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Briefcase,CheckCircle,Clock,Download,Eye,FileText,GraduationCap,Lightbulb,RotateCcw,Save,Sparkles,Target,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Briefcase,CheckCircle,Clock,Download,Eye,FileText,GraduationCap,Lightbulb,RotateCcw,Save,Sparkles,Target,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/graduation-cap.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Briefcase,CheckCircle,Clock,Download,Eye,FileText,GraduationCap,Lightbulb,RotateCcw,Save,Sparkles,Target,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Briefcase,CheckCircle,Clock,Download,Eye,FileText,GraduationCap,Lightbulb,RotateCcw,Save,Sparkles,Target,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Briefcase,CheckCircle,Clock,Download,Eye,FileText,GraduationCap,Lightbulb,RotateCcw,Save,Sparkles,Target,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Briefcase,CheckCircle,Clock,Download,Eye,FileText,GraduationCap,Lightbulb,RotateCcw,Save,Sparkles,Target,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Briefcase,CheckCircle,Clock,Download,Eye,FileText,GraduationCap,Lightbulb,RotateCcw,Save,Sparkles,Target,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Briefcase,CheckCircle,Clock,Download,Eye,FileText,GraduationCap,Lightbulb,RotateCcw,Save,Sparkles,Target,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _StepNavigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./StepNavigation */ \"(app-pages-browser)/./src/components/resume/StepNavigation.jsx\");\n/* harmony import */ var _layout_SmartNavigationBar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../layout/SmartNavigationBar */ \"(app-pages-browser)/./src/components/layout/SmartNavigationBar.jsx\");\n/* harmony import */ var _forms_ResumeFormComponents__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./forms/ResumeFormComponents */ \"(app-pages-browser)/./src/components/resume/forms/ResumeFormComponents.jsx\");\n/* harmony import */ var _forms_ExperienceForm__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./forms/ExperienceForm */ \"(app-pages-browser)/./src/components/resume/forms/ExperienceForm.jsx\");\n/* harmony import */ var _forms_SkillsProjectsForm__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./forms/SkillsProjectsForm */ \"(app-pages-browser)/./src/components/resume/forms/SkillsProjectsForm.jsx\");\n/* harmony import */ var _forms_ProjectsForm__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./forms/ProjectsForm */ \"(app-pages-browser)/./src/components/resume/forms/ProjectsForm.jsx\");\n/* harmony import */ var _forms_SkillsForm__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./forms/SkillsForm */ \"(app-pages-browser)/./src/components/resume/forms/SkillsForm.jsx\");\n/* harmony import */ var _forms_ReviewForm__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./forms/ReviewForm */ \"(app-pages-browser)/./src/components/resume/forms/ReviewForm.jsx\");\n/* harmony import */ var _EnhancedProgressIndicator__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./EnhancedProgressIndicator */ \"(app-pages-browser)/./src/components/resume/EnhancedProgressIndicator.jsx\");\n/* harmony import */ var _EnhancedNavigation__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./EnhancedNavigation */ \"(app-pages-browser)/./src/components/resume/EnhancedNavigation.jsx\");\n/* harmony import */ var _common_ProgressBar__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../common/ProgressBar */ \"(app-pages-browser)/./src/components/common/ProgressBar.jsx\");\n/* harmony import */ var _ResumePreview__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./ResumePreview */ \"(app-pages-browser)/./src/components/resume/ResumePreview.jsx\");\n/* harmony import */ var _SuccessScreen__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./SuccessScreen */ \"(app-pages-browser)/./src/components/resume/SuccessScreen.jsx\");\n/* harmony import */ var _hooks_useATSAnalysis__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/hooks/useATSAnalysis */ \"(app-pages-browser)/./src/hooks/useATSAnalysis.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst EnhancedResumeBuilder = (param)=>{\n    let { hideHeader = false } = param;\n    var _atsAnalysis_recommendations;\n    _s();\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [selectedTemplate, setSelectedTemplate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('modern');\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        personal: {\n            firstName: \"\",\n            lastName: \"\",\n            email: \"\",\n            phone: \"\",\n            location: \"\",\n            linkedin: \"\",\n            portfolio: \"\",\n            summary: \"\",\n            profileImage: \"\"\n        },\n        education: [\n            {\n                id: 1,\n                degree: \"\",\n                institution: \"\",\n                location: \"\",\n                startDate: \"\",\n                endDate: \"\",\n                gpa: \"\",\n                relevant: \"\"\n            }\n        ],\n        experience: [\n            {\n                id: 1,\n                title: \"\",\n                company: \"\",\n                location: \"\",\n                startDate: \"\",\n                endDate: \"\",\n                current: false,\n                description: \"\"\n            }\n        ],\n        skills: {\n            technical: [],\n            languages: [],\n            certifications: []\n        },\n        projects: [\n            {\n                id: 1,\n                name: \"\",\n                description: \"\",\n                technologies: \"\",\n                link: \"\"\n            }\n        ],\n        jobDescription: \"\"\n    });\n    const [showPreview, setShowPreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showProgressBar, setShowProgressBar] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [resumeGenerated, setResumeGenerated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [validationErrors, setValidationErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [showValidationErrors, setShowValidationErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [completedSteps, setCompletedSteps] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [autoSaveEnabled, setAutoSaveEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [lastSaved, setLastSaved] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [resumeData, setResumeData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [resumeUrl, setResumeUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [atsScore, setAtsScore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [suggestions, setSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [aiSuggestionsEnabled, setAiSuggestionsEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [smartValidation, setSmartValidation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showHints, setShowHints] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const steps = [\n        {\n            id: 0,\n            title: \"Personal Information\",\n            icon: _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n            description: \"Tell us about yourself\",\n            estimatedTime: 3,\n            required: true\n        },\n        {\n            id: 1,\n            title: \"Education\",\n            icon: _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n            description: \"Your academic background\",\n            estimatedTime: 5,\n            required: true\n        },\n        {\n            id: 2,\n            title: \"Experience\",\n            icon: _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n            description: \"Your work experience\",\n            estimatedTime: 8,\n            required: true\n        },\n        {\n            id: 3,\n            title: \"Projects\",\n            icon: _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n            description: \"Showcase your projects\",\n            estimatedTime: 4,\n            required: false\n        },\n        {\n            id: 4,\n            title: \"Skills\",\n            icon: _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n            description: \"Your technical & soft skills\",\n            estimatedTime: 3,\n            required: false\n        },\n        {\n            id: 5,\n            title: \"Review & Generate\",\n            icon: _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n            description: \"Finalize your resume\",\n            estimatedTime: 2,\n            required: false\n        }\n    ];\n    const atsAnalysis = (0,_hooks_useATSAnalysis__WEBPACK_IMPORTED_MODULE_16__[\"default\"])(formData);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EnhancedResumeBuilder.useEffect\": ()=>{\n            // Check for backup data from auth flow first\n            const backupData = localStorage.getItem('resumeFormDataBackup');\n            const authTimestamp = localStorage.getItem('authFlowTimestamp');\n            const savedData = localStorage.getItem('resumeFormData');\n            // If backup data exists and is recent (within 10 minutes), use it\n            if (backupData && authTimestamp) {\n                const timeDiff = Date.now() - parseInt(authTimestamp);\n                if (timeDiff < 10 * 60 * 1000) {\n                    try {\n                        const parsed = JSON.parse(backupData);\n                        setFormData(parsed);\n                        setLastSaved(new Date());\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success('Your work has been restored after sign-in!');\n                        // Clean up backup data\n                        localStorage.removeItem('resumeFormDataBackup');\n                        localStorage.removeItem('authFlowTimestamp');\n                        // Update main storage\n                        localStorage.setItem('resumeFormData', backupData);\n                        localStorage.setItem('resumeLastSaved', new Date().toISOString());\n                        return;\n                    } catch (error) {\n                        console.error('Failed to load backup data:', error);\n                    }\n                }\n            }\n            // Fallback to regular saved data\n            if (savedData) {\n                try {\n                    const parsed = JSON.parse(savedData);\n                    setFormData(parsed);\n                    setLastSaved(new Date(localStorage.getItem('resumeLastSaved') || Date.now()));\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success('Previous work restored!');\n                } catch (error) {\n                    console.error('Failed to load saved data:', error);\n                }\n            }\n            // Clean up old backup data\n            localStorage.removeItem('resumeFormDataBackup');\n            localStorage.removeItem('authFlowTimestamp');\n        }\n    }[\"EnhancedResumeBuilder.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EnhancedResumeBuilder.useEffect\": ()=>{\n            if (autoSaveEnabled && formData) {\n                // Make form data globally accessible for auth flow\n                if (true) {\n                    window.formData = formData;\n                }\n                const timeoutId = setTimeout({\n                    \"EnhancedResumeBuilder.useEffect.timeoutId\": ()=>{\n                        localStorage.setItem('resumeFormData', JSON.stringify(formData));\n                        localStorage.setItem('resumeLastSaved', new Date().toISOString());\n                        setLastSaved(new Date());\n                    }\n                }[\"EnhancedResumeBuilder.useEffect.timeoutId\"], 2000);\n                return ({\n                    \"EnhancedResumeBuilder.useEffect\": ()=>clearTimeout(timeoutId)\n                })[\"EnhancedResumeBuilder.useEffect\"];\n            }\n        }\n    }[\"EnhancedResumeBuilder.useEffect\"], [\n        formData,\n        autoSaveEnabled\n    ]);\n    const validateStep = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[validateStep]\": (stepIndex)=>{\n            const errors = {};\n            switch(stepIndex){\n                case 0:\n                    var _formData_personal_firstName, _formData_personal_lastName, _formData_personal_email;\n                    if (!((_formData_personal_firstName = formData.personal.firstName) === null || _formData_personal_firstName === void 0 ? void 0 : _formData_personal_firstName.trim())) errors.firstName = 'First name is required';\n                    if (!((_formData_personal_lastName = formData.personal.lastName) === null || _formData_personal_lastName === void 0 ? void 0 : _formData_personal_lastName.trim())) errors.lastName = 'Last name is required';\n                    if (!((_formData_personal_email = formData.personal.email) === null || _formData_personal_email === void 0 ? void 0 : _formData_personal_email.trim())) errors.email = 'Email is required';\n                    else if (!/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(formData.personal.email)) errors.email = 'Please enter a valid email address';\n                    break;\n                case 1:\n                    var _formData_education;\n                    const validEducation = (_formData_education = formData.education) === null || _formData_education === void 0 ? void 0 : _formData_education.filter({\n                        \"EnhancedResumeBuilder.useCallback[validateStep]\": (edu)=>{\n                            var _edu_degree, _edu_institution;\n                            return ((_edu_degree = edu.degree) === null || _edu_degree === void 0 ? void 0 : _edu_degree.trim()) && ((_edu_institution = edu.institution) === null || _edu_institution === void 0 ? void 0 : _edu_institution.trim());\n                        }\n                    }[\"EnhancedResumeBuilder.useCallback[validateStep]\"]);\n                    if (!validEducation || validEducation.length === 0) errors.education = 'At least one education entry with degree and institution is required';\n                    break;\n                case 2:\n                    var _formData_experience;\n                    const validExperience = (_formData_experience = formData.experience) === null || _formData_experience === void 0 ? void 0 : _formData_experience.filter({\n                        \"EnhancedResumeBuilder.useCallback[validateStep]\": (exp)=>{\n                            var _exp_title, _exp_company;\n                            return ((_exp_title = exp.title) === null || _exp_title === void 0 ? void 0 : _exp_title.trim()) && ((_exp_company = exp.company) === null || _exp_company === void 0 ? void 0 : _exp_company.trim());\n                        }\n                    }[\"EnhancedResumeBuilder.useCallback[validateStep]\"]);\n                    if (!validExperience || validExperience.length === 0) errors.experience = 'At least one work experience entry with job title and company is required';\n                    break;\n                case 3:\n                    break;\n                case 4:\n                    break;\n                default:\n                    break;\n            }\n            return errors;\n        }\n    }[\"EnhancedResumeBuilder.useCallback[validateStep]\"], [\n        formData\n    ]);\n    const canProceedToNextStep = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[canProceedToNextStep]\": (stepIndex)=>{\n            const errors = validateStep(stepIndex);\n            return Object.keys(errors).length === 0;\n        }\n    }[\"EnhancedResumeBuilder.useCallback[canProceedToNextStep]\"], [\n        validateStep\n    ]);\n    const getCompletedSteps = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[getCompletedSteps]\": ()=>{\n            const completed = [];\n            for(let i = 0; i < currentStep; i++){\n                const errors = validateStep(i);\n                if (Object.keys(errors).length === 0) {\n                    completed.push(i);\n                }\n            }\n            return completed;\n        }\n    }[\"EnhancedResumeBuilder.useCallback[getCompletedSteps]\"], [\n        currentStep,\n        validateStep\n    ]);\n    const getStepCompletionStatus = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[getStepCompletionStatus]\": ()=>{\n            const status = {};\n            steps.forEach({\n                \"EnhancedResumeBuilder.useCallback[getStepCompletionStatus]\": (step, index)=>{\n                    const errors = validateStep(index);\n                    status[index] = Object.keys(errors).length === 0;\n                }\n            }[\"EnhancedResumeBuilder.useCallback[getStepCompletionStatus]\"]);\n            return status;\n        }\n    }[\"EnhancedResumeBuilder.useCallback[getStepCompletionStatus]\"], [\n        steps,\n        validateStep\n    ]);\n    const canGenerateResume = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[canGenerateResume]\": ()=>{\n            for(let i = 0; i < steps.length - 1; i++){\n                if (!canProceedToNextStep(i)) return false;\n            }\n            return true;\n        }\n    }[\"EnhancedResumeBuilder.useCallback[canGenerateResume]\"], [\n        canProceedToNextStep,\n        steps.length\n    ]);\n    const updateFormData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[updateFormData]\": function(section, field, value) {\n            let index = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : null;\n            setFormData({\n                \"EnhancedResumeBuilder.useCallback[updateFormData]\": (prev)=>{\n                    let newData = {\n                        ...prev\n                    };\n                    if (index !== null && Array.isArray(prev[section])) {\n                        const newArray = [\n                            ...prev[section]\n                        ];\n                        newArray[index] = {\n                            ...newArray[index],\n                            [field]: value\n                        };\n                        newData[section] = newArray;\n                    } else if (typeof prev[section] === 'object' && !Array.isArray(prev[section])) {\n                        newData[section] = {\n                            ...prev[section],\n                            [field]: value\n                        };\n                    } else {\n                        newData[field] = value;\n                    }\n                    return newData;\n                }\n            }[\"EnhancedResumeBuilder.useCallback[updateFormData]\"]);\n            if (validationErrors[field]) {\n                setValidationErrors({\n                    \"EnhancedResumeBuilder.useCallback[updateFormData]\": (prev)=>{\n                        const newErrors = {\n                            ...prev\n                        };\n                        delete newErrors[field];\n                        return newErrors;\n                    }\n                }[\"EnhancedResumeBuilder.useCallback[updateFormData]\"]);\n            }\n        }\n    }[\"EnhancedResumeBuilder.useCallback[updateFormData]\"], [\n        validationErrors\n    ]);\n    const addArrayItem = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[addArrayItem]\": (section, template)=>{\n            setFormData({\n                \"EnhancedResumeBuilder.useCallback[addArrayItem]\": (prev)=>({\n                        ...prev,\n                        [section]: [\n                            ...prev[section],\n                            {\n                                ...template,\n                                id: Math.random().toString(36).substring(2, 11)\n                            }\n                        ]\n                    })\n            }[\"EnhancedResumeBuilder.useCallback[addArrayItem]\"]);\n        }\n    }[\"EnhancedResumeBuilder.useCallback[addArrayItem]\"], []);\n    const removeArrayItem = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[removeArrayItem]\": (section, id)=>{\n            setFormData({\n                \"EnhancedResumeBuilder.useCallback[removeArrayItem]\": (prev)=>({\n                        ...prev,\n                        [section]: prev[section].filter({\n                            \"EnhancedResumeBuilder.useCallback[removeArrayItem]\": (item)=>item.id !== id\n                        }[\"EnhancedResumeBuilder.useCallback[removeArrayItem]\"])\n                    })\n            }[\"EnhancedResumeBuilder.useCallback[removeArrayItem]\"]);\n        }\n    }[\"EnhancedResumeBuilder.useCallback[removeArrayItem]\"], []);\n    const nextStep = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[nextStep]\": ()=>{\n            const errors = validateStep(currentStep);\n            if (Object.keys(errors).length > 0) {\n                setValidationErrors(errors);\n                setShowValidationErrors(true);\n                const errorMessages = Object.values(errors);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(errorMessages[0]);\n                return;\n            }\n            setCompletedSteps({\n                \"EnhancedResumeBuilder.useCallback[nextStep]\": (prev)=>[\n                        ...new Set([\n                            ...prev,\n                            currentStep\n                        ])\n                    ]\n            }[\"EnhancedResumeBuilder.useCallback[nextStep]\"]);\n            setValidationErrors({});\n            setShowValidationErrors(false);\n            if (currentStep < steps.length - 1) {\n                setCurrentStep(currentStep + 1);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"\".concat(steps[currentStep].title, \" completed!\"));\n            }\n        }\n    }[\"EnhancedResumeBuilder.useCallback[nextStep]\"], [\n        currentStep,\n        validateStep,\n        steps\n    ]);\n    const prevStep = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[prevStep]\": ()=>{\n            setValidationErrors({});\n            setShowValidationErrors(false);\n            if (currentStep > 0) setCurrentStep(currentStep - 1);\n        }\n    }[\"EnhancedResumeBuilder.useCallback[prevStep]\"], [\n        currentStep\n    ]);\n    const handleStepClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[handleStepClick]\": (stepIndex)=>{\n            if (stepIndex <= currentStep || completedSteps.includes(stepIndex)) {\n                setCurrentStep(stepIndex);\n                setValidationErrors({});\n                setShowValidationErrors(false);\n            }\n        }\n    }[\"EnhancedResumeBuilder.useCallback[handleStepClick]\"], [\n        currentStep,\n        completedSteps\n    ]);\n    const generateResume = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[generateResume]\": async ()=>{\n            setValidationErrors({});\n            setShowValidationErrors(false);\n            if (!canGenerateResume()) {\n                const allErrors = {};\n                for(let i = 0; i < steps.length - 1; i++){\n                    const stepErrors = validateStep(i);\n                    Object.assign(allErrors, stepErrors);\n                }\n                setValidationErrors(allErrors);\n                setShowValidationErrors(true);\n                for(let i = 0; i < steps.length - 1; i++){\n                    const stepErrors = validateStep(i);\n                    if (Object.keys(stepErrors).length > 0) {\n                        setCurrentStep(i);\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Please complete all required fields in \".concat(steps[i].title));\n                        return;\n                    }\n                }\n                return;\n            }\n            try {\n                setIsGenerating(true);\n                setShowProgressBar(true);\n                setResumeGenerated(false);\n                const response = await fetch('/api/generate-resume', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        formData,\n                        templateId: selectedTemplate\n                    })\n                });\n                const contentType = response.headers.get('content-type');\n                if (!contentType || !contentType.includes('application/json')) throw new Error('Server returned non-JSON response');\n                const data = await response.json();\n                if (!response.ok) throw new Error(data.error || 'Failed to generate resume');\n                if (!data.resumeData || !data.downloadUrl) throw new Error('Generated resume data is incomplete');\n                setResumeUrl(data.downloadUrl);\n                setResumeData(data.resumeData);\n                setAtsScore(data.atsScore || 75);\n                setSuggestions(data.suggestions || []);\n                setResumeGenerated(true);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success('Resume generated successfully!');\n            } catch (error) {\n                console.error('Resume generation error:', error);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(error.message || 'Failed to generate resume');\n            } finally{\n                setIsGenerating(false);\n                setShowProgressBar(false);\n            }\n        }\n    }[\"EnhancedResumeBuilder.useCallback[generateResume]\"], [\n        canGenerateResume,\n        formData,\n        steps,\n        validateStep,\n        selectedTemplate\n    ]);\n    const handleSave = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[handleSave]\": ()=>{\n            localStorage.setItem('resumeFormData', JSON.stringify(formData));\n            localStorage.setItem('resumeLastSaved', new Date().toISOString());\n            setLastSaved(new Date());\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success('Progress saved!');\n        }\n    }[\"EnhancedResumeBuilder.useCallback[handleSave]\"], [\n        formData\n    ]);\n    const handleAISuggest = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[handleAISuggest]\": async (section)=>{\n            if (!aiSuggestionsEnabled) return;\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.loading('Getting AI suggestions...');\n            try {\n                await new Promise({\n                    \"EnhancedResumeBuilder.useCallback[handleAISuggest]\": (resolve)=>setTimeout(resolve, 1500)\n                }[\"EnhancedResumeBuilder.useCallback[handleAISuggest]\"]);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.dismiss();\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success('AI suggestions applied!');\n            } catch (error) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.dismiss();\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error('Failed to get AI suggestions');\n            }\n        }\n    }[\"EnhancedResumeBuilder.useCallback[handleAISuggest]\"], [\n        aiSuggestionsEnabled\n    ]);\n    // Set up event listener for resume generation from ReviewForm\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EnhancedResumeBuilder.useEffect\": ()=>{\n            const handleGenerateResume = {\n                \"EnhancedResumeBuilder.useEffect.handleGenerateResume\": ()=>{\n                    // Check if we can generate resume and call the function\n                    if (canGenerateResume()) {\n                        generateResume();\n                    } else {\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error('Please complete all required sections before generating your resume.');\n                    }\n                }\n            }[\"EnhancedResumeBuilder.useEffect.handleGenerateResume\"];\n            if (true) {\n                window.addEventListener('generateResume', handleGenerateResume);\n                return ({\n                    \"EnhancedResumeBuilder.useEffect\": ()=>window.removeEventListener('generateResume', handleGenerateResume)\n                })[\"EnhancedResumeBuilder.useEffect\"];\n            }\n        }\n    }[\"EnhancedResumeBuilder.useEffect\"], [\n        canGenerateResume,\n        generateResume\n    ]);\n    const getStepValidation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[getStepValidation]\": ()=>{\n            const validation = {};\n            steps.forEach({\n                \"EnhancedResumeBuilder.useCallback[getStepValidation]\": (step, index)=>{\n                    const errors = validateStep(index);\n                    validation[index] = Object.keys(errors).length === 0;\n                }\n            }[\"EnhancedResumeBuilder.useCallback[getStepValidation]\"]);\n            return validation;\n        }\n    }[\"EnhancedResumeBuilder.useCallback[getStepValidation]\"], [\n        steps,\n        validateStep\n    ]);\n    const renderStepContent = ()=>{\n        const commonProps = {\n            formData,\n            updateFormData,\n            addArrayItem,\n            removeArrayItem,\n            atsAnalysis,\n            validationErrors,\n            showValidationErrors,\n            onSave: handleSave,\n            onAISuggest: handleAISuggest\n        };\n        switch(currentStep){\n            case 0:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_ResumeFormComponents__WEBPACK_IMPORTED_MODULE_5__.PersonalInfoForm, {\n                    ...commonProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                    lineNumber: 382,\n                    columnNumber: 16\n                }, undefined);\n            case 1:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_ResumeFormComponents__WEBPACK_IMPORTED_MODULE_5__.EducationForm, {\n                    ...commonProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                    lineNumber: 384,\n                    columnNumber: 16\n                }, undefined);\n            case 2:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_ExperienceForm__WEBPACK_IMPORTED_MODULE_6__.EnhancedExperienceForm, {\n                    ...commonProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                    lineNumber: 386,\n                    columnNumber: 16\n                }, undefined);\n            case 3:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_ProjectsForm__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    ...commonProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                    lineNumber: 388,\n                    columnNumber: 16\n                }, undefined);\n            case 4:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_SkillsForm__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    ...commonProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                    lineNumber: 390,\n                    columnNumber: 16\n                }, undefined);\n            case 5:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_ReviewForm__WEBPACK_IMPORTED_MODULE_10__.EnhancedReviewForm, {\n                    ...commonProps,\n                    selectedTemplate: selectedTemplate,\n                    onTemplateSelect: setSelectedTemplate\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                    lineNumber: 392,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return null;\n        }\n    };\n    if (resumeGenerated && resumeData && resumeUrl) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SuccessScreen__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n            formData: formData,\n            resumeData: resumeData,\n            onStartOver: ()=>{\n                setResumeGenerated(false);\n                setResumeData(null);\n                setResumeUrl(\"\");\n                setCurrentStep(0);\n            },\n            onEditResume: ()=>{\n                setResumeGenerated(false);\n                setCurrentStep(4); // Go back to review step\n            }\n        }, void 0, false, {\n            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n            lineNumber: 400,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-b from-black to-[#0A0A0A] text-white relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 opacity-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 bg-[url('/grid.svg')] [mask-image:radial-gradient(ellipse_at_center,white,transparent)]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                    lineNumber: 420,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                lineNumber: 419,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_common_ProgressBar__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                isVisible: showProgressBar,\n                onComplete: ()=>setShowProgressBar(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                lineNumber: 422,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 pb-32\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 py-8\",\n                    children: [\n                        !hideHeader && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_22__.motion.div, {\n                            className: \"text-center mb-8\",\n                            initial: {\n                                opacity: 0,\n                                y: -20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center gap-3 mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                    className: \"h-10 w-10 text-neural-pink animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                    lineNumber: 429,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-neural-pink opacity-20 rounded-full blur-md\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                    lineNumber: 430,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                            lineNumber: 428,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-4xl lg:text-5xl font-bold text-white\",\n                                            children: \"Enhanced Resume Builder\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                            lineNumber: 432,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                    lineNumber: 427,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 text-lg max-w-2xl mx-auto\",\n                                    children: \"Create professional, ATS-friendly resumes with our enhanced AI-powered builder\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                    lineNumber: 434,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                            lineNumber: 426,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 xl:grid-cols-1 gap-8 max-w-4xl xl:max-w-5xl mx-auto xl:mr-96\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"sticky top-4 z-30 bg-gray-900/95 backdrop-blur-sm rounded-2xl border border-gray-700/50 mb-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EnhancedProgressIndicator__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        steps: steps,\n                                        currentStep: currentStep,\n                                        completedSteps: completedSteps,\n                                        onStepClick: handleStepClick,\n                                        variant: \"minimal\",\n                                        showLabels: true,\n                                        showProgress: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                        lineNumber: 440,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                    lineNumber: 439,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_24__.AnimatePresence, {\n                                        mode: \"wait\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_22__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                x: 20\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                x: 0\n                                            },\n                                            exit: {\n                                                opacity: 0,\n                                                x: -20\n                                            },\n                                            transition: {\n                                                duration: 0.3\n                                            },\n                                            children: renderStepContent()\n                                        }, currentStep, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                            lineNumber: 452,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                        lineNumber: 451,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                    lineNumber: 450,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden xl:block\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"xl:sticky xl:top-4 xl:w-80 xl:h-[calc(100vh-2rem)] xl:overflow-hidden xl:ml-8\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_22__.motion.div, {\n                                                className: \"bg-gray-900/60 backdrop-blur-md rounded-2xl border border-white/10 h-full flex flex-col\",\n                                                initial: {\n                                                    opacity: 0,\n                                                    x: 20\n                                                },\n                                                animate: {\n                                                    opacity: 1,\n                                                    x: 0\n                                                },\n                                                transition: {\n                                                    delay: 0.2\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between p-4 border-b border-white/10\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-semibold flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-neural-blue\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                                        lineNumber: 469,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    \"Live Preview\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                                lineNumber: 468,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>setShowPreview(!showPreview),\n                                                                    className: \"px-3 py-1.5 rounded-lg text-sm font-medium transition-colors \".concat(showPreview ? 'bg-neural-purple text-white' : 'bg-gray-800 text-gray-300 hover:bg-gray-700'),\n                                                                    children: showPreview ? 'Hide' : 'Show'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                                    lineNumber: 473,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                                lineNumber: 472,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                        lineNumber: 467,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 overflow-hidden\",\n                                                        children: showPreview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-full overflow-y-auto p-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"transform scale-[0.65] origin-top-left w-[154%] h-[154%]\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-white rounded-lg shadow-lg\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ResumePreview__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                        formData: formData,\n                                                                        selectedTemplate: selectedTemplate\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                                        lineNumber: 490,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                                    lineNumber: 489,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                                lineNumber: 488,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                            lineNumber: 487,\n                                                            columnNumber: 23\n                                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-center h-full text-gray-400\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                        className: \"h-16 w-16 mx-auto mb-4 opacity-30\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                                        lineNumber: 497,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm\",\n                                                                        children: 'Click \"Show\" to preview'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                                        lineNumber: 498,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-500 mt-1\",\n                                                                        children: \"Live updates as you type\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                                        lineNumber: 499,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                                lineNumber: 496,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                            lineNumber: 495,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                        lineNumber: 485,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    showPreview && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-3 border-t border-white/10 bg-gray-800/30\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between text-xs text-gray-400\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        \"Template: \",\n                                                                        selectedTemplate\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                                    lineNumber: 509,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Auto-updating\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                                    lineNumber: 510,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                            lineNumber: 508,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                        lineNumber: 507,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                lineNumber: 460,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                            lineNumber: 459,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"xl:hidden\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_22__.motion.div, {\n                                                className: \"bg-gray-900/60 backdrop-blur-md rounded-2xl p-6 border border-white/10\",\n                                                initial: {\n                                                    opacity: 0,\n                                                    x: 20\n                                                },\n                                                animate: {\n                                                    opacity: 1,\n                                                    x: 0\n                                                },\n                                                transition: {\n                                                    delay: 0.2\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between mb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-semibold flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-neural-blue\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                                        lineNumber: 522,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    \"Resume Preview\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                                lineNumber: 521,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>setShowPreview(!showPreview),\n                                                                className: \"px-3 py-1.5 rounded-lg text-sm font-medium transition-colors \".concat(showPreview ? 'bg-neural-purple text-white' : 'bg-gray-800 text-gray-300 hover:bg-gray-700'),\n                                                                children: showPreview ? 'Hide' : 'Show'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                                lineNumber: 525,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                        lineNumber: 520,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    showPreview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"max-h-[600px] overflow-y-auto border border-white/10 rounded-lg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ResumePreview__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            formData: formData,\n                                                            selectedTemplate: selectedTemplate\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                            lineNumber: 536,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                        lineNumber: 535,\n                                                        columnNumber: 21\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center py-12 text-gray-400\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                className: \"h-12 w-12 mx-auto mb-3 opacity-30\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                                lineNumber: 540,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm\",\n                                                                children: 'Click \"Show\" to preview your resume'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                                lineNumber: 541,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                        lineNumber: 539,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                lineNumber: 519,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                            lineNumber: 518,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_22__.motion.div, {\n                                            className: \"bg-gray-900/60 backdrop-blur-md rounded-2xl p-6 border border-white/10\",\n                                            initial: {\n                                                opacity: 0,\n                                                x: 20\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                x: 0\n                                            },\n                                            transition: {\n                                                delay: 0.3\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                            className: \"h-5 w-5 text-neural-purple\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                            lineNumber: 548,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-semibold\",\n                                                            children: \"ATS Score\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                            lineNumber: 549,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                    lineNumber: 547,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-3xl font-bold \".concat((atsAnalysis.overallScore || 0) >= 80 ? 'text-green-400' : (atsAnalysis.overallScore || 0) >= 60 ? 'text-yellow-400' : 'text-red-400'),\n                                                            children: [\n                                                                atsAnalysis.overallScore || 0,\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                            lineNumber: 552,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-400\",\n                                                            children: (atsAnalysis.overallScore || 0) >= 80 ? 'Excellent' : (atsAnalysis.overallScore || 0) >= 60 ? 'Good' : 'Needs Work'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                            lineNumber: 558,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                    lineNumber: 551,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                ((_atsAnalysis_recommendations = atsAnalysis.recommendations) === null || _atsAnalysis_recommendations === void 0 ? void 0 : _atsAnalysis_recommendations.length) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-500 mb-2\",\n                                                            children: \"Top Suggestions:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                            lineNumber: 565,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        atsAnalysis.recommendations.slice(0, 2).map((rec, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-start gap-2 text-sm\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-1.5 h-1.5 bg-yellow-400 rounded-full mt-2 flex-shrink-0\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                                        lineNumber: 568,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-300\",\n                                                                        children: rec.message\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                                        lineNumber: 569,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                                lineNumber: 567,\n                                                                columnNumber: 23\n                                                            }, undefined))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                    lineNumber: 564,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                            lineNumber: 546,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                    lineNumber: 457,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                            lineNumber: 437,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                    lineNumber: 424,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                lineNumber: 423,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_layout_SmartNavigationBar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                currentStep: currentStep,\n                totalSteps: steps.length,\n                onPrevious: prevStep,\n                onNext: nextStep,\n                onGenerate: generateResume,\n                onSave: handleSave,\n                onPreview: ()=>setShowPreview(!showPreview),\n                isGenerating: isGenerating,\n                canProceed: currentStep === steps.length - 1 ? canGenerateResume() : canProceedToNextStep(currentStep),\n                showPreview: showPreview,\n                steps: steps,\n                formData: formData,\n                atsScore: atsAnalysis.overallScore,\n                autoSaveEnabled: autoSaveEnabled\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                lineNumber: 580,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n        lineNumber: 418,\n        columnNumber: 5\n    }, undefined);\n};\n_s(EnhancedResumeBuilder, \"cH7kc5DhB7P/1+m/Kivxo+IObKI=\", false, function() {\n    return [\n        _hooks_useATSAnalysis__WEBPACK_IMPORTED_MODULE_16__[\"default\"]\n    ];\n});\n_c = EnhancedResumeBuilder;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (EnhancedResumeBuilder);\nvar _c;\n$RefreshReg$(_c, \"EnhancedResumeBuilder\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/resume/ResumeBuilder.jsx\n"));

/***/ })

});
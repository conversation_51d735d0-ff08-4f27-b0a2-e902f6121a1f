"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/resume-builder/page",{

/***/ "(app-pages-browser)/./src/services/geminiService.js":
/*!***************************************!*\
  !*** ./src/services/geminiService.js ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   analyzeResumeATS: () => (/* binding */ analyzeResumeATS),\n/* harmony export */   enhanceContent: () => (/* binding */ enhanceContent),\n/* harmony export */   getFallbackATSAnalysis: () => (/* binding */ getFallbackATSAnalysis),\n/* harmony export */   getFallbackContentEnhancement: () => (/* binding */ getFallbackContentEnhancement),\n/* harmony export */   getJobMatchScore: () => (/* binding */ getJobMatchScore)\n/* harmony export */ });\n/* harmony import */ var _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @google/generative-ai */ \"(app-pages-browser)/./node_modules/@google/generative-ai/dist/index.mjs\");\n\n// Initialize Gemini AI with error handling\nlet genAI = null;\nlet model = null;\ntry {\n    if (true) {\n        genAI = new _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__.GoogleGenerativeAI(\"AIzaSyBpbTY-HKaAjgel4sjfYyXdB3eA2VUwEo4\");\n        model = genAI.getGenerativeModel({\n            model: 'gemini-pro'\n        });\n    } else {}\n} catch (error) {\n    console.error('Failed to initialize Gemini AI:', error);\n}\n// Rate limiting\nconst API_CALLS = new Map();\nconst RATE_LIMIT = 10; // calls per minute\nconst RATE_WINDOW = 60000; // 1 minute\nconst checkRateLimit = function() {\n    let userId = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 'anonymous';\n    const now = Date.now();\n    const userCalls = API_CALLS.get(userId) || [];\n    // Remove calls older than rate window\n    const recentCalls = userCalls.filter((time)=>now - time < RATE_WINDOW);\n    if (recentCalls.length >= RATE_LIMIT) {\n        throw new Error('Rate limit exceeded. Please wait a moment before trying again.');\n    }\n    recentCalls.push(now);\n    API_CALLS.set(userId, recentCalls);\n};\n// ATS Scoring Service\nconst analyzeResumeATS = async function(resumeData) {\n    let userId = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'anonymous';\n    try {\n        // Check if AI is available\n        if (!model) {\n            return getFallbackATSAnalysis();\n        }\n        // Rate limiting\n        checkRateLimit(userId);\n        const prompt = \"\\n    Analyze this resume data for ATS (Applicant Tracking System) compatibility and provide a detailed score:\\n\\n    Resume Data:\\n    \".concat(JSON.stringify(resumeData, null, 2), '\\n\\n    Please provide a JSON response with the following structure:\\n    {\\n      \"overallScore\": number (0-100),\\n      \"scores\": {\\n        \"formatting\": number (0-100),\\n        \"keywords\": number (0-100),\\n        \"sections\": number (0-100),\\n        \"content\": number (0-100)\\n      },\\n      \"recommendations\": [\\n        {\\n          \"category\": \"string\",\\n          \"issue\": \"string\",\\n          \"suggestion\": \"string\",\\n          \"priority\": \"high|medium|low\"\\n        }\\n      ],\\n      \"strengths\": [\"string\"],\\n      \"keywordDensity\": {\\n        \"total\": number,\\n        \"relevant\": number,\\n        \"missing\": [\"string\"]\\n      }\\n    }\\n\\n    Focus on:\\n    1. ATS-friendly formatting\\n    2. Keyword optimization\\n    3. Section completeness\\n    4. Content quality and relevance\\n    5. Industry-specific requirements\\n    ');\n        const result = await model.generateContent(prompt);\n        const response = await result.response;\n        const text = response.text();\n        // Parse JSON response\n        try {\n            const jsonMatch = text.match(/\\{[\\s\\S]*\\}/);\n            if (jsonMatch) {\n                return JSON.parse(jsonMatch[0]);\n            }\n        } catch (parseError) {\n            console.error('Error parsing ATS analysis:', parseError);\n        }\n        // Fallback response if parsing fails\n        return {\n            overallScore: 75,\n            scores: {\n                formatting: 80,\n                keywords: 70,\n                sections: 75,\n                content: 75\n            },\n            recommendations: [\n                {\n                    category: \"keywords\",\n                    issue: \"Could use more industry-specific keywords\",\n                    suggestion: \"Add relevant technical skills and industry terms\",\n                    priority: \"medium\"\n                }\n            ],\n            strengths: [\n                \"Clear structure\",\n                \"Professional formatting\"\n            ],\n            keywordDensity: {\n                total: 0,\n                relevant: 0,\n                missing: [\n                    \"industry-specific terms\"\n                ]\n            }\n        };\n    } catch (error) {\n        console.error('Error analyzing resume with ATS:', error);\n        throw new Error('Failed to analyze resume. Please try again.');\n    }\n};\n// Content Enhancement Service\nconst enhanceContent = async function(content, type) {\n    let context = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {};\n    try {\n        let prompt = '';\n        switch(type){\n            case 'summary':\n                prompt = '\\n        Enhance this professional summary for maximum ATS impact:\\n        \\n        Original: \"'.concat(content, '\"\\n        \\n        Context:\\n        - Industry: ').concat(context.industry || 'General', \"\\n        - Experience Level: \").concat(context.experienceLevel || 'Mid-level', \"\\n        - Target Role: \").concat(context.targetRole || 'Professional', '\\n        \\n        Provide 3 enhanced versions that are:\\n        1. ATS-optimized with relevant keywords\\n        2. Compelling and professional\\n        3. 2-3 sentences each\\n        4. Tailored to the industry and role\\n        \\n        Return as JSON:\\n        {\\n          \"variations\": [\\n            {\\n              \"title\": \"Professional & Direct\",\\n              \"content\": \"enhanced summary text\"\\n            },\\n            {\\n              \"title\": \"Achievement-Focused\",\\n              \"content\": \"enhanced summary text\"\\n            },\\n            {\\n              \"title\": \"Skills-Emphasized\",\\n              \"content\": \"enhanced summary text\"\\n            }\\n          ]\\n        }\\n        ');\n                break;\n            case 'experience':\n                prompt = '\\n        Enhance this job description for ATS optimization:\\n        \\n        Original: \"'.concat(content, '\"\\n        \\n        Context:\\n        - Job Title: ').concat(context.title || 'Professional', \"\\n        - Company: \").concat(context.company || 'Company', \"\\n        - Industry: \").concat(context.industry || 'General', '\\n        \\n        Provide 3 enhanced versions with:\\n        1. Strong action verbs\\n        2. Quantified achievements where possible\\n        3. Industry-relevant keywords\\n        4. ATS-friendly formatting\\n        \\n        Return as JSON:\\n        {\\n          \"variations\": [\\n            {\\n              \"title\": \"Achievement-Focused\",\\n              \"content\": \"enhanced bullet points\"\\n            },\\n            {\\n              \"title\": \"Skills-Emphasized\",\\n              \"content\": \"enhanced bullet points\"\\n            },\\n            {\\n              \"title\": \"Results-Driven\",\\n              \"content\": \"enhanced bullet points\"\\n            }\\n          ]\\n        }\\n        ');\n                break;\n            case 'skills':\n                prompt = \"\\n        Suggest relevant skills for this professional profile:\\n        \\n        Current Skills: \".concat(Array.isArray(content) ? content.join(', ') : content, \"\\n        \\n        Context:\\n        - Industry: \").concat(context.industry || 'General', \"\\n        - Role: \").concat(context.role || 'Professional', \"\\n        - Experience Level: \").concat(context.experienceLevel || 'Mid-level', '\\n        \\n        Provide skill suggestions in categories:\\n        \\n        Return as JSON:\\n        {\\n          \"suggestions\": {\\n            \"technical\": [\"skill1\", \"skill2\"],\\n            \"soft\": [\"skill1\", \"skill2\"],\\n            \"industry\": [\"skill1\", \"skill2\"],\\n            \"trending\": [\"skill1\", \"skill2\"]\\n          }\\n        }\\n        ');\n                break;\n            default:\n                throw new Error('Invalid content type');\n        }\n        const result = await model.generateContent(prompt);\n        const response = await result.response;\n        const text = response.text();\n        // Parse JSON response\n        try {\n            const jsonMatch = text.match(/\\{[\\s\\S]*\\}/);\n            if (jsonMatch) {\n                return JSON.parse(jsonMatch[0]);\n            }\n        } catch (parseError) {\n            console.error('Error parsing content enhancement:', parseError);\n        }\n        // Fallback response\n        return {\n            variations: [\n                {\n                    title: \"Enhanced Version\",\n                    content: content\n                }\n            ]\n        };\n    } catch (error) {\n        console.error('Error enhancing content:', error);\n        throw new Error('Failed to enhance content. Please try again.');\n    }\n};\n// Job Matching Service\nconst getJobMatchScore = async (resumeData, jobDescription)=>{\n    try {\n        const prompt = \"\\n    Analyze how well this resume matches the job description:\\n    \\n    Resume Data:\\n    \".concat(JSON.stringify(resumeData, null, 2), '\\n    \\n    Job Description:\\n    \"').concat(jobDescription, '\"\\n    \\n    Provide a JSON response:\\n    {\\n      \"matchScore\": number (0-100),\\n      \"matchedSkills\": [\"skill1\", \"skill2\"],\\n      \"missingSkills\": [\"skill1\", \"skill2\"],\\n      \"recommendations\": [\\n        {\\n          \"action\": \"string\",\\n          \"description\": \"string\",\\n          \"impact\": \"high|medium|low\"\\n        }\\n      ],\\n      \"keywordAlignment\": number (0-100)\\n    }\\n    ');\n        const result = await model.generateContent(prompt);\n        const response = await result.response;\n        const text = response.text();\n        try {\n            const jsonMatch = text.match(/\\{[\\s\\S]*\\}/);\n            if (jsonMatch) {\n                return JSON.parse(jsonMatch[0]);\n            }\n        } catch (parseError) {\n            console.error('Error parsing job match analysis:', parseError);\n        }\n        return {\n            matchScore: 75,\n            matchedSkills: [],\n            missingSkills: [],\n            recommendations: [],\n            keywordAlignment: 75\n        };\n    } catch (error) {\n        console.error('Error analyzing job match:', error);\n        throw new Error('Failed to analyze job match. Please try again.');\n    }\n};\n// Fallback Functions for when AI is unavailable\nconst getFallbackATSAnalysis = ()=>({\n        overallScore: 75,\n        scores: {\n            formatting: 80,\n            keywords: 70,\n            sections: 75,\n            content: 75\n        },\n        recommendations: [\n            {\n                category: \"keywords\",\n                issue: \"AI analysis temporarily unavailable\",\n                suggestion: \"Continue building your resume. AI features will be restored shortly.\",\n                priority: \"low\"\n            }\n        ],\n        strengths: [\n            \"Professional structure\",\n            \"Clear formatting\"\n        ],\n        keywordDensity: {\n            total: 0,\n            relevant: 0,\n            missing: [\n                \"AI analysis pending\"\n            ]\n        }\n    });\nconst getFallbackContentEnhancement = (content, type)=>({\n        variations: [\n            {\n                title: \"Original Content\",\n                content: content || \"Please add your content here.\"\n            },\n            {\n                title: \"Enhanced Version\",\n                content: content ? \"\".concat(content, \" (AI enhancement will be available shortly)\") : \"AI enhancement temporarily unavailable.\"\n            }\n        ]\n    });\n// Export fallback functions for testing\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9zZXJ2aWNlcy9nZW1pbmlTZXJ2aWNlLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUEyRDtBQUUzRCwyQ0FBMkM7QUFDM0MsSUFBSUMsUUFBUTtBQUNaLElBQUlDLFFBQVE7QUFFWixJQUFJO0lBQ0YsSUFBSUMsSUFBc0MsRUFBRTtRQUMxQ0YsUUFBUSxJQUFJRCxxRUFBa0JBLENBQUNHLHlDQUFzQztRQUNyRUQsUUFBUUQsTUFBTUssa0JBQWtCLENBQUM7WUFBRUosT0FBTztRQUFhO0lBQ3pELE9BQU8sRUFFTjtBQUNILEVBQUUsT0FBT08sT0FBTztJQUNkRixRQUFRRSxLQUFLLENBQUMsbUNBQW1DQTtBQUNuRDtBQUVBLGdCQUFnQjtBQUNoQixNQUFNQyxZQUFZLElBQUlDO0FBQ3RCLE1BQU1DLGFBQWEsSUFBSSxtQkFBbUI7QUFDMUMsTUFBTUMsY0FBYyxPQUFPLFdBQVc7QUFFdEMsTUFBTUMsaUJBQWlCO1FBQUNDLDBFQUFTO0lBQy9CLE1BQU1DLE1BQU1DLEtBQUtELEdBQUc7SUFDcEIsTUFBTUUsWUFBWVIsVUFBVVMsR0FBRyxDQUFDSixXQUFXLEVBQUU7SUFFN0Msc0NBQXNDO0lBQ3RDLE1BQU1LLGNBQWNGLFVBQVVHLE1BQU0sQ0FBQ0MsQ0FBQUEsT0FBUU4sTUFBTU0sT0FBT1Q7SUFFMUQsSUFBSU8sWUFBWUcsTUFBTSxJQUFJWCxZQUFZO1FBQ3BDLE1BQU0sSUFBSVksTUFBTTtJQUNsQjtJQUVBSixZQUFZSyxJQUFJLENBQUNUO0lBQ2pCTixVQUFVZ0IsR0FBRyxDQUFDWCxRQUFRSztBQUN4QjtBQUVBLHNCQUFzQjtBQUNmLE1BQU1PLG1CQUFtQixlQUFPQztRQUFZYiwwRUFBUztJQUMxRCxJQUFJO1FBQ0YsMkJBQTJCO1FBQzNCLElBQUksQ0FBQ2IsT0FBTztZQUNWLE9BQU8yQjtRQUNUO1FBRUEsZ0JBQWdCO1FBQ2hCZixlQUFlQztRQUVmLE1BQU1lLFNBQVMsMklBSXVCLE9BQXBDQyxLQUFLQyxTQUFTLENBQUNKLFlBQVksTUFBTSxJQUFHO1FBbUN0QyxNQUFNSyxTQUFTLE1BQU0vQixNQUFNZ0MsZUFBZSxDQUFDSjtRQUMzQyxNQUFNSyxXQUFXLE1BQU1GLE9BQU9FLFFBQVE7UUFDdEMsTUFBTUMsT0FBT0QsU0FBU0MsSUFBSTtRQUUxQixzQkFBc0I7UUFDdEIsSUFBSTtZQUNGLE1BQU1DLFlBQVlELEtBQUtFLEtBQUssQ0FBQztZQUM3QixJQUFJRCxXQUFXO2dCQUNiLE9BQU9OLEtBQUtRLEtBQUssQ0FBQ0YsU0FBUyxDQUFDLEVBQUU7WUFDaEM7UUFDRixFQUFFLE9BQU9HLFlBQVk7WUFDbkJqQyxRQUFRRSxLQUFLLENBQUMsK0JBQStCK0I7UUFDL0M7UUFFQSxxQ0FBcUM7UUFDckMsT0FBTztZQUNMQyxjQUFjO1lBQ2RDLFFBQVE7Z0JBQ05DLFlBQVk7Z0JBQ1pDLFVBQVU7Z0JBQ1ZDLFVBQVU7Z0JBQ1ZDLFNBQVM7WUFDWDtZQUNBQyxpQkFBaUI7Z0JBQ2Y7b0JBQ0VDLFVBQVU7b0JBQ1ZDLE9BQU87b0JBQ1BDLFlBQVk7b0JBQ1pDLFVBQVU7Z0JBQ1o7YUFDRDtZQUNEQyxXQUFXO2dCQUFDO2dCQUFtQjthQUEwQjtZQUN6REMsZ0JBQWdCO2dCQUNkQyxPQUFPO2dCQUNQQyxVQUFVO2dCQUNWQyxTQUFTO29CQUFDO2lCQUEwQjtZQUN0QztRQUNGO0lBQ0YsRUFBRSxPQUFPL0MsT0FBTztRQUNkRixRQUFRRSxLQUFLLENBQUMsb0NBQW9DQTtRQUNsRCxNQUFNLElBQUllLE1BQU07SUFDbEI7QUFDRixFQUFFO0FBRUYsOEJBQThCO0FBQ3ZCLE1BQU1pQyxpQkFBaUIsZUFBT1gsU0FBU1k7UUFBTUMsMkVBQVUsQ0FBQztJQUM3RCxJQUFJO1FBQ0YsSUFBSTdCLFNBQVM7UUFFYixPQUFRNEI7WUFDTixLQUFLO2dCQUNINUIsU0FBUyxxR0FNSzZCLE9BSERiLFNBQVEsdURBSUNhLE9BRFJBLFFBQVFDLFFBQVEsSUFBSSxXQUFVLGtDQUUzQkQsT0FES0EsUUFBUUUsZUFBZSxJQUFJLGFBQVksNkJBQ1AsT0FBckNGLFFBQVFHLFVBQVUsSUFBSSxnQkFBZTtnQkEwQnREO1lBRUYsS0FBSztnQkFDSGhDLFNBQVMsOEZBTU02QixPQUhGYixTQUFRLHdEQUlSYSxPQURFQSxRQUFRSSxLQUFLLElBQUksZ0JBQWUseUJBRWpDSixPQUREQSxRQUFRSyxPQUFPLElBQUksV0FBVSwwQkFDRSxPQUE5QkwsUUFBUUMsUUFBUSxJQUFJLFdBQVU7Z0JBMEI1QztZQUVGLEtBQUs7Z0JBQ0g5QixTQUFTLHVHQU1LNkIsT0FISU0sTUFBTUMsT0FBTyxDQUFDcEIsV0FBV0EsUUFBUXFCLElBQUksQ0FBQyxRQUFRckIsU0FBUSxzREFJOURhLE9BRElBLFFBQVFDLFFBQVEsSUFBSSxXQUFVLHNCQUV0QkQsT0FEWkEsUUFBUVMsSUFBSSxJQUFJLGdCQUFlLGtDQUNvQixPQUF2Q1QsUUFBUUUsZUFBZSxJQUFJLGFBQVk7Z0JBYzdEO1lBRUY7Z0JBQ0UsTUFBTSxJQUFJckMsTUFBTTtRQUNwQjtRQUVBLE1BQU1TLFNBQVMsTUFBTS9CLE1BQU1nQyxlQUFlLENBQUNKO1FBQzNDLE1BQU1LLFdBQVcsTUFBTUYsT0FBT0UsUUFBUTtRQUN0QyxNQUFNQyxPQUFPRCxTQUFTQyxJQUFJO1FBRTFCLHNCQUFzQjtRQUN0QixJQUFJO1lBQ0YsTUFBTUMsWUFBWUQsS0FBS0UsS0FBSyxDQUFDO1lBQzdCLElBQUlELFdBQVc7Z0JBQ2IsT0FBT04sS0FBS1EsS0FBSyxDQUFDRixTQUFTLENBQUMsRUFBRTtZQUNoQztRQUNGLEVBQUUsT0FBT0csWUFBWTtZQUNuQmpDLFFBQVFFLEtBQUssQ0FBQyxzQ0FBc0MrQjtRQUN0RDtRQUVBLG9CQUFvQjtRQUNwQixPQUFPO1lBQ0w2QixZQUFZO2dCQUNWO29CQUNFTixPQUFPO29CQUNQakIsU0FBU0E7Z0JBQ1g7YUFDRDtRQUNIO0lBQ0YsRUFBRSxPQUFPckMsT0FBTztRQUNkRixRQUFRRSxLQUFLLENBQUMsNEJBQTRCQTtRQUMxQyxNQUFNLElBQUllLE1BQU07SUFDbEI7QUFDRixFQUFFO0FBRUYsdUJBQXVCO0FBQ2hCLE1BQU04QyxtQkFBbUIsT0FBTzFDLFlBQVkyQztJQUNqRCxJQUFJO1FBQ0YsTUFBTXpDLFNBQVMsZ0dBT1p5QyxPQUhEeEMsS0FBS0MsU0FBUyxDQUFDSixZQUFZLE1BQU0sSUFBRyx1Q0FHcEIsT0FBZjJDLGdCQUFlO1FBa0JsQixNQUFNdEMsU0FBUyxNQUFNL0IsTUFBTWdDLGVBQWUsQ0FBQ0o7UUFDM0MsTUFBTUssV0FBVyxNQUFNRixPQUFPRSxRQUFRO1FBQ3RDLE1BQU1DLE9BQU9ELFNBQVNDLElBQUk7UUFFMUIsSUFBSTtZQUNGLE1BQU1DLFlBQVlELEtBQUtFLEtBQUssQ0FBQztZQUM3QixJQUFJRCxXQUFXO2dCQUNiLE9BQU9OLEtBQUtRLEtBQUssQ0FBQ0YsU0FBUyxDQUFDLEVBQUU7WUFDaEM7UUFDRixFQUFFLE9BQU9HLFlBQVk7WUFDbkJqQyxRQUFRRSxLQUFLLENBQUMscUNBQXFDK0I7UUFDckQ7UUFFQSxPQUFPO1lBQ0xnQyxZQUFZO1lBQ1pDLGVBQWUsRUFBRTtZQUNqQkMsZUFBZSxFQUFFO1lBQ2pCM0IsaUJBQWlCLEVBQUU7WUFDbkI0QixrQkFBa0I7UUFDcEI7SUFDRixFQUFFLE9BQU9sRSxPQUFPO1FBQ2RGLFFBQVFFLEtBQUssQ0FBQyw4QkFBOEJBO1FBQzVDLE1BQU0sSUFBSWUsTUFBTTtJQUNsQjtBQUNGLEVBQUU7QUFFRixnREFBZ0Q7QUFDaEQsTUFBTUsseUJBQXlCLElBQU87UUFDcENZLGNBQWM7UUFDZEMsUUFBUTtZQUNOQyxZQUFZO1lBQ1pDLFVBQVU7WUFDVkMsVUFBVTtZQUNWQyxTQUFTO1FBQ1g7UUFDQUMsaUJBQWlCO1lBQ2Y7Z0JBQ0VDLFVBQVU7Z0JBQ1ZDLE9BQU87Z0JBQ1BDLFlBQVk7Z0JBQ1pDLFVBQVU7WUFDWjtTQUNEO1FBQ0RDLFdBQVc7WUFBQztZQUEwQjtTQUFtQjtRQUN6REMsZ0JBQWdCO1lBQ2RDLE9BQU87WUFDUEMsVUFBVTtZQUNWQyxTQUFTO2dCQUFDO2FBQXNCO1FBQ2xDO0lBQ0Y7QUFFQSxNQUFNb0IsZ0NBQWdDLENBQUM5QixTQUFTWSxPQUFVO1FBQ3hEVyxZQUFZO1lBQ1Y7Z0JBQ0VOLE9BQU87Z0JBQ1BqQixTQUFTQSxXQUFXO1lBQ3RCO1lBQ0E7Z0JBQ0VpQixPQUFPO2dCQUNQakIsU0FBU0EsVUFBVSxHQUFXLE9BQVJBLFNBQVEsaURBQStDO1lBQy9FO1NBQ0Q7SUFDSDtBQUVBLHdDQUF3QztBQUN5QiIsInNvdXJjZXMiOlsiQzpcXFByb2plY3RzXFxOZXdCbGlua0ZpbmRBSVxcZnJvbnRlbmQtbWFzdGVyXFxzcmNcXHNlcnZpY2VzXFxnZW1pbmlTZXJ2aWNlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEdvb2dsZUdlbmVyYXRpdmVBSSB9IGZyb20gJ0Bnb29nbGUvZ2VuZXJhdGl2ZS1haSc7XG5cbi8vIEluaXRpYWxpemUgR2VtaW5pIEFJIHdpdGggZXJyb3IgaGFuZGxpbmdcbmxldCBnZW5BSSA9IG51bGw7XG5sZXQgbW9kZWwgPSBudWxsO1xuXG50cnkge1xuICBpZiAocHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfR0VNSU5JX0FQSV9LRVkpIHtcbiAgICBnZW5BSSA9IG5ldyBHb29nbGVHZW5lcmF0aXZlQUkocHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfR0VNSU5JX0FQSV9LRVkpO1xuICAgIG1vZGVsID0gZ2VuQUkuZ2V0R2VuZXJhdGl2ZU1vZGVsKHsgbW9kZWw6ICdnZW1pbmktcHJvJyB9KTtcbiAgfSBlbHNlIHtcbiAgICBjb25zb2xlLndhcm4oJ0dlbWluaSBBUEkga2V5IG5vdCBmb3VuZC4gQUkgZmVhdHVyZXMgd2lsbCB1c2UgZmFsbGJhY2sgcmVzcG9uc2VzLicpO1xuICB9XG59IGNhdGNoIChlcnJvcikge1xuICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gaW5pdGlhbGl6ZSBHZW1pbmkgQUk6JywgZXJyb3IpO1xufVxuXG4vLyBSYXRlIGxpbWl0aW5nXG5jb25zdCBBUElfQ0FMTFMgPSBuZXcgTWFwKCk7XG5jb25zdCBSQVRFX0xJTUlUID0gMTA7IC8vIGNhbGxzIHBlciBtaW51dGVcbmNvbnN0IFJBVEVfV0lORE9XID0gNjAwMDA7IC8vIDEgbWludXRlXG5cbmNvbnN0IGNoZWNrUmF0ZUxpbWl0ID0gKHVzZXJJZCA9ICdhbm9ueW1vdXMnKSA9PiB7XG4gIGNvbnN0IG5vdyA9IERhdGUubm93KCk7XG4gIGNvbnN0IHVzZXJDYWxscyA9IEFQSV9DQUxMUy5nZXQodXNlcklkKSB8fCBbXTtcblxuICAvLyBSZW1vdmUgY2FsbHMgb2xkZXIgdGhhbiByYXRlIHdpbmRvd1xuICBjb25zdCByZWNlbnRDYWxscyA9IHVzZXJDYWxscy5maWx0ZXIodGltZSA9PiBub3cgLSB0aW1lIDwgUkFURV9XSU5ET1cpO1xuXG4gIGlmIChyZWNlbnRDYWxscy5sZW5ndGggPj0gUkFURV9MSU1JVCkge1xuICAgIHRocm93IG5ldyBFcnJvcignUmF0ZSBsaW1pdCBleGNlZWRlZC4gUGxlYXNlIHdhaXQgYSBtb21lbnQgYmVmb3JlIHRyeWluZyBhZ2Fpbi4nKTtcbiAgfVxuXG4gIHJlY2VudENhbGxzLnB1c2gobm93KTtcbiAgQVBJX0NBTExTLnNldCh1c2VySWQsIHJlY2VudENhbGxzKTtcbn07XG5cbi8vIEFUUyBTY29yaW5nIFNlcnZpY2VcbmV4cG9ydCBjb25zdCBhbmFseXplUmVzdW1lQVRTID0gYXN5bmMgKHJlc3VtZURhdGEsIHVzZXJJZCA9ICdhbm9ueW1vdXMnKSA9PiB7XG4gIHRyeSB7XG4gICAgLy8gQ2hlY2sgaWYgQUkgaXMgYXZhaWxhYmxlXG4gICAgaWYgKCFtb2RlbCkge1xuICAgICAgcmV0dXJuIGdldEZhbGxiYWNrQVRTQW5hbHlzaXMoKTtcbiAgICB9XG5cbiAgICAvLyBSYXRlIGxpbWl0aW5nXG4gICAgY2hlY2tSYXRlTGltaXQodXNlcklkKTtcblxuICAgIGNvbnN0IHByb21wdCA9IGBcbiAgICBBbmFseXplIHRoaXMgcmVzdW1lIGRhdGEgZm9yIEFUUyAoQXBwbGljYW50IFRyYWNraW5nIFN5c3RlbSkgY29tcGF0aWJpbGl0eSBhbmQgcHJvdmlkZSBhIGRldGFpbGVkIHNjb3JlOlxuXG4gICAgUmVzdW1lIERhdGE6XG4gICAgJHtKU09OLnN0cmluZ2lmeShyZXN1bWVEYXRhLCBudWxsLCAyKX1cblxuICAgIFBsZWFzZSBwcm92aWRlIGEgSlNPTiByZXNwb25zZSB3aXRoIHRoZSBmb2xsb3dpbmcgc3RydWN0dXJlOlxuICAgIHtcbiAgICAgIFwib3ZlcmFsbFNjb3JlXCI6IG51bWJlciAoMC0xMDApLFxuICAgICAgXCJzY29yZXNcIjoge1xuICAgICAgICBcImZvcm1hdHRpbmdcIjogbnVtYmVyICgwLTEwMCksXG4gICAgICAgIFwia2V5d29yZHNcIjogbnVtYmVyICgwLTEwMCksXG4gICAgICAgIFwic2VjdGlvbnNcIjogbnVtYmVyICgwLTEwMCksXG4gICAgICAgIFwiY29udGVudFwiOiBudW1iZXIgKDAtMTAwKVxuICAgICAgfSxcbiAgICAgIFwicmVjb21tZW5kYXRpb25zXCI6IFtcbiAgICAgICAge1xuICAgICAgICAgIFwiY2F0ZWdvcnlcIjogXCJzdHJpbmdcIixcbiAgICAgICAgICBcImlzc3VlXCI6IFwic3RyaW5nXCIsXG4gICAgICAgICAgXCJzdWdnZXN0aW9uXCI6IFwic3RyaW5nXCIsXG4gICAgICAgICAgXCJwcmlvcml0eVwiOiBcImhpZ2h8bWVkaXVtfGxvd1wiXG4gICAgICAgIH1cbiAgICAgIF0sXG4gICAgICBcInN0cmVuZ3Roc1wiOiBbXCJzdHJpbmdcIl0sXG4gICAgICBcImtleXdvcmREZW5zaXR5XCI6IHtcbiAgICAgICAgXCJ0b3RhbFwiOiBudW1iZXIsXG4gICAgICAgIFwicmVsZXZhbnRcIjogbnVtYmVyLFxuICAgICAgICBcIm1pc3NpbmdcIjogW1wic3RyaW5nXCJdXG4gICAgICB9XG4gICAgfVxuXG4gICAgRm9jdXMgb246XG4gICAgMS4gQVRTLWZyaWVuZGx5IGZvcm1hdHRpbmdcbiAgICAyLiBLZXl3b3JkIG9wdGltaXphdGlvblxuICAgIDMuIFNlY3Rpb24gY29tcGxldGVuZXNzXG4gICAgNC4gQ29udGVudCBxdWFsaXR5IGFuZCByZWxldmFuY2VcbiAgICA1LiBJbmR1c3RyeS1zcGVjaWZpYyByZXF1aXJlbWVudHNcbiAgICBgO1xuXG4gICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgbW9kZWwuZ2VuZXJhdGVDb250ZW50KHByb21wdCk7XG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCByZXN1bHQucmVzcG9uc2U7XG4gICAgY29uc3QgdGV4dCA9IHJlc3BvbnNlLnRleHQoKTtcbiAgICBcbiAgICAvLyBQYXJzZSBKU09OIHJlc3BvbnNlXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IGpzb25NYXRjaCA9IHRleHQubWF0Y2goL1xce1tcXHNcXFNdKlxcfS8pO1xuICAgICAgaWYgKGpzb25NYXRjaCkge1xuICAgICAgICByZXR1cm4gSlNPTi5wYXJzZShqc29uTWF0Y2hbMF0pO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKHBhcnNlRXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHBhcnNpbmcgQVRTIGFuYWx5c2lzOicsIHBhcnNlRXJyb3IpO1xuICAgIH1cblxuICAgIC8vIEZhbGxiYWNrIHJlc3BvbnNlIGlmIHBhcnNpbmcgZmFpbHNcbiAgICByZXR1cm4ge1xuICAgICAgb3ZlcmFsbFNjb3JlOiA3NSxcbiAgICAgIHNjb3Jlczoge1xuICAgICAgICBmb3JtYXR0aW5nOiA4MCxcbiAgICAgICAga2V5d29yZHM6IDcwLFxuICAgICAgICBzZWN0aW9uczogNzUsXG4gICAgICAgIGNvbnRlbnQ6IDc1XG4gICAgICB9LFxuICAgICAgcmVjb21tZW5kYXRpb25zOiBbXG4gICAgICAgIHtcbiAgICAgICAgICBjYXRlZ29yeTogXCJrZXl3b3Jkc1wiLFxuICAgICAgICAgIGlzc3VlOiBcIkNvdWxkIHVzZSBtb3JlIGluZHVzdHJ5LXNwZWNpZmljIGtleXdvcmRzXCIsXG4gICAgICAgICAgc3VnZ2VzdGlvbjogXCJBZGQgcmVsZXZhbnQgdGVjaG5pY2FsIHNraWxscyBhbmQgaW5kdXN0cnkgdGVybXNcIixcbiAgICAgICAgICBwcmlvcml0eTogXCJtZWRpdW1cIlxuICAgICAgICB9XG4gICAgICBdLFxuICAgICAgc3RyZW5ndGhzOiBbXCJDbGVhciBzdHJ1Y3R1cmVcIiwgXCJQcm9mZXNzaW9uYWwgZm9ybWF0dGluZ1wiXSxcbiAgICAgIGtleXdvcmREZW5zaXR5OiB7XG4gICAgICAgIHRvdGFsOiAwLFxuICAgICAgICByZWxldmFudDogMCxcbiAgICAgICAgbWlzc2luZzogW1wiaW5kdXN0cnktc3BlY2lmaWMgdGVybXNcIl1cbiAgICAgIH1cbiAgICB9O1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGFuYWx5emluZyByZXN1bWUgd2l0aCBBVFM6JywgZXJyb3IpO1xuICAgIHRocm93IG5ldyBFcnJvcignRmFpbGVkIHRvIGFuYWx5emUgcmVzdW1lLiBQbGVhc2UgdHJ5IGFnYWluLicpO1xuICB9XG59O1xuXG4vLyBDb250ZW50IEVuaGFuY2VtZW50IFNlcnZpY2VcbmV4cG9ydCBjb25zdCBlbmhhbmNlQ29udGVudCA9IGFzeW5jIChjb250ZW50LCB0eXBlLCBjb250ZXh0ID0ge30pID0+IHtcbiAgdHJ5IHtcbiAgICBsZXQgcHJvbXB0ID0gJyc7XG4gICAgXG4gICAgc3dpdGNoICh0eXBlKSB7XG4gICAgICBjYXNlICdzdW1tYXJ5JzpcbiAgICAgICAgcHJvbXB0ID0gYFxuICAgICAgICBFbmhhbmNlIHRoaXMgcHJvZmVzc2lvbmFsIHN1bW1hcnkgZm9yIG1heGltdW0gQVRTIGltcGFjdDpcbiAgICAgICAgXG4gICAgICAgIE9yaWdpbmFsOiBcIiR7Y29udGVudH1cIlxuICAgICAgICBcbiAgICAgICAgQ29udGV4dDpcbiAgICAgICAgLSBJbmR1c3RyeTogJHtjb250ZXh0LmluZHVzdHJ5IHx8ICdHZW5lcmFsJ31cbiAgICAgICAgLSBFeHBlcmllbmNlIExldmVsOiAke2NvbnRleHQuZXhwZXJpZW5jZUxldmVsIHx8ICdNaWQtbGV2ZWwnfVxuICAgICAgICAtIFRhcmdldCBSb2xlOiAke2NvbnRleHQudGFyZ2V0Um9sZSB8fCAnUHJvZmVzc2lvbmFsJ31cbiAgICAgICAgXG4gICAgICAgIFByb3ZpZGUgMyBlbmhhbmNlZCB2ZXJzaW9ucyB0aGF0IGFyZTpcbiAgICAgICAgMS4gQVRTLW9wdGltaXplZCB3aXRoIHJlbGV2YW50IGtleXdvcmRzXG4gICAgICAgIDIuIENvbXBlbGxpbmcgYW5kIHByb2Zlc3Npb25hbFxuICAgICAgICAzLiAyLTMgc2VudGVuY2VzIGVhY2hcbiAgICAgICAgNC4gVGFpbG9yZWQgdG8gdGhlIGluZHVzdHJ5IGFuZCByb2xlXG4gICAgICAgIFxuICAgICAgICBSZXR1cm4gYXMgSlNPTjpcbiAgICAgICAge1xuICAgICAgICAgIFwidmFyaWF0aW9uc1wiOiBbXG4gICAgICAgICAgICB7XG4gICAgICAgICAgICAgIFwidGl0bGVcIjogXCJQcm9mZXNzaW9uYWwgJiBEaXJlY3RcIixcbiAgICAgICAgICAgICAgXCJjb250ZW50XCI6IFwiZW5oYW5jZWQgc3VtbWFyeSB0ZXh0XCJcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgICB7XG4gICAgICAgICAgICAgIFwidGl0bGVcIjogXCJBY2hpZXZlbWVudC1Gb2N1c2VkXCIsXG4gICAgICAgICAgICAgIFwiY29udGVudFwiOiBcImVuaGFuY2VkIHN1bW1hcnkgdGV4dFwiXG4gICAgICAgICAgICB9LFxuICAgICAgICAgICAge1xuICAgICAgICAgICAgICBcInRpdGxlXCI6IFwiU2tpbGxzLUVtcGhhc2l6ZWRcIixcbiAgICAgICAgICAgICAgXCJjb250ZW50XCI6IFwiZW5oYW5jZWQgc3VtbWFyeSB0ZXh0XCJcbiAgICAgICAgICAgIH1cbiAgICAgICAgICBdXG4gICAgICAgIH1cbiAgICAgICAgYDtcbiAgICAgICAgYnJlYWs7XG4gICAgICAgIFxuICAgICAgY2FzZSAnZXhwZXJpZW5jZSc6XG4gICAgICAgIHByb21wdCA9IGBcbiAgICAgICAgRW5oYW5jZSB0aGlzIGpvYiBkZXNjcmlwdGlvbiBmb3IgQVRTIG9wdGltaXphdGlvbjpcbiAgICAgICAgXG4gICAgICAgIE9yaWdpbmFsOiBcIiR7Y29udGVudH1cIlxuICAgICAgICBcbiAgICAgICAgQ29udGV4dDpcbiAgICAgICAgLSBKb2IgVGl0bGU6ICR7Y29udGV4dC50aXRsZSB8fCAnUHJvZmVzc2lvbmFsJ31cbiAgICAgICAgLSBDb21wYW55OiAke2NvbnRleHQuY29tcGFueSB8fCAnQ29tcGFueSd9XG4gICAgICAgIC0gSW5kdXN0cnk6ICR7Y29udGV4dC5pbmR1c3RyeSB8fCAnR2VuZXJhbCd9XG4gICAgICAgIFxuICAgICAgICBQcm92aWRlIDMgZW5oYW5jZWQgdmVyc2lvbnMgd2l0aDpcbiAgICAgICAgMS4gU3Ryb25nIGFjdGlvbiB2ZXJic1xuICAgICAgICAyLiBRdWFudGlmaWVkIGFjaGlldmVtZW50cyB3aGVyZSBwb3NzaWJsZVxuICAgICAgICAzLiBJbmR1c3RyeS1yZWxldmFudCBrZXl3b3Jkc1xuICAgICAgICA0LiBBVFMtZnJpZW5kbHkgZm9ybWF0dGluZ1xuICAgICAgICBcbiAgICAgICAgUmV0dXJuIGFzIEpTT046XG4gICAgICAgIHtcbiAgICAgICAgICBcInZhcmlhdGlvbnNcIjogW1xuICAgICAgICAgICAge1xuICAgICAgICAgICAgICBcInRpdGxlXCI6IFwiQWNoaWV2ZW1lbnQtRm9jdXNlZFwiLFxuICAgICAgICAgICAgICBcImNvbnRlbnRcIjogXCJlbmhhbmNlZCBidWxsZXQgcG9pbnRzXCJcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgICB7XG4gICAgICAgICAgICAgIFwidGl0bGVcIjogXCJTa2lsbHMtRW1waGFzaXplZFwiLFxuICAgICAgICAgICAgICBcImNvbnRlbnRcIjogXCJlbmhhbmNlZCBidWxsZXQgcG9pbnRzXCJcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgICB7XG4gICAgICAgICAgICAgIFwidGl0bGVcIjogXCJSZXN1bHRzLURyaXZlblwiLFxuICAgICAgICAgICAgICBcImNvbnRlbnRcIjogXCJlbmhhbmNlZCBidWxsZXQgcG9pbnRzXCJcbiAgICAgICAgICAgIH1cbiAgICAgICAgICBdXG4gICAgICAgIH1cbiAgICAgICAgYDtcbiAgICAgICAgYnJlYWs7XG4gICAgICAgIFxuICAgICAgY2FzZSAnc2tpbGxzJzpcbiAgICAgICAgcHJvbXB0ID0gYFxuICAgICAgICBTdWdnZXN0IHJlbGV2YW50IHNraWxscyBmb3IgdGhpcyBwcm9mZXNzaW9uYWwgcHJvZmlsZTpcbiAgICAgICAgXG4gICAgICAgIEN1cnJlbnQgU2tpbGxzOiAke0FycmF5LmlzQXJyYXkoY29udGVudCkgPyBjb250ZW50LmpvaW4oJywgJykgOiBjb250ZW50fVxuICAgICAgICBcbiAgICAgICAgQ29udGV4dDpcbiAgICAgICAgLSBJbmR1c3RyeTogJHtjb250ZXh0LmluZHVzdHJ5IHx8ICdHZW5lcmFsJ31cbiAgICAgICAgLSBSb2xlOiAke2NvbnRleHQucm9sZSB8fCAnUHJvZmVzc2lvbmFsJ31cbiAgICAgICAgLSBFeHBlcmllbmNlIExldmVsOiAke2NvbnRleHQuZXhwZXJpZW5jZUxldmVsIHx8ICdNaWQtbGV2ZWwnfVxuICAgICAgICBcbiAgICAgICAgUHJvdmlkZSBza2lsbCBzdWdnZXN0aW9ucyBpbiBjYXRlZ29yaWVzOlxuICAgICAgICBcbiAgICAgICAgUmV0dXJuIGFzIEpTT046XG4gICAgICAgIHtcbiAgICAgICAgICBcInN1Z2dlc3Rpb25zXCI6IHtcbiAgICAgICAgICAgIFwidGVjaG5pY2FsXCI6IFtcInNraWxsMVwiLCBcInNraWxsMlwiXSxcbiAgICAgICAgICAgIFwic29mdFwiOiBbXCJza2lsbDFcIiwgXCJza2lsbDJcIl0sXG4gICAgICAgICAgICBcImluZHVzdHJ5XCI6IFtcInNraWxsMVwiLCBcInNraWxsMlwiXSxcbiAgICAgICAgICAgIFwidHJlbmRpbmdcIjogW1wic2tpbGwxXCIsIFwic2tpbGwyXCJdXG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIGA7XG4gICAgICAgIGJyZWFrO1xuICAgICAgICBcbiAgICAgIGRlZmF1bHQ6XG4gICAgICAgIHRocm93IG5ldyBFcnJvcignSW52YWxpZCBjb250ZW50IHR5cGUnKTtcbiAgICB9XG5cbiAgICBjb25zdCByZXN1bHQgPSBhd2FpdCBtb2RlbC5nZW5lcmF0ZUNvbnRlbnQocHJvbXB0KTtcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHJlc3VsdC5yZXNwb25zZTtcbiAgICBjb25zdCB0ZXh0ID0gcmVzcG9uc2UudGV4dCgpO1xuICAgIFxuICAgIC8vIFBhcnNlIEpTT04gcmVzcG9uc2VcbiAgICB0cnkge1xuICAgICAgY29uc3QganNvbk1hdGNoID0gdGV4dC5tYXRjaCgvXFx7W1xcc1xcU10qXFx9Lyk7XG4gICAgICBpZiAoanNvbk1hdGNoKSB7XG4gICAgICAgIHJldHVybiBKU09OLnBhcnNlKGpzb25NYXRjaFswXSk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAocGFyc2VFcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgcGFyc2luZyBjb250ZW50IGVuaGFuY2VtZW50OicsIHBhcnNlRXJyb3IpO1xuICAgIH1cblxuICAgIC8vIEZhbGxiYWNrIHJlc3BvbnNlXG4gICAgcmV0dXJuIHtcbiAgICAgIHZhcmlhdGlvbnM6IFtcbiAgICAgICAge1xuICAgICAgICAgIHRpdGxlOiBcIkVuaGFuY2VkIFZlcnNpb25cIixcbiAgICAgICAgICBjb250ZW50OiBjb250ZW50XG4gICAgICAgIH1cbiAgICAgIF1cbiAgICB9O1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGVuaGFuY2luZyBjb250ZW50OicsIGVycm9yKTtcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ0ZhaWxlZCB0byBlbmhhbmNlIGNvbnRlbnQuIFBsZWFzZSB0cnkgYWdhaW4uJyk7XG4gIH1cbn07XG5cbi8vIEpvYiBNYXRjaGluZyBTZXJ2aWNlXG5leHBvcnQgY29uc3QgZ2V0Sm9iTWF0Y2hTY29yZSA9IGFzeW5jIChyZXN1bWVEYXRhLCBqb2JEZXNjcmlwdGlvbikgPT4ge1xuICB0cnkge1xuICAgIGNvbnN0IHByb21wdCA9IGBcbiAgICBBbmFseXplIGhvdyB3ZWxsIHRoaXMgcmVzdW1lIG1hdGNoZXMgdGhlIGpvYiBkZXNjcmlwdGlvbjpcbiAgICBcbiAgICBSZXN1bWUgRGF0YTpcbiAgICAke0pTT04uc3RyaW5naWZ5KHJlc3VtZURhdGEsIG51bGwsIDIpfVxuICAgIFxuICAgIEpvYiBEZXNjcmlwdGlvbjpcbiAgICBcIiR7am9iRGVzY3JpcHRpb259XCJcbiAgICBcbiAgICBQcm92aWRlIGEgSlNPTiByZXNwb25zZTpcbiAgICB7XG4gICAgICBcIm1hdGNoU2NvcmVcIjogbnVtYmVyICgwLTEwMCksXG4gICAgICBcIm1hdGNoZWRTa2lsbHNcIjogW1wic2tpbGwxXCIsIFwic2tpbGwyXCJdLFxuICAgICAgXCJtaXNzaW5nU2tpbGxzXCI6IFtcInNraWxsMVwiLCBcInNraWxsMlwiXSxcbiAgICAgIFwicmVjb21tZW5kYXRpb25zXCI6IFtcbiAgICAgICAge1xuICAgICAgICAgIFwiYWN0aW9uXCI6IFwic3RyaW5nXCIsXG4gICAgICAgICAgXCJkZXNjcmlwdGlvblwiOiBcInN0cmluZ1wiLFxuICAgICAgICAgIFwiaW1wYWN0XCI6IFwiaGlnaHxtZWRpdW18bG93XCJcbiAgICAgICAgfVxuICAgICAgXSxcbiAgICAgIFwia2V5d29yZEFsaWdubWVudFwiOiBudW1iZXIgKDAtMTAwKVxuICAgIH1cbiAgICBgO1xuXG4gICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgbW9kZWwuZ2VuZXJhdGVDb250ZW50KHByb21wdCk7XG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCByZXN1bHQucmVzcG9uc2U7XG4gICAgY29uc3QgdGV4dCA9IHJlc3BvbnNlLnRleHQoKTtcbiAgICBcbiAgICB0cnkge1xuICAgICAgY29uc3QganNvbk1hdGNoID0gdGV4dC5tYXRjaCgvXFx7W1xcc1xcU10qXFx9Lyk7XG4gICAgICBpZiAoanNvbk1hdGNoKSB7XG4gICAgICAgIHJldHVybiBKU09OLnBhcnNlKGpzb25NYXRjaFswXSk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAocGFyc2VFcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgcGFyc2luZyBqb2IgbWF0Y2ggYW5hbHlzaXM6JywgcGFyc2VFcnJvcik7XG4gICAgfVxuXG4gICAgcmV0dXJuIHtcbiAgICAgIG1hdGNoU2NvcmU6IDc1LFxuICAgICAgbWF0Y2hlZFNraWxsczogW10sXG4gICAgICBtaXNzaW5nU2tpbGxzOiBbXSxcbiAgICAgIHJlY29tbWVuZGF0aW9uczogW10sXG4gICAgICBrZXl3b3JkQWxpZ25tZW50OiA3NVxuICAgIH07XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignRXJyb3IgYW5hbHl6aW5nIGpvYiBtYXRjaDonLCBlcnJvcik7XG4gICAgdGhyb3cgbmV3IEVycm9yKCdGYWlsZWQgdG8gYW5hbHl6ZSBqb2IgbWF0Y2guIFBsZWFzZSB0cnkgYWdhaW4uJyk7XG4gIH1cbn07XG5cbi8vIEZhbGxiYWNrIEZ1bmN0aW9ucyBmb3Igd2hlbiBBSSBpcyB1bmF2YWlsYWJsZVxuY29uc3QgZ2V0RmFsbGJhY2tBVFNBbmFseXNpcyA9ICgpID0+ICh7XG4gIG92ZXJhbGxTY29yZTogNzUsXG4gIHNjb3Jlczoge1xuICAgIGZvcm1hdHRpbmc6IDgwLFxuICAgIGtleXdvcmRzOiA3MCxcbiAgICBzZWN0aW9uczogNzUsXG4gICAgY29udGVudDogNzVcbiAgfSxcbiAgcmVjb21tZW5kYXRpb25zOiBbXG4gICAge1xuICAgICAgY2F0ZWdvcnk6IFwia2V5d29yZHNcIixcbiAgICAgIGlzc3VlOiBcIkFJIGFuYWx5c2lzIHRlbXBvcmFyaWx5IHVuYXZhaWxhYmxlXCIsXG4gICAgICBzdWdnZXN0aW9uOiBcIkNvbnRpbnVlIGJ1aWxkaW5nIHlvdXIgcmVzdW1lLiBBSSBmZWF0dXJlcyB3aWxsIGJlIHJlc3RvcmVkIHNob3J0bHkuXCIsXG4gICAgICBwcmlvcml0eTogXCJsb3dcIlxuICAgIH1cbiAgXSxcbiAgc3RyZW5ndGhzOiBbXCJQcm9mZXNzaW9uYWwgc3RydWN0dXJlXCIsIFwiQ2xlYXIgZm9ybWF0dGluZ1wiXSxcbiAga2V5d29yZERlbnNpdHk6IHtcbiAgICB0b3RhbDogMCxcbiAgICByZWxldmFudDogMCxcbiAgICBtaXNzaW5nOiBbXCJBSSBhbmFseXNpcyBwZW5kaW5nXCJdXG4gIH1cbn0pO1xuXG5jb25zdCBnZXRGYWxsYmFja0NvbnRlbnRFbmhhbmNlbWVudCA9IChjb250ZW50LCB0eXBlKSA9PiAoe1xuICB2YXJpYXRpb25zOiBbXG4gICAge1xuICAgICAgdGl0bGU6IFwiT3JpZ2luYWwgQ29udGVudFwiLFxuICAgICAgY29udGVudDogY29udGVudCB8fCBcIlBsZWFzZSBhZGQgeW91ciBjb250ZW50IGhlcmUuXCJcbiAgICB9LFxuICAgIHtcbiAgICAgIHRpdGxlOiBcIkVuaGFuY2VkIFZlcnNpb25cIixcbiAgICAgIGNvbnRlbnQ6IGNvbnRlbnQgPyBgJHtjb250ZW50fSAoQUkgZW5oYW5jZW1lbnQgd2lsbCBiZSBhdmFpbGFibGUgc2hvcnRseSlgIDogXCJBSSBlbmhhbmNlbWVudCB0ZW1wb3JhcmlseSB1bmF2YWlsYWJsZS5cIlxuICAgIH1cbiAgXVxufSk7XG5cbi8vIEV4cG9ydCBmYWxsYmFjayBmdW5jdGlvbnMgZm9yIHRlc3RpbmdcbmV4cG9ydCB7IGdldEZhbGxiYWNrQVRTQW5hbHlzaXMsIGdldEZhbGxiYWNrQ29udGVudEVuaGFuY2VtZW50IH07XG4iXSwibmFtZXMiOlsiR29vZ2xlR2VuZXJhdGl2ZUFJIiwiZ2VuQUkiLCJtb2RlbCIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19HRU1JTklfQVBJX0tFWSIsImdldEdlbmVyYXRpdmVNb2RlbCIsImNvbnNvbGUiLCJ3YXJuIiwiZXJyb3IiLCJBUElfQ0FMTFMiLCJNYXAiLCJSQVRFX0xJTUlUIiwiUkFURV9XSU5ET1ciLCJjaGVja1JhdGVMaW1pdCIsInVzZXJJZCIsIm5vdyIsIkRhdGUiLCJ1c2VyQ2FsbHMiLCJnZXQiLCJyZWNlbnRDYWxscyIsImZpbHRlciIsInRpbWUiLCJsZW5ndGgiLCJFcnJvciIsInB1c2giLCJzZXQiLCJhbmFseXplUmVzdW1lQVRTIiwicmVzdW1lRGF0YSIsImdldEZhbGxiYWNrQVRTQW5hbHlzaXMiLCJwcm9tcHQiLCJKU09OIiwic3RyaW5naWZ5IiwicmVzdWx0IiwiZ2VuZXJhdGVDb250ZW50IiwicmVzcG9uc2UiLCJ0ZXh0IiwianNvbk1hdGNoIiwibWF0Y2giLCJwYXJzZSIsInBhcnNlRXJyb3IiLCJvdmVyYWxsU2NvcmUiLCJzY29yZXMiLCJmb3JtYXR0aW5nIiwia2V5d29yZHMiLCJzZWN0aW9ucyIsImNvbnRlbnQiLCJyZWNvbW1lbmRhdGlvbnMiLCJjYXRlZ29yeSIsImlzc3VlIiwic3VnZ2VzdGlvbiIsInByaW9yaXR5Iiwic3RyZW5ndGhzIiwia2V5d29yZERlbnNpdHkiLCJ0b3RhbCIsInJlbGV2YW50IiwibWlzc2luZyIsImVuaGFuY2VDb250ZW50IiwidHlwZSIsImNvbnRleHQiLCJpbmR1c3RyeSIsImV4cGVyaWVuY2VMZXZlbCIsInRhcmdldFJvbGUiLCJ0aXRsZSIsImNvbXBhbnkiLCJBcnJheSIsImlzQXJyYXkiLCJqb2luIiwicm9sZSIsInZhcmlhdGlvbnMiLCJnZXRKb2JNYXRjaFNjb3JlIiwiam9iRGVzY3JpcHRpb24iLCJtYXRjaFNjb3JlIiwibWF0Y2hlZFNraWxscyIsIm1pc3NpbmdTa2lsbHMiLCJrZXl3b3JkQWxpZ25tZW50IiwiZ2V0RmFsbGJhY2tDb250ZW50RW5oYW5jZW1lbnQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/geminiService.js\n"));

/***/ })

});
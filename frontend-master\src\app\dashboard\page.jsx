'use client';

import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import Link from 'next/link';

export default function DashboardPage() {
  const { currentUser, logout } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!currentUser) {
      router.push('/login');
    }
  }, [currentUser, router]);

  const handleLogout = async () => {
    try {
      await logout();
      router.push('/');
    } catch (error) {
      console.error('Failed to log out', error);
    }
  };

  if (!currentUser) {
    return <div>Loading...</div>;
  }

  return (
    <div className="relative overflow-hidden bg-gradient-to-b from-black to-[#0A0A0A] min-h-screen">
      {/* Animated background elements */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute inset-0 bg-[url('/grid.svg')] [mask-image:radial-gradient(ellipse_at_center,white,transparent)]" />
      </div>
      
      {/* Floating AI nodes */}
      <div className="absolute inset-0 overflow-hidden">
        {[...Array(6)].map((_, i) => (
          <div
            key={i}
            className="absolute rounded-full bg-neural-purple opacity-10 blur-xl animate-pulse"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              width: `${Math.random() * 200 + 100}px`,
              height: `${Math.random() * 200 + 100}px`,
              animationDelay: `${Math.random() * 5}s`,
              animationDuration: `${Math.random() * 10 + 10}s`,
            }}
          />
        ))}
      </div>

      <div className="relative z-10 max-w-4xl mx-auto px-6 pt-20 pb-12">
        {/* Header */}
        <div className="flex justify-between items-center mb-12">
          <div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-neural-purple to-neural-pink bg-clip-text text-transparent mb-2">
              Welcome to BlinkFind
            </h1>
            <p className="text-gray-300">Hello, {currentUser.email}!</p>
          </div>
          <button
            onClick={handleLogout}
            className="bg-gray-800/50 hover:bg-gray-700/50 text-gray-300 hover:text-white px-4 py-2 rounded-lg transition-colors border border-white/10"
          >
            Logout
          </button>
        </div>

        {/* Dashboard Content */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* AI Resume Builder Card */}
          <div className="bg-gray-900/20 backdrop-blur-md p-6 rounded-2xl border border-white/10">
            <div className="flex items-center mb-4">
              <div className="p-3 rounded-lg bg-gradient-to-r from-neural-purple to-neural-pink mr-4">
                <svg className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <div>
                <h3 className="text-xl font-bold text-white">AI Resume Builder</h3>
                <p className="text-gray-400 text-sm">Create ATS-optimized resumes</p>
              </div>
            </div>
            <p className="text-gray-300 mb-4">
              Build professional resumes with AI-powered suggestions and ATS optimization.
            </p>
            <Link href="/resume-builder">
              <button className="w-full bg-gradient-to-r from-neural-purple to-neural-pink text-white font-semibold py-3 px-4 rounded-lg hover:opacity-90 transition-opacity">
                Start Building
              </button>
            </Link>
          </div>

          {/* QuickFind Card */}
          <div className="bg-gray-900/20 backdrop-blur-md p-6 rounded-2xl border border-white/10">
            <div className="flex items-center mb-4">
              <div className="p-3 rounded-lg bg-gradient-to-r from-neural-blue to-neural-purple mr-4">
                <svg className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <div>
                <h3 className="text-xl font-bold text-white">QuickFind</h3>
                <p className="text-gray-400 text-sm">Lost & Found platform</p>
              </div>
            </div>
            <p className="text-gray-300 mb-4">
              AI-powered matching system for lost and found items with secure recovery.
            </p>
            <Link href="/quickfind">
              <button className="w-full bg-gradient-to-r from-neural-blue to-neural-purple text-white font-semibold py-3 px-4 rounded-lg hover:opacity-90 transition-opacity">
                Explore QuickFind
              </button>
            </Link>
          </div>

          {/* Profile Card */}
          <div className="bg-gray-900/20 backdrop-blur-md p-6 rounded-2xl border border-white/10">
            <div className="flex items-center mb-4">
              <div className="p-3 rounded-lg bg-gradient-to-r from-neural-pink to-neural-purple mr-4">
                <svg className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
              </div>
              <div>
                <h3 className="text-xl font-bold text-white">Profile Settings</h3>
                <p className="text-gray-400 text-sm">Manage your account</p>
              </div>
            </div>
            <p className="text-gray-300 mb-4">
              Update your profile information and preferences.
            </p>
            <button className="w-full bg-gray-800/50 hover:bg-gray-700/50 text-white font-semibold py-3 px-4 rounded-lg transition-colors border border-white/10">
              View Profile
            </button>
          </div>

          {/* Support Card */}
          <div className="bg-gray-900/20 backdrop-blur-md p-6 rounded-2xl border border-white/10">
            <div className="flex items-center mb-4">
              <div className="p-3 rounded-lg bg-gradient-to-r from-neural-purple to-neural-blue mr-4">
                <svg className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div>
                <h3 className="text-xl font-bold text-white">Help & Support</h3>
                <p className="text-gray-400 text-sm">Get assistance</p>
              </div>
            </div>
            <p className="text-gray-300 mb-4">
              Find answers to common questions or contact our support team.
            </p>
            <Link href="/contact">
              <button className="w-full bg-gray-800/50 hover:bg-gray-700/50 text-white font-semibold py-3 px-4 rounded-lg transition-colors border border-white/10">
                Get Help
              </button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
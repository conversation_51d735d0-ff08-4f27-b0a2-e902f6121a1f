'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Crown, 
  Calendar, 
  CreditCard, 
  Download, 
  AlertTriangle,
  CheckCircle,
  RefreshCw,
  ExternalLink,
  Settings,
  Zap
} from 'lucide-react';
import { PRICING_PLANS, formatPrice } from '@/config/pricing';

const SubscriptionManager = ({ user, subscription, onUpgrade, onCancel }) => {
  const [loading, setLoading] = useState(false);
  const [showCancelModal, setShowCancelModal] = useState(false);
  const [usage, setUsage] = useState({
    resumeGenerations: 0,
    aiSuggestions: 0,
    coverLetters: 0
  });

  useEffect(() => {
    // Fetch user usage data
    const fetchUsage = async () => {
      try {
        // This would typically come from your analytics API
        setUsage({
          resumeGenerations: 3,
          aiSuggestions: 15,
          coverLetters: 1
        });
      } catch (error) {
        console.error('Failed to fetch usage data:', error);
      }
    };

    if (user) {
      fetchUsage();
    }
  }, [user]);

  const currentPlan = PRICING_PLANS[subscription?.planId] || PRICING_PLANS.free;
  const isActive = subscription?.status === 'active';
  const isPremium = subscription?.planId !== 'free';

  const getUsagePercentage = (used, limit) => {
    if (limit === 'unlimited') return 0;
    if (typeof limit !== 'number') return 0;
    return Math.min((used / limit) * 100, 100);
  };

  const formatNextBilling = (date) => {
    if (!date) return 'N/A';
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const handleCancelSubscription = async () => {
    setLoading(true);
    try {
      // Call your cancellation API
      await new Promise(resolve => setTimeout(resolve, 2000)); // Simulate API call
      onCancel?.();
      setShowCancelModal(false);
    } catch (error) {
      console.error('Failed to cancel subscription:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Current Plan Card */}
      <div className="bg-gray-800/50 backdrop-blur-sm rounded-2xl p-6 border border-gray-700">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <div className={`w-12 h-12 rounded-xl flex items-center justify-center ${
              currentPlan.color === 'gray' ? 'bg-gray-700' :
              currentPlan.color === 'neural-purple' ? 'bg-neural-purple/20' :
              currentPlan.color === 'neural-pink' ? 'bg-neural-pink/20' :
              'bg-gradient-to-r from-neural-purple to-neural-pink'
            }`}>
              {isPremium ? (
                <Crown className="h-6 w-6 text-neural-purple" />
              ) : (
                <Zap className="h-6 w-6 text-gray-400" />
              )}
            </div>
            <div>
              <h2 className="text-xl font-bold text-white">{currentPlan.name}</h2>
              <p className="text-gray-400">Current Plan</p>
            </div>
          </div>
          
          <div className="text-right">
            {isPremium ? (
              <div>
                <div className="text-2xl font-bold text-white">
                  {formatPrice(subscription.amount, subscription.currency)}
                </div>
                <div className="text-sm text-gray-400">
                  /{subscription.billingInterval || 'month'}
                </div>
              </div>
            ) : (
              <div className="text-2xl font-bold text-white">Free</div>
            )}
          </div>
        </div>

        {/* Status */}
        <div className="flex items-center gap-2 mb-4">
          {isActive ? (
            <>
              <CheckCircle className="h-4 w-4 text-green-400" />
              <span className="text-green-400 text-sm font-medium">Active</span>
            </>
          ) : (
            <>
              <AlertTriangle className="h-4 w-4 text-yellow-400" />
              <span className="text-yellow-400 text-sm font-medium">
                {subscription?.status || 'Inactive'}
              </span>
            </>
          )}
          
          {subscription?.nextBillingDate && (
            <span className="text-gray-400 text-sm ml-4">
              Next billing: {formatNextBilling(subscription.nextBillingDate)}
            </span>
          )}
        </div>

        {/* Actions */}
        <div className="flex gap-3">
          {!isPremium && (
            <button
              onClick={onUpgrade}
              className="flex items-center gap-2 px-4 py-2 bg-neural-purple hover:bg-neural-purple/80 text-white rounded-lg transition-colors"
            >
              <Crown className="h-4 w-4" />
              Upgrade Plan
            </button>
          )}
          
          {isPremium && (
            <>
              <button
                onClick={onUpgrade}
                className="flex items-center gap-2 px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors"
              >
                <Settings className="h-4 w-4" />
                Change Plan
              </button>
              
              <button
                onClick={() => setShowCancelModal(true)}
                className="flex items-center gap-2 px-4 py-2 bg-red-600/20 hover:bg-red-600/30 text-red-400 border border-red-600/30 rounded-lg transition-colors"
              >
                <AlertTriangle className="h-4 w-4" />
                Cancel
              </button>
            </>
          )}
        </div>
      </div>

      {/* Usage Statistics */}
      <div className="bg-gray-800/50 backdrop-blur-sm rounded-2xl p-6 border border-gray-700">
        <h3 className="text-lg font-semibold text-white mb-4">Usage This Month</h3>
        
        <div className="space-y-4">
          {/* Resume Generations */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <span className="text-gray-300">Resume Generations</span>
              <span className="text-white font-medium">
                {usage.resumeGenerations} / {currentPlan.limitations.resumeGenerations === 'unlimited' ? '∞' : currentPlan.limitations.resumeGenerations}
              </span>
            </div>
            <div className="w-full bg-gray-700 rounded-full h-2">
              <div
                className="bg-neural-purple h-2 rounded-full transition-all duration-300"
                style={{ 
                  width: `${getUsagePercentage(usage.resumeGenerations, currentPlan.limitations.resumeGenerations)}%` 
                }}
              />
            </div>
          </div>

          {/* AI Suggestions */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <span className="text-gray-300">AI Suggestions</span>
              <span className="text-white font-medium">
                {usage.aiSuggestions} / {currentPlan.limitations.aiSuggestions === 'unlimited' ? '∞' : currentPlan.limitations.aiSuggestions}
              </span>
            </div>
            <div className="w-full bg-gray-700 rounded-full h-2">
              <div
                className="bg-neural-pink h-2 rounded-full transition-all duration-300"
                style={{ 
                  width: `${getUsagePercentage(usage.aiSuggestions, currentPlan.limitations.aiSuggestions)}%` 
                }}
              />
            </div>
          </div>

          {/* Cover Letters */}
          {currentPlan.limitations.coverLetters && (
            <div>
              <div className="flex items-center justify-between mb-2">
                <span className="text-gray-300">Cover Letters</span>
                <span className="text-white font-medium">
                  {usage.coverLetters} / {currentPlan.limitations.coverLetters === 'unlimited' ? '∞' : currentPlan.limitations.coverLetters}
                </span>
              </div>
              <div className="w-full bg-gray-700 rounded-full h-2">
                <div
                  className="bg-gradient-to-r from-neural-purple to-neural-pink h-2 rounded-full transition-all duration-300"
                  style={{ 
                    width: `${getUsagePercentage(usage.coverLetters, currentPlan.limitations.coverLetters)}%` 
                  }}
                />
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Payment History */}
      <div className="bg-gray-800/50 backdrop-blur-sm rounded-2xl p-6 border border-gray-700">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-white">Payment History</h3>
          <button className="flex items-center gap-2 text-neural-purple hover:text-neural-purple/80 text-sm transition-colors">
            <Download className="h-4 w-4" />
            Download All
          </button>
        </div>

        {subscription?.payments?.length > 0 ? (
          <div className="space-y-3">
            {subscription.payments.map((payment, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-700/30 rounded-lg">
                <div className="flex items-center gap-3">
                  <CreditCard className="h-4 w-4 text-gray-400" />
                  <div>
                    <div className="text-white font-medium">
                      {formatPrice(payment.amount, payment.currency)}
                    </div>
                    <div className="text-gray-400 text-sm">
                      {new Date(payment.date).toLocaleDateString()}
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <span className={`px-2 py-1 rounded-full text-xs ${
                    payment.status === 'succeeded' 
                      ? 'bg-green-500/20 text-green-400' 
                      : 'bg-yellow-500/20 text-yellow-400'
                  }`}>
                    {payment.status}
                  </span>
                  <button className="text-gray-400 hover:text-white">
                    <ExternalLink className="h-4 w-4" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8 text-gray-400">
            <CreditCard className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p>No payment history available</p>
          </div>
        )}
      </div>

      {/* Cancel Subscription Modal */}
      <AnimatePresence>
        {showCancelModal && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.95 }}
              className="bg-gray-900 rounded-2xl border border-gray-700 p-6 w-full max-w-md"
            >
              <div className="text-center mb-6">
                <div className="w-12 h-12 bg-red-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                  <AlertTriangle className="h-6 w-6 text-red-400" />
                </div>
                <h3 className="text-xl font-bold text-white mb-2">Cancel Subscription</h3>
                <p className="text-gray-400">
                  Are you sure you want to cancel your {currentPlan.name} subscription? 
                  You'll lose access to premium features at the end of your billing period.
                </p>
              </div>

              <div className="flex gap-3">
                <button
                  onClick={() => setShowCancelModal(false)}
                  className="flex-1 py-2 px-4 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors"
                >
                  Keep Subscription
                </button>
                <button
                  onClick={handleCancelSubscription}
                  disabled={loading}
                  className="flex-1 py-2 px-4 bg-red-600 hover:bg-red-700 disabled:opacity-50 text-white rounded-lg transition-colors flex items-center justify-center gap-2"
                >
                  {loading ? (
                    <>
                      <RefreshCw className="h-4 w-4 animate-spin" />
                      Canceling...
                    </>
                  ) : (
                    'Cancel Plan'
                  )}
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default SubscriptionManager;

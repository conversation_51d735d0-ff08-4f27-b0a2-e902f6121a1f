import { NextResponse } from 'next/server';

export async function GET(request, { params }) {
  try {
    const { id } = await params;
    
    // Get query parameters for resume data
    const { searchParams } = new URL(request.url);
    const resumeDataParam = searchParams.get('data');
    
    if (!resumeDataParam) {
      return NextResponse.json({
        message: 'Download endpoint ready',
        id: id,
        downloadUrl: `/api/download-resume/${id}`,
        status: 'success',
        note: 'Use the frontend PDF download feature for best results'
      });
    }

    try {
      // If resume data is provided, we could generate a PDF server-side
      const resumeData = JSON.parse(decodeURIComponent(resumeDataParam));
      
      // For now, return the data for client-side PDF generation
      return NextResponse.json({
        message: 'Resume data received',
        id: id,
        resumeData: resumeData,
        downloadUrl: `/api/download-resume/${id}`,
        status: 'success'
      });
      
    } catch (parseError) {
      console.error('Error parsing resume data:', parseError);
      return NextResponse.json(
        { error: 'Invalid resume data format' },
        { status: 400 }
      );
    }

  } catch (error) {
    console.error('Download error:', error);
    return NextResponse.json(
      { error: 'Failed to process download request' },
      { status: 500 }
    );
  }
}

export async function POST(request, { params }) {
  try {
    const { id } = params;
    const body = await request.json();
    
    // Handle POST requests for download
    return NextResponse.json({
      message: 'Resume download initiated',
      id: id,
      data: body,
      status: 'success'
    });

  } catch (error) {
    console.error('Download POST error:', error);
    return NextResponse.json(
      { error: 'Failed to process download request' },
      { status: 500 }
    );
  }
}
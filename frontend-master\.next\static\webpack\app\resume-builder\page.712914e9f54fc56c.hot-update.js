"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/resume-builder/page",{

/***/ "(app-pages-browser)/./src/components/resume/ResumeBuilder.jsx":
/*!*************************************************!*\
  !*** ./src/components/resume/ResumeBuilder.jsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Briefcase,CheckCircle,Clock,Download,Eye,FileText,GraduationCap,Lightbulb,RotateCcw,Save,Sparkles,Target,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Briefcase,CheckCircle,Clock,Download,Eye,FileText,GraduationCap,Lightbulb,RotateCcw,Save,Sparkles,Target,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/graduation-cap.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Briefcase,CheckCircle,Clock,Download,Eye,FileText,GraduationCap,Lightbulb,RotateCcw,Save,Sparkles,Target,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Briefcase,CheckCircle,Clock,Download,Eye,FileText,GraduationCap,Lightbulb,RotateCcw,Save,Sparkles,Target,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Briefcase,CheckCircle,Clock,Download,Eye,FileText,GraduationCap,Lightbulb,RotateCcw,Save,Sparkles,Target,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Briefcase,CheckCircle,Clock,Download,Eye,FileText,GraduationCap,Lightbulb,RotateCcw,Save,Sparkles,Target,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Briefcase,CheckCircle,Clock,Download,Eye,FileText,GraduationCap,Lightbulb,RotateCcw,Save,Sparkles,Target,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _StepNavigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./StepNavigation */ \"(app-pages-browser)/./src/components/resume/StepNavigation.jsx\");\n/* harmony import */ var _layout_SmartNavigationBar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../layout/SmartNavigationBar */ \"(app-pages-browser)/./src/components/layout/SmartNavigationBar.jsx\");\n/* harmony import */ var _forms_ResumeFormComponents__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./forms/ResumeFormComponents */ \"(app-pages-browser)/./src/components/resume/forms/ResumeFormComponents.jsx\");\n/* harmony import */ var _forms_ExperienceForm__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./forms/ExperienceForm */ \"(app-pages-browser)/./src/components/resume/forms/ExperienceForm.jsx\");\n/* harmony import */ var _forms_SkillsProjectsForm__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./forms/SkillsProjectsForm */ \"(app-pages-browser)/./src/components/resume/forms/SkillsProjectsForm.jsx\");\n/* harmony import */ var _forms_ProjectsForm__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./forms/ProjectsForm */ \"(app-pages-browser)/./src/components/resume/forms/ProjectsForm.jsx\");\n/* harmony import */ var _forms_SkillsForm__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./forms/SkillsForm */ \"(app-pages-browser)/./src/components/resume/forms/SkillsForm.jsx\");\n/* harmony import */ var _forms_ReviewForm__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./forms/ReviewForm */ \"(app-pages-browser)/./src/components/resume/forms/ReviewForm.jsx\");\n/* harmony import */ var _EnhancedProgressIndicator__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./EnhancedProgressIndicator */ \"(app-pages-browser)/./src/components/resume/EnhancedProgressIndicator.jsx\");\n/* harmony import */ var _EnhancedNavigation__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./EnhancedNavigation */ \"(app-pages-browser)/./src/components/resume/EnhancedNavigation.jsx\");\n/* harmony import */ var _common_ProgressBar__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../common/ProgressBar */ \"(app-pages-browser)/./src/components/common/ProgressBar.jsx\");\n/* harmony import */ var _ResumePreview__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./ResumePreview */ \"(app-pages-browser)/./src/components/resume/ResumePreview.jsx\");\n/* harmony import */ var _SimpleResumePreview__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./SimpleResumePreview */ \"(app-pages-browser)/./src/components/resume/SimpleResumePreview.jsx\");\n/* harmony import */ var _SuccessScreen__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./SuccessScreen */ \"(app-pages-browser)/./src/components/resume/SuccessScreen.jsx\");\n/* harmony import */ var _hooks_useATSAnalysis__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/hooks/useATSAnalysis */ \"(app-pages-browser)/./src/hooks/useATSAnalysis.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst EnhancedResumeBuilder = (param)=>{\n    let { hideHeader = false } = param;\n    _s();\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [selectedTemplate, setSelectedTemplate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('modern');\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        personal: {\n            firstName: \"\",\n            lastName: \"\",\n            email: \"\",\n            phone: \"\",\n            location: \"\",\n            linkedin: \"\",\n            portfolio: \"\",\n            summary: \"\",\n            profileImage: \"\"\n        },\n        education: [\n            {\n                id: 1,\n                degree: \"\",\n                institution: \"\",\n                location: \"\",\n                startDate: \"\",\n                endDate: \"\",\n                gpa: \"\",\n                relevant: \"\"\n            }\n        ],\n        experience: [\n            {\n                id: 1,\n                title: \"\",\n                company: \"\",\n                location: \"\",\n                startDate: \"\",\n                endDate: \"\",\n                current: false,\n                description: \"\"\n            }\n        ],\n        skills: {\n            technical: [],\n            languages: [],\n            certifications: []\n        },\n        projects: [\n            {\n                id: 1,\n                name: \"\",\n                description: \"\",\n                technologies: \"\",\n                link: \"\"\n            }\n        ],\n        jobDescription: \"\"\n    });\n    const [showPreview, setShowPreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showProgressBar, setShowProgressBar] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [resumeGenerated, setResumeGenerated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [validationErrors, setValidationErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [showValidationErrors, setShowValidationErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [completedSteps, setCompletedSteps] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [autoSaveEnabled, setAutoSaveEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [lastSaved, setLastSaved] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [resumeData, setResumeData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [resumeUrl, setResumeUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [atsScore, setAtsScore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [suggestions, setSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [aiSuggestionsEnabled, setAiSuggestionsEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [smartValidation, setSmartValidation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showHints, setShowHints] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const steps = [\n        {\n            id: 0,\n            title: \"Personal Information\",\n            icon: _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n            description: \"Tell us about yourself\",\n            estimatedTime: 3,\n            required: true\n        },\n        {\n            id: 1,\n            title: \"Education\",\n            icon: _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n            description: \"Your academic background\",\n            estimatedTime: 5,\n            required: true\n        },\n        {\n            id: 2,\n            title: \"Experience\",\n            icon: _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n            description: \"Your work experience\",\n            estimatedTime: 8,\n            required: true\n        },\n        {\n            id: 3,\n            title: \"Projects\",\n            icon: _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n            description: \"Showcase your projects\",\n            estimatedTime: 4,\n            required: false\n        },\n        {\n            id: 4,\n            title: \"Skills\",\n            icon: _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"],\n            description: \"Your technical & soft skills\",\n            estimatedTime: 3,\n            required: false\n        },\n        {\n            id: 5,\n            title: \"Review & Generate\",\n            icon: _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n            description: \"Finalize your resume\",\n            estimatedTime: 2,\n            required: false\n        }\n    ];\n    const atsAnalysis = (0,_hooks_useATSAnalysis__WEBPACK_IMPORTED_MODULE_17__[\"default\"])(formData);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EnhancedResumeBuilder.useEffect\": ()=>{\n            // Check for backup data from auth flow first\n            const backupData = localStorage.getItem('resumeFormDataBackup');\n            const authTimestamp = localStorage.getItem('authFlowTimestamp');\n            const savedData = localStorage.getItem('resumeFormData');\n            // If backup data exists and is recent (within 10 minutes), use it\n            if (backupData && authTimestamp) {\n                const timeDiff = Date.now() - parseInt(authTimestamp);\n                if (timeDiff < 10 * 60 * 1000) {\n                    try {\n                        const parsed = JSON.parse(backupData);\n                        setFormData(parsed);\n                        setLastSaved(new Date());\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success('Your work has been restored after sign-in!');\n                        // Clean up backup data\n                        localStorage.removeItem('resumeFormDataBackup');\n                        localStorage.removeItem('authFlowTimestamp');\n                        // Update main storage\n                        localStorage.setItem('resumeFormData', backupData);\n                        localStorage.setItem('resumeLastSaved', new Date().toISOString());\n                        return;\n                    } catch (error) {\n                        console.error('Failed to load backup data:', error);\n                    }\n                }\n            }\n            // Fallback to regular saved data\n            if (savedData) {\n                try {\n                    const parsed = JSON.parse(savedData);\n                    setFormData(parsed);\n                    setLastSaved(new Date(localStorage.getItem('resumeLastSaved') || Date.now()));\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success('Previous work restored!');\n                } catch (error) {\n                    console.error('Failed to load saved data:', error);\n                }\n            }\n            // Clean up old backup data\n            localStorage.removeItem('resumeFormDataBackup');\n            localStorage.removeItem('authFlowTimestamp');\n        }\n    }[\"EnhancedResumeBuilder.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EnhancedResumeBuilder.useEffect\": ()=>{\n            if (autoSaveEnabled && formData) {\n                // Make form data globally accessible for auth flow\n                if (true) {\n                    window.formData = formData;\n                }\n                const timeoutId = setTimeout({\n                    \"EnhancedResumeBuilder.useEffect.timeoutId\": ()=>{\n                        localStorage.setItem('resumeFormData', JSON.stringify(formData));\n                        localStorage.setItem('resumeLastSaved', new Date().toISOString());\n                        setLastSaved(new Date());\n                    }\n                }[\"EnhancedResumeBuilder.useEffect.timeoutId\"], 2000);\n                return ({\n                    \"EnhancedResumeBuilder.useEffect\": ()=>clearTimeout(timeoutId)\n                })[\"EnhancedResumeBuilder.useEffect\"];\n            }\n        }\n    }[\"EnhancedResumeBuilder.useEffect\"], [\n        formData,\n        autoSaveEnabled\n    ]);\n    const validateStep = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[validateStep]\": (stepIndex)=>{\n            const errors = {};\n            switch(stepIndex){\n                case 0:\n                    var _formData_personal_firstName, _formData_personal_lastName, _formData_personal_email;\n                    if (!((_formData_personal_firstName = formData.personal.firstName) === null || _formData_personal_firstName === void 0 ? void 0 : _formData_personal_firstName.trim())) errors.firstName = 'First name is required';\n                    if (!((_formData_personal_lastName = formData.personal.lastName) === null || _formData_personal_lastName === void 0 ? void 0 : _formData_personal_lastName.trim())) errors.lastName = 'Last name is required';\n                    if (!((_formData_personal_email = formData.personal.email) === null || _formData_personal_email === void 0 ? void 0 : _formData_personal_email.trim())) errors.email = 'Email is required';\n                    else if (!/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(formData.personal.email)) errors.email = 'Please enter a valid email address';\n                    break;\n                case 1:\n                    var _formData_education;\n                    const validEducation = (_formData_education = formData.education) === null || _formData_education === void 0 ? void 0 : _formData_education.filter({\n                        \"EnhancedResumeBuilder.useCallback[validateStep]\": (edu)=>{\n                            var _edu_degree, _edu_institution;\n                            return ((_edu_degree = edu.degree) === null || _edu_degree === void 0 ? void 0 : _edu_degree.trim()) && ((_edu_institution = edu.institution) === null || _edu_institution === void 0 ? void 0 : _edu_institution.trim());\n                        }\n                    }[\"EnhancedResumeBuilder.useCallback[validateStep]\"]);\n                    if (!validEducation || validEducation.length === 0) errors.education = 'At least one education entry with degree and institution is required';\n                    break;\n                case 2:\n                    var _formData_experience;\n                    const validExperience = (_formData_experience = formData.experience) === null || _formData_experience === void 0 ? void 0 : _formData_experience.filter({\n                        \"EnhancedResumeBuilder.useCallback[validateStep]\": (exp)=>{\n                            var _exp_title, _exp_company;\n                            return ((_exp_title = exp.title) === null || _exp_title === void 0 ? void 0 : _exp_title.trim()) && ((_exp_company = exp.company) === null || _exp_company === void 0 ? void 0 : _exp_company.trim());\n                        }\n                    }[\"EnhancedResumeBuilder.useCallback[validateStep]\"]);\n                    if (!validExperience || validExperience.length === 0) errors.experience = 'At least one work experience entry with job title and company is required';\n                    break;\n                case 3:\n                    break;\n                case 4:\n                    break;\n                default:\n                    break;\n            }\n            return errors;\n        }\n    }[\"EnhancedResumeBuilder.useCallback[validateStep]\"], [\n        formData\n    ]);\n    const canProceedToNextStep = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[canProceedToNextStep]\": (stepIndex)=>{\n            const errors = validateStep(stepIndex);\n            return Object.keys(errors).length === 0;\n        }\n    }[\"EnhancedResumeBuilder.useCallback[canProceedToNextStep]\"], [\n        validateStep\n    ]);\n    const getCompletedSteps = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[getCompletedSteps]\": ()=>{\n            const completed = [];\n            for(let i = 0; i < currentStep; i++){\n                const errors = validateStep(i);\n                if (Object.keys(errors).length === 0) {\n                    completed.push(i);\n                }\n            }\n            return completed;\n        }\n    }[\"EnhancedResumeBuilder.useCallback[getCompletedSteps]\"], [\n        currentStep,\n        validateStep\n    ]);\n    const getStepCompletionStatus = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[getStepCompletionStatus]\": ()=>{\n            const status = {};\n            steps.forEach({\n                \"EnhancedResumeBuilder.useCallback[getStepCompletionStatus]\": (step, index)=>{\n                    const errors = validateStep(index);\n                    status[index] = Object.keys(errors).length === 0;\n                }\n            }[\"EnhancedResumeBuilder.useCallback[getStepCompletionStatus]\"]);\n            return status;\n        }\n    }[\"EnhancedResumeBuilder.useCallback[getStepCompletionStatus]\"], [\n        steps,\n        validateStep\n    ]);\n    const canGenerateResume = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[canGenerateResume]\": ()=>{\n            for(let i = 0; i < steps.length - 1; i++){\n                if (!canProceedToNextStep(i)) return false;\n            }\n            return true;\n        }\n    }[\"EnhancedResumeBuilder.useCallback[canGenerateResume]\"], [\n        canProceedToNextStep,\n        steps.length\n    ]);\n    const updateFormData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[updateFormData]\": function(section, field, value) {\n            let index = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : null;\n            setFormData({\n                \"EnhancedResumeBuilder.useCallback[updateFormData]\": (prev)=>{\n                    let newData = {\n                        ...prev\n                    };\n                    if (index !== null && Array.isArray(prev[section])) {\n                        const newArray = [\n                            ...prev[section]\n                        ];\n                        newArray[index] = {\n                            ...newArray[index],\n                            [field]: value\n                        };\n                        newData[section] = newArray;\n                    } else if (typeof prev[section] === 'object' && !Array.isArray(prev[section])) {\n                        newData[section] = {\n                            ...prev[section],\n                            [field]: value\n                        };\n                    } else {\n                        newData[field] = value;\n                    }\n                    return newData;\n                }\n            }[\"EnhancedResumeBuilder.useCallback[updateFormData]\"]);\n            if (validationErrors[field]) {\n                setValidationErrors({\n                    \"EnhancedResumeBuilder.useCallback[updateFormData]\": (prev)=>{\n                        const newErrors = {\n                            ...prev\n                        };\n                        delete newErrors[field];\n                        return newErrors;\n                    }\n                }[\"EnhancedResumeBuilder.useCallback[updateFormData]\"]);\n            }\n        }\n    }[\"EnhancedResumeBuilder.useCallback[updateFormData]\"], [\n        validationErrors\n    ]);\n    const addArrayItem = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[addArrayItem]\": (section, template)=>{\n            setFormData({\n                \"EnhancedResumeBuilder.useCallback[addArrayItem]\": (prev)=>({\n                        ...prev,\n                        [section]: [\n                            ...prev[section],\n                            {\n                                ...template,\n                                id: Math.random().toString(36).substring(2, 11)\n                            }\n                        ]\n                    })\n            }[\"EnhancedResumeBuilder.useCallback[addArrayItem]\"]);\n        }\n    }[\"EnhancedResumeBuilder.useCallback[addArrayItem]\"], []);\n    const removeArrayItem = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[removeArrayItem]\": (section, id)=>{\n            setFormData({\n                \"EnhancedResumeBuilder.useCallback[removeArrayItem]\": (prev)=>({\n                        ...prev,\n                        [section]: prev[section].filter({\n                            \"EnhancedResumeBuilder.useCallback[removeArrayItem]\": (item)=>item.id !== id\n                        }[\"EnhancedResumeBuilder.useCallback[removeArrayItem]\"])\n                    })\n            }[\"EnhancedResumeBuilder.useCallback[removeArrayItem]\"]);\n        }\n    }[\"EnhancedResumeBuilder.useCallback[removeArrayItem]\"], []);\n    const nextStep = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[nextStep]\": ()=>{\n            const errors = validateStep(currentStep);\n            if (Object.keys(errors).length > 0) {\n                setValidationErrors(errors);\n                setShowValidationErrors(true);\n                const errorMessages = Object.values(errors);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(errorMessages[0]);\n                return;\n            }\n            setCompletedSteps({\n                \"EnhancedResumeBuilder.useCallback[nextStep]\": (prev)=>[\n                        ...new Set([\n                            ...prev,\n                            currentStep\n                        ])\n                    ]\n            }[\"EnhancedResumeBuilder.useCallback[nextStep]\"]);\n            setValidationErrors({});\n            setShowValidationErrors(false);\n            if (currentStep < steps.length - 1) {\n                setCurrentStep(currentStep + 1);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"\".concat(steps[currentStep].title, \" completed!\"));\n            }\n        }\n    }[\"EnhancedResumeBuilder.useCallback[nextStep]\"], [\n        currentStep,\n        validateStep,\n        steps\n    ]);\n    const prevStep = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[prevStep]\": ()=>{\n            setValidationErrors({});\n            setShowValidationErrors(false);\n            if (currentStep > 0) setCurrentStep(currentStep - 1);\n        }\n    }[\"EnhancedResumeBuilder.useCallback[prevStep]\"], [\n        currentStep\n    ]);\n    const handleStepClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[handleStepClick]\": (stepIndex)=>{\n            if (stepIndex <= currentStep || completedSteps.includes(stepIndex)) {\n                setCurrentStep(stepIndex);\n                setValidationErrors({});\n                setShowValidationErrors(false);\n            }\n        }\n    }[\"EnhancedResumeBuilder.useCallback[handleStepClick]\"], [\n        currentStep,\n        completedSteps\n    ]);\n    const generateResume = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[generateResume]\": async ()=>{\n            setValidationErrors({});\n            setShowValidationErrors(false);\n            if (!canGenerateResume()) {\n                const allErrors = {};\n                for(let i = 0; i < steps.length - 1; i++){\n                    const stepErrors = validateStep(i);\n                    Object.assign(allErrors, stepErrors);\n                }\n                setValidationErrors(allErrors);\n                setShowValidationErrors(true);\n                for(let i = 0; i < steps.length - 1; i++){\n                    const stepErrors = validateStep(i);\n                    if (Object.keys(stepErrors).length > 0) {\n                        setCurrentStep(i);\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Please complete all required fields in \".concat(steps[i].title));\n                        return;\n                    }\n                }\n                return;\n            }\n            try {\n                setIsGenerating(true);\n                setShowProgressBar(true);\n                setResumeGenerated(false);\n                const response = await fetch('/api/generate-resume', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        formData,\n                        templateId: selectedTemplate\n                    })\n                });\n                const contentType = response.headers.get('content-type');\n                if (!contentType || !contentType.includes('application/json')) throw new Error('Server returned non-JSON response');\n                const data = await response.json();\n                if (!response.ok) throw new Error(data.error || 'Failed to generate resume');\n                if (!data.resumeData || !data.downloadUrl) throw new Error('Generated resume data is incomplete');\n                setResumeUrl(data.downloadUrl);\n                setResumeData(data.resumeData);\n                setAtsScore(data.atsScore || 75);\n                setSuggestions(data.suggestions || []);\n                setResumeGenerated(true);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success('Resume generated successfully!');\n            } catch (error) {\n                console.error('Resume generation error:', error);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(error.message || 'Failed to generate resume');\n            } finally{\n                setIsGenerating(false);\n                setShowProgressBar(false);\n            }\n        }\n    }[\"EnhancedResumeBuilder.useCallback[generateResume]\"], [\n        canGenerateResume,\n        formData,\n        steps,\n        validateStep,\n        selectedTemplate\n    ]);\n    const handleSave = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[handleSave]\": ()=>{\n            localStorage.setItem('resumeFormData', JSON.stringify(formData));\n            localStorage.setItem('resumeLastSaved', new Date().toISOString());\n            setLastSaved(new Date());\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success('Progress saved!');\n        }\n    }[\"EnhancedResumeBuilder.useCallback[handleSave]\"], [\n        formData\n    ]);\n    const handleAISuggest = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[handleAISuggest]\": async (section)=>{\n            if (!aiSuggestionsEnabled) return;\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.loading('Getting AI suggestions...');\n            try {\n                await new Promise({\n                    \"EnhancedResumeBuilder.useCallback[handleAISuggest]\": (resolve)=>setTimeout(resolve, 1500)\n                }[\"EnhancedResumeBuilder.useCallback[handleAISuggest]\"]);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.dismiss();\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success('AI suggestions applied!');\n            } catch (error) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.dismiss();\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error('Failed to get AI suggestions');\n            }\n        }\n    }[\"EnhancedResumeBuilder.useCallback[handleAISuggest]\"], [\n        aiSuggestionsEnabled\n    ]);\n    // Set up event listener for resume generation from ReviewForm\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EnhancedResumeBuilder.useEffect\": ()=>{\n            const handleGenerateResume = {\n                \"EnhancedResumeBuilder.useEffect.handleGenerateResume\": ()=>{\n                    // Check if we can generate resume and call the function\n                    if (canGenerateResume()) {\n                        generateResume();\n                    } else {\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error('Please complete all required sections before generating your resume.');\n                    }\n                }\n            }[\"EnhancedResumeBuilder.useEffect.handleGenerateResume\"];\n            if (true) {\n                window.addEventListener('generateResume', handleGenerateResume);\n                return ({\n                    \"EnhancedResumeBuilder.useEffect\": ()=>window.removeEventListener('generateResume', handleGenerateResume)\n                })[\"EnhancedResumeBuilder.useEffect\"];\n            }\n        }\n    }[\"EnhancedResumeBuilder.useEffect\"], [\n        canGenerateResume,\n        generateResume\n    ]);\n    const getStepValidation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[getStepValidation]\": ()=>{\n            const validation = {};\n            steps.forEach({\n                \"EnhancedResumeBuilder.useCallback[getStepValidation]\": (step, index)=>{\n                    const errors = validateStep(index);\n                    validation[index] = Object.keys(errors).length === 0;\n                }\n            }[\"EnhancedResumeBuilder.useCallback[getStepValidation]\"]);\n            return validation;\n        }\n    }[\"EnhancedResumeBuilder.useCallback[getStepValidation]\"], [\n        steps,\n        validateStep\n    ]);\n    const renderStepContent = ()=>{\n        const commonProps = {\n            formData,\n            updateFormData,\n            addArrayItem,\n            removeArrayItem,\n            atsAnalysis,\n            validationErrors,\n            showValidationErrors,\n            onSave: handleSave,\n            onAISuggest: handleAISuggest\n        };\n        switch(currentStep){\n            case 0:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_ResumeFormComponents__WEBPACK_IMPORTED_MODULE_5__.PersonalInfoForm, {\n                    ...commonProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                    lineNumber: 383,\n                    columnNumber: 16\n                }, undefined);\n            case 1:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_ResumeFormComponents__WEBPACK_IMPORTED_MODULE_5__.EducationForm, {\n                    ...commonProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                    lineNumber: 385,\n                    columnNumber: 16\n                }, undefined);\n            case 2:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_ExperienceForm__WEBPACK_IMPORTED_MODULE_6__.EnhancedExperienceForm, {\n                    ...commonProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                    lineNumber: 387,\n                    columnNumber: 16\n                }, undefined);\n            case 3:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_ProjectsForm__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    ...commonProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                    lineNumber: 389,\n                    columnNumber: 16\n                }, undefined);\n            case 4:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_SkillsForm__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    ...commonProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                    lineNumber: 391,\n                    columnNumber: 16\n                }, undefined);\n            case 5:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_ReviewForm__WEBPACK_IMPORTED_MODULE_10__.EnhancedReviewForm, {\n                    ...commonProps,\n                    selectedTemplate: selectedTemplate,\n                    onTemplateSelect: setSelectedTemplate\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                    lineNumber: 393,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return null;\n        }\n    };\n    if (resumeGenerated && resumeData && resumeUrl) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SuccessScreen__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n            formData: formData,\n            resumeData: resumeData,\n            onStartOver: ()=>{\n                setResumeGenerated(false);\n                setResumeData(null);\n                setResumeUrl(\"\");\n                setCurrentStep(0);\n            },\n            onEditResume: ()=>{\n                setResumeGenerated(false);\n                setCurrentStep(4); // Go back to review step\n            }\n        }, void 0, false, {\n            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n            lineNumber: 401,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-b from-black to-[#0A0A0A] text-white relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 opacity-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 bg-[url('/grid.svg')] [mask-image:radial-gradient(ellipse_at_center,white,transparent)]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                    lineNumber: 421,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                lineNumber: 420,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_common_ProgressBar__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                isVisible: showProgressBar,\n                onComplete: ()=>setShowProgressBar(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                lineNumber: 423,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 pb-32\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 py-8\",\n                    children: [\n                        !hideHeader && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_23__.motion.div, {\n                            className: \"text-center mb-8\",\n                            initial: {\n                                opacity: 0,\n                                y: -20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center gap-3 mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    className: \"h-10 w-10 text-neural-pink animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                    lineNumber: 430,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-neural-pink opacity-20 rounded-full blur-md\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                    lineNumber: 431,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                            lineNumber: 429,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-4xl lg:text-5xl font-bold text-white\",\n                                            children: \"Enhanced Resume Builder\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                            lineNumber: 433,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                    lineNumber: 428,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 text-lg max-w-2xl mx-auto\",\n                                    children: \"Create professional, ATS-friendly resumes with our enhanced AI-powered builder\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                    lineNumber: 435,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                            lineNumber: 427,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col xl:flex-row gap-8 max-w-7xl mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 max-w-4xl\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"sticky top-4 z-30 bg-gray-900/95 backdrop-blur-sm rounded-2xl border border-gray-700/50 mb-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EnhancedProgressIndicator__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                steps: steps,\n                                                currentStep: currentStep,\n                                                completedSteps: completedSteps,\n                                                onStepClick: handleStepClick,\n                                                variant: \"minimal\",\n                                                showLabels: true,\n                                                showProgress: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                lineNumber: 443,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                            lineNumber: 442,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_25__.AnimatePresence, {\n                                                mode: \"wait\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_23__.motion.div, {\n                                                    initial: {\n                                                        opacity: 0,\n                                                        x: 20\n                                                    },\n                                                    animate: {\n                                                        opacity: 1,\n                                                        x: 0\n                                                    },\n                                                    exit: {\n                                                        opacity: 0,\n                                                        x: -20\n                                                    },\n                                                    transition: {\n                                                        duration: 0.3\n                                                    },\n                                                    children: renderStepContent()\n                                                }, currentStep, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                    lineNumber: 457,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                lineNumber: 456,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                            lineNumber: 455,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                    lineNumber: 440,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden xl:block xl:w-80 xl:flex-shrink-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"sticky top-4 h-[calc(100vh-2rem)]\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SimpleResumePreview__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            formData: formData,\n                                            selectedTemplate: selectedTemplate,\n                                            showPreview: showPreview,\n                                            onTogglePreview: ()=>setShowPreview(!showPreview),\n                                            onOpenFullscreen: ()=>setShowFullscreenPreview(true)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                            lineNumber: 468,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                        lineNumber: 467,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                    lineNumber: 465,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"xl:hidden mt-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_23__.motion.div, {\n                                        className: \"bg-gray-900/60 backdrop-blur-md rounded-2xl p-6 border border-white/10\",\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            delay: 0.2\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                className: \"h-5 w-5 text-neural-blue\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                                lineNumber: 488,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            \"Resume Preview\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                        lineNumber: 487,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setShowPreview(!showPreview),\n                                                        className: \"px-3 py-1.5 rounded-lg text-sm font-medium transition-colors \".concat(showPreview ? 'bg-neural-purple text-white' : 'bg-gray-800 text-gray-300 hover:bg-gray-700'),\n                                                        children: showPreview ? 'Hide' : 'Show'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                        lineNumber: 491,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                lineNumber: 486,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            showPreview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"max-h-[600px] overflow-y-auto border border-white/10 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ResumePreview__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    formData: formData,\n                                                    selectedTemplate: selectedTemplate\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                    lineNumber: 502,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                lineNumber: 501,\n                                                columnNumber: 19\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-12 text-gray-400\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                        className: \"h-12 w-12 mx-auto mb-3 opacity-30\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                        lineNumber: 506,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm\",\n                                                        children: 'Click \"Show\" to preview your resume'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                        lineNumber: 507,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                lineNumber: 505,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                        lineNumber: 480,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                    lineNumber: 479,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                            lineNumber: 438,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                    lineNumber: 425,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                lineNumber: 424,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_layout_SmartNavigationBar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                currentStep: currentStep,\n                totalSteps: steps.length,\n                onPrevious: prevStep,\n                onNext: nextStep,\n                onGenerate: generateResume,\n                onSave: handleSave,\n                onPreview: ()=>setShowPreview(!showPreview),\n                isGenerating: isGenerating,\n                canProceed: currentStep === steps.length - 1 ? canGenerateResume() : canProceedToNextStep(currentStep),\n                showPreview: showPreview,\n                steps: steps,\n                formData: formData,\n                atsScore: atsAnalysis.overallScore,\n                autoSaveEnabled: autoSaveEnabled\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                lineNumber: 515,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n        lineNumber: 419,\n        columnNumber: 5\n    }, undefined);\n};\n_s(EnhancedResumeBuilder, \"fDIam3iIEkONwVfjFA/qcKl+co8=\", false, function() {\n    return [\n        _hooks_useATSAnalysis__WEBPACK_IMPORTED_MODULE_17__[\"default\"]\n    ];\n});\n_c = EnhancedResumeBuilder;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (EnhancedResumeBuilder);\nvar _c;\n$RefreshReg$(_c, \"EnhancedResumeBuilder\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3Jlc3VtZS9SZXN1bWVCdWlsZGVyLmpzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQ2dFO0FBQ1I7QUFHbEM7QUFDa0I7QUFDYztBQUNRO0FBQ3NJO0FBQ3BJO0FBQ1E7QUFDaEI7QUFDSjtBQUNJO0FBQ1k7QUFDZDtBQUNOO0FBQ0k7QUFDSTtBQUNaO0FBQ1E7QUFFcEQsTUFBTTRDLHdCQUF3QjtRQUFDLEVBQUVDLGFBQWEsS0FBSyxFQUFFOztJQUNuRCxNQUFNLENBQUNDLGFBQWFDLGVBQWUsR0FBRzlDLCtDQUFRQSxDQUFDO0lBQy9DLE1BQU0sQ0FBQytDLGtCQUFrQkMsb0JBQW9CLEdBQUdoRCwrQ0FBUUEsQ0FBQztJQUN6RCxNQUFNLENBQUNpRCxVQUFVQyxZQUFZLEdBQUdsRCwrQ0FBUUEsQ0FBQztRQUN2Q21ELFVBQVU7WUFDUkMsV0FBVztZQUNYQyxVQUFVO1lBQ1ZDLE9BQU87WUFDUEMsT0FBTztZQUNQQyxVQUFVO1lBQ1ZDLFVBQVU7WUFDVkMsV0FBVztZQUNYQyxTQUFTO1lBQ1RDLGNBQWM7UUFDaEI7UUFDQUMsV0FBVztZQUNUO2dCQUFFQyxJQUFJO2dCQUFHQyxRQUFRO2dCQUFJQyxhQUFhO2dCQUFJUixVQUFVO2dCQUFJUyxXQUFXO2dCQUFJQyxTQUFTO2dCQUFJQyxLQUFLO2dCQUFJQyxVQUFVO1lBQUc7U0FDdkc7UUFDREMsWUFBWTtZQUNWO2dCQUFFUCxJQUFJO2dCQUFHUSxPQUFPO2dCQUFJQyxTQUFTO2dCQUFJZixVQUFVO2dCQUFJUyxXQUFXO2dCQUFJQyxTQUFTO2dCQUFJTSxTQUFTO2dCQUFPQyxhQUFhO1lBQUc7U0FDNUc7UUFDREMsUUFBUTtZQUFFQyxXQUFXLEVBQUU7WUFBRUMsV0FBVyxFQUFFO1lBQUVDLGdCQUFnQixFQUFFO1FBQUM7UUFDM0RDLFVBQVU7WUFDUjtnQkFBRWhCLElBQUk7Z0JBQUdpQixNQUFNO2dCQUFJTixhQUFhO2dCQUFJTyxjQUFjO2dCQUFJQyxNQUFNO1lBQUc7U0FDaEU7UUFDREMsZ0JBQWdCO0lBQ2xCO0lBQ0EsTUFBTSxDQUFDQyxhQUFhQyxlQUFlLEdBQUdwRiwrQ0FBUUEsQ0FBQztJQUMvQyxNQUFNLENBQUNxRixjQUFjQyxnQkFBZ0IsR0FBR3RGLCtDQUFRQSxDQUFDO0lBQ2pELE1BQU0sQ0FBQ3VGLGlCQUFpQkMsbUJBQW1CLEdBQUd4RiwrQ0FBUUEsQ0FBQztJQUN2RCxNQUFNLENBQUN5RixpQkFBaUJDLG1CQUFtQixHQUFHMUYsK0NBQVFBLENBQUM7SUFDdkQsTUFBTSxDQUFDMkYsa0JBQWtCQyxvQkFBb0IsR0FBRzVGLCtDQUFRQSxDQUFDLENBQUM7SUFDMUQsTUFBTSxDQUFDNkYsc0JBQXNCQyx3QkFBd0IsR0FBRzlGLCtDQUFRQSxDQUFDO0lBQ2pFLE1BQU0sQ0FBQytGLGdCQUFnQkMsa0JBQWtCLEdBQUdoRywrQ0FBUUEsQ0FBQyxFQUFFO0lBQ3ZELE1BQU0sQ0FBQ2lHLGlCQUFpQkMsbUJBQW1CLEdBQUdsRywrQ0FBUUEsQ0FBQztJQUN2RCxNQUFNLENBQUNtRyxXQUFXQyxhQUFhLEdBQUdwRywrQ0FBUUEsQ0FBQztJQUMzQyxNQUFNLENBQUNxRyxZQUFZQyxjQUFjLEdBQUd0RywrQ0FBUUEsQ0FBQztJQUM3QyxNQUFNLENBQUN1RyxXQUFXQyxhQUFhLEdBQUd4RywrQ0FBUUEsQ0FBQztJQUMzQyxNQUFNLENBQUN5RyxVQUFVQyxZQUFZLEdBQUcxRywrQ0FBUUEsQ0FBQztJQUN6QyxNQUFNLENBQUMyRyxhQUFhQyxlQUFlLEdBQUc1RywrQ0FBUUEsQ0FBQyxFQUFFO0lBQ2pELE1BQU0sQ0FBQzZHLHNCQUFzQkMsd0JBQXdCLEdBQUc5RywrQ0FBUUEsQ0FBQztJQUNqRSxNQUFNLENBQUMrRyxpQkFBaUJDLG1CQUFtQixHQUFHaEgsK0NBQVFBLENBQUM7SUFDdkQsTUFBTSxDQUFDaUgsV0FBV0MsYUFBYSxHQUFHbEgsK0NBQVFBLENBQUM7SUFFM0MsTUFBTW1ILFFBQVE7UUFDWjtZQUFFckQsSUFBSTtZQUFHUSxPQUFPO1lBQXdCOEMsTUFBTS9HLDhNQUFJQTtZQUFFb0UsYUFBYTtZQUEwQjRDLGVBQWU7WUFBR0MsVUFBVTtRQUFLO1FBQzVIO1lBQUV4RCxJQUFJO1lBQUdRLE9BQU87WUFBYThDLE1BQU05Ryw4TUFBYUE7WUFBRW1FLGFBQWE7WUFBNEI0QyxlQUFlO1lBQUdDLFVBQVU7UUFBSztRQUM1SDtZQUFFeEQsSUFBSTtZQUFHUSxPQUFPO1lBQWM4QyxNQUFNN0csOE1BQVNBO1lBQUVrRSxhQUFhO1lBQXdCNEMsZUFBZTtZQUFHQyxVQUFVO1FBQUs7UUFDckg7WUFBRXhELElBQUk7WUFBR1EsT0FBTztZQUFZOEMsTUFBTTNHLDhNQUFRQTtZQUFFZ0UsYUFBYTtZQUEwQjRDLGVBQWU7WUFBR0MsVUFBVTtRQUFNO1FBQ3JIO1lBQUV4RCxJQUFJO1lBQUdRLE9BQU87WUFBVThDLE1BQU01Ryw4TUFBS0E7WUFBRWlFLGFBQWE7WUFBZ0M0QyxlQUFlO1lBQUdDLFVBQVU7UUFBTTtRQUN0SDtZQUFFeEQsSUFBSTtZQUFHUSxPQUFPO1lBQXFCOEMsTUFBTTNHLDhNQUFRQTtZQUFFZ0UsYUFBYTtZQUF3QjRDLGVBQWU7WUFBR0MsVUFBVTtRQUFNO0tBQzdIO0lBRUQsTUFBTUMsY0FBYzdFLGtFQUFjQSxDQUFDTztJQUVuQ2hELGdEQUFTQTsyQ0FBQztZQUNSLDZDQUE2QztZQUM3QyxNQUFNdUgsYUFBYUMsYUFBYUMsT0FBTyxDQUFDO1lBQ3hDLE1BQU1DLGdCQUFnQkYsYUFBYUMsT0FBTyxDQUFDO1lBQzNDLE1BQU1FLFlBQVlILGFBQWFDLE9BQU8sQ0FBQztZQUV2QyxrRUFBa0U7WUFDbEUsSUFBSUYsY0FBY0csZUFBZTtnQkFDL0IsTUFBTUUsV0FBV0MsS0FBS0MsR0FBRyxLQUFLQyxTQUFTTDtnQkFDdkMsSUFBSUUsV0FBVyxLQUFLLEtBQUssTUFBTTtvQkFDN0IsSUFBSTt3QkFDRixNQUFNSSxTQUFTQyxLQUFLQyxLQUFLLENBQUNYO3dCQUMxQnRFLFlBQVkrRTt3QkFDWjdCLGFBQWEsSUFBSTBCO3dCQUNqQjFHLGtEQUFLQSxDQUFDZ0gsT0FBTyxDQUFDO3dCQUNkLHVCQUF1Qjt3QkFDdkJYLGFBQWFZLFVBQVUsQ0FBQzt3QkFDeEJaLGFBQWFZLFVBQVUsQ0FBQzt3QkFDeEIsc0JBQXNCO3dCQUN0QlosYUFBYWEsT0FBTyxDQUFDLGtCQUFrQmQ7d0JBQ3ZDQyxhQUFhYSxPQUFPLENBQUMsbUJBQW1CLElBQUlSLE9BQU9TLFdBQVc7d0JBQzlEO29CQUNGLEVBQUUsT0FBT0MsT0FBTzt3QkFDZEMsUUFBUUQsS0FBSyxDQUFDLCtCQUErQkE7b0JBQy9DO2dCQUNGO1lBQ0Y7WUFFQSxpQ0FBaUM7WUFDakMsSUFBSVosV0FBVztnQkFDYixJQUFJO29CQUNGLE1BQU1LLFNBQVNDLEtBQUtDLEtBQUssQ0FBQ1A7b0JBQzFCMUUsWUFBWStFO29CQUNaN0IsYUFBYSxJQUFJMEIsS0FBS0wsYUFBYUMsT0FBTyxDQUFDLHNCQUFzQkksS0FBS0MsR0FBRztvQkFDekUzRyxrREFBS0EsQ0FBQ2dILE9BQU8sQ0FBQztnQkFDaEIsRUFBRSxPQUFPSSxPQUFPO29CQUNkQyxRQUFRRCxLQUFLLENBQUMsOEJBQThCQTtnQkFDOUM7WUFDRjtZQUVBLDJCQUEyQjtZQUMzQmYsYUFBYVksVUFBVSxDQUFDO1lBQ3hCWixhQUFhWSxVQUFVLENBQUM7UUFFMUI7MENBQUcsRUFBRTtJQUlMcEksZ0RBQVNBOzJDQUFDO1lBQ1IsSUFBSWdHLG1CQUFtQmhELFVBQVU7Z0JBQy9CLG1EQUFtRDtnQkFDbkQsSUFBSSxJQUE2QixFQUFFO29CQUNqQ3lGLE9BQU96RixRQUFRLEdBQUdBO2dCQUNwQjtnQkFFQSxNQUFNMEYsWUFBWUM7aUVBQVc7d0JBQzNCbkIsYUFBYWEsT0FBTyxDQUFDLGtCQUFrQkosS0FBS1csU0FBUyxDQUFDNUY7d0JBQ3REd0UsYUFBYWEsT0FBTyxDQUFDLG1CQUFtQixJQUFJUixPQUFPUyxXQUFXO3dCQUM5RG5DLGFBQWEsSUFBSTBCO29CQUNuQjtnRUFBRztnQkFDSDt1REFBTyxJQUFNZ0IsYUFBYUg7O1lBQzVCO1FBQ0Y7MENBQUc7UUFBQzFGO1FBQVVnRDtLQUFnQjtJQUU5QixNQUFNOEMsZUFBZTdJLGtEQUFXQTsyREFBQyxDQUFDOEk7WUFDaEMsTUFBTUMsU0FBUyxDQUFDO1lBQ2hCLE9BQVFEO2dCQUNOLEtBQUs7d0JBQ0UvRiw4QkFDQUEsNkJBQ0FBO29CQUZMLElBQUksR0FBQ0EsK0JBQUFBLFNBQVNFLFFBQVEsQ0FBQ0MsU0FBUyxjQUEzQkgsbURBQUFBLDZCQUE2QmlHLElBQUksS0FBSUQsT0FBTzdGLFNBQVMsR0FBRztvQkFDN0QsSUFBSSxHQUFDSCw4QkFBQUEsU0FBU0UsUUFBUSxDQUFDRSxRQUFRLGNBQTFCSixrREFBQUEsNEJBQTRCaUcsSUFBSSxLQUFJRCxPQUFPNUYsUUFBUSxHQUFHO29CQUMzRCxJQUFJLEdBQUNKLDJCQUFBQSxTQUFTRSxRQUFRLENBQUNHLEtBQUssY0FBdkJMLCtDQUFBQSx5QkFBeUJpRyxJQUFJLEtBQUlELE9BQU8zRixLQUFLLEdBQUc7eUJBQ2hELElBQUksQ0FBQyw2QkFBNkI2RixJQUFJLENBQUNsRyxTQUFTRSxRQUFRLENBQUNHLEtBQUssR0FBRzJGLE9BQU8zRixLQUFLLEdBQUc7b0JBQ3JGO2dCQUNGLEtBQUs7d0JBQ29CTDtvQkFBdkIsTUFBTW1HLGtCQUFpQm5HLHNCQUFBQSxTQUFTWSxTQUFTLGNBQWxCWiwwQ0FBQUEsb0JBQW9Cb0csTUFBTTsyRUFBQ0MsQ0FBQUE7Z0NBQU9BLGFBQXNCQTttQ0FBdEJBLEVBQUFBLGNBQUFBLElBQUl2RixNQUFNLGNBQVZ1RixrQ0FBQUEsWUFBWUosSUFBSSxTQUFNSSxtQkFBQUEsSUFBSXRGLFdBQVcsY0FBZnNGLHVDQUFBQSxpQkFBaUJKLElBQUk7OztvQkFDcEcsSUFBSSxDQUFDRSxrQkFBa0JBLGVBQWVHLE1BQU0sS0FBSyxHQUFHTixPQUFPcEYsU0FBUyxHQUFHO29CQUN2RTtnQkFDRixLQUFLO3dCQUNxQlo7b0JBQXhCLE1BQU11RyxtQkFBa0J2Ryx1QkFBQUEsU0FBU29CLFVBQVUsY0FBbkJwQiwyQ0FBQUEscUJBQXFCb0csTUFBTTsyRUFBQ0ksQ0FBQUE7Z0NBQU9BLFlBQXFCQTttQ0FBckJBLEVBQUFBLGFBQUFBLElBQUluRixLQUFLLGNBQVRtRixpQ0FBQUEsV0FBV1AsSUFBSSxTQUFNTyxlQUFBQSxJQUFJbEYsT0FBTyxjQUFYa0YsbUNBQUFBLGFBQWFQLElBQUk7OztvQkFDakcsSUFBSSxDQUFDTSxtQkFBbUJBLGdCQUFnQkQsTUFBTSxLQUFLLEdBQUdOLE9BQU81RSxVQUFVLEdBQUc7b0JBQzFFO2dCQUNGLEtBQUs7b0JBRUg7Z0JBQ0YsS0FBSztvQkFFSDtnQkFDRjtvQkFDRTtZQUNKO1lBQ0EsT0FBTzRFO1FBQ1Q7MERBQUc7UUFBQ2hHO0tBQVM7SUFFYixNQUFNeUcsdUJBQXVCeEosa0RBQVdBO21FQUFDLENBQUM4STtZQUN4QyxNQUFNQyxTQUFTRixhQUFhQztZQUM1QixPQUFPVyxPQUFPQyxJQUFJLENBQUNYLFFBQVFNLE1BQU0sS0FBSztRQUN4QztrRUFBRztRQUFDUjtLQUFhO0lBRWpCLE1BQU1jLG9CQUFvQjNKLGtEQUFXQTtnRUFBQztZQUNwQyxNQUFNNEosWUFBWSxFQUFFO1lBQ3BCLElBQUssSUFBSUMsSUFBSSxHQUFHQSxJQUFJbEgsYUFBYWtILElBQUs7Z0JBQ3BDLE1BQU1kLFNBQVNGLGFBQWFnQjtnQkFDNUIsSUFBSUosT0FBT0MsSUFBSSxDQUFDWCxRQUFRTSxNQUFNLEtBQUssR0FBRztvQkFDcENPLFVBQVVFLElBQUksQ0FBQ0Q7Z0JBQ2pCO1lBQ0Y7WUFDQSxPQUFPRDtRQUNUOytEQUFHO1FBQUNqSDtRQUFha0c7S0FBYTtJQUU5QixNQUFNa0IsMEJBQTBCL0osa0RBQVdBO3NFQUFDO1lBQzFDLE1BQU1nSyxTQUFTLENBQUM7WUFDaEIvQyxNQUFNZ0QsT0FBTzs4RUFBQyxDQUFDQyxNQUFNQztvQkFDbkIsTUFBTXBCLFNBQVNGLGFBQWFzQjtvQkFDNUJILE1BQU0sQ0FBQ0csTUFBTSxHQUFHVixPQUFPQyxJQUFJLENBQUNYLFFBQVFNLE1BQU0sS0FBSztnQkFDakQ7O1lBQ0EsT0FBT1c7UUFDVDtxRUFBRztRQUFDL0M7UUFBTzRCO0tBQWE7SUFFeEIsTUFBTXVCLG9CQUFvQnBLLGtEQUFXQTtnRUFBQztZQUNwQyxJQUFLLElBQUk2SixJQUFJLEdBQUdBLElBQUk1QyxNQUFNb0MsTUFBTSxHQUFHLEdBQUdRLElBQUs7Z0JBQ3pDLElBQUksQ0FBQ0wscUJBQXFCSyxJQUFJLE9BQU87WUFDdkM7WUFDQSxPQUFPO1FBQ1Q7K0RBQUc7UUFBQ0w7UUFBc0J2QyxNQUFNb0MsTUFBTTtLQUFDO0lBRXZDLE1BQU1nQixpQkFBaUJySyxrREFBV0E7NkRBQUMsU0FBQ3NLLFNBQVNDLE9BQU9DO2dCQUFPTCx5RUFBUTtZQUNqRW5IO3FFQUFZeUgsQ0FBQUE7b0JBQ1YsSUFBSUMsVUFBVTt3QkFBRSxHQUFHRCxJQUFJO29CQUFDO29CQUN4QixJQUFJTixVQUFVLFFBQVFRLE1BQU1DLE9BQU8sQ0FBQ0gsSUFBSSxDQUFDSCxRQUFRLEdBQUc7d0JBQ2xELE1BQU1PLFdBQVc7K0JBQUlKLElBQUksQ0FBQ0gsUUFBUTt5QkFBQzt3QkFDbkNPLFFBQVEsQ0FBQ1YsTUFBTSxHQUFHOzRCQUFFLEdBQUdVLFFBQVEsQ0FBQ1YsTUFBTTs0QkFBRSxDQUFDSSxNQUFNLEVBQUVDO3dCQUFNO3dCQUN2REUsT0FBTyxDQUFDSixRQUFRLEdBQUdPO29CQUNyQixPQUFPLElBQUksT0FBT0osSUFBSSxDQUFDSCxRQUFRLEtBQUssWUFBWSxDQUFDSyxNQUFNQyxPQUFPLENBQUNILElBQUksQ0FBQ0gsUUFBUSxHQUFHO3dCQUM3RUksT0FBTyxDQUFDSixRQUFRLEdBQUc7NEJBQUUsR0FBR0csSUFBSSxDQUFDSCxRQUFROzRCQUFFLENBQUNDLE1BQU0sRUFBRUM7d0JBQU07b0JBQ3hELE9BQU87d0JBQ0xFLE9BQU8sQ0FBQ0gsTUFBTSxHQUFHQztvQkFDbkI7b0JBQ0EsT0FBT0U7Z0JBQ1Q7O1lBQ0EsSUFBSWpGLGdCQUFnQixDQUFDOEUsTUFBTSxFQUFFO2dCQUMzQjdFO3lFQUFvQitFLENBQUFBO3dCQUNsQixNQUFNSyxZQUFZOzRCQUFFLEdBQUdMLElBQUk7d0JBQUM7d0JBQzVCLE9BQU9LLFNBQVMsQ0FBQ1AsTUFBTTt3QkFDdkIsT0FBT087b0JBQ1Q7O1lBQ0Y7UUFDRjs0REFBRztRQUFDckY7S0FBaUI7SUFFckIsTUFBTXNGLGVBQWUvSyxrREFBV0E7MkRBQUMsQ0FBQ3NLLFNBQVNVO1lBQ3pDaEk7bUVBQVl5SCxDQUFBQSxPQUFTO3dCQUNuQixHQUFHQSxJQUFJO3dCQUNQLENBQUNILFFBQVEsRUFBRTsrQkFBSUcsSUFBSSxDQUFDSCxRQUFROzRCQUFFO2dDQUFFLEdBQUdVLFFBQVE7Z0NBQUVwSCxJQUFJcUgsS0FBS0MsTUFBTSxHQUFHQyxRQUFRLENBQUMsSUFBSUMsU0FBUyxDQUFDLEdBQUc7NEJBQUk7eUJBQUU7b0JBQ2pHOztRQUNGOzBEQUFHLEVBQUU7SUFFTCxNQUFNQyxrQkFBa0JyTCxrREFBV0E7OERBQUMsQ0FBQ3NLLFNBQVMxRztZQUM1Q1o7c0VBQVl5SCxDQUFBQSxPQUFTO3dCQUNuQixHQUFHQSxJQUFJO3dCQUNQLENBQUNILFFBQVEsRUFBRUcsSUFBSSxDQUFDSCxRQUFRLENBQUNuQixNQUFNO2tGQUFDbUMsQ0FBQUEsT0FBUUEsS0FBSzFILEVBQUUsS0FBS0E7O29CQUN0RDs7UUFDRjs2REFBRyxFQUFFO0lBRUwsTUFBTTJILFdBQVd2TCxrREFBV0E7dURBQUM7WUFDM0IsTUFBTStJLFNBQVNGLGFBQWFsRztZQUM1QixJQUFJOEcsT0FBT0MsSUFBSSxDQUFDWCxRQUFRTSxNQUFNLEdBQUcsR0FBRztnQkFDbEMzRCxvQkFBb0JxRDtnQkFDcEJuRCx3QkFBd0I7Z0JBQ3hCLE1BQU00RixnQkFBZ0IvQixPQUFPZ0MsTUFBTSxDQUFDMUM7Z0JBQ3BDN0gsa0RBQUtBLENBQUNvSCxLQUFLLENBQUNrRCxhQUFhLENBQUMsRUFBRTtnQkFDNUI7WUFDRjtZQUNBMUY7K0RBQWtCMkUsQ0FBQUEsT0FBUTsyQkFBSSxJQUFJaUIsSUFBSTsrQkFBSWpCOzRCQUFNOUg7eUJBQVk7cUJBQUU7O1lBQzlEK0Msb0JBQW9CLENBQUM7WUFDckJFLHdCQUF3QjtZQUN4QixJQUFJakQsY0FBY3NFLE1BQU1vQyxNQUFNLEdBQUcsR0FBRztnQkFDbEN6RyxlQUFlRCxjQUFjO2dCQUM3QnpCLGtEQUFLQSxDQUFDZ0gsT0FBTyxDQUFDLEdBQTRCLE9BQXpCakIsS0FBSyxDQUFDdEUsWUFBWSxDQUFDeUIsS0FBSyxFQUFDO1lBQzVDO1FBQ0Y7c0RBQUc7UUFBQ3pCO1FBQWFrRztRQUFjNUI7S0FBTTtJQUVyQyxNQUFNMEUsV0FBVzNMLGtEQUFXQTt1REFBQztZQUMzQjBGLG9CQUFvQixDQUFDO1lBQ3JCRSx3QkFBd0I7WUFDeEIsSUFBSWpELGNBQWMsR0FBR0MsZUFBZUQsY0FBYztRQUNwRDtzREFBRztRQUFDQTtLQUFZO0lBRWhCLE1BQU1pSixrQkFBa0I1TCxrREFBV0E7OERBQUMsQ0FBQzhJO1lBQ25DLElBQUlBLGFBQWFuRyxlQUFla0QsZUFBZWdHLFFBQVEsQ0FBQy9DLFlBQVk7Z0JBQ2xFbEcsZUFBZWtHO2dCQUNmcEQsb0JBQW9CLENBQUM7Z0JBQ3JCRSx3QkFBd0I7WUFDMUI7UUFDRjs2REFBRztRQUFDakQ7UUFBYWtEO0tBQWU7SUFFaEMsTUFBTWlHLGlCQUFpQjlMLGtEQUFXQTs2REFBQztZQUNqQzBGLG9CQUFvQixDQUFDO1lBQ3JCRSx3QkFBd0I7WUFDeEIsSUFBSSxDQUFDd0UscUJBQXFCO2dCQUN4QixNQUFNMkIsWUFBWSxDQUFDO2dCQUNuQixJQUFLLElBQUlsQyxJQUFJLEdBQUdBLElBQUk1QyxNQUFNb0MsTUFBTSxHQUFHLEdBQUdRLElBQUs7b0JBQ3pDLE1BQU1tQyxhQUFhbkQsYUFBYWdCO29CQUNoQ0osT0FBT3dDLE1BQU0sQ0FBQ0YsV0FBV0M7Z0JBQzNCO2dCQUNBdEcsb0JBQW9CcUc7Z0JBQ3BCbkcsd0JBQXdCO2dCQUN4QixJQUFLLElBQUlpRSxJQUFJLEdBQUdBLElBQUk1QyxNQUFNb0MsTUFBTSxHQUFHLEdBQUdRLElBQUs7b0JBQ3pDLE1BQU1tQyxhQUFhbkQsYUFBYWdCO29CQUNoQyxJQUFJSixPQUFPQyxJQUFJLENBQUNzQyxZQUFZM0MsTUFBTSxHQUFHLEdBQUc7d0JBQ3RDekcsZUFBZWlIO3dCQUNmM0ksa0RBQUtBLENBQUNvSCxLQUFLLENBQUMsMENBQXlELE9BQWZyQixLQUFLLENBQUM0QyxFQUFFLENBQUN6RixLQUFLO3dCQUNwRTtvQkFDRjtnQkFDRjtnQkFDQTtZQUNGO1lBQ0EsSUFBSTtnQkFDRmdCLGdCQUFnQjtnQkFDaEJFLG1CQUFtQjtnQkFDbkJFLG1CQUFtQjtnQkFDbkIsTUFBTTBHLFdBQVcsTUFBTUMsTUFBTSx3QkFBd0I7b0JBQ25EQyxRQUFRO29CQUNSQyxTQUFTO3dCQUFFLGdCQUFnQjtvQkFBbUI7b0JBQzlDQyxNQUFNdEUsS0FBS1csU0FBUyxDQUFDO3dCQUFFNUY7d0JBQVV3SixZQUFZMUo7b0JBQWlCO2dCQUNoRTtnQkFDQSxNQUFNMkosY0FBY04sU0FBU0csT0FBTyxDQUFDSSxHQUFHLENBQUM7Z0JBQ3pDLElBQUksQ0FBQ0QsZUFBZSxDQUFDQSxZQUFZWCxRQUFRLENBQUMscUJBQXFCLE1BQU0sSUFBSWEsTUFBTTtnQkFDL0UsTUFBTUMsT0FBTyxNQUFNVCxTQUFTVSxJQUFJO2dCQUNoQyxJQUFJLENBQUNWLFNBQVNXLEVBQUUsRUFBRSxNQUFNLElBQUlILE1BQU1DLEtBQUtyRSxLQUFLLElBQUk7Z0JBQ2hELElBQUksQ0FBQ3FFLEtBQUt4RyxVQUFVLElBQUksQ0FBQ3dHLEtBQUtHLFdBQVcsRUFBRSxNQUFNLElBQUlKLE1BQU07Z0JBQzNEcEcsYUFBYXFHLEtBQUtHLFdBQVc7Z0JBQzdCMUcsY0FBY3VHLEtBQUt4RyxVQUFVO2dCQUM3QkssWUFBWW1HLEtBQUtwRyxRQUFRLElBQUk7Z0JBQzdCRyxlQUFlaUcsS0FBS2xHLFdBQVcsSUFBSSxFQUFFO2dCQUNyQ2pCLG1CQUFtQjtnQkFDbkJ0RSxrREFBS0EsQ0FBQ2dILE9BQU8sQ0FBQztZQUNoQixFQUFFLE9BQU9JLE9BQU87Z0JBQ2RDLFFBQVFELEtBQUssQ0FBQyw0QkFBNEJBO2dCQUMxQ3BILGtEQUFLQSxDQUFDb0gsS0FBSyxDQUFDQSxNQUFNeUUsT0FBTyxJQUFJO1lBQy9CLFNBQVU7Z0JBQ1IzSCxnQkFBZ0I7Z0JBQ2hCRSxtQkFBbUI7WUFDckI7UUFDRjs0REFBRztRQUFDOEU7UUFBbUJySDtRQUFVa0U7UUFBTzRCO1FBQWNoRztLQUFpQjtJQUV2RSxNQUFNbUssYUFBYWhOLGtEQUFXQTt5REFBQztZQUM3QnVILGFBQWFhLE9BQU8sQ0FBQyxrQkFBa0JKLEtBQUtXLFNBQVMsQ0FBQzVGO1lBQ3REd0UsYUFBYWEsT0FBTyxDQUFDLG1CQUFtQixJQUFJUixPQUFPUyxXQUFXO1lBQzlEbkMsYUFBYSxJQUFJMEI7WUFDakIxRyxrREFBS0EsQ0FBQ2dILE9BQU8sQ0FBQztRQUNoQjt3REFBRztRQUFDbkY7S0FBUztJQUViLE1BQU1rSyxrQkFBa0JqTixrREFBV0E7OERBQUMsT0FBT3NLO1lBQ3pDLElBQUksQ0FBQzNELHNCQUFzQjtZQUMzQnpGLGtEQUFLQSxDQUFDZ00sT0FBTyxDQUFDO1lBQ2QsSUFBSTtnQkFDRixNQUFNLElBQUlDOzBFQUFRQyxDQUFBQSxVQUFXMUUsV0FBVzBFLFNBQVM7O2dCQUNqRGxNLGtEQUFLQSxDQUFDbU0sT0FBTztnQkFDYm5NLGtEQUFLQSxDQUFDZ0gsT0FBTyxDQUFDO1lBQ2hCLEVBQUUsT0FBT0ksT0FBTztnQkFDZHBILGtEQUFLQSxDQUFDbU0sT0FBTztnQkFDYm5NLGtEQUFLQSxDQUFDb0gsS0FBSyxDQUFDO1lBQ2Q7UUFDRjs2REFBRztRQUFDM0I7S0FBcUI7SUFFekIsOERBQThEO0lBQzlENUcsZ0RBQVNBOzJDQUFDO1lBQ1IsTUFBTXVOO3dFQUF1QjtvQkFDM0Isd0RBQXdEO29CQUN4RCxJQUFJbEQscUJBQXFCO3dCQUN2QjBCO29CQUNGLE9BQU87d0JBQ0w1SyxrREFBS0EsQ0FBQ29ILEtBQUssQ0FBQztvQkFDZDtnQkFDRjs7WUFFQSxJQUFJLElBQTZCLEVBQUU7Z0JBQ2pDRSxPQUFPK0UsZ0JBQWdCLENBQUMsa0JBQWtCRDtnQkFDMUM7dURBQU8sSUFBTTlFLE9BQU9nRixtQkFBbUIsQ0FBQyxrQkFBa0JGOztZQUM1RDtRQUNGOzBDQUFHO1FBQUNsRDtRQUFtQjBCO0tBQWU7SUFFdEMsTUFBTTJCLG9CQUFvQnpOLGtEQUFXQTtnRUFBQztZQUNwQyxNQUFNME4sYUFBYSxDQUFDO1lBQ3BCekcsTUFBTWdELE9BQU87d0VBQUMsQ0FBQ0MsTUFBTUM7b0JBQ25CLE1BQU1wQixTQUFTRixhQUFhc0I7b0JBQzVCdUQsVUFBVSxDQUFDdkQsTUFBTSxHQUFHVixPQUFPQyxJQUFJLENBQUNYLFFBQVFNLE1BQU0sS0FBSztnQkFDckQ7O1lBQ0EsT0FBT3FFO1FBQ1Q7K0RBQUc7UUFBQ3pHO1FBQU80QjtLQUFhO0lBRXhCLE1BQU04RSxvQkFBb0I7UUFDeEIsTUFBTUMsY0FBYztZQUNsQjdLO1lBQ0FzSDtZQUNBVTtZQUNBTTtZQUNBaEU7WUFDQTVCO1lBQ0FFO1lBQ0FrSSxRQUFRYjtZQUNSYyxhQUFhYjtRQUNmO1FBQ0EsT0FBUXRLO1lBQ04sS0FBSztnQkFDSCxxQkFBTyw4REFBQ3RCLHlFQUFnQkE7b0JBQUUsR0FBR3VNLFdBQVc7Ozs7OztZQUMxQyxLQUFLO2dCQUNILHFCQUFPLDhEQUFDdE0sc0VBQWFBO29CQUFFLEdBQUdzTSxXQUFXOzs7Ozs7WUFDdkMsS0FBSztnQkFDSCxxQkFBTyw4REFBQy9MLHlFQUFzQkE7b0JBQUUsR0FBRytMLFdBQVc7Ozs7OztZQUNoRCxLQUFLO2dCQUNILHFCQUFPLDhEQUFDN0wsMkRBQW9CQTtvQkFBRSxHQUFHNkwsV0FBVzs7Ozs7O1lBQzlDLEtBQUs7Z0JBQ0gscUJBQU8sOERBQUM1TCx5REFBa0JBO29CQUFFLEdBQUc0TCxXQUFXOzs7Ozs7WUFDNUMsS0FBSztnQkFDSCxxQkFBTyw4REFBQzNMLGtFQUFrQkE7b0JBQUUsR0FBRzJMLFdBQVc7b0JBQUUvSyxrQkFBa0JBO29CQUFrQmtMLGtCQUFrQmpMOzs7Ozs7WUFDcEc7Z0JBQ0UsT0FBTztRQUNYO0lBQ0Y7SUFFQSxJQUFJeUMsbUJBQW1CWSxjQUFjRSxXQUFXO1FBQzlDLHFCQUNFLDhEQUFDOUQsdURBQWFBO1lBQ1pRLFVBQVVBO1lBQ1ZvRCxZQUFZQTtZQUNaNkgsYUFBYTtnQkFDWHhJLG1CQUFtQjtnQkFDbkJZLGNBQWM7Z0JBQ2RFLGFBQWE7Z0JBQ2IxRCxlQUFlO1lBQ2pCO1lBQ0FxTCxjQUFjO2dCQUNaekksbUJBQW1CO2dCQUNuQjVDLGVBQWUsSUFBSSx5QkFBeUI7WUFDOUM7Ozs7OztJQUdOO0lBRUEscUJBQ0UsOERBQUNzTDtRQUFJQyxXQUFVOzswQkFDYiw4REFBQ0Q7Z0JBQUlDLFdBQVU7MEJBQ2IsNEVBQUNEO29CQUFJQyxXQUFVOzs7Ozs7Ozs7OzswQkFFakIsOERBQUMvTCw0REFBV0E7Z0JBQUNnTSxXQUFXL0k7Z0JBQWlCZ0osWUFBWSxJQUFNL0ksbUJBQW1COzs7Ozs7MEJBQzlFLDhEQUFDNEk7Z0JBQUlDLFdBQVU7MEJBQ2IsNEVBQUNEO29CQUFJQyxXQUFVOzt3QkFDWixDQUFDekwsNEJBQ0EsOERBQUN6QyxrREFBTUEsQ0FBQ2lPLEdBQUc7NEJBQUNDLFdBQVU7NEJBQW1CRyxTQUFTO2dDQUFFQyxTQUFTO2dDQUFHQyxHQUFHLENBQUM7NEJBQUc7NEJBQUdDLFNBQVM7Z0NBQUVGLFNBQVM7Z0NBQUdDLEdBQUc7NEJBQUU7OzhDQUNwRyw4REFBQ047b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUMzTiw4TUFBUUE7b0RBQUMyTixXQUFVOzs7Ozs7OERBQ3BCLDhEQUFDRDtvREFBSUMsV0FBVTs7Ozs7Ozs7Ozs7O3NEQUVqQiw4REFBQ087NENBQUdQLFdBQVU7c0RBQTRDOzs7Ozs7Ozs7Ozs7OENBRTVELDhEQUFDUTtvQ0FBRVIsV0FBVTs4Q0FBMEM7Ozs7Ozs7Ozs7OztzQ0FHM0QsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FFYiw4REFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUViLDhEQUFDRDs0Q0FBSUMsV0FBVTtzREFDYiw0RUFBQ2pNLG1FQUF5QkE7Z0RBQ3hCK0UsT0FBT0E7Z0RBQ1B0RSxhQUFhQTtnREFDYmtELGdCQUFnQkE7Z0RBQ2hCK0ksYUFBYWhEO2dEQUNiaUQsU0FBUTtnREFDUkMsWUFBWTtnREFDWkMsY0FBYzs7Ozs7Ozs7Ozs7c0RBS2xCLDhEQUFDYjs0Q0FBSUMsV0FBVTtzREFDYiw0RUFBQ2pPLDJEQUFlQTtnREFBQzhPLE1BQUs7MERBQ3BCLDRFQUFDL08sa0RBQU1BLENBQUNpTyxHQUFHO29EQUFtQkksU0FBUzt3REFBRUMsU0FBUzt3REFBR1UsR0FBRztvREFBRztvREFBR1IsU0FBUzt3REFBRUYsU0FBUzt3REFBR1UsR0FBRztvREFBRTtvREFBR0MsTUFBTTt3REFBRVgsU0FBUzt3REFBR1UsR0FBRyxDQUFDO29EQUFHO29EQUFHRSxZQUFZO3dEQUFFQyxVQUFVO29EQUFJOzhEQUNwSnpCO21EQURjaEw7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FRdkIsOERBQUN1TDtvQ0FBSUMsV0FBVTs4Q0FFYiw0RUFBQ0Q7d0NBQUlDLFdBQVU7a0RBQ2IsNEVBQUM3TCw2REFBbUJBOzRDQUNsQlMsVUFBVUE7NENBQ1ZGLGtCQUFrQkE7NENBQ2xCb0MsYUFBYUE7NENBQ2JvSyxpQkFBaUIsSUFBTW5LLGVBQWUsQ0FBQ0Q7NENBQ3ZDcUssa0JBQWtCLElBQU1DLHlCQUF5Qjs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FNdkQsOERBQUNyQjtvQ0FBSUMsV0FBVTs4Q0FDYiw0RUFBQ2xPLGtEQUFNQSxDQUFDaU8sR0FBRzt3Q0FDVEMsV0FBVTt3Q0FDVkcsU0FBUzs0Q0FBRUMsU0FBUzs0Q0FBR0MsR0FBRzt3Q0FBRzt3Q0FDN0JDLFNBQVM7NENBQUVGLFNBQVM7NENBQUdDLEdBQUc7d0NBQUU7d0NBQzVCVyxZQUFZOzRDQUFFSyxPQUFPO3dDQUFJOzswREFFekIsOERBQUN0QjtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUNzQjt3REFBR3RCLFdBQVU7OzBFQUNaLDhEQUFDdk4sOE1BQUdBO2dFQUFDdU4sV0FBVTs7Ozs7OzREQUE2Qjs7Ozs7OztrRUFHOUMsOERBQUN1Qjt3REFDQ0MsU0FBUyxJQUFNekssZUFBZSxDQUFDRDt3REFDL0JrSixXQUFXLGdFQUVWLE9BRENsSixjQUFjLGdDQUFnQztrRUFHL0NBLGNBQWMsU0FBUzs7Ozs7Ozs7Ozs7OzRDQUczQkEsNEJBQ0MsOERBQUNpSjtnREFBSUMsV0FBVTswREFDYiw0RUFBQzlMLHVEQUFxQkE7b0RBQUNVLFVBQVVBO29EQUFVRixrQkFBa0JBOzs7Ozs7Ozs7OzBFQUcvRCw4REFBQ3FMO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ3ZOLDhNQUFHQTt3REFBQ3VOLFdBQVU7Ozs7OztrRUFDZiw4REFBQ1E7d0RBQUVSLFdBQVU7a0VBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBUXJDLDhEQUFDL00sa0VBQWtCQTtnQkFBQ3VCLGFBQWFBO2dCQUFhaU4sWUFBWTNJLE1BQU1vQyxNQUFNO2dCQUFFd0csWUFBWWxFO2dCQUFVbUUsUUFBUXZFO2dCQUFVd0UsWUFBWWpFO2dCQUFnQitCLFFBQVFiO2dCQUFZZ0QsV0FBVyxJQUFNOUssZUFBZSxDQUFDRDtnQkFBY0UsY0FBY0E7Z0JBQWM4SyxZQUFZdE4sZ0JBQWdCc0UsTUFBTW9DLE1BQU0sR0FBRyxJQUFJZSxzQkFBc0JaLHFCQUFxQjdHO2dCQUFjc0MsYUFBYUE7Z0JBQWFnQyxPQUFPQTtnQkFBT2xFLFVBQVVBO2dCQUFVd0QsVUFBVWMsWUFBWTZJLFlBQVk7Z0JBQUVuSyxpQkFBaUJBOzs7Ozs7Ozs7Ozs7QUFLMWM7R0FoZk10RDs7UUFxRGdCRCw4REFBY0E7OztLQXJEOUJDO0FBa2ZOLGlFQUFlQSxxQkFBcUJBLEVBQUMiLCJzb3VyY2VzIjpbIkM6XFxQcm9qZWN0c1xcTmV3QmxpbmtGaW5kQUlcXGZyb250ZW5kLW1hc3Rlclxcc3JjXFxjb21wb25lbnRzXFxyZXN1bWVcXFJlc3VtZUJ1aWxkZXIuanN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcbmltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0LCB1c2VDYWxsYmFjayB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IG1vdGlvbiwgQW5pbWF0ZVByZXNlbmNlIH0gZnJvbSAnZnJhbWVyLW1vdGlvbic7XG5pbXBvcnQgeyBcbiAgVXNlciwgR3JhZHVhdGlvbkNhcCwgQnJpZWZjYXNlLCBBd2FyZCwgRmlsZVRleHQsIFNwYXJrbGVzLCBTYXZlLCBSb3RhdGVDY3csIERvd25sb2FkLCBFeWUsIFRhcmdldCwgQ2xvY2ssIENoZWNrQ2lyY2xlLCBBbGVydFRyaWFuZ2xlLCBMaWdodGJ1bGJcbn0gZnJvbSAnbHVjaWRlLXJlYWN0JztcbmltcG9ydCB7IHRvYXN0IH0gZnJvbSAncmVhY3QtaG90LXRvYXN0JztcbmltcG9ydCBFbmhhbmNlZFN0ZXBOYXZpZ2F0aW9uIGZyb20gJy4vU3RlcE5hdmlnYXRpb24nO1xuaW1wb3J0IFNtYXJ0TmF2aWdhdGlvbkJhciBmcm9tICcuLi9sYXlvdXQvU21hcnROYXZpZ2F0aW9uQmFyJztcbmltcG9ydCB7IFBlcnNvbmFsSW5mb0Zvcm0sIEVkdWNhdGlvbkZvcm0sIEV4cGVyaWVuY2VGb3JtIGFzIEJhc2ljRXhwZXJpZW5jZUZvcm0sIFNraWxsc1Byb2plY3RzRm9ybSBhcyBCYXNpY1NraWxsc1Byb2plY3RzRm9ybSwgUmV2aWV3Rm9ybSBhcyBCYXNpY1Jldmlld0Zvcm0gfSBmcm9tICcuL2Zvcm1zL1Jlc3VtZUZvcm1Db21wb25lbnRzJztcbmltcG9ydCB7IEVuaGFuY2VkRXhwZXJpZW5jZUZvcm0gfSBmcm9tICcuL2Zvcm1zL0V4cGVyaWVuY2VGb3JtJztcbmltcG9ydCB7IEVuaGFuY2VkU2tpbGxzUHJvamVjdHNGb3JtIH0gZnJvbSAnLi9mb3Jtcy9Ta2lsbHNQcm9qZWN0c0Zvcm0nO1xuaW1wb3J0IEVuaGFuY2VkUHJvamVjdHNGb3JtIGZyb20gJy4vZm9ybXMvUHJvamVjdHNGb3JtJztcbmltcG9ydCBFbmhhbmNlZFNraWxsc0Zvcm0gZnJvbSAnLi9mb3Jtcy9Ta2lsbHNGb3JtJztcbmltcG9ydCB7IEVuaGFuY2VkUmV2aWV3Rm9ybSB9IGZyb20gJy4vZm9ybXMvUmV2aWV3Rm9ybSc7XG5pbXBvcnQgRW5oYW5jZWRQcm9ncmVzc0luZGljYXRvciBmcm9tICcuL0VuaGFuY2VkUHJvZ3Jlc3NJbmRpY2F0b3InO1xuaW1wb3J0IEVuaGFuY2VkTmF2aWdhdGlvbiBmcm9tICcuL0VuaGFuY2VkTmF2aWdhdGlvbic7XG5pbXBvcnQgUHJvZ3Jlc3NCYXIgZnJvbSAnLi4vY29tbW9uL1Byb2dyZXNzQmFyJztcbmltcG9ydCBFbmhhbmNlZFJlc3VtZVByZXZpZXcgZnJvbSAnLi9SZXN1bWVQcmV2aWV3JztcbmltcG9ydCBTaW1wbGVSZXN1bWVQcmV2aWV3IGZyb20gJy4vU2ltcGxlUmVzdW1lUHJldmlldyc7XG5pbXBvcnQgU3VjY2Vzc1NjcmVlbiBmcm9tICcuL1N1Y2Nlc3NTY3JlZW4nO1xuaW1wb3J0IHVzZUFUU0FuYWx5c2lzIGZyb20gJ0AvaG9va3MvdXNlQVRTQW5hbHlzaXMnO1xuXG5jb25zdCBFbmhhbmNlZFJlc3VtZUJ1aWxkZXIgPSAoeyBoaWRlSGVhZGVyID0gZmFsc2UgfSkgPT4ge1xuICBjb25zdCBbY3VycmVudFN0ZXAsIHNldEN1cnJlbnRTdGVwXSA9IHVzZVN0YXRlKDApO1xuICBjb25zdCBbc2VsZWN0ZWRUZW1wbGF0ZSwgc2V0U2VsZWN0ZWRUZW1wbGF0ZV0gPSB1c2VTdGF0ZSgnbW9kZXJuJyk7XG4gIGNvbnN0IFtmb3JtRGF0YSwgc2V0Rm9ybURhdGFdID0gdXNlU3RhdGUoe1xuICAgIHBlcnNvbmFsOiB7XG4gICAgICBmaXJzdE5hbWU6IFwiXCIsXG4gICAgICBsYXN0TmFtZTogXCJcIixcbiAgICAgIGVtYWlsOiBcIlwiLFxuICAgICAgcGhvbmU6IFwiXCIsXG4gICAgICBsb2NhdGlvbjogXCJcIixcbiAgICAgIGxpbmtlZGluOiBcIlwiLFxuICAgICAgcG9ydGZvbGlvOiBcIlwiLFxuICAgICAgc3VtbWFyeTogXCJcIixcbiAgICAgIHByb2ZpbGVJbWFnZTogXCJcIlxuICAgIH0sXG4gICAgZWR1Y2F0aW9uOiBbXG4gICAgICB7IGlkOiAxLCBkZWdyZWU6IFwiXCIsIGluc3RpdHV0aW9uOiBcIlwiLCBsb2NhdGlvbjogXCJcIiwgc3RhcnREYXRlOiBcIlwiLCBlbmREYXRlOiBcIlwiLCBncGE6IFwiXCIsIHJlbGV2YW50OiBcIlwiIH1cbiAgICBdLFxuICAgIGV4cGVyaWVuY2U6IFtcbiAgICAgIHsgaWQ6IDEsIHRpdGxlOiBcIlwiLCBjb21wYW55OiBcIlwiLCBsb2NhdGlvbjogXCJcIiwgc3RhcnREYXRlOiBcIlwiLCBlbmREYXRlOiBcIlwiLCBjdXJyZW50OiBmYWxzZSwgZGVzY3JpcHRpb246IFwiXCIgfVxuICAgIF0sXG4gICAgc2tpbGxzOiB7IHRlY2huaWNhbDogW10sIGxhbmd1YWdlczogW10sIGNlcnRpZmljYXRpb25zOiBbXSB9LFxuICAgIHByb2plY3RzOiBbXG4gICAgICB7IGlkOiAxLCBuYW1lOiBcIlwiLCBkZXNjcmlwdGlvbjogXCJcIiwgdGVjaG5vbG9naWVzOiBcIlwiLCBsaW5rOiBcIlwiIH1cbiAgICBdLFxuICAgIGpvYkRlc2NyaXB0aW9uOiBcIlwiXG4gIH0pO1xuICBjb25zdCBbc2hvd1ByZXZpZXcsIHNldFNob3dQcmV2aWV3XSA9IHVzZVN0YXRlKHRydWUpO1xuICBjb25zdCBbaXNHZW5lcmF0aW5nLCBzZXRJc0dlbmVyYXRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbc2hvd1Byb2dyZXNzQmFyLCBzZXRTaG93UHJvZ3Jlc3NCYXJdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbcmVzdW1lR2VuZXJhdGVkLCBzZXRSZXN1bWVHZW5lcmF0ZWRdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbdmFsaWRhdGlvbkVycm9ycywgc2V0VmFsaWRhdGlvbkVycm9yc10gPSB1c2VTdGF0ZSh7fSk7XG4gIGNvbnN0IFtzaG93VmFsaWRhdGlvbkVycm9ycywgc2V0U2hvd1ZhbGlkYXRpb25FcnJvcnNdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbY29tcGxldGVkU3RlcHMsIHNldENvbXBsZXRlZFN0ZXBzXSA9IHVzZVN0YXRlKFtdKTtcbiAgY29uc3QgW2F1dG9TYXZlRW5hYmxlZCwgc2V0QXV0b1NhdmVFbmFibGVkXSA9IHVzZVN0YXRlKHRydWUpO1xuICBjb25zdCBbbGFzdFNhdmVkLCBzZXRMYXN0U2F2ZWRdID0gdXNlU3RhdGUobnVsbCk7XG4gIGNvbnN0IFtyZXN1bWVEYXRhLCBzZXRSZXN1bWVEYXRhXSA9IHVzZVN0YXRlKG51bGwpO1xuICBjb25zdCBbcmVzdW1lVXJsLCBzZXRSZXN1bWVVcmxdID0gdXNlU3RhdGUoXCJcIik7XG4gIGNvbnN0IFthdHNTY29yZSwgc2V0QXRzU2NvcmVdID0gdXNlU3RhdGUobnVsbCk7XG4gIGNvbnN0IFtzdWdnZXN0aW9ucywgc2V0U3VnZ2VzdGlvbnNdID0gdXNlU3RhdGUoW10pO1xuICBjb25zdCBbYWlTdWdnZXN0aW9uc0VuYWJsZWQsIHNldEFpU3VnZ2VzdGlvbnNFbmFibGVkXSA9IHVzZVN0YXRlKHRydWUpO1xuICBjb25zdCBbc21hcnRWYWxpZGF0aW9uLCBzZXRTbWFydFZhbGlkYXRpb25dID0gdXNlU3RhdGUodHJ1ZSk7XG4gIGNvbnN0IFtzaG93SGludHMsIHNldFNob3dIaW50c10gPSB1c2VTdGF0ZSh0cnVlKTtcblxuICBjb25zdCBzdGVwcyA9IFtcbiAgICB7IGlkOiAwLCB0aXRsZTogXCJQZXJzb25hbCBJbmZvcm1hdGlvblwiLCBpY29uOiBVc2VyLCBkZXNjcmlwdGlvbjogXCJUZWxsIHVzIGFib3V0IHlvdXJzZWxmXCIsIGVzdGltYXRlZFRpbWU6IDMsIHJlcXVpcmVkOiB0cnVlIH0sXG4gICAgeyBpZDogMSwgdGl0bGU6IFwiRWR1Y2F0aW9uXCIsIGljb246IEdyYWR1YXRpb25DYXAsIGRlc2NyaXB0aW9uOiBcIllvdXIgYWNhZGVtaWMgYmFja2dyb3VuZFwiLCBlc3RpbWF0ZWRUaW1lOiA1LCByZXF1aXJlZDogdHJ1ZSB9LFxuICAgIHsgaWQ6IDIsIHRpdGxlOiBcIkV4cGVyaWVuY2VcIiwgaWNvbjogQnJpZWZjYXNlLCBkZXNjcmlwdGlvbjogXCJZb3VyIHdvcmsgZXhwZXJpZW5jZVwiLCBlc3RpbWF0ZWRUaW1lOiA4LCByZXF1aXJlZDogdHJ1ZSB9LFxuICAgIHsgaWQ6IDMsIHRpdGxlOiBcIlByb2plY3RzXCIsIGljb246IEZpbGVUZXh0LCBkZXNjcmlwdGlvbjogXCJTaG93Y2FzZSB5b3VyIHByb2plY3RzXCIsIGVzdGltYXRlZFRpbWU6IDQsIHJlcXVpcmVkOiBmYWxzZSB9LFxuICAgIHsgaWQ6IDQsIHRpdGxlOiBcIlNraWxsc1wiLCBpY29uOiBBd2FyZCwgZGVzY3JpcHRpb246IFwiWW91ciB0ZWNobmljYWwgJiBzb2Z0IHNraWxsc1wiLCBlc3RpbWF0ZWRUaW1lOiAzLCByZXF1aXJlZDogZmFsc2UgfSxcbiAgICB7IGlkOiA1LCB0aXRsZTogXCJSZXZpZXcgJiBHZW5lcmF0ZVwiLCBpY29uOiBGaWxlVGV4dCwgZGVzY3JpcHRpb246IFwiRmluYWxpemUgeW91ciByZXN1bWVcIiwgZXN0aW1hdGVkVGltZTogMiwgcmVxdWlyZWQ6IGZhbHNlIH1cbiAgXTtcblxuICBjb25zdCBhdHNBbmFseXNpcyA9IHVzZUFUU0FuYWx5c2lzKGZvcm1EYXRhKTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIC8vIENoZWNrIGZvciBiYWNrdXAgZGF0YSBmcm9tIGF1dGggZmxvdyBmaXJzdFxuICAgIGNvbnN0IGJhY2t1cERhdGEgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgncmVzdW1lRm9ybURhdGFCYWNrdXAnKTtcbiAgICBjb25zdCBhdXRoVGltZXN0YW1wID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ2F1dGhGbG93VGltZXN0YW1wJyk7XG4gICAgY29uc3Qgc2F2ZWREYXRhID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3Jlc3VtZUZvcm1EYXRhJyk7XG5cbiAgICAvLyBJZiBiYWNrdXAgZGF0YSBleGlzdHMgYW5kIGlzIHJlY2VudCAod2l0aGluIDEwIG1pbnV0ZXMpLCB1c2UgaXRcbiAgICBpZiAoYmFja3VwRGF0YSAmJiBhdXRoVGltZXN0YW1wKSB7XG4gICAgICBjb25zdCB0aW1lRGlmZiA9IERhdGUubm93KCkgLSBwYXJzZUludChhdXRoVGltZXN0YW1wKTtcbiAgICAgIGlmICh0aW1lRGlmZiA8IDEwICogNjAgKiAxMDAwKSB7IC8vIDEwIG1pbnV0ZXNcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICBjb25zdCBwYXJzZWQgPSBKU09OLnBhcnNlKGJhY2t1cERhdGEpO1xuICAgICAgICAgIHNldEZvcm1EYXRhKHBhcnNlZCk7XG4gICAgICAgICAgc2V0TGFzdFNhdmVkKG5ldyBEYXRlKCkpO1xuICAgICAgICAgIHRvYXN0LnN1Y2Nlc3MoJ1lvdXIgd29yayBoYXMgYmVlbiByZXN0b3JlZCBhZnRlciBzaWduLWluIScpO1xuICAgICAgICAgIC8vIENsZWFuIHVwIGJhY2t1cCBkYXRhXG4gICAgICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oJ3Jlc3VtZUZvcm1EYXRhQmFja3VwJyk7XG4gICAgICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oJ2F1dGhGbG93VGltZXN0YW1wJyk7XG4gICAgICAgICAgLy8gVXBkYXRlIG1haW4gc3RvcmFnZVxuICAgICAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCdyZXN1bWVGb3JtRGF0YScsIGJhY2t1cERhdGEpO1xuICAgICAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCdyZXN1bWVMYXN0U2F2ZWQnLCBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCkpO1xuICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gbG9hZCBiYWNrdXAgZGF0YTonLCBlcnJvcik7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG5cbiAgICAvLyBGYWxsYmFjayB0byByZWd1bGFyIHNhdmVkIGRhdGFcbiAgICBpZiAoc2F2ZWREYXRhKSB7XG4gICAgICB0cnkge1xuICAgICAgICBjb25zdCBwYXJzZWQgPSBKU09OLnBhcnNlKHNhdmVkRGF0YSk7XG4gICAgICAgIHNldEZvcm1EYXRhKHBhcnNlZCk7XG4gICAgICAgIHNldExhc3RTYXZlZChuZXcgRGF0ZShsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgncmVzdW1lTGFzdFNhdmVkJykgfHwgRGF0ZS5ub3coKSkpO1xuICAgICAgICB0b2FzdC5zdWNjZXNzKCdQcmV2aW91cyB3b3JrIHJlc3RvcmVkIScpO1xuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIGxvYWQgc2F2ZWQgZGF0YTonLCBlcnJvcik7XG4gICAgICB9XG4gICAgfVxuXG4gICAgLy8gQ2xlYW4gdXAgb2xkIGJhY2t1cCBkYXRhXG4gICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oJ3Jlc3VtZUZvcm1EYXRhQmFja3VwJyk7XG4gICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oJ2F1dGhGbG93VGltZXN0YW1wJyk7XG5cbiAgfSwgW10pO1xuXG5cblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmIChhdXRvU2F2ZUVuYWJsZWQgJiYgZm9ybURhdGEpIHtcbiAgICAgIC8vIE1ha2UgZm9ybSBkYXRhIGdsb2JhbGx5IGFjY2Vzc2libGUgZm9yIGF1dGggZmxvd1xuICAgICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgIHdpbmRvdy5mb3JtRGF0YSA9IGZvcm1EYXRhO1xuICAgICAgfVxuXG4gICAgICBjb25zdCB0aW1lb3V0SWQgPSBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oJ3Jlc3VtZUZvcm1EYXRhJywgSlNPTi5zdHJpbmdpZnkoZm9ybURhdGEpKTtcbiAgICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oJ3Jlc3VtZUxhc3RTYXZlZCcsIG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSk7XG4gICAgICAgIHNldExhc3RTYXZlZChuZXcgRGF0ZSgpKTtcbiAgICAgIH0sIDIwMDApO1xuICAgICAgcmV0dXJuICgpID0+IGNsZWFyVGltZW91dCh0aW1lb3V0SWQpO1xuICAgIH1cbiAgfSwgW2Zvcm1EYXRhLCBhdXRvU2F2ZUVuYWJsZWRdKTtcblxuICBjb25zdCB2YWxpZGF0ZVN0ZXAgPSB1c2VDYWxsYmFjaygoc3RlcEluZGV4KSA9PiB7XG4gICAgY29uc3QgZXJyb3JzID0ge307XG4gICAgc3dpdGNoIChzdGVwSW5kZXgpIHtcbiAgICAgIGNhc2UgMDpcbiAgICAgICAgaWYgKCFmb3JtRGF0YS5wZXJzb25hbC5maXJzdE5hbWU/LnRyaW0oKSkgZXJyb3JzLmZpcnN0TmFtZSA9ICdGaXJzdCBuYW1lIGlzIHJlcXVpcmVkJztcbiAgICAgICAgaWYgKCFmb3JtRGF0YS5wZXJzb25hbC5sYXN0TmFtZT8udHJpbSgpKSBlcnJvcnMubGFzdE5hbWUgPSAnTGFzdCBuYW1lIGlzIHJlcXVpcmVkJztcbiAgICAgICAgaWYgKCFmb3JtRGF0YS5wZXJzb25hbC5lbWFpbD8udHJpbSgpKSBlcnJvcnMuZW1haWwgPSAnRW1haWwgaXMgcmVxdWlyZWQnO1xuICAgICAgICBlbHNlIGlmICghL15bXlxcc0BdK0BbXlxcc0BdK1xcLlteXFxzQF0rJC8udGVzdChmb3JtRGF0YS5wZXJzb25hbC5lbWFpbCkpIGVycm9ycy5lbWFpbCA9ICdQbGVhc2UgZW50ZXIgYSB2YWxpZCBlbWFpbCBhZGRyZXNzJztcbiAgICAgICAgYnJlYWs7XG4gICAgICBjYXNlIDE6XG4gICAgICAgIGNvbnN0IHZhbGlkRWR1Y2F0aW9uID0gZm9ybURhdGEuZWR1Y2F0aW9uPy5maWx0ZXIoZWR1ID0+IGVkdS5kZWdyZWU/LnRyaW0oKSAmJiBlZHUuaW5zdGl0dXRpb24/LnRyaW0oKSk7XG4gICAgICAgIGlmICghdmFsaWRFZHVjYXRpb24gfHwgdmFsaWRFZHVjYXRpb24ubGVuZ3RoID09PSAwKSBlcnJvcnMuZWR1Y2F0aW9uID0gJ0F0IGxlYXN0IG9uZSBlZHVjYXRpb24gZW50cnkgd2l0aCBkZWdyZWUgYW5kIGluc3RpdHV0aW9uIGlzIHJlcXVpcmVkJztcbiAgICAgICAgYnJlYWs7XG4gICAgICBjYXNlIDI6XG4gICAgICAgIGNvbnN0IHZhbGlkRXhwZXJpZW5jZSA9IGZvcm1EYXRhLmV4cGVyaWVuY2U/LmZpbHRlcihleHAgPT4gZXhwLnRpdGxlPy50cmltKCkgJiYgZXhwLmNvbXBhbnk/LnRyaW0oKSk7XG4gICAgICAgIGlmICghdmFsaWRFeHBlcmllbmNlIHx8IHZhbGlkRXhwZXJpZW5jZS5sZW5ndGggPT09IDApIGVycm9ycy5leHBlcmllbmNlID0gJ0F0IGxlYXN0IG9uZSB3b3JrIGV4cGVyaWVuY2UgZW50cnkgd2l0aCBqb2IgdGl0bGUgYW5kIGNvbXBhbnkgaXMgcmVxdWlyZWQnO1xuICAgICAgICBicmVhaztcbiAgICAgIGNhc2UgMzpcbiAgICAgICAgLy8gUHJvamVjdHMgYXJlIG9wdGlvbmFsIC0gbm8gdmFsaWRhdGlvbiByZXF1aXJlZFxuICAgICAgICBicmVhaztcbiAgICAgIGNhc2UgNDpcbiAgICAgICAgLy8gU2tpbGxzIGFyZSBvcHRpb25hbCAtIG5vIHZhbGlkYXRpb24gcmVxdWlyZWRcbiAgICAgICAgYnJlYWs7XG4gICAgICBkZWZhdWx0OlxuICAgICAgICBicmVhaztcbiAgICB9XG4gICAgcmV0dXJuIGVycm9ycztcbiAgfSwgW2Zvcm1EYXRhXSk7XG5cbiAgY29uc3QgY2FuUHJvY2VlZFRvTmV4dFN0ZXAgPSB1c2VDYWxsYmFjaygoc3RlcEluZGV4KSA9PiB7XG4gICAgY29uc3QgZXJyb3JzID0gdmFsaWRhdGVTdGVwKHN0ZXBJbmRleCk7XG4gICAgcmV0dXJuIE9iamVjdC5rZXlzKGVycm9ycykubGVuZ3RoID09PSAwO1xuICB9LCBbdmFsaWRhdGVTdGVwXSk7XG5cbiAgY29uc3QgZ2V0Q29tcGxldGVkU3RlcHMgPSB1c2VDYWxsYmFjaygoKSA9PiB7XG4gICAgY29uc3QgY29tcGxldGVkID0gW107XG4gICAgZm9yIChsZXQgaSA9IDA7IGkgPCBjdXJyZW50U3RlcDsgaSsrKSB7XG4gICAgICBjb25zdCBlcnJvcnMgPSB2YWxpZGF0ZVN0ZXAoaSk7XG4gICAgICBpZiAoT2JqZWN0LmtleXMoZXJyb3JzKS5sZW5ndGggPT09IDApIHtcbiAgICAgICAgY29tcGxldGVkLnB1c2goaSk7XG4gICAgICB9XG4gICAgfVxuICAgIHJldHVybiBjb21wbGV0ZWQ7XG4gIH0sIFtjdXJyZW50U3RlcCwgdmFsaWRhdGVTdGVwXSk7XG5cbiAgY29uc3QgZ2V0U3RlcENvbXBsZXRpb25TdGF0dXMgPSB1c2VDYWxsYmFjaygoKSA9PiB7XG4gICAgY29uc3Qgc3RhdHVzID0ge307XG4gICAgc3RlcHMuZm9yRWFjaCgoc3RlcCwgaW5kZXgpID0+IHtcbiAgICAgIGNvbnN0IGVycm9ycyA9IHZhbGlkYXRlU3RlcChpbmRleCk7XG4gICAgICBzdGF0dXNbaW5kZXhdID0gT2JqZWN0LmtleXMoZXJyb3JzKS5sZW5ndGggPT09IDA7XG4gICAgfSk7XG4gICAgcmV0dXJuIHN0YXR1cztcbiAgfSwgW3N0ZXBzLCB2YWxpZGF0ZVN0ZXBdKTtcblxuICBjb25zdCBjYW5HZW5lcmF0ZVJlc3VtZSA9IHVzZUNhbGxiYWNrKCgpID0+IHtcbiAgICBmb3IgKGxldCBpID0gMDsgaSA8IHN0ZXBzLmxlbmd0aCAtIDE7IGkrKykge1xuICAgICAgaWYgKCFjYW5Qcm9jZWVkVG9OZXh0U3RlcChpKSkgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgICByZXR1cm4gdHJ1ZTtcbiAgfSwgW2NhblByb2NlZWRUb05leHRTdGVwLCBzdGVwcy5sZW5ndGhdKTtcblxuICBjb25zdCB1cGRhdGVGb3JtRGF0YSA9IHVzZUNhbGxiYWNrKChzZWN0aW9uLCBmaWVsZCwgdmFsdWUsIGluZGV4ID0gbnVsbCkgPT4ge1xuICAgIHNldEZvcm1EYXRhKHByZXYgPT4ge1xuICAgICAgbGV0IG5ld0RhdGEgPSB7IC4uLnByZXYgfTtcbiAgICAgIGlmIChpbmRleCAhPT0gbnVsbCAmJiBBcnJheS5pc0FycmF5KHByZXZbc2VjdGlvbl0pKSB7XG4gICAgICAgIGNvbnN0IG5ld0FycmF5ID0gWy4uLnByZXZbc2VjdGlvbl1dO1xuICAgICAgICBuZXdBcnJheVtpbmRleF0gPSB7IC4uLm5ld0FycmF5W2luZGV4XSwgW2ZpZWxkXTogdmFsdWUgfTtcbiAgICAgICAgbmV3RGF0YVtzZWN0aW9uXSA9IG5ld0FycmF5O1xuICAgICAgfSBlbHNlIGlmICh0eXBlb2YgcHJldltzZWN0aW9uXSA9PT0gJ29iamVjdCcgJiYgIUFycmF5LmlzQXJyYXkocHJldltzZWN0aW9uXSkpIHtcbiAgICAgICAgbmV3RGF0YVtzZWN0aW9uXSA9IHsgLi4ucHJldltzZWN0aW9uXSwgW2ZpZWxkXTogdmFsdWUgfTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIG5ld0RhdGFbZmllbGRdID0gdmFsdWU7XG4gICAgICB9XG4gICAgICByZXR1cm4gbmV3RGF0YTtcbiAgICB9KTtcbiAgICBpZiAodmFsaWRhdGlvbkVycm9yc1tmaWVsZF0pIHtcbiAgICAgIHNldFZhbGlkYXRpb25FcnJvcnMocHJldiA9PiB7XG4gICAgICAgIGNvbnN0IG5ld0Vycm9ycyA9IHsgLi4ucHJldiB9O1xuICAgICAgICBkZWxldGUgbmV3RXJyb3JzW2ZpZWxkXTtcbiAgICAgICAgcmV0dXJuIG5ld0Vycm9ycztcbiAgICAgIH0pO1xuICAgIH1cbiAgfSwgW3ZhbGlkYXRpb25FcnJvcnNdKTtcblxuICBjb25zdCBhZGRBcnJheUl0ZW0gPSB1c2VDYWxsYmFjaygoc2VjdGlvbiwgdGVtcGxhdGUpID0+IHtcbiAgICBzZXRGb3JtRGF0YShwcmV2ID0+ICh7XG4gICAgICAuLi5wcmV2LFxuICAgICAgW3NlY3Rpb25dOiBbLi4ucHJldltzZWN0aW9uXSwgeyAuLi50ZW1wbGF0ZSwgaWQ6IE1hdGgucmFuZG9tKCkudG9TdHJpbmcoMzYpLnN1YnN0cmluZygyLCAxMSkgfV1cbiAgICB9KSk7XG4gIH0sIFtdKTtcblxuICBjb25zdCByZW1vdmVBcnJheUl0ZW0gPSB1c2VDYWxsYmFjaygoc2VjdGlvbiwgaWQpID0+IHtcbiAgICBzZXRGb3JtRGF0YShwcmV2ID0+ICh7XG4gICAgICAuLi5wcmV2LFxuICAgICAgW3NlY3Rpb25dOiBwcmV2W3NlY3Rpb25dLmZpbHRlcihpdGVtID0+IGl0ZW0uaWQgIT09IGlkKVxuICAgIH0pKTtcbiAgfSwgW10pO1xuXG4gIGNvbnN0IG5leHRTdGVwID0gdXNlQ2FsbGJhY2soKCkgPT4ge1xuICAgIGNvbnN0IGVycm9ycyA9IHZhbGlkYXRlU3RlcChjdXJyZW50U3RlcCk7XG4gICAgaWYgKE9iamVjdC5rZXlzKGVycm9ycykubGVuZ3RoID4gMCkge1xuICAgICAgc2V0VmFsaWRhdGlvbkVycm9ycyhlcnJvcnMpO1xuICAgICAgc2V0U2hvd1ZhbGlkYXRpb25FcnJvcnModHJ1ZSk7XG4gICAgICBjb25zdCBlcnJvck1lc3NhZ2VzID0gT2JqZWN0LnZhbHVlcyhlcnJvcnMpO1xuICAgICAgdG9hc3QuZXJyb3IoZXJyb3JNZXNzYWdlc1swXSk7XG4gICAgICByZXR1cm47XG4gICAgfVxuICAgIHNldENvbXBsZXRlZFN0ZXBzKHByZXYgPT4gWy4uLm5ldyBTZXQoWy4uLnByZXYsIGN1cnJlbnRTdGVwXSldKTtcbiAgICBzZXRWYWxpZGF0aW9uRXJyb3JzKHt9KTtcbiAgICBzZXRTaG93VmFsaWRhdGlvbkVycm9ycyhmYWxzZSk7XG4gICAgaWYgKGN1cnJlbnRTdGVwIDwgc3RlcHMubGVuZ3RoIC0gMSkge1xuICAgICAgc2V0Q3VycmVudFN0ZXAoY3VycmVudFN0ZXAgKyAxKTtcbiAgICAgIHRvYXN0LnN1Y2Nlc3MoYCR7c3RlcHNbY3VycmVudFN0ZXBdLnRpdGxlfSBjb21wbGV0ZWQhYCk7XG4gICAgfVxuICB9LCBbY3VycmVudFN0ZXAsIHZhbGlkYXRlU3RlcCwgc3RlcHNdKTtcblxuICBjb25zdCBwcmV2U3RlcCA9IHVzZUNhbGxiYWNrKCgpID0+IHtcbiAgICBzZXRWYWxpZGF0aW9uRXJyb3JzKHt9KTtcbiAgICBzZXRTaG93VmFsaWRhdGlvbkVycm9ycyhmYWxzZSk7XG4gICAgaWYgKGN1cnJlbnRTdGVwID4gMCkgc2V0Q3VycmVudFN0ZXAoY3VycmVudFN0ZXAgLSAxKTtcbiAgfSwgW2N1cnJlbnRTdGVwXSk7XG5cbiAgY29uc3QgaGFuZGxlU3RlcENsaWNrID0gdXNlQ2FsbGJhY2soKHN0ZXBJbmRleCkgPT4ge1xuICAgIGlmIChzdGVwSW5kZXggPD0gY3VycmVudFN0ZXAgfHwgY29tcGxldGVkU3RlcHMuaW5jbHVkZXMoc3RlcEluZGV4KSkge1xuICAgICAgc2V0Q3VycmVudFN0ZXAoc3RlcEluZGV4KTtcbiAgICAgIHNldFZhbGlkYXRpb25FcnJvcnMoe30pO1xuICAgICAgc2V0U2hvd1ZhbGlkYXRpb25FcnJvcnMoZmFsc2UpO1xuICAgIH1cbiAgfSwgW2N1cnJlbnRTdGVwLCBjb21wbGV0ZWRTdGVwc10pO1xuXG4gIGNvbnN0IGdlbmVyYXRlUmVzdW1lID0gdXNlQ2FsbGJhY2soYXN5bmMgKCkgPT4ge1xuICAgIHNldFZhbGlkYXRpb25FcnJvcnMoe30pO1xuICAgIHNldFNob3dWYWxpZGF0aW9uRXJyb3JzKGZhbHNlKTtcbiAgICBpZiAoIWNhbkdlbmVyYXRlUmVzdW1lKCkpIHtcbiAgICAgIGNvbnN0IGFsbEVycm9ycyA9IHt9O1xuICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBzdGVwcy5sZW5ndGggLSAxOyBpKyspIHtcbiAgICAgICAgY29uc3Qgc3RlcEVycm9ycyA9IHZhbGlkYXRlU3RlcChpKTtcbiAgICAgICAgT2JqZWN0LmFzc2lnbihhbGxFcnJvcnMsIHN0ZXBFcnJvcnMpO1xuICAgICAgfVxuICAgICAgc2V0VmFsaWRhdGlvbkVycm9ycyhhbGxFcnJvcnMpO1xuICAgICAgc2V0U2hvd1ZhbGlkYXRpb25FcnJvcnModHJ1ZSk7XG4gICAgICBmb3IgKGxldCBpID0gMDsgaSA8IHN0ZXBzLmxlbmd0aCAtIDE7IGkrKykge1xuICAgICAgICBjb25zdCBzdGVwRXJyb3JzID0gdmFsaWRhdGVTdGVwKGkpO1xuICAgICAgICBpZiAoT2JqZWN0LmtleXMoc3RlcEVycm9ycykubGVuZ3RoID4gMCkge1xuICAgICAgICAgIHNldEN1cnJlbnRTdGVwKGkpO1xuICAgICAgICAgIHRvYXN0LmVycm9yKGBQbGVhc2UgY29tcGxldGUgYWxsIHJlcXVpcmVkIGZpZWxkcyBpbiAke3N0ZXBzW2ldLnRpdGxlfWApO1xuICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgICAgfVxuICAgICAgcmV0dXJuO1xuICAgIH1cbiAgICB0cnkge1xuICAgICAgc2V0SXNHZW5lcmF0aW5nKHRydWUpO1xuICAgICAgc2V0U2hvd1Byb2dyZXNzQmFyKHRydWUpO1xuICAgICAgc2V0UmVzdW1lR2VuZXJhdGVkKGZhbHNlKTtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGkvZ2VuZXJhdGUtcmVzdW1lJywge1xuICAgICAgICBtZXRob2Q6ICdQT1NUJyxcbiAgICAgICAgaGVhZGVyczogeyAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nIH0sXG4gICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHsgZm9ybURhdGEsIHRlbXBsYXRlSWQ6IHNlbGVjdGVkVGVtcGxhdGUgfSksXG4gICAgICB9KTtcbiAgICAgIGNvbnN0IGNvbnRlbnRUeXBlID0gcmVzcG9uc2UuaGVhZGVycy5nZXQoJ2NvbnRlbnQtdHlwZScpO1xuICAgICAgaWYgKCFjb250ZW50VHlwZSB8fCAhY29udGVudFR5cGUuaW5jbHVkZXMoJ2FwcGxpY2F0aW9uL2pzb24nKSkgdGhyb3cgbmV3IEVycm9yKCdTZXJ2ZXIgcmV0dXJuZWQgbm9uLUpTT04gcmVzcG9uc2UnKTtcbiAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgICBpZiAoIXJlc3BvbnNlLm9rKSB0aHJvdyBuZXcgRXJyb3IoZGF0YS5lcnJvciB8fCAnRmFpbGVkIHRvIGdlbmVyYXRlIHJlc3VtZScpO1xuICAgICAgaWYgKCFkYXRhLnJlc3VtZURhdGEgfHwgIWRhdGEuZG93bmxvYWRVcmwpIHRocm93IG5ldyBFcnJvcignR2VuZXJhdGVkIHJlc3VtZSBkYXRhIGlzIGluY29tcGxldGUnKTtcbiAgICAgIHNldFJlc3VtZVVybChkYXRhLmRvd25sb2FkVXJsKTtcbiAgICAgIHNldFJlc3VtZURhdGEoZGF0YS5yZXN1bWVEYXRhKTtcbiAgICAgIHNldEF0c1Njb3JlKGRhdGEuYXRzU2NvcmUgfHwgNzUpO1xuICAgICAgc2V0U3VnZ2VzdGlvbnMoZGF0YS5zdWdnZXN0aW9ucyB8fCBbXSk7XG4gICAgICBzZXRSZXN1bWVHZW5lcmF0ZWQodHJ1ZSk7XG4gICAgICB0b2FzdC5zdWNjZXNzKCdSZXN1bWUgZ2VuZXJhdGVkIHN1Y2Nlc3NmdWxseSEnKTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignUmVzdW1lIGdlbmVyYXRpb24gZXJyb3I6JywgZXJyb3IpO1xuICAgICAgdG9hc3QuZXJyb3IoZXJyb3IubWVzc2FnZSB8fCAnRmFpbGVkIHRvIGdlbmVyYXRlIHJlc3VtZScpO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRJc0dlbmVyYXRpbmcoZmFsc2UpO1xuICAgICAgc2V0U2hvd1Byb2dyZXNzQmFyKGZhbHNlKTtcbiAgICB9XG4gIH0sIFtjYW5HZW5lcmF0ZVJlc3VtZSwgZm9ybURhdGEsIHN0ZXBzLCB2YWxpZGF0ZVN0ZXAsIHNlbGVjdGVkVGVtcGxhdGVdKTtcblxuICBjb25zdCBoYW5kbGVTYXZlID0gdXNlQ2FsbGJhY2soKCkgPT4ge1xuICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCdyZXN1bWVGb3JtRGF0YScsIEpTT04uc3RyaW5naWZ5KGZvcm1EYXRhKSk7XG4gICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oJ3Jlc3VtZUxhc3RTYXZlZCcsIG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSk7XG4gICAgc2V0TGFzdFNhdmVkKG5ldyBEYXRlKCkpO1xuICAgIHRvYXN0LnN1Y2Nlc3MoJ1Byb2dyZXNzIHNhdmVkIScpO1xuICB9LCBbZm9ybURhdGFdKTtcblxuICBjb25zdCBoYW5kbGVBSVN1Z2dlc3QgPSB1c2VDYWxsYmFjayhhc3luYyAoc2VjdGlvbikgPT4ge1xuICAgIGlmICghYWlTdWdnZXN0aW9uc0VuYWJsZWQpIHJldHVybjtcbiAgICB0b2FzdC5sb2FkaW5nKCdHZXR0aW5nIEFJIHN1Z2dlc3Rpb25zLi4uJyk7XG4gICAgdHJ5IHtcbiAgICAgIGF3YWl0IG5ldyBQcm9taXNlKHJlc29sdmUgPT4gc2V0VGltZW91dChyZXNvbHZlLCAxNTAwKSk7XG4gICAgICB0b2FzdC5kaXNtaXNzKCk7XG4gICAgICB0b2FzdC5zdWNjZXNzKCdBSSBzdWdnZXN0aW9ucyBhcHBsaWVkIScpO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICB0b2FzdC5kaXNtaXNzKCk7XG4gICAgICB0b2FzdC5lcnJvcignRmFpbGVkIHRvIGdldCBBSSBzdWdnZXN0aW9ucycpO1xuICAgIH1cbiAgfSwgW2FpU3VnZ2VzdGlvbnNFbmFibGVkXSk7XG5cbiAgLy8gU2V0IHVwIGV2ZW50IGxpc3RlbmVyIGZvciByZXN1bWUgZ2VuZXJhdGlvbiBmcm9tIFJldmlld0Zvcm1cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCBoYW5kbGVHZW5lcmF0ZVJlc3VtZSA9ICgpID0+IHtcbiAgICAgIC8vIENoZWNrIGlmIHdlIGNhbiBnZW5lcmF0ZSByZXN1bWUgYW5kIGNhbGwgdGhlIGZ1bmN0aW9uXG4gICAgICBpZiAoY2FuR2VuZXJhdGVSZXN1bWUoKSkge1xuICAgICAgICBnZW5lcmF0ZVJlc3VtZSgpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgdG9hc3QuZXJyb3IoJ1BsZWFzZSBjb21wbGV0ZSBhbGwgcmVxdWlyZWQgc2VjdGlvbnMgYmVmb3JlIGdlbmVyYXRpbmcgeW91ciByZXN1bWUuJyk7XG4gICAgICB9XG4gICAgfTtcblxuICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoJ2dlbmVyYXRlUmVzdW1lJywgaGFuZGxlR2VuZXJhdGVSZXN1bWUpO1xuICAgICAgcmV0dXJuICgpID0+IHdpbmRvdy5yZW1vdmVFdmVudExpc3RlbmVyKCdnZW5lcmF0ZVJlc3VtZScsIGhhbmRsZUdlbmVyYXRlUmVzdW1lKTtcbiAgICB9XG4gIH0sIFtjYW5HZW5lcmF0ZVJlc3VtZSwgZ2VuZXJhdGVSZXN1bWVdKTtcblxuICBjb25zdCBnZXRTdGVwVmFsaWRhdGlvbiA9IHVzZUNhbGxiYWNrKCgpID0+IHtcbiAgICBjb25zdCB2YWxpZGF0aW9uID0ge307XG4gICAgc3RlcHMuZm9yRWFjaCgoc3RlcCwgaW5kZXgpID0+IHtcbiAgICAgIGNvbnN0IGVycm9ycyA9IHZhbGlkYXRlU3RlcChpbmRleCk7XG4gICAgICB2YWxpZGF0aW9uW2luZGV4XSA9IE9iamVjdC5rZXlzKGVycm9ycykubGVuZ3RoID09PSAwO1xuICAgIH0pO1xuICAgIHJldHVybiB2YWxpZGF0aW9uO1xuICB9LCBbc3RlcHMsIHZhbGlkYXRlU3RlcF0pO1xuXG4gIGNvbnN0IHJlbmRlclN0ZXBDb250ZW50ID0gKCkgPT4ge1xuICAgIGNvbnN0IGNvbW1vblByb3BzID0ge1xuICAgICAgZm9ybURhdGEsXG4gICAgICB1cGRhdGVGb3JtRGF0YSxcbiAgICAgIGFkZEFycmF5SXRlbSxcbiAgICAgIHJlbW92ZUFycmF5SXRlbSxcbiAgICAgIGF0c0FuYWx5c2lzLFxuICAgICAgdmFsaWRhdGlvbkVycm9ycyxcbiAgICAgIHNob3dWYWxpZGF0aW9uRXJyb3JzLFxuICAgICAgb25TYXZlOiBoYW5kbGVTYXZlLFxuICAgICAgb25BSVN1Z2dlc3Q6IGhhbmRsZUFJU3VnZ2VzdFxuICAgIH07XG4gICAgc3dpdGNoIChjdXJyZW50U3RlcCkge1xuICAgICAgY2FzZSAwOlxuICAgICAgICByZXR1cm4gPFBlcnNvbmFsSW5mb0Zvcm0gey4uLmNvbW1vblByb3BzfSAvPjtcbiAgICAgIGNhc2UgMTpcbiAgICAgICAgcmV0dXJuIDxFZHVjYXRpb25Gb3JtIHsuLi5jb21tb25Qcm9wc30gLz47XG4gICAgICBjYXNlIDI6XG4gICAgICAgIHJldHVybiA8RW5oYW5jZWRFeHBlcmllbmNlRm9ybSB7Li4uY29tbW9uUHJvcHN9IC8+O1xuICAgICAgY2FzZSAzOlxuICAgICAgICByZXR1cm4gPEVuaGFuY2VkUHJvamVjdHNGb3JtIHsuLi5jb21tb25Qcm9wc30gLz47XG4gICAgICBjYXNlIDQ6XG4gICAgICAgIHJldHVybiA8RW5oYW5jZWRTa2lsbHNGb3JtIHsuLi5jb21tb25Qcm9wc30gLz47XG4gICAgICBjYXNlIDU6XG4gICAgICAgIHJldHVybiA8RW5oYW5jZWRSZXZpZXdGb3JtIHsuLi5jb21tb25Qcm9wc30gc2VsZWN0ZWRUZW1wbGF0ZT17c2VsZWN0ZWRUZW1wbGF0ZX0gb25UZW1wbGF0ZVNlbGVjdD17c2V0U2VsZWN0ZWRUZW1wbGF0ZX0gLz47XG4gICAgICBkZWZhdWx0OlxuICAgICAgICByZXR1cm4gbnVsbDtcbiAgICB9XG4gIH07XG5cbiAgaWYgKHJlc3VtZUdlbmVyYXRlZCAmJiByZXN1bWVEYXRhICYmIHJlc3VtZVVybCkge1xuICAgIHJldHVybiAoXG4gICAgICA8U3VjY2Vzc1NjcmVlbiBcbiAgICAgICAgZm9ybURhdGE9e2Zvcm1EYXRhfVxuICAgICAgICByZXN1bWVEYXRhPXtyZXN1bWVEYXRhfVxuICAgICAgICBvblN0YXJ0T3Zlcj17KCkgPT4ge1xuICAgICAgICAgIHNldFJlc3VtZUdlbmVyYXRlZChmYWxzZSk7XG4gICAgICAgICAgc2V0UmVzdW1lRGF0YShudWxsKTtcbiAgICAgICAgICBzZXRSZXN1bWVVcmwoXCJcIik7XG4gICAgICAgICAgc2V0Q3VycmVudFN0ZXAoMCk7XG4gICAgICAgIH19XG4gICAgICAgIG9uRWRpdFJlc3VtZT17KCkgPT4ge1xuICAgICAgICAgIHNldFJlc3VtZUdlbmVyYXRlZChmYWxzZSk7XG4gICAgICAgICAgc2V0Q3VycmVudFN0ZXAoNCk7IC8vIEdvIGJhY2sgdG8gcmV2aWV3IHN0ZXBcbiAgICAgICAgfX1cbiAgICAgIC8+XG4gICAgKTtcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gYmctZ3JhZGllbnQtdG8tYiBmcm9tLWJsYWNrIHRvLVsjMEEwQTBBXSB0ZXh0LXdoaXRlIHJlbGF0aXZlXCI+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgb3BhY2l0eS0yMFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgYmctW3VybCgnL2dyaWQuc3ZnJyldIFttYXNrLWltYWdlOnJhZGlhbC1ncmFkaWVudChlbGxpcHNlX2F0X2NlbnRlcix3aGl0ZSx0cmFuc3BhcmVudCldXCIgLz5cbiAgICAgIDwvZGl2PlxuICAgICAgPFByb2dyZXNzQmFyIGlzVmlzaWJsZT17c2hvd1Byb2dyZXNzQmFyfSBvbkNvbXBsZXRlPXsoKSA9PiBzZXRTaG93UHJvZ3Jlc3NCYXIoZmFsc2UpfSAvPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSB6LTEwIHBiLTMyXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29udGFpbmVyIG14LWF1dG8gcHgtNCBweS04XCI+XG4gICAgICAgICAgeyFoaWRlSGVhZGVyICYmIChcbiAgICAgICAgICAgIDxtb3Rpb24uZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIG1iLThcIiBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHk6IC0yMCB9fSBhbmltYXRlPXt7IG9wYWNpdHk6IDEsIHk6IDAgfX0+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgZ2FwLTMgbWItNlwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmVcIj5cbiAgICAgICAgICAgICAgICAgIDxTcGFya2xlcyBjbGFzc05hbWU9XCJoLTEwIHctMTAgdGV4dC1uZXVyYWwtcGluayBhbmltYXRlLXB1bHNlXCIgLz5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCBiZy1uZXVyYWwtcGluayBvcGFjaXR5LTIwIHJvdW5kZWQtZnVsbCBibHVyLW1kXCI+PC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtNHhsIGxnOnRleHQtNXhsIGZvbnQtYm9sZCB0ZXh0LXdoaXRlXCI+RW5oYW5jZWQgUmVzdW1lIEJ1aWxkZXI8L2gxPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTMwMCB0ZXh0LWxnIG1heC13LTJ4bCBteC1hdXRvXCI+Q3JlYXRlIHByb2Zlc3Npb25hbCwgQVRTLWZyaWVuZGx5IHJlc3VtZXMgd2l0aCBvdXIgZW5oYW5jZWQgQUktcG93ZXJlZCBidWlsZGVyPC9wPlxuICAgICAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgICAgICl9XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIHhsOmZsZXgtcm93IGdhcC04IG1heC13LTd4bCBteC1hdXRvXCI+XG4gICAgICAgICAgICB7LyogTWFpbiBDb250ZW50IEFyZWEgKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSBtYXgtdy00eGxcIj5cbiAgICAgICAgICAgICAgey8qIFN0aWNreSBQcm9ncmVzcyBJbmRpY2F0b3IgKi99XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3RpY2t5IHRvcC00IHotMzAgYmctZ3JheS05MDAvOTUgYmFja2Ryb3AtYmx1ci1zbSByb3VuZGVkLTJ4bCBib3JkZXIgYm9yZGVyLWdyYXktNzAwLzUwIG1iLTZcIj5cbiAgICAgICAgICAgICAgICA8RW5oYW5jZWRQcm9ncmVzc0luZGljYXRvclxuICAgICAgICAgICAgICAgICAgc3RlcHM9e3N0ZXBzfVxuICAgICAgICAgICAgICAgICAgY3VycmVudFN0ZXA9e2N1cnJlbnRTdGVwfVxuICAgICAgICAgICAgICAgICAgY29tcGxldGVkU3RlcHM9e2NvbXBsZXRlZFN0ZXBzfVxuICAgICAgICAgICAgICAgICAgb25TdGVwQ2xpY2s9e2hhbmRsZVN0ZXBDbGlja31cbiAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJtaW5pbWFsXCJcbiAgICAgICAgICAgICAgICAgIHNob3dMYWJlbHM9e3RydWV9XG4gICAgICAgICAgICAgICAgICBzaG93UHJvZ3Jlc3M9e3RydWV9XG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgey8qIEZvcm0gQ29udGVudCAqL31cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LWZ1bGxcIj5cbiAgICAgICAgICAgICAgICA8QW5pbWF0ZVByZXNlbmNlIG1vZGU9XCJ3YWl0XCI+XG4gICAgICAgICAgICAgICAgICA8bW90aW9uLmRpdiBrZXk9e2N1cnJlbnRTdGVwfSBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHg6IDIwIH19IGFuaW1hdGU9e3sgb3BhY2l0eTogMSwgeDogMCB9fSBleGl0PXt7IG9wYWNpdHk6IDAsIHg6IC0yMCB9fSB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjMgfX0+XG4gICAgICAgICAgICAgICAgICAgIHtyZW5kZXJTdGVwQ29udGVudCgpfVxuICAgICAgICAgICAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgICAgICAgICAgIDwvQW5pbWF0ZVByZXNlbmNlPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7LyogUHJldmlldyBTaWRlYmFyIC0gRGVza3RvcCBPbmx5ICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoaWRkZW4geGw6YmxvY2sgeGw6dy04MCB4bDpmbGV4LXNocmluay0wXCI+XG4gICAgICAgICAgICAgIHsvKiBTdGlja3kgUHJldmlldyBDb250YWluZXIgKi99XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3RpY2t5IHRvcC00IGgtW2NhbGMoMTAwdmgtMnJlbSldXCI+XG4gICAgICAgICAgICAgICAgPFNpbXBsZVJlc3VtZVByZXZpZXdcbiAgICAgICAgICAgICAgICAgIGZvcm1EYXRhPXtmb3JtRGF0YX1cbiAgICAgICAgICAgICAgICAgIHNlbGVjdGVkVGVtcGxhdGU9e3NlbGVjdGVkVGVtcGxhdGV9XG4gICAgICAgICAgICAgICAgICBzaG93UHJldmlldz17c2hvd1ByZXZpZXd9XG4gICAgICAgICAgICAgICAgICBvblRvZ2dsZVByZXZpZXc9eygpID0+IHNldFNob3dQcmV2aWV3KCFzaG93UHJldmlldyl9XG4gICAgICAgICAgICAgICAgICBvbk9wZW5GdWxsc2NyZWVuPXsoKSA9PiBzZXRTaG93RnVsbHNjcmVlblByZXZpZXcodHJ1ZSl9XG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIE1vYmlsZS9UYWJsZXQgUHJldmlldyAoTm9uLXN0aWNreSkgKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInhsOmhpZGRlbiBtdC04XCI+XG4gICAgICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctZ3JheS05MDAvNjAgYmFja2Ryb3AtYmx1ci1tZCByb3VuZGVkLTJ4bCBwLTYgYm9yZGVyIGJvcmRlci13aGl0ZS8xMFwiXG4gICAgICAgICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB5OiAyMCB9fVxuICAgICAgICAgICAgICAgIGFuaW1hdGU9e3sgb3BhY2l0eTogMSwgeTogMCB9fVxuICAgICAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZGVsYXk6IDAuMiB9fVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gbWItNFwiPlxuICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCBmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgICA8RXllIGNsYXNzTmFtZT1cImgtNSB3LTUgdGV4dC1uZXVyYWwtYmx1ZVwiIC8+XG4gICAgICAgICAgICAgICAgICAgIFJlc3VtZSBQcmV2aWV3XG4gICAgICAgICAgICAgICAgICA8L2gzPlxuICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTaG93UHJldmlldyghc2hvd1ByZXZpZXcpfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BweC0zIHB5LTEuNSByb3VuZGVkLWxnIHRleHQtc20gZm9udC1tZWRpdW0gdHJhbnNpdGlvbi1jb2xvcnMgJHtcbiAgICAgICAgICAgICAgICAgICAgICBzaG93UHJldmlldyA/ICdiZy1uZXVyYWwtcHVycGxlIHRleHQtd2hpdGUnIDogJ2JnLWdyYXktODAwIHRleHQtZ3JheS0zMDAgaG92ZXI6YmctZ3JheS03MDAnXG4gICAgICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICB7c2hvd1ByZXZpZXcgPyAnSGlkZScgOiAnU2hvdyd9XG4gICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICB7c2hvd1ByZXZpZXcgPyAoXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC1oLVs2MDBweF0gb3ZlcmZsb3cteS1hdXRvIGJvcmRlciBib3JkZXItd2hpdGUvMTAgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgICAgICAgICAgICA8RW5oYW5jZWRSZXN1bWVQcmV2aWV3IGZvcm1EYXRhPXtmb3JtRGF0YX0gc2VsZWN0ZWRUZW1wbGF0ZT17c2VsZWN0ZWRUZW1wbGF0ZX0gLz5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHB5LTEyIHRleHQtZ3JheS00MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgPEV5ZSBjbGFzc05hbWU9XCJoLTEyIHctMTIgbXgtYXV0byBtYi0zIG9wYWNpdHktMzBcIiAvPlxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtXCI+Q2xpY2sgXCJTaG93XCIgdG8gcHJldmlldyB5b3VyIHJlc3VtZTwvcD5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIDwvbW90aW9uLmRpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgICAgPFNtYXJ0TmF2aWdhdGlvbkJhciBjdXJyZW50U3RlcD17Y3VycmVudFN0ZXB9IHRvdGFsU3RlcHM9e3N0ZXBzLmxlbmd0aH0gb25QcmV2aW91cz17cHJldlN0ZXB9IG9uTmV4dD17bmV4dFN0ZXB9IG9uR2VuZXJhdGU9e2dlbmVyYXRlUmVzdW1lfSBvblNhdmU9e2hhbmRsZVNhdmV9IG9uUHJldmlldz17KCkgPT4gc2V0U2hvd1ByZXZpZXcoIXNob3dQcmV2aWV3KX0gaXNHZW5lcmF0aW5nPXtpc0dlbmVyYXRpbmd9IGNhblByb2NlZWQ9e2N1cnJlbnRTdGVwID09PSBzdGVwcy5sZW5ndGggLSAxID8gY2FuR2VuZXJhdGVSZXN1bWUoKSA6IGNhblByb2NlZWRUb05leHRTdGVwKGN1cnJlbnRTdGVwKX0gc2hvd1ByZXZpZXc9e3Nob3dQcmV2aWV3fSBzdGVwcz17c3RlcHN9IGZvcm1EYXRhPXtmb3JtRGF0YX0gYXRzU2NvcmU9e2F0c0FuYWx5c2lzLm92ZXJhbGxTY29yZX0gYXV0b1NhdmVFbmFibGVkPXthdXRvU2F2ZUVuYWJsZWR9IC8+XG5cblxuICAgIDwvZGl2PlxuICApO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgRW5oYW5jZWRSZXN1bWVCdWlsZGVyO1xuIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJ1c2VDYWxsYmFjayIsIm1vdGlvbiIsIkFuaW1hdGVQcmVzZW5jZSIsIlVzZXIiLCJHcmFkdWF0aW9uQ2FwIiwiQnJpZWZjYXNlIiwiQXdhcmQiLCJGaWxlVGV4dCIsIlNwYXJrbGVzIiwiU2F2ZSIsIlJvdGF0ZUNjdyIsIkRvd25sb2FkIiwiRXllIiwiVGFyZ2V0IiwiQ2xvY2siLCJDaGVja0NpcmNsZSIsIkFsZXJ0VHJpYW5nbGUiLCJMaWdodGJ1bGIiLCJ0b2FzdCIsIkVuaGFuY2VkU3RlcE5hdmlnYXRpb24iLCJTbWFydE5hdmlnYXRpb25CYXIiLCJQZXJzb25hbEluZm9Gb3JtIiwiRWR1Y2F0aW9uRm9ybSIsIkV4cGVyaWVuY2VGb3JtIiwiQmFzaWNFeHBlcmllbmNlRm9ybSIsIlNraWxsc1Byb2plY3RzRm9ybSIsIkJhc2ljU2tpbGxzUHJvamVjdHNGb3JtIiwiUmV2aWV3Rm9ybSIsIkJhc2ljUmV2aWV3Rm9ybSIsIkVuaGFuY2VkRXhwZXJpZW5jZUZvcm0iLCJFbmhhbmNlZFNraWxsc1Byb2plY3RzRm9ybSIsIkVuaGFuY2VkUHJvamVjdHNGb3JtIiwiRW5oYW5jZWRTa2lsbHNGb3JtIiwiRW5oYW5jZWRSZXZpZXdGb3JtIiwiRW5oYW5jZWRQcm9ncmVzc0luZGljYXRvciIsIkVuaGFuY2VkTmF2aWdhdGlvbiIsIlByb2dyZXNzQmFyIiwiRW5oYW5jZWRSZXN1bWVQcmV2aWV3IiwiU2ltcGxlUmVzdW1lUHJldmlldyIsIlN1Y2Nlc3NTY3JlZW4iLCJ1c2VBVFNBbmFseXNpcyIsIkVuaGFuY2VkUmVzdW1lQnVpbGRlciIsImhpZGVIZWFkZXIiLCJjdXJyZW50U3RlcCIsInNldEN1cnJlbnRTdGVwIiwic2VsZWN0ZWRUZW1wbGF0ZSIsInNldFNlbGVjdGVkVGVtcGxhdGUiLCJmb3JtRGF0YSIsInNldEZvcm1EYXRhIiwicGVyc29uYWwiLCJmaXJzdE5hbWUiLCJsYXN0TmFtZSIsImVtYWlsIiwicGhvbmUiLCJsb2NhdGlvbiIsImxpbmtlZGluIiwicG9ydGZvbGlvIiwic3VtbWFyeSIsInByb2ZpbGVJbWFnZSIsImVkdWNhdGlvbiIsImlkIiwiZGVncmVlIiwiaW5zdGl0dXRpb24iLCJzdGFydERhdGUiLCJlbmREYXRlIiwiZ3BhIiwicmVsZXZhbnQiLCJleHBlcmllbmNlIiwidGl0bGUiLCJjb21wYW55IiwiY3VycmVudCIsImRlc2NyaXB0aW9uIiwic2tpbGxzIiwidGVjaG5pY2FsIiwibGFuZ3VhZ2VzIiwiY2VydGlmaWNhdGlvbnMiLCJwcm9qZWN0cyIsIm5hbWUiLCJ0ZWNobm9sb2dpZXMiLCJsaW5rIiwiam9iRGVzY3JpcHRpb24iLCJzaG93UHJldmlldyIsInNldFNob3dQcmV2aWV3IiwiaXNHZW5lcmF0aW5nIiwic2V0SXNHZW5lcmF0aW5nIiwic2hvd1Byb2dyZXNzQmFyIiwic2V0U2hvd1Byb2dyZXNzQmFyIiwicmVzdW1lR2VuZXJhdGVkIiwic2V0UmVzdW1lR2VuZXJhdGVkIiwidmFsaWRhdGlvbkVycm9ycyIsInNldFZhbGlkYXRpb25FcnJvcnMiLCJzaG93VmFsaWRhdGlvbkVycm9ycyIsInNldFNob3dWYWxpZGF0aW9uRXJyb3JzIiwiY29tcGxldGVkU3RlcHMiLCJzZXRDb21wbGV0ZWRTdGVwcyIsImF1dG9TYXZlRW5hYmxlZCIsInNldEF1dG9TYXZlRW5hYmxlZCIsImxhc3RTYXZlZCIsInNldExhc3RTYXZlZCIsInJlc3VtZURhdGEiLCJzZXRSZXN1bWVEYXRhIiwicmVzdW1lVXJsIiwic2V0UmVzdW1lVXJsIiwiYXRzU2NvcmUiLCJzZXRBdHNTY29yZSIsInN1Z2dlc3Rpb25zIiwic2V0U3VnZ2VzdGlvbnMiLCJhaVN1Z2dlc3Rpb25zRW5hYmxlZCIsInNldEFpU3VnZ2VzdGlvbnNFbmFibGVkIiwic21hcnRWYWxpZGF0aW9uIiwic2V0U21hcnRWYWxpZGF0aW9uIiwic2hvd0hpbnRzIiwic2V0U2hvd0hpbnRzIiwic3RlcHMiLCJpY29uIiwiZXN0aW1hdGVkVGltZSIsInJlcXVpcmVkIiwiYXRzQW5hbHlzaXMiLCJiYWNrdXBEYXRhIiwibG9jYWxTdG9yYWdlIiwiZ2V0SXRlbSIsImF1dGhUaW1lc3RhbXAiLCJzYXZlZERhdGEiLCJ0aW1lRGlmZiIsIkRhdGUiLCJub3ciLCJwYXJzZUludCIsInBhcnNlZCIsIkpTT04iLCJwYXJzZSIsInN1Y2Nlc3MiLCJyZW1vdmVJdGVtIiwic2V0SXRlbSIsInRvSVNPU3RyaW5nIiwiZXJyb3IiLCJjb25zb2xlIiwid2luZG93IiwidGltZW91dElkIiwic2V0VGltZW91dCIsInN0cmluZ2lmeSIsImNsZWFyVGltZW91dCIsInZhbGlkYXRlU3RlcCIsInN0ZXBJbmRleCIsImVycm9ycyIsInRyaW0iLCJ0ZXN0IiwidmFsaWRFZHVjYXRpb24iLCJmaWx0ZXIiLCJlZHUiLCJsZW5ndGgiLCJ2YWxpZEV4cGVyaWVuY2UiLCJleHAiLCJjYW5Qcm9jZWVkVG9OZXh0U3RlcCIsIk9iamVjdCIsImtleXMiLCJnZXRDb21wbGV0ZWRTdGVwcyIsImNvbXBsZXRlZCIsImkiLCJwdXNoIiwiZ2V0U3RlcENvbXBsZXRpb25TdGF0dXMiLCJzdGF0dXMiLCJmb3JFYWNoIiwic3RlcCIsImluZGV4IiwiY2FuR2VuZXJhdGVSZXN1bWUiLCJ1cGRhdGVGb3JtRGF0YSIsInNlY3Rpb24iLCJmaWVsZCIsInZhbHVlIiwicHJldiIsIm5ld0RhdGEiLCJBcnJheSIsImlzQXJyYXkiLCJuZXdBcnJheSIsIm5ld0Vycm9ycyIsImFkZEFycmF5SXRlbSIsInRlbXBsYXRlIiwiTWF0aCIsInJhbmRvbSIsInRvU3RyaW5nIiwic3Vic3RyaW5nIiwicmVtb3ZlQXJyYXlJdGVtIiwiaXRlbSIsIm5leHRTdGVwIiwiZXJyb3JNZXNzYWdlcyIsInZhbHVlcyIsIlNldCIsInByZXZTdGVwIiwiaGFuZGxlU3RlcENsaWNrIiwiaW5jbHVkZXMiLCJnZW5lcmF0ZVJlc3VtZSIsImFsbEVycm9ycyIsInN0ZXBFcnJvcnMiLCJhc3NpZ24iLCJyZXNwb25zZSIsImZldGNoIiwibWV0aG9kIiwiaGVhZGVycyIsImJvZHkiLCJ0ZW1wbGF0ZUlkIiwiY29udGVudFR5cGUiLCJnZXQiLCJFcnJvciIsImRhdGEiLCJqc29uIiwib2siLCJkb3dubG9hZFVybCIsIm1lc3NhZ2UiLCJoYW5kbGVTYXZlIiwiaGFuZGxlQUlTdWdnZXN0IiwibG9hZGluZyIsIlByb21pc2UiLCJyZXNvbHZlIiwiZGlzbWlzcyIsImhhbmRsZUdlbmVyYXRlUmVzdW1lIiwiYWRkRXZlbnRMaXN0ZW5lciIsInJlbW92ZUV2ZW50TGlzdGVuZXIiLCJnZXRTdGVwVmFsaWRhdGlvbiIsInZhbGlkYXRpb24iLCJyZW5kZXJTdGVwQ29udGVudCIsImNvbW1vblByb3BzIiwib25TYXZlIiwib25BSVN1Z2dlc3QiLCJvblRlbXBsYXRlU2VsZWN0Iiwib25TdGFydE92ZXIiLCJvbkVkaXRSZXN1bWUiLCJkaXYiLCJjbGFzc05hbWUiLCJpc1Zpc2libGUiLCJvbkNvbXBsZXRlIiwiaW5pdGlhbCIsIm9wYWNpdHkiLCJ5IiwiYW5pbWF0ZSIsImgxIiwicCIsIm9uU3RlcENsaWNrIiwidmFyaWFudCIsInNob3dMYWJlbHMiLCJzaG93UHJvZ3Jlc3MiLCJtb2RlIiwieCIsImV4aXQiLCJ0cmFuc2l0aW9uIiwiZHVyYXRpb24iLCJvblRvZ2dsZVByZXZpZXciLCJvbk9wZW5GdWxsc2NyZWVuIiwic2V0U2hvd0Z1bGxzY3JlZW5QcmV2aWV3IiwiZGVsYXkiLCJoMyIsImJ1dHRvbiIsIm9uQ2xpY2siLCJ0b3RhbFN0ZXBzIiwib25QcmV2aW91cyIsIm9uTmV4dCIsIm9uR2VuZXJhdGUiLCJvblByZXZpZXciLCJjYW5Qcm9jZWVkIiwib3ZlcmFsbFNjb3JlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/resume/ResumeBuilder.jsx\n"));

/***/ })

});
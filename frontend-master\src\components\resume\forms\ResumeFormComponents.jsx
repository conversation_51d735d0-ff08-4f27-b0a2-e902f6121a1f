import { motion } from "framer-motion";
import {
  Briefcase,
  Award,
  FileText,
  Plus,
  Trash2,
  MapPin,
  Calendar,
  CheckCircle,
  User,
  Mail,
  Phone,
  Globe,
  Link,
  GraduationCap
} from "lucide-react";
import ATSFieldIndicator from "@/components/resume/ats/FieldIndicator";
import ClientOnly from "@/components/common/ClientOnly";
import ATSTooltip from "@/components/resume/ats/Tooltip";

// Personal Information Form Component
export const PersonalInfoForm = ({ formData, updateFormData, atsAnalysis, validationErrors = {}, showValidationErrors = false }) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    exit={{ opacity: 0, y: -20 }}
    className="space-y-6"
  >
    {/* Validation Error Summary */}
    {showValidationErrors && (validationErrors.firstName || validationErrors.lastName || validationErrors.email) && (
      <motion.div
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-red-500/10 border border-red-500/20 rounded-xl p-4 mb-6"
      >
        <div className="flex items-center gap-2 mb-2">
          <div className="w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
            <span className="text-white text-xs font-bold">!</span>
          </div>
          <h4 className="text-red-400 font-medium">Personal Information Required</h4>
        </div>
        <div className="space-y-1">
          {validationErrors.firstName && <p className="text-red-300 text-sm">{validationErrors.firstName}</p>}
          {validationErrors.lastName && <p className="text-red-300 text-sm">{validationErrors.lastName}</p>}
          {validationErrors.email && <p className="text-red-300 text-sm">{validationErrors.email}</p>}
        </div>
      </motion.div>
    )}

    <div className="flex items-center justify-between mb-6">
      <div className="flex items-center gap-3">
        <User className="h-6 w-6 text-neural-purple" />
        <h2 className="text-2xl font-bold">Personal Information</h2>
      </div>
    </div>

    <div className="bg-gray-800/30 p-6 rounded-xl border border-gray-700 space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            First Name *
          </label>
          <input
            type="text"
            value={formData.personal.firstName}
            onChange={(e) => updateFormData('personal', 'firstName', e.target.value)}
            className={`w-full px-4 py-3 bg-gray-800/50 border rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400 ${
              showValidationErrors && validationErrors.firstName
                ? 'border-red-500 focus:ring-red-500 focus:border-red-500' 
                : 'border-gray-700'
            }`}
            placeholder="John"
          />
          <ClientOnly>
            <ATSFieldIndicator
              fieldName="firstName"
              value={formData.personal.firstName}
              analysis={atsAnalysis?.fieldAnalysis?.firstName}
            />
          </ClientOnly>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Last Name *
          </label>
          <input
            type="text"
            value={formData.personal.lastName}
            onChange={(e) => updateFormData('personal', 'lastName', e.target.value)}
            className={`w-full px-4 py-3 bg-gray-800/50 border rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400 ${
              showValidationErrors && validationErrors.lastName
                ? 'border-red-500 focus:ring-red-500 focus:border-red-500' 
                : 'border-gray-700'
            }`}
            placeholder="Doe"
          />
          <ClientOnly>
            <ATSFieldIndicator
              fieldName="lastName"
              value={formData.personal.lastName}
              analysis={atsAnalysis?.fieldAnalysis?.lastName}
            />
          </ClientOnly>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            <Mail className="inline h-4 w-4 mr-1" />
            Email Address *
          </label>
          <input
            type="email"
            value={formData.personal.email}
            onChange={(e) => updateFormData('personal', 'email', e.target.value)}
            className={`w-full px-4 py-3 bg-gray-800/50 border rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400 ${
              showValidationErrors && validationErrors.email
                ? 'border-red-500 focus:ring-red-500 focus:border-red-500' 
                : 'border-gray-700'
            }`}
            placeholder="<EMAIL>"
          />
          <ClientOnly>
            <ATSFieldIndicator
              fieldName="email"
              value={formData.personal.email}
              analysis={atsAnalysis?.fieldAnalysis?.email}
            />
          </ClientOnly>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            <Phone className="inline h-4 w-4 mr-1" />
            Phone Number
          </label>
          <input
            type="tel"
            value={formData.personal.phone}
            onChange={(e) => updateFormData('personal', 'phone', e.target.value)}
            className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400"
            placeholder="+****************"
          />
          <ClientOnly>
            <ATSFieldIndicator
              fieldName="phone"
              value={formData.personal.phone}
              analysis={atsAnalysis?.fieldAnalysis?.phone}
            />
          </ClientOnly>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            <MapPin className="inline h-4 w-4 mr-1" />
            Location
          </label>
          <input
            type="text"
            value={formData.personal.location}
            onChange={(e) => updateFormData('personal', 'location', e.target.value)}
            className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400"
            placeholder="San Francisco, CA"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            <Link className="inline h-4 w-4 mr-1" />
            LinkedIn Profile
          </label>
          <input
            type="url"
            value={formData.personal.linkedin}
            onChange={(e) => updateFormData('personal', 'linkedin', e.target.value)}
            className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400"
            placeholder="https://linkedin.com/in/johndoe"
          />
        </div>

        <div className="md:col-span-2">
          <label className="block text-sm font-medium text-gray-300 mb-2">
            <Globe className="inline h-4 w-4 mr-1" />
            Portfolio/Website
          </label>
          <input
            type="url"
            value={formData.personal.portfolio}
            onChange={(e) => updateFormData('personal', 'portfolio', e.target.value)}
            className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400"
            placeholder="https://johndoe.com"
          />
        </div>

        <div className="md:col-span-2">
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Professional Summary
          </label>
          <textarea
            value={formData.personal.summary}
            onChange={(e) => updateFormData('personal', 'summary', e.target.value)}
            rows={4}
            className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400 resize-none"
            placeholder="Write a compelling summary that highlights your key skills, experience, and career objectives. This should be 2-3 sentences that grab the hiring manager's attention."
          />
          <ClientOnly>
            <ATSFieldIndicator
              fieldName="summary"
              value={formData.personal.summary}
              analysis={atsAnalysis?.fieldAnalysis?.summary}
              showDetails={true}
            />
          </ClientOnly>
        </div>
      </div>
    </div>
  </motion.div>
);

// Education Form Component
export const EducationForm = ({ formData, updateFormData, addArrayItem, removeArrayItem, atsAnalysis, validationErrors = {}, showValidationErrors = false }) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    exit={{ opacity: 0, y: -20 }}
    className="space-y-6"
  >
    {/* Validation Error Summary */}
    {showValidationErrors && validationErrors.education && (
      <motion.div
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-red-500/10 border border-red-500/20 rounded-xl p-4 mb-6"
      >
        <div className="flex items-center gap-2 mb-2">
          <div className="w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
            <span className="text-white text-xs font-bold">!</span>
          </div>
          <h4 className="text-red-400 font-medium">Education Required</h4>
        </div>
        <p className="text-red-300 text-sm">{validationErrors.education}</p>
      </motion.div>
    )}

    <div className="flex items-center justify-between mb-6">
      <div className="flex items-center gap-3">
        <GraduationCap className="h-6 w-6 text-neural-purple" />
        <h2 className="text-2xl font-bold">Education</h2>
      </div>
      <button
        onClick={() => addArrayItem('education', {
          degree: "",
          institution: "",
          location: "",
          startDate: "",
          endDate: "",
          gpa: "",
          relevant: ""
        })}
        className="inline-flex items-center gap-2 px-4 py-2 bg-neural-purple hover:bg-neural-purple/80 text-white rounded-lg transition-colors"
      >
        <Plus className="h-4 w-4" />
        Add Education
      </button>
    </div>

    {formData.education.map((edu, index) => (
      <div key={edu.id} className="bg-gray-800/30 p-6 rounded-xl border border-gray-700 space-y-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold">Education {index + 1}</h3>
          {formData.education.length > 1 && (
            <button
              onClick={() => removeArrayItem('education', edu.id)}
              className="text-red-400 hover:text-red-300 transition-colors"
            >
              <Trash2 className="h-4 w-4" />
            </button>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Degree/Qualification *
            </label>
            <input
              type="text"
              value={edu.degree}
              onChange={(e) => updateFormData('education', 'degree', e.target.value, index)}
              className={`w-full px-4 py-3 bg-gray-800/50 border rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400 ${
                showValidationErrors && validationErrors.education && !edu.degree.trim()
                  ? 'border-red-500 focus:ring-red-500 focus:border-red-500' 
                  : 'border-gray-700'
              }`}
              placeholder="Bachelor of Science in Computer Science"
            />
            <ClientOnly>
              <ATSFieldIndicator
                fieldName="degree"
                value={edu.degree}
                analysis={atsAnalysis?.fieldAnalysis?.[`education_degree_${index}`]}
              />
            </ClientOnly>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Institution/University *
            </label>
            <input
              type="text"
              value={edu.institution}
              onChange={(e) => updateFormData('education', 'institution', e.target.value, index)}
              className={`w-full px-4 py-3 bg-gray-800/50 border rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400 ${
                showValidationErrors && validationErrors.education && !edu.institution.trim()
                  ? 'border-red-500 focus:ring-red-500 focus:border-red-500' 
                  : 'border-gray-700'
              }`}
              placeholder="Stanford University"
            />
            <ClientOnly>
              <ATSFieldIndicator
                fieldName="institution"
                value={edu.institution}
                analysis={atsAnalysis?.fieldAnalysis?.[`education_institution_${index}`]}
              />
            </ClientOnly>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              <MapPin className="inline h-4 w-4 mr-1" />
              Location
            </label>
            <input
              type="text"
              value={edu.location}
              onChange={(e) => updateFormData('education', 'location', e.target.value, index)}
              className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400"
              placeholder="Palo Alto, CA"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              GPA (Optional)
            </label>
            <input
              type="text"
              value={edu.gpa}
              onChange={(e) => updateFormData('education', 'gpa', e.target.value, index)}
              className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400"
              placeholder="3.8/4.0"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              <Calendar className="inline h-4 w-4 mr-1" />
              Start Date
            </label>
            <input
              type="month"
              value={edu.startDate}
              onChange={(e) => updateFormData('education', 'startDate', e.target.value, index)}
              className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              <Calendar className="inline h-4 w-4 mr-1" />
              End Date
            </label>
            <input
              type="month"
              value={edu.endDate}
              onChange={(e) => updateFormData('education', 'endDate', e.target.value, index)}
              className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white"
            />
          </div>

          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Relevant Coursework, Projects, or Achievements
            </label>
            <textarea
              value={edu.relevant}
              onChange={(e) => updateFormData('education', 'relevant', e.target.value, index)}
              rows={3}
              className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400 resize-none"
              placeholder="• Data Structures and Algorithms, Software Engineering, Database Systems&#10;• Dean's List (Fall 2019, Spring 2020)&#10;• Capstone Project: Built a machine learning model for fraud detection"
            />
            <ClientOnly>
              <ATSFieldIndicator
                fieldName="education_relevant"
                value={edu.relevant}
                analysis={atsAnalysis?.fieldAnalysis?.[`education_relevant_${index}`]}
                showDetails={true}
              />
            </ClientOnly>
          </div>
        </div>
      </div>
    ))}
  </motion.div>
);

// Experience Form Component
export const ExperienceForm = ({ formData, updateFormData, addArrayItem, removeArrayItem, atsAnalysis, validationErrors = {}, showValidationErrors = false }) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    exit={{ opacity: 0, y: -20 }}
    className="space-y-6"
  >
    {/* Validation Error Summary */}
    {showValidationErrors && validationErrors.experience && (
      <motion.div
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-red-500/10 border border-red-500/20 rounded-xl p-4 mb-6"
      >
        <div className="flex items-center gap-2 mb-2">
          <div className="w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
            <span className="text-white text-xs font-bold">!</span>
          </div>
          <h4 className="text-red-400 font-medium">Work Experience Required</h4>
        </div>
        <p className="text-red-300 text-sm">{validationErrors.experience}</p>
      </motion.div>
    )}

    <div className="flex items-center justify-between mb-6">
      <div className="flex items-center gap-3">
        <Briefcase className="h-6 w-6 text-neural-purple" />
        <h2 className="text-2xl font-bold">Work Experience</h2>
      </div>
      <button
        onClick={() => addArrayItem('experience', {
          title: "",
          company: "",
          location: "",
          startDate: "",
          endDate: "",
          current: false,
          description: ""
        })}
        className="flex items-center gap-2 px-4 py-2 bg-neural-purple/20 hover:bg-neural-purple/30 border border-neural-purple/50 rounded-lg transition-colors"
      >
        <Plus className="h-4 w-4" />
        Add Experience
      </button>
    </div>

    <div className="space-y-6">
      {formData.experience.map((exp, index) => (
        <div key={exp.id} className="bg-gray-800/30 p-6 rounded-lg border border-gray-700">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold">Experience {index + 1}</h3>
            {formData.experience.length > 1 && (
              <button
                onClick={() => removeArrayItem('experience', exp.id)}
                className="text-red-400 hover:text-red-300 transition-colors"
              >
                <Trash2 className="h-4 w-4" />
              </button>
            )}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Job Title *
                <ATSTooltip fieldType="job_title" className="ml-2" />
              </label>
              <input
                type="text"
                value={exp.title}
                onChange={(e) => updateFormData('experience', 'title', e.target.value, index)}
                className={`w-full px-4 py-3 bg-gray-800/50 border rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400 ${
                  showValidationErrors && validationErrors.experience && !exp.title.trim()
                    ? 'border-red-500 focus:ring-red-500 focus:border-red-500' 
                    : 'border-gray-700'
                }`}
                placeholder="Software Developer"
              />
              <ClientOnly>
                <ATSFieldIndicator
                  fieldName="job_title"
                  value={exp.title}
                  analysis={atsAnalysis?.fieldAnalysis?.[`experience_title_${index}`]}
                />
              </ClientOnly>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Company *
              </label>
              <input
                type="text"
                value={exp.company}
                onChange={(e) => updateFormData('experience', 'company', e.target.value, index)}
                className={`w-full px-4 py-3 bg-gray-800/50 border rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400 ${
                  showValidationErrors && validationErrors.experience && !exp.company.trim()
                    ? 'border-red-500 focus:ring-red-500 focus:border-red-500' 
                    : 'border-gray-700'
                }`}
                placeholder="Tata Consultancy Services (TCS)"
              />
              <ClientOnly>
                <ATSFieldIndicator
                  fieldName="company"
                  value={exp.company}
                  analysis={atsAnalysis?.fieldAnalysis?.[`experience_company_${index}`]}
                />
              </ClientOnly>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                <MapPin className="inline h-4 w-4 mr-1" />
                Location
              </label>
              <input
                type="text"
                value={exp.location}
                onChange={(e) => updateFormData('experience', 'location', e.target.value, index)}
                className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400"
                placeholder="Bangalore, Karnataka"
              />
            </div>

            <div className="flex items-center space-x-4">
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={exp.current}
                  onChange={(e) => updateFormData('experience', 'current', e.target.checked, index)}
                  className="w-4 h-4 text-neural-purple bg-gray-800 border-gray-600 rounded focus:ring-neural-purple focus:ring-2"
                />
                <span className="text-sm text-gray-300">Currently working here</span>
              </label>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                <Calendar className="inline h-4 w-4 mr-1" />
                Start Date
              </label>
              <input
                type="month"
                value={exp.startDate}
                onChange={(e) => updateFormData('experience', 'startDate', e.target.value, index)}
                className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                <Calendar className="inline h-4 w-4 mr-1" />
                End Date
              </label>
              <input
                type="month"
                value={exp.endDate}
                onChange={(e) => updateFormData('experience', 'endDate', e.target.value, index)}
                disabled={exp.current}
                className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white disabled:opacity-50 disabled:cursor-not-allowed"
              />
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Job Description & Achievements
              </label>
              <textarea
                value={exp.description}
                onChange={(e) => updateFormData('experience', 'description', e.target.value, index)}
                rows={4}
                className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400 resize-none"
                placeholder="• Developed and maintained web applications using React and Node.js&#10;• Led a team of 5 developers in implementing new features&#10;• Improved application performance by 40% and reduced load time by 2 seconds&#10;• Collaborated with cross-functional teams to deliver projects on time"
              />
              <ClientOnly>
                <ATSFieldIndicator
                  fieldName="experience_description"
                  value={exp.description}
                  analysis={atsAnalysis?.fieldAnalysis?.[`experience_description_${index}`]}
                  showDetails={true}
                />
              </ClientOnly>
            </div>
          </div>
        </div>
      ))}
    </div>
  </motion.div>
);

// Skills & Projects Form Component
export const SkillsProjectsForm = ({ formData, updateFormData, addArrayItem, removeArrayItem, atsAnalysis, validationErrors = {}, showValidationErrors = false }) => {
  const addSkill = (category, skill) => {
    if (skill.trim() && !formData.skills[category].includes(skill.trim())) {
      const newSkills = [...formData.skills[category], skill.trim()];
      updateFormData('skills', category, newSkills);
    }
  };

  const removeSkill = (category, skillToRemove) => {
    const newSkills = formData.skills[category].filter(skill => skill !== skillToRemove);
    updateFormData('skills', category, newSkills);
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="space-y-8"
    >
      {/* Validation Error Summary */}
      {showValidationErrors && validationErrors.skills && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-red-500/10 border border-red-500/20 rounded-xl p-4 mb-6"
        >
          <div className="flex items-center gap-2 mb-2">
            <div className="w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
              <span className="text-white text-xs font-bold">!</span>
            </div>
            <h4 className="text-red-400 font-medium">Skills Required</h4>
          </div>
          <p className="text-red-300 text-sm">{validationErrors.skills}</p>
        </motion.div>
      )}

      <div className="flex items-center gap-3 mb-6">
        <Award className="h-6 w-6 text-neural-purple" />
        <h2 className="text-2xl font-bold">Skills & Projects</h2>
      </div>

      {/* Skills Section */}
      <div className="space-y-6">
        <h3 className="text-xl font-semibold">Skills</h3>
        
        {/* Technical Skills */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Technical Skills
          </label>
          <SkillInput
            skills={formData.skills.technical}
            onAdd={(skill) => addSkill('technical', skill)}
            onRemove={(skill) => removeSkill('technical', skill)}
            placeholder="JavaScript, React, Node.js, Python, Java, Spring Boot..."
            hasError={showValidationErrors && validationErrors.skills}
          />
          <ClientOnly>
            <ATSFieldIndicator
              fieldName="technical_skills"
              value={formData.skills.technical}
              analysis={atsAnalysis?.fieldAnalysis?.technical_skills}
              showDetails={true}
            />
          </ClientOnly>
        </div>

        {/* Languages */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Languages
          </label>
          <SkillInput 
            skills={formData.skills.languages}
            onAdd={(skill) => addSkill('languages', skill)}
            onRemove={(skill) => removeSkill('languages', skill)}
            placeholder="English (Fluent), Hindi (Native), Tamil (Conversational)..."
            hasError={showValidationErrors && validationErrors.skills}
          />
        </div>

        {/* Certifications */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Certifications
          </label>
          <SkillInput 
            skills={formData.skills.certifications}
            onAdd={(skill) => addSkill('certifications', skill)}
            onRemove={(skill) => removeSkill('certifications', skill)}
            placeholder="AWS Certified Developer, Oracle Java Certified, Google Cloud Professional..."
            hasError={showValidationErrors && validationErrors.skills}
          />
        </div>
      </div>

      {/* Projects Section */}
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h3 className="text-xl font-semibold">Projects</h3>
          <button
            onClick={() => addArrayItem('projects', {
              name: "",
              description: "",
              technologies: "",
              link: ""
            })}
            className="flex items-center gap-2 px-4 py-2 bg-neural-purple/20 hover:bg-neural-purple/30 border border-neural-purple/50 rounded-lg transition-colors"
          >
            <Plus className="h-4 w-4" />
            Add Project
          </button>
        </div>

        <div className="space-y-4">
          {formData.projects.map((project, index) => (
            <div key={project.id} className="bg-gray-800/30 p-6 rounded-lg border border-gray-700">
              <div className="flex justify-between items-center mb-4">
                <h4 className="text-lg font-semibold">Project {index + 1}</h4>
                {formData.projects.length > 1 && (
                  <button
                    onClick={() => removeArrayItem('projects', project.id)}
                    className="text-red-400 hover:text-red-300 transition-colors"
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Project Name *
                  </label>
                  <input
                    type="text"
                    value={project.name}
                    onChange={(e) => updateFormData('projects', 'name', e.target.value, index)}
                    className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400"
                    placeholder="Online Banking System"
                  />
                  <ClientOnly>
                    <ATSFieldIndicator
                      fieldName="project_name"
                      value={project.name}
                      analysis={atsAnalysis?.fieldAnalysis?.[`project_name_${index}`]}
                    />
                  </ClientOnly>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Project Link
                  </label>
                  <input
                    type="url"
                    value={project.link}
                    onChange={(e) => updateFormData('projects', 'link', e.target.value, index)}
                    className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400"
                    placeholder="https://github.com/rahulsharma/banking-system"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Technologies Used
                  </label>
                  <input
                    type="text"
                    value={project.technologies}
                    onChange={(e) => updateFormData('projects', 'technologies', e.target.value, index)}
                    className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400"
                    placeholder="React, Node.js, MongoDB, Express.js, JWT"
                  />
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Project Description
                  </label>
                  <textarea
                    value={project.description}
                    onChange={(e) => updateFormData('projects', 'description', e.target.value, index)}
                    rows={3}
                    className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400 resize-none"
                    placeholder="Developed a secure online banking system with user authentication, transaction management, and real-time notifications. Implemented robust security measures and achieved 99.9% uptime..."
                  />
                  <ClientOnly>
                    <ATSFieldIndicator
                      fieldName="project_description"
                      value={project.description}
                      analysis={atsAnalysis?.fieldAnalysis?.[`project_description_${index}`]}
                      showDetails={true}
                    />
                  </ClientOnly>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </motion.div>
  );
};

// Skill Input Component
const SkillInput = ({ skills, onAdd, onRemove, placeholder, hasError = false }) => {
  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && e.target.value.trim()) {
      onAdd(e.target.value);
      e.target.value = '';
    }
  };

  return (
    <div className="space-y-3">
      <input
        type="text"
        onKeyPress={handleKeyPress}
        className={`w-full px-4 py-3 bg-gray-800/50 border rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400 ${
          hasError ? 'border-red-500 focus:ring-red-500' : 'border-gray-700'
        }`}
        placeholder={`${placeholder} (Press Enter to add)`}
      />
      <div className="flex flex-wrap gap-2">
        {skills.map((skill, index) => (
          <span
            key={index}
            className="inline-flex items-center gap-1 px-3 py-1 bg-neural-purple/20 border border-neural-purple/50 rounded-full text-sm"
          >
            {skill}
            <button
              onClick={() => onRemove(skill)}
              className="text-neural-pink hover:text-red-400 transition-colors"
            >
              ×
            </button>
          </span>
        ))}
      </div>
    </div>
  );
};

// Review Form Component
export const ReviewForm = ({ formData, updateFormData, atsAnalysis }) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    exit={{ opacity: 0, y: -20 }}
    className="space-y-6"
  >
    <div className="flex items-center gap-3 mb-6">
      <FileText className="h-6 w-6 text-neural-purple" />
      <h2 className="text-2xl font-bold">Review & Generate</h2>
    </div>

    {/* Job Description Field for AI Optimization */}
    <div className="bg-gradient-to-r from-neural-purple/10 to-neural-pink/10 border border-neural-purple/30 rounded-xl p-6 mb-6">
      <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
        <Award className="h-5 w-5 text-neural-purple" />
        AI-Powered Job Matching (Optional)
      </h3>
      <p className="text-gray-300 text-sm mb-4">
        Paste a job description below to optimize your resume for this specific role using AI.
        Our system will automatically highlight relevant skills and experiences.
      </p>
      <textarea
        value={formData.jobDescription || ''}
        onChange={(e) => updateFormData('jobDescription', '', e.target.value)}
        rows={6}
        className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400 resize-none"
        placeholder="Paste the job description here for AI-powered resume optimization...

Example:
We are looking for a Senior Software Engineer with 5+ years of experience in React, Node.js, and cloud technologies. The ideal candidate should have experience with microservices, AWS, and agile development methodologies..."
      />
      <div className="mt-2 text-xs text-gray-400">
        💡 Adding a job description helps our AI tailor your resume to match specific requirements and keywords
      </div>
    </div>

    <div className="space-y-6">
      {/* Personal Info Summary */}
      <div className="bg-gray-800/30 p-6 rounded-lg border border-gray-700">
        <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
          <CheckCircle className="h-5 w-5 text-green-500" />
          Personal Information
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <p><span className="text-gray-400">Name:</span> {formData.personal.firstName} {formData.personal.lastName}</p>
          <p><span className="text-gray-400">Email:</span> {formData.personal.email}</p>
          <p><span className="text-gray-400">Phone:</span> {formData.personal.phone || 'Not provided'}</p>
          <p><span className="text-gray-400">Location:</span> {formData.personal.location || 'Not provided'}</p>
        </div>
      </div>

      {/* Education Summary */}
      <div className="bg-gray-800/30 p-6 rounded-lg border border-gray-700">
        <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
          <CheckCircle className="h-5 w-5 text-green-500" />
          Education ({formData.education.length})
        </h3>
        {formData.education.map((edu, index) => (
          <div key={edu.id} className="mb-3 last:mb-0">
            <p className="font-medium">{edu.degree} - {edu.institution}</p>
            <p className="text-sm text-gray-400">{edu.startDate} - {edu.endDate}</p>
          </div>
        ))}
      </div>

      {/* Experience Summary */}
      <div className="bg-gray-800/30 p-6 rounded-lg border border-gray-700">
        <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
          <CheckCircle className="h-5 w-5 text-green-500" />
          Experience ({formData.experience.length})
        </h3>
        {formData.experience.map((exp, index) => (
          <div key={exp.id} className="mb-3 last:mb-0">
            <p className="font-medium">{exp.title} - {exp.company}</p>
            <p className="text-sm text-gray-400">{exp.startDate} - {exp.current ? 'Present' : exp.endDate}</p>
          </div>
        ))}
      </div>

      {/* Skills Summary */}
      <div className="bg-gray-800/30 p-6 rounded-lg border border-gray-700">
        <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
          <CheckCircle className="h-5 w-5 text-green-500" />
          Skills & Projects
        </h3>
        <div className="space-y-2 text-sm">
          <p><span className="text-gray-400">Technical Skills:</span> {formData.skills.technical.join(', ') || 'None added'}</p>
          <p><span className="text-gray-400">Languages:</span> {formData.skills.languages.join(', ') || 'None added'}</p>
          <p><span className="text-gray-400">Projects:</span> {formData.projects.length} project(s)</p>
        </div>
      </div>
    </div>

    <div className="bg-neural-purple/10 border border-neural-purple/30 rounded-lg p-6">
      <h4 className="text-lg font-semibold mb-2 text-neural-purple">Ready to Generate!</h4>
      <p className="text-gray-300">
        Your resume information looks complete. Click "Generate Resume" to create your professional resume using AI.
      </p>
    </div>
  </motion.div>
);

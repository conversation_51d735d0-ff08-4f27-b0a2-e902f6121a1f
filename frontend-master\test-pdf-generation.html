<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF Generation Test</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
</head>
<body>
    <div id="test-content" style="font-family: Arial, sans-serif; color: #333; line-height: 1.6; max-width: 210mm; margin: 0; padding: 20mm; background: white;">
        <!-- Header Section -->
        <div style="text-align: center; margin-bottom: 30px; padding-bottom: 20px;">
            <h1 style="margin: 0 0 15px 0; font-size: 24pt; font-weight: bold; color: #000; text-transform: uppercase; letter-spacing: 1px;">
                JOHN DOE
            </h1>
            <div style="margin: 12px 0; color: #333; font-size: 12pt; font-weight: 500;">
                <EMAIL> | (555) 123-4567 | New York, NY
            </div>
            <div style="width: 60%; height: 2px; background: #000; margin: 15px auto;"></div>
        </div>        <!-- Professional Summary -->
        <div style="margin-bottom: 30px;">
            <h2 style="font-size: 14pt; font-weight: bold; color: #000; margin: 0 0 5px 0; text-transform: uppercase; letter-spacing: 0.8px;">
                PROFESSIONAL SUMMARY
            </h2>
            <hr style="border: none; border-top: 2px solid #000; width: 50px; margin: 0 0 15px 0;" />
            <p style="margin: 0; font-size: 11pt; line-height: 1.6; color: #333; text-align: justify;">
                Experienced software developer with 5+ years of expertise in full-stack development, 
                specializing in React, Node.js, and cloud technologies. Proven track record of 
                delivering scalable applications and leading development teams.
            </p>
        </div>

        <!-- Professional Experience -->
        <div style="margin-bottom: 30px;">
            <h2 style="font-size: 14pt; font-weight: bold; color: #000; margin: 0 0 5px 0; text-transform: uppercase; letter-spacing: 0.8px;">
                PROFESSIONAL EXPERIENCE
            </h2>
            <hr style="border: none; border-top: 2px solid #000; width: 50px; margin: 0 0 15px 0;" />
            <div style="margin-bottom: 25px;">
                <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 10px;">
                    <div style="flex: 1;">
                        <h3 style="margin: 0; font-size: 13pt; font-weight: bold; color: #000;">Senior Software Developer</h3>
                        <p style="margin: 4px 0; font-size: 12pt; color: #000; font-weight: 600;">Tech Company Inc.</p>
                        <p style="margin: 2px 0; font-size: 10pt; color: #333;">San Francisco, CA</p>
                    </div>
                    <div style="text-align: right; font-size: 11pt; color: #333; font-weight: 500; margin-left: 20px; white-space: nowrap;">
                        Jan 2020 - Present
                    </div>
                </div>
                <ul style="margin: 8px 0 0 20px; padding: 0; list-style-type: disc;">
                    <li style="margin-bottom: 6px; font-size: 11pt; line-height: 1.6; color: #333;">Led development of React-based dashboard improving user engagement by 40%</li>
                    <li style="margin-bottom: 6px; font-size: 11pt; line-height: 1.6; color: #333;">Architected microservices infrastructure reducing system downtime by 60%</li>
                    <li style="margin-bottom: 6px; font-size: 11pt; line-height: 1.6; color: #333;">Mentored junior developers and established coding best practices</li>
                </ul>
            </div>
        </div>
    </div>

    <button onclick="generatePDF()">Test PDF Generation</button>

    <script>
        async function generatePDF() {
            try {
                const element = document.getElementById('test-content');
                
                const canvas = await html2canvas(element, {
                    scale: 2,
                    useCORS: true,
                    backgroundColor: '#ffffff',
                    allowTaint: true,
                    foreignObjectRendering: true,
                    imageTimeout: 15000,
                    removeContainer: true
                });

                const imgData = canvas.toDataURL('image/png');
                const { jsPDF } = window.jspdf;
                const pdf = new jsPDF('p', 'mm', 'a4');

                const imgWidth = 210; // A4 width
                const pageHeight = 295; // A4 height
                const imgHeight = (canvas.height * imgWidth) / canvas.width;
                let heightLeft = imgHeight;

                let position = 0;

                pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
                heightLeft -= pageHeight;

                while (heightLeft >= 0) {
                    position = heightLeft - imgHeight;
                    pdf.addPage();
                    pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
                    heightLeft -= pageHeight;
                }

                pdf.save('test-resume.pdf');
                alert('PDF generated successfully!');
            } catch (error) {
                console.error('Error generating PDF:', error);
                alert('Error generating PDF: ' + error.message);
            }
        }
    </script>
</body>
</html>

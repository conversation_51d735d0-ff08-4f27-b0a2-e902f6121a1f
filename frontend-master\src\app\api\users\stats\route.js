import { NextResponse } from 'next/server';
import { collection, getCountFromServer, getDocs, query, orderBy, limit } from 'firebase/firestore';
import { firestore } from '@/firebase/config';

export async function GET() {
  try {
    // Get total count of reviews (as a proxy for engagement)
    const reviewsCollection = collection(firestore, 'reviews');
    const reviewsCountSnapshot = await getCountFromServer(reviewsCollection);
    const reviewsCount = reviewsCountSnapshot.data().count;

    // Get recent reviews for activity metrics
    const recentReviewsQuery = query(
      reviewsCollection,
      orderBy('createdAt', 'desc'),
      limit(10)
    );
    const recentReviewsSnapshot = await getDocs(recentReviewsQuery);
    
    const recentReviews = [];
    const uniqueUsers = new Set();
    
    recentReviewsSnapshot.forEach((doc) => {
      const data = doc.data();
      recentReviews.push({
        id: doc.id,
        userEmail: data.userEmail,
        rating: data.rating,
        comment: data.comment,
        createdAt: data.createdAt?.toDate?.()?.toISOString() || data.createdAt
      });
      
      if (data.userId) {
        uniqueUsers.add(data.userId);
      }
    });

    // Calculate estimated total users (base + engagement multiplier)
    const baseUsers = 5847;
    const engagementMultiplier = 15; // Estimate users per reviewer
    const estimatedUsers = baseUsers + (reviewsCount * engagementMultiplier);

    return NextResponse.json({
      success: true,
      data: {
        totalReviews: reviewsCount,
        estimatedUsers: estimatedUsers,
        uniqueActiveUsers: uniqueUsers.size,
        recentReviews: recentReviews,
        lastUpdated: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('Error fetching user stats:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch user statistics' },
      { status: 500 }
    );
  }
}

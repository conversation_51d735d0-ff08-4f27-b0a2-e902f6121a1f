'use client';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Maximize2, Eye } from 'lucide-react';
import { useEffect } from 'react';

const MobilePreviewModal = ({
  isOpen,
  onClose,
  formData,
  selectedTemplate
}) => {
  // <PERSON>le escape key and body scroll
  useEffect(() => {
    if (isOpen) {
      const handleEscape = (e) => {
        if (e.key === 'Escape') onClose();
      };
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden';
      
      return () => {
        document.removeEventListener('keydown', handleEscape);
        document.body.style.overflow = 'unset';
      };
    }
  }, [isOpen, onClose]);

  // Check if we have meaningful data to preview
  const hasContent = formData && (
    formData.personal?.firstName ||
    formData.personal?.email ||
    formData.experience?.some(exp => exp.title || exp.company) ||
    formData.education?.some(edu => edu.degree || edu.institution)
  );

  const renderPreviewContent = () => {
    if (!hasContent) {
      return (
        <div className="flex flex-col items-center justify-center h-full text-gray-400 p-8">
          <div className="relative mb-4">
            <Eye className="h-16 w-16 opacity-50" />
            <div className="absolute -top-1 -right-1 w-6 h-6 bg-neural-pink rounded-full flex items-center justify-center">
              <span className="text-sm">✨</span>
            </div>
          </div>
          <h3 className="text-lg font-medium mb-2 text-white">AI Resume Preview</h3>
          <p className="text-center opacity-75 mb-2">
            Your professional resume will appear here as you fill out the form
          </p>
          <p className="text-center opacity-50 text-sm">
            ⚡ Powered by AI for instant results
          </p>
        </div>
      );
    }

    return (
      <div className="p-6 space-y-6 text-sm bg-white text-black min-h-full">
        {/* Header Section */}
        <div className="text-center border-b-2 border-gray-300 pb-4">
          <h1 className="text-2xl font-bold text-gray-900 mb-3">
            {formData.personal?.firstName || 'Your Name'} {formData.personal?.lastName || ''}
          </h1>
          
          <div className="flex flex-wrap justify-center gap-3 text-sm text-gray-600">
            {formData.personal?.email && (
              <div className="flex items-center gap-1">
                <span>📧</span>
                <span>{formData.personal.email}</span>
              </div>
            )}
            {formData.personal?.phone && (
              <div className="flex items-center gap-1">
                <span>📱</span>
                <span>{formData.personal.phone}</span>
              </div>
            )}
            {formData.personal?.location && (
              <div className="flex items-center gap-1">
                <span>📍</span>
                <span>{formData.personal.location}</span>
              </div>
            )}
          </div>
        </div>

        {/* Professional Summary */}
        {formData.personal?.summary && (
          <div>
            <h2 className="text-lg font-semibold text-gray-900 mb-3">Professional Summary</h2>
            <p className="text-sm text-gray-700 leading-relaxed text-justify">
              {formData.personal.summary}
            </p>
          </div>
        )}

        {/* Experience Section */}
        {formData.experience?.some(exp => exp.title || exp.company) && (
          <div>
            <h2 className="text-lg font-semibold text-gray-900 mb-3">Experience</h2>
            <div className="space-y-4">
              {formData.experience
                .filter(exp => exp.title || exp.company)
                .map((exp, index) => (
                  <div key={index} className="border-l-4 border-neural-purple pl-4">
                    <div className="flex justify-between items-start mb-1">
                      <h3 className="font-medium text-gray-900">
                        {exp.title || 'Job Title'}
                      </h3>
                      <span className="text-sm text-gray-500">
                        {exp.startDate} {exp.startDate && (exp.endDate || exp.current) && ' - '}
                        {exp.current ? 'Present' : exp.endDate}
                      </span>
                    </div>
                    <p className="text-sm text-gray-600 mb-2">
                      {exp.company || 'Company Name'}
                      {exp.location && ` • ${exp.location}`}
                    </p>
                    {exp.description && (
                      <p className="text-sm text-gray-600 leading-relaxed">
                        {exp.description}
                      </p>
                    )}
                  </div>
                ))}
            </div>
          </div>
        )}

        {/* Education Section */}
        {formData.education?.some(edu => edu.degree || edu.institution) && (
          <div>
            <h2 className="text-lg font-semibold text-gray-900 mb-3">Education</h2>
            <div className="space-y-3">
              {formData.education
                .filter(edu => edu.degree || edu.institution)
                .map((edu, index) => (
                  <div key={index} className="border-l-4 border-neural-blue pl-4">
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="font-medium text-gray-900">
                          {edu.degree || 'Degree'} {edu.field && `in ${edu.field}`}
                        </h3>
                        <p className="text-sm text-gray-600">
                          {edu.institution || 'Institution'}
                        </p>
                      </div>
                      <span className="text-sm text-gray-500">
                        {edu.graduationDate}
                      </span>
                    </div>
                  </div>
                ))}
            </div>
          </div>
        )}

        {/* Skills Section */}
        {(formData.skills?.technical?.length > 0 || formData.skills?.languages?.length > 0) && (
          <div>
            <h2 className="text-lg font-semibold text-gray-900 mb-3">Skills</h2>
            <div className="space-y-2">
              {formData.skills.technical?.length > 0 && (
                <div>
                  <span className="font-medium text-gray-700">Technical: </span>
                  <span className="text-gray-600">
                    {formData.skills.technical.join(', ')}
                  </span>
                </div>
              )}
              {formData.skills.languages?.length > 0 && (
                <div>
                  <span className="font-medium text-gray-700">Languages: </span>
                  <span className="text-gray-600">
                    {formData.skills.languages.join(', ')}
                  </span>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex flex-col"
          onClick={onClose}
        >
          {/* Header */}
          <div className="flex items-center justify-between p-4 bg-gray-900/95 backdrop-blur-sm border-b border-gray-700/50">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-neural-pink rounded-full animate-pulse"></div>
              <span className="text-white font-medium">Resume Preview</span>
            </div>
            
            <div className="flex items-center gap-2">
              <button
                onClick={onClose}
                className="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors"
                title="Close"
              >
                <X className="h-4 w-4" />
              </button>
            </div>
          </div>

          {/* Content */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 20 }}
            className="flex-1 overflow-auto"
            onClick={(e) => e.stopPropagation()}
          >
            {renderPreviewContent()}
          </motion.div>

          {/* Footer */}
          {hasContent && (
            <div className="p-3 bg-gray-900/95 backdrop-blur-sm border-t border-gray-700/50">
              <div className="flex items-center justify-center text-sm text-gray-400">
                <span>Template: {selectedTemplate?.replace('_', ' ') || 'Professional'} • ATS Optimized</span>
              </div>
            </div>
          )}
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default MobilePreviewModal;

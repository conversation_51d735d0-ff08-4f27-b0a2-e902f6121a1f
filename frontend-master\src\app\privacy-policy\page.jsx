'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { Database, Lock, Shield, User, ArrowLeft } from 'lucide-react'
import Link from 'next/link'

const PrivacyPolicyPage = () => {
  const sections = [
    {
      icon: Database,
      title: "Information We Collect",
      content: "We collect information you provide directly to us, including:",
      items: [
        "Name and contact information",
        "Account credentials",
        "Resume data and job preferences",
        "Usage data and analytics",
        "Device information and IP address"
      ]
    },
    {
      icon: Shield,
      title: "How We Use Your Information",
      content: "We use the information we collect to:",
      items: [
        "Provide and improve our AI-powered services",
        "Generate personalized resumes and job recommendations",
        "Send important updates and notifications",
        "Analyze usage patterns to enhance user experience",
        "Protect against fraud and unauthorized access"
      ]
    },
    {
      icon: Lock,
      title: "Data Security",
      content: "We implement industry-standard security measures to protect your personal information. This includes end-to-end encryption, secure cloud storage, regular security audits, and compliance with data protection regulations."
    },
    {
      icon: User,
      title: "Your Rights",
      content: "You have the right to:",
      items: [
        "Access your personal data",
        "Request data correction or updates",
        "Delete your account and associated data",
        "Export your data in a portable format",
        "Opt-out of marketing communications"
      ]
    }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-b from-black via-gray-950 to-black">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0 bg-[url('/grid.svg')] [mask-image:radial-gradient(ellipse_at_center,white,transparent)]" />
      </div>

      {/* Floating Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-20 left-20 w-32 h-32 bg-neural-purple/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 right-20 w-40 h-40 bg-neural-pink/10 rounded-full blur-3xl"></div>
      </div>

      <div className="relative container mx-auto px-6 py-12">
        {/* Back Button */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5 }}
          className="mb-8"
        >
          <Link
            href="/"
            className="inline-flex items-center gap-2 text-gray-400 hover:text-neural-purple transition-colors"
          >
            <ArrowLeft className="h-4 w-4" />
            Back to Home
          </Link>
        </motion.div>

        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-neural-purple to-neural-pink bg-clip-text text-transparent mb-4">
            Privacy Policy
          </h1>
          <p className="text-gray-400 text-lg max-w-2xl mx-auto">
            Your privacy is important to us. This policy explains how BlinkFind collects, uses, and protects your information.
          </p>
          <p className="text-gray-500 text-sm mt-4">
            Last updated: {new Date().toLocaleDateString()}
          </p>
        </motion.div>

        {/* Content */}
        <div className="max-w-4xl mx-auto">
          <div className="space-y-8">
            {sections.map((section, index) => (
              <motion.div
                key={section.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="bg-gray-900/20 backdrop-blur-md rounded-2xl p-8 border border-white/10"
              >
                <div className="flex items-start gap-4">
                  <div className="w-12 h-12 bg-neural-purple/20 rounded-xl flex items-center justify-center flex-shrink-0">
                    <section.icon className="h-6 w-6 text-neural-purple" />
                  </div>
                  <div className="flex-1">
                    <h2 className="text-2xl font-bold text-white mb-4">{section.title}</h2>
                    <p className="text-gray-300 mb-4 leading-relaxed">{section.content}</p>
                    {section.items && (
                      <ul className="space-y-2">
                        {section.items.map((item, itemIndex) => (
                          <li key={itemIndex} className="flex items-start gap-3 text-gray-300">
                            <div className="w-1.5 h-1.5 bg-neural-purple rounded-full mt-2 flex-shrink-0"></div>
                            <span>{item}</span>
                          </li>
                        ))}
                      </ul>
                    )}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Contact Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.5 }}
            className="mt-12 bg-gradient-to-r from-neural-purple/10 to-neural-pink/10 backdrop-blur-md rounded-2xl p-8 border border-white/10 text-center"
          >
            <h3 className="text-xl font-bold text-white mb-4">Questions About This Policy?</h3>
            <p className="text-gray-300 mb-6">
              If you have any questions about this Privacy Policy, please contact us.
            </p>
            <Link
              href="/#contact"
              className="inline-flex items-center gap-2 bg-gradient-to-r from-neural-purple to-neural-pink text-white px-6 py-3 rounded-lg font-medium hover:opacity-90 transition-opacity"
            >
              Contact Us
            </Link>
          </motion.div>
        </div>
      </div>
    </div>
  )
}

export default PrivacyPolicyPage

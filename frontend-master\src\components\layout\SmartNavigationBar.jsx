'use client';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  ArrowLeft, 
  ArrowRight, 
  Save, 
  Eye, 
  EyeOff, 
  Download, 
  Sparkles,
  CheckCircle,
  AlertCircle,
  Target,
  Zap,
  RefreshCw
} from 'lucide-react';
import { useState, useEffect } from 'react';

const SmartNavigationBar = ({
  currentStep,
  totalSteps,
  onPrevious,
  onNext,
  onGenerate,
  onSave,
  onPreview,
  isGenerating = false,
  canProceed = true,
  showPreview = false,
  steps = [],
  formData = {},
  atsScore = 0,
  autoSaveEnabled = true
}) => {
  const [lastSaved, setLastSaved] = useState(null);
  const [saveStatus, setSaveStatus] = useState('saved'); // 'saving', 'saved', 'error'
  const [showQuickActions, setShowQuickActions] = useState(false);

  // Auto-save functionality
  useEffect(() => {
    if (autoSaveEnabled && formData) {
      setSaveStatus('saving');
      const timeoutId = setTimeout(() => {
        localStorage.setItem('resumeFormData', JSON.stringify(formData));
        setLastSaved(new Date());
        setSaveStatus('saved');
      }, 1000);

      return () => clearTimeout(timeoutId);
    }
  }, [formData, autoSaveEnabled]);

  const handleSave = () => {
    setSaveStatus('saving');
    onSave?.();
    setTimeout(() => {
      setLastSaved(new Date());
      setSaveStatus('saved');
    }, 500);
  };

  const getStepProgress = () => {
    return Math.round(((currentStep + 1) / totalSteps) * 100);
  };

  const getNextStepInfo = () => {
    if (currentStep < totalSteps - 1) {
      return steps[currentStep + 1];
    }
    return null;
  };

  return (
    <div className="fixed bottom-0 left-0 right-0 z-50">
      {/* Main Navigation Bar */}
      <motion.div
        initial={{ y: 100 }}
        animate={{ y: 0 }}
        className="bg-gray-900/95 backdrop-blur-lg border-t border-white/10 shadow-2xl"
      >
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            {/* Left Section - Progress & Info */}
            <div className="flex items-center gap-4">
              {/* Step Progress */}
              <div className="hidden md:flex items-center gap-3">
                <div className="flex items-center gap-2">
                  <div className="w-8 h-8 bg-neural-purple/20 rounded-lg flex items-center justify-center">
                    <span className="text-neural-purple font-semibold text-sm">{currentStep + 1}</span>
                  </div>
                  <div>
                    <div className="text-sm font-medium text-white">
                      Step {currentStep + 1} of {totalSteps}
                    </div>
                    <div className="text-xs text-gray-400">
                      {steps[currentStep]?.title || 'Current Step'}
                    </div>
                  </div>
                </div>

                {/* Progress Bar */}
                <div className="w-32 bg-gray-700 rounded-full h-2">
                  <motion.div
                    className="bg-gradient-to-r from-neural-purple to-neural-pink h-2 rounded-full"
                    initial={{ width: 0 }}
                    animate={{ width: `${getStepProgress()}%` }}
                    transition={{ duration: 0.5 }}
                  />
                </div>

                <span className="text-xs text-neural-purple font-medium">
                  {getStepProgress()}%
                </span>
              </div>

              {/* Mobile Progress */}
              <div className="md:hidden flex items-center gap-2">
                <div className="text-sm font-medium text-white">
                  {currentStep + 1}/{totalSteps}
                </div>
                <div className="w-20 bg-gray-700 rounded-full h-2">
                  <motion.div
                    className="bg-gradient-to-r from-neural-purple to-neural-pink h-2 rounded-full"
                    animate={{ width: `${getStepProgress()}%` }}
                  />
                </div>
              </div>

              {/* Auto-save Status */}
              <div className="hidden lg:flex items-center gap-2 text-xs">
                <div className="flex items-center gap-1">
                  {saveStatus === 'saving' && (
                    <>
                      <RefreshCw className="h-3 w-3 text-yellow-400 animate-spin" />
                      <span className="text-yellow-400">Saving...</span>
                    </>
                  )}
                  {saveStatus === 'saved' && (
                    <>
                      <CheckCircle className="h-3 w-3 text-green-400" />
                      <span className="text-green-400">
                        Saved {lastSaved && new Date(lastSaved).toLocaleTimeString()}
                      </span>
                    </>
                  )}
                  {saveStatus === 'error' && (
                    <>
                      <AlertCircle className="h-3 w-3 text-red-400" />
                      <span className="text-red-400">Save failed</span>
                    </>
                  )}
                </div>
              </div>
            </div>

            {/* Center Section - Quick Actions */}
            <div className="hidden md:flex items-center gap-2">
              {/* ATS Score */}
              {atsScore > 0 && (
                <div className="flex items-center gap-2 px-3 py-2 bg-gray-800/50 rounded-lg">
                  <Target className="h-4 w-4 text-neural-purple" />
                  <span className="text-sm font-medium text-white">ATS: {atsScore}%</span>
                </div>
              )}

              {/* Preview Toggle */}
              <button
                onClick={onPreview}
                className={`flex items-center gap-2 px-3 py-2 rounded-lg transition-colors ${
                  showPreview 
                    ? 'bg-neural-purple text-white' 
                    : 'bg-gray-800/50 text-gray-300 hover:bg-gray-700'
                }`}
              >
                {showPreview ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                <span className="text-sm">Preview</span>
              </button>

              {/* Save Button */}
              <button
                onClick={handleSave}
                className="flex items-center gap-2 px-3 py-2 bg-gray-800/50 hover:bg-gray-700 text-gray-300 rounded-lg transition-colors"
              >
                <Save className="h-4 w-4" />
                <span className="text-sm">Save</span>
              </button>
            </div>

            {/* Right Section - Navigation */}
            <div className="flex items-center gap-3">
              {/* Previous Button */}
              <button
                onClick={onPrevious}
                disabled={currentStep === 0}
                className={`flex items-center gap-2 px-4 py-2 rounded-xl transition-all duration-200 ${
                  currentStep === 0
                    ? 'bg-gray-800/50 text-gray-500 cursor-not-allowed'
                    : 'bg-gray-800 hover:bg-gray-700 text-white hover:scale-105'
                }`}
              >
                <ArrowLeft className="h-4 w-4" />
                <span className="hidden sm:inline">Previous</span>
              </button>

              {/* Next/Generate Button */}
              {currentStep < totalSteps - 1 ? (
                <motion.button
                  onClick={onNext}
                  disabled={!canProceed}
                  className={`flex items-center gap-2 px-6 py-2 rounded-xl font-medium transition-all duration-200 ${
                    canProceed
                      ? 'bg-gradient-to-r from-neural-purple to-neural-pink text-white hover:scale-105 shadow-lg hover:shadow-xl'
                      : 'bg-gray-700 text-gray-400 cursor-not-allowed'
                  }`}
                  whileHover={canProceed ? { scale: 1.05 } : {}}
                  whileTap={canProceed ? { scale: 0.95 } : {}}
                >
                  <span>Next</span>
                  <ArrowRight className="h-4 w-4" />
                  
                  {/* Next Step Preview */}
                  {canProceed && getNextStepInfo() && (
                    <div className="hidden lg:block ml-2 text-xs opacity-75">
                      → {getNextStepInfo().title}
                    </div>
                  )}
                </motion.button>
              ) : (
                <motion.button
                  onClick={onGenerate}
                  disabled={!canProceed || isGenerating}
                  className={`flex items-center gap-2 px-6 py-2 rounded-xl font-medium transition-all duration-200 ${
                    canProceed && !isGenerating
                      ? 'bg-gradient-to-r from-green-500 to-emerald-600 text-white hover:scale-105 shadow-lg hover:shadow-xl'
                      : 'bg-gray-700 text-gray-400 cursor-not-allowed'
                  }`}
                  whileHover={canProceed && !isGenerating ? { scale: 1.05 } : {}}
                  whileTap={canProceed && !isGenerating ? { scale: 0.95 } : {}}
                >
                  {isGenerating ? (
                    <>
                      <RefreshCw className="h-4 w-4 animate-spin" />
                      <span>Generating...</span>
                    </>
                  ) : (
                    <>
                      <Sparkles className="h-4 w-4" />
                      <span>Generate Resume</span>
                    </>
                  )}
                </motion.button>
              )}
            </div>
          </div>

          {/* Mobile Quick Actions */}
          <div className="md:hidden mt-3 flex items-center justify-between">
            <div className="flex items-center gap-2">
              {atsScore > 0 && (
                <div className="flex items-center gap-1 text-xs">
                  <Target className="h-3 w-3 text-neural-purple" />
                  <span className="text-white">ATS: {atsScore}%</span>
                </div>
              )}
            </div>

            <div className="flex items-center gap-2">
              <button
                onClick={onPreview}
                className={`p-2 rounded-lg transition-colors ${
                  showPreview ? 'bg-neural-purple text-white' : 'bg-gray-800 text-gray-300'
                }`}
              >
                {showPreview ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              </button>
              <button
                onClick={handleSave}
                className="p-2 bg-gray-800 hover:bg-gray-700 text-gray-300 rounded-lg transition-colors"
              >
                <Save className="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Validation Issues Banner */}
      <AnimatePresence>
        {!canProceed && currentStep < totalSteps - 1 && (
          <motion.div
            initial={{ y: 100, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            exit={{ y: 100, opacity: 0 }}
            className="bg-yellow-500/10 border-t border-yellow-500/20 px-4 py-2"
          >
            <div className="container mx-auto flex items-center justify-between">
              <div className="flex items-center gap-2 text-yellow-400">
                <AlertCircle className="h-4 w-4" />
                <span className="text-sm">Please complete all required fields to continue</span>
              </div>
              <button
                onClick={() => {
                  // Scroll to first error field
                  const errorField = document.querySelector('.border-red-500');
                  if (errorField) {
                    errorField.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    errorField.focus();
                  }
                }}
                className="text-xs text-yellow-300 hover:text-yellow-200 underline"
              >
                Find Issues
              </button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default SmartNavigationBar;
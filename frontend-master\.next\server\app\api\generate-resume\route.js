/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/generate-resume/route";
exports.ids = ["app/api/generate-resume/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-resume%2Froute&page=%2Fapi%2Fgenerate-resume%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-resume%2Froute.js&appDir=C%3A%5CProjects%5CNewBlinkFindAI%5Cfrontend-master%5Csrc%5Capp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&rootDir=C%3A%5CProjects%5CNewBlinkFindAI%5Cfrontend-master&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-resume%2Froute&page=%2Fapi%2Fgenerate-resume%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-resume%2Froute.js&appDir=C%3A%5CProjects%5CNewBlinkFindAI%5Cfrontend-master%5Csrc%5Capp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&rootDir=C%3A%5CProjects%5CNewBlinkFindAI%5Cfrontend-master&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Projects_NewBlinkFindAI_frontend_master_src_app_api_generate_resume_route_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/generate-resume/route.js */ \"(rsc)/./src/app/api/generate-resume/route.js\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/generate-resume/route\",\n        pathname: \"/api/generate-resume\",\n        filename: \"route\",\n        bundlePath: \"app/api/generate-resume/route\"\n    },\n    resolvedPagePath: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\api\\\\generate-resume\\\\route.js\",\n    nextConfigOutput,\n    userland: C_Projects_NewBlinkFindAI_frontend_master_src_app_api_generate_resume_route_js__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-resume%2Froute&page=%2Fapi%2Fgenerate-resume%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-resume%2Froute.js&appDir=C%3A%5CProjects%5CNewBlinkFindAI%5Cfrontend-master%5Csrc%5Capp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&rootDir=C%3A%5CProjects%5CNewBlinkFindAI%5Cfrontend-master&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/generate-resume/route.js":
/*!**********************************************!*\
  !*** ./src/app/api/generate-resume/route.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @google/generative-ai */ \"(rsc)/./node_modules/@google/generative-ai/dist/index.mjs\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\n\n// Initialize Gemini AI with API key\nconst genAI = new _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__.GoogleGenerativeAI(process.env.GEMINI_API_KEY);\nasync function POST(request) {\n    let formData = {};\n    try {\n        console.log('🚀 Resume generation API called');\n        const requestData = await request.json();\n        console.log('📝 Request data received:', JSON.stringify(requestData, null, 2));\n        // Handle nested formData structure\n        formData = requestData.formData || requestData;\n        console.log('📋 Processed form data:', JSON.stringify(formData, null, 2));\n        // Validate required fields\n        if (!formData.personal?.firstName || !formData.personal?.lastName || !formData.personal?.email) {\n            console.error('❌ Missing required fields');\n            console.log('🔍 Form data structure:', {\n                hasPersonal: !!formData.personal,\n                firstName: formData.personal?.firstName,\n                lastName: formData.personal?.lastName,\n                email: formData.personal?.email\n            });\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                error: 'Missing required personal information (First Name, Last Name, and Email are required)'\n            }, {\n                status: 400\n            });\n        }\n        // Check if API key is configured\n        if (!process.env.GEMINI_API_KEY || process.env.GEMINI_API_KEY === 'your-gemini-api-key-here') {\n            console.log('⚠️ Gemini API key not configured, using fallback');\n            // Fallback: return a basic resume structure\n            const fallbackResume = createFallbackResume(formData);\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                success: true,\n                resumeData: fallbackResume,\n                atsScore: fallbackResume.atsScore?.overall || 75,\n                suggestions: fallbackResume.atsScore?.improvements || [],\n                downloadUrl: '/api/download-resume/' + Date.now(),\n                message: 'Resume generated successfully (using template)',\n                fallback: true\n            });\n        }\n        console.log('🤖 Initializing Gemini AI...');\n        // Get the generative model - Using Gemini 2.0 Flash (latest and most efficient)\n        const model = genAI.getGenerativeModel({\n            model: 'gemini-2.0-flash-exp',\n            generationConfig: {\n                temperature: 0.3,\n                topK: 40,\n                topP: 0.95,\n                maxOutputTokens: 8000\n            }\n        });\n        // Prepare prompt for Gemini API\n        const prompt = createResumePrompt(formData);\n        console.log('📋 Prompt created, length:', prompt.length);\n        // Generate content using the official SDK\n        console.log('🔄 Calling Gemini API...');\n        const result = await model.generateContent(prompt);\n        const response = await result.response;\n        const generatedContent = response.text();\n        console.log('✅ Gemini response received, length:', generatedContent?.length || 0);\n        if (!generatedContent) {\n            throw new Error('No content generated from Gemini API');\n        }\n        // Process the generated content and create resume\n        console.log('🔧 Processing generated content...');\n        const resumeData = processGeneratedContent(generatedContent, formData);\n        console.log('📊 Resume data processed:', {\n            hasEnhancedContent: !!resumeData.enhancedContent,\n            atsScore: resumeData.atsScore?.overall,\n            suggestionsCount: resumeData.atsScore?.improvements?.length || 0\n        });\n        // Return the processed data with enhanced structure\n        const responseData = {\n            success: true,\n            resumeData,\n            atsScore: resumeData.atsScore?.overall || 75,\n            suggestions: resumeData.atsScore?.improvements || [],\n            downloadUrl: '/api/download-resume/' + Date.now(),\n            message: 'Resume generated successfully with AI optimization',\n            generatedAt: new Date().toISOString()\n        };\n        console.log('✨ Sending successful response');\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json(responseData);\n    } catch (error) {\n        console.error('💥 Resume generation error:', error);\n        console.error('Error stack:', error.stack);\n        // Enhanced error handling with fallback\n        try {\n            const fallbackResume = createFallbackResume(formData || {});\n            const fallbackResponse = {\n                success: true,\n                resumeData: fallbackResume,\n                atsScore: fallbackResume.atsScore?.overall || 75,\n                suggestions: fallbackResume.atsScore?.improvements || [],\n                downloadUrl: '/api/download-resume/' + Date.now(),\n                message: 'Resume generated successfully (fallback mode)',\n                fallback: true,\n                error:  true ? error.message : 0\n            };\n            console.log('🔄 Sending fallback response');\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json(fallbackResponse);\n        } catch (fallbackError) {\n            console.error('💥 Fallback also failed:', fallbackError);\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                error: 'Failed to generate resume',\n                details:  true ? error.message : 0\n            }, {\n                status: 500\n            });\n        }\n    }\n}\nfunction createResumePrompt(formData) {\n    const { personal, education, experience, skills, projects } = formData;\n    // Create a raw resume text from form data\n    const rawResumeText = `\nNAME: ${personal.firstName} ${personal.lastName}\nEMAIL: ${personal.email}\nPHONE: ${personal.phone || 'Not provided'}\nLOCATION: ${personal.location || 'Not provided'}\nLINKEDIN: ${personal.linkedin || 'Not provided'}\nPORTFOLIO: ${personal.portfolio || 'Not provided'}\n\nSUMMARY: ${personal.summary || 'Not provided'}\n\nEDUCATION:\n${education?.map((edu)=>`${edu.degree} | ${edu.institution} | ${edu.startDate} - ${edu.endDate}`).join('\\n') || 'Not provided'}\n\nEXPERIENCE:\n${experience?.map((exp)=>`${exp.title} | ${exp.company} | ${exp.startDate} - ${exp.current ? 'Present' : exp.endDate}\\n${exp.description || ''}`).join('\\n\\n') || 'Not provided'}\n\nSKILLS:\nTechnical: ${skills?.technical?.join(', ') || 'Not provided'}\nLanguages: ${skills?.languages?.join(', ') || 'Not provided'}\nCertifications: ${skills?.certifications?.join(', ') || 'Not provided'}\n\nPROJECTS:\n${projects?.map((proj)=>`${proj.name} | ${proj.technologies}\\n${proj.description || ''}`).join('\\n\\n') || 'Not provided'}\n  `;\n    return `You are a professional ATS resume optimizer with expertise in realistic scoring based on actual ATS system requirements.\n\nYour tasks:\n✅ Enhance resume content with professional formatting and action verbs\n✅ Add relevant keywords naturally (avoid keyword stuffing)\n✅ Improve bullet points with quantified achievements where possible\n✅ Maintain ATS-friendly structure and standard section headers\n✅ Provide realistic ATS scoring based on actual content quality\n\nResume Format Reference:\n---\nNAME\n📧 Email | 📱 Phone | 🔗 LinkedIn | 🌐 Portfolio\n\n*PROFESSIONAL SUMMARY*\n2–3 lines highlighting role focus, key skills, and relevant experience\n\n*EDUCATION*\nDegree | Institution | Year | Relevant details\n\n*EXPERIENCE*\nRole | Company | Date Range\n• Achievement with action verb and context\n• Technical contribution or responsibility\n• Impact or result (with metrics if available)\n\n*PROJECTS*\nProject Title | Technologies Used\n• Project description with technical approach\n• Outcome or learning achieved\n\n*SKILLS*\nTechnical Skills: Relevant technologies and tools\nLanguages: Spoken languages\nCertifications: Professional certifications\n---\n\nATS SCORING CRITERIA (Be realistic and accurate):\n- Keywords (25%): Relevant industry/technical terms naturally integrated\n- Formatting (25%): ATS-friendly structure, standard headers, clean layout  \n- Content Quality (25%): Clear achievements, action verbs, professional language\n- Completeness (25%): All sections filled, contact info, relevant experience\n\nSCORING GUIDELINES:\n- 90-100: Exceptional resume with quantified achievements, perfect formatting, comprehensive content\n- 80-89: Strong resume with good content, clear structure, relevant keywords\n- 70-79: Decent resume with adequate content, standard formatting, some improvements needed\n- 60-69: Basic resume with minimal content, simple formatting, significant improvements needed\n- 50-59: Poor resume with weak content, formatting issues, major improvements required\n- Below 50: Inadequate resume missing critical elements\n\nOUTPUT FORMAT: Return a JSON object with this exact structure:\n{\n  \"enhancedContent\": {\n    \"professionalSummary\": \"Enhanced 2-3 line summary\",\n    \"experience\": [\n      {\n        \"title\": \"Job title\",\n        \"company\": \"Company name\",\n        \"location\": \"Location\",\n        \"startDate\": \"MM/YYYY\",\n        \"endDate\": \"MM/YYYY or Present\",\n        \"achievements\": [\n          \"• Action verb + responsibility/achievement\",\n          \"• Technical contribution or project\",\n          \"• Result or impact (quantified if possible)\"\n        ]\n      }\n    ],\n    \"education\": [\n      {\n        \"degree\": \"Degree name\",\n        \"institution\": \"Institution name\",\n        \"location\": \"Location\",\n        \"startDate\": \"MM/YYYY\",\n        \"endDate\": \"MM/YYYY\",\n        \"gpa\": \"GPA if provided\",\n        \"relevant\": \"Relevant details if any\"\n      }\n    ],\n    \"skills\": {\n      \"technical\": [\"Enhanced technical skills\"],\n      \"languages\": [\"Languages with levels\"],\n      \"certifications\": [\"Professional certifications\"]\n    },\n    \"projects\": [\n      {\n        \"name\": \"Project name\",\n        \"description\": \"Enhanced description with technical details\",\n        \"technologies\": \"Technologies used\",\n        \"link\": \"Project link if available\"\n      }\n    ]\n  },\n  \"atsScore\": {\n    \"overall\": [realistic score 50-85 based on actual content quality],\n    \"breakdown\": {\n      \"keywords\": [50-85 based on relevant terms],\n      \"formatting\": [60-90 based on structure],\n      \"content\": [40-85 based on achievement quality],\n      \"completeness\": [50-95 based on section coverage]\n    },\n    \"improvements\": [\n      \"Specific, actionable improvement suggestions\",\n      \"Based on actual content gaps or weaknesses\",\n      \"Realistic recommendations for score improvement\"\n    ]\n  },\n  \"keywords\": [\"relevant technical and industry keywords found\"]\n}\n\nResume to optimize:\n${rawResumeText}\n\nProvide realistic scoring and meaningful enhancements. Do not inflate scores - be honest about content quality.`;\n}\nfunction processGeneratedContent(content, originalData) {\n    try {\n        // Extract JSON from the AI response (handle potential markdown formatting)\n        let jsonContent = content;\n        // Remove markdown code blocks if present\n        if (content.includes('```json')) {\n            const jsonMatch = content.match(/```json\\s*([\\s\\S]*?)\\s*```/);\n            if (jsonMatch) {\n                jsonContent = jsonMatch[1];\n            }\n        } else if (content.includes('```')) {\n            const jsonMatch = content.match(/```\\s*([\\s\\S]*?)\\s*```/);\n            if (jsonMatch) {\n                jsonContent = jsonMatch[1];\n            }\n        }\n        // Parse the enhanced content\n        const enhancedData = JSON.parse(jsonContent);\n        // Validate the structure\n        if (!enhancedData.enhancedContent || !enhancedData.atsScore) {\n            throw new Error('Invalid AI response structure');\n        }\n        return {\n            enhancedContent: enhancedData.enhancedContent,\n            atsScore: enhancedData.atsScore,\n            keywords: enhancedData.keywords || [],\n            originalData,\n            timestamp: new Date().toISOString(),\n            version: '2.0',\n            type: 'ai-enhanced'\n        };\n    } catch (error) {\n        console.error('Error parsing AI response:', error);\n        // Fallback: create a basic enhanced structure\n        return createEnhancedFallback(originalData, content);\n    }\n}\nfunction createEnhancedFallback(originalData, rawContent) {\n    const { personal, education, experience, skills, projects } = originalData;\n    // Calculate realistic ATS score based on actual content\n    const atsScore = calculateRealisticATSScore(originalData);\n    return {\n        enhancedContent: {\n            professionalSummary: personal.summary || 'Dedicated professional with strong background and commitment to excellence.',\n            experience: experience?.map((exp)=>({\n                    title: exp.title,\n                    company: exp.company,\n                    location: exp.location,\n                    startDate: exp.startDate,\n                    endDate: exp.current ? 'Present' : exp.endDate,\n                    achievements: exp.description ? [\n                        exp.description\n                    ] : [\n                        'Contributed to team success and organizational goals'\n                    ]\n                })) || [],\n            education: education || [],\n            skills: skills || {\n                technical: [],\n                languages: [],\n                certifications: []\n            },\n            projects: projects || []\n        },\n        atsScore,\n        keywords: extractKeywords(originalData),\n        originalData,\n        timestamp: new Date().toISOString(),\n        version: '2.0',\n        type: 'fallback-enhanced',\n        rawContent\n    };\n}\nfunction createFallbackResume(formData) {\n    console.log('🔄 Creating fallback resume for:', formData);\n    // Ensure formData has the expected structure\n    const safeFormData = {\n        personal: formData?.personal || {},\n        education: formData?.education || [],\n        experience: formData?.experience || [],\n        skills: formData?.skills || {\n            technical: [],\n            languages: [],\n            certifications: []\n        },\n        projects: formData?.projects || []\n    };\n    const { personal, education, experience, skills, projects } = safeFormData;\n    // Calculate realistic ATS score\n    const atsScore = calculateRealisticATSScore(safeFormData);\n    const fallbackData = {\n        enhancedContent: {\n            professionalSummary: personal.summary || 'Dedicated professional with strong educational background and commitment to excellence.',\n            experience: experience.map((exp)=>({\n                    title: exp.title || 'Position',\n                    company: exp.company || 'Company',\n                    location: exp.location || '',\n                    startDate: exp.startDate || '',\n                    endDate: exp.current ? 'Present' : exp.endDate || '',\n                    achievements: exp.description ? [\n                        exp.description\n                    ] : [\n                        'Contributed to team objectives and organizational success'\n                    ]\n                })),\n            education: education,\n            skills: {\n                technical: skills.technical || [],\n                languages: skills.languages || [],\n                certifications: skills.certifications || []\n            },\n            projects: projects\n        },\n        atsScore,\n        keywords: extractKeywords(safeFormData),\n        originalData: safeFormData,\n        timestamp: new Date().toISOString(),\n        version: '2.0',\n        type: 'fallback'\n    };\n    console.log('✅ Fallback resume created with ATS score:', atsScore.overall);\n    return fallbackData;\n}\n// Realistic ATS scoring algorithm based on actual content quality\nfunction calculateRealisticATSScore(formData) {\n    const { personal, education, experience, skills, projects } = formData;\n    // Keywords scoring (25% weight)\n    let keywordScore = 20; // Base score\n    const technicalSkills = skills?.technical || [];\n    const certifications = skills?.certifications || [];\n    if (technicalSkills.length > 0) keywordScore += Math.min(25, technicalSkills.length * 3);\n    if (certifications.length > 0) keywordScore += Math.min(15, certifications.length * 5);\n    // Experience descriptions keyword analysis\n    const experienceText = experience?.map((exp)=>exp.description || '').join(' ').toLowerCase();\n    const actionVerbs = [\n        'led',\n        'developed',\n        'implemented',\n        'achieved',\n        'managed',\n        'created',\n        'improved',\n        'optimized',\n        'designed',\n        'built'\n    ];\n    const actionVerbCount = actionVerbs.filter((verb)=>experienceText.includes(verb)).length;\n    keywordScore += Math.min(10, actionVerbCount * 2);\n    keywordScore = Math.min(85, keywordScore); // Cap at 85\n    // Formatting scoring (25% weight)\n    let formattingScore = 60; // Base for standard structure\n    if (personal?.email && personal?.firstName && personal?.lastName) formattingScore += 10;\n    if (personal?.phone) formattingScore += 5;\n    if (personal?.linkedin) formattingScore += 5;\n    if (education?.length > 0) formattingScore += 10;\n    if (experience?.length > 0) formattingScore += 10;\n    formattingScore = Math.min(90, formattingScore);\n    // Content quality scoring (25% weight)\n    let contentScore = 30; // Base score\n    // Professional summary quality\n    if (personal?.summary && personal.summary.length > 50) contentScore += 10;\n    // Experience quality\n    const experienceQuality = experience?.reduce((score, exp)=>{\n        let expScore = 0;\n        if (exp.description && exp.description.length > 30) expScore += 5;\n        if (exp.description && /\\d+/.test(exp.description)) expScore += 3; // Contains numbers\n        if (exp.company && exp.title) expScore += 2;\n        return score + expScore;\n    }, 0) || 0;\n    contentScore += Math.min(25, experienceQuality);\n    // Projects quality\n    const projectQuality = projects?.reduce((score, proj)=>{\n        let projScore = 0;\n        if (proj.description && proj.description.length > 30) projScore += 3;\n        if (proj.technologies) projScore += 2;\n        return score + projScore;\n    }, 0) || 0;\n    contentScore += Math.min(10, projectQuality);\n    contentScore = Math.min(85, contentScore);\n    // Completeness scoring (25% weight)\n    let completenessScore = 40; // Base score\n    if (personal?.summary) completenessScore += 10;\n    if (education?.length > 0) completenessScore += 15;\n    if (experience?.length > 0) completenessScore += 20;\n    if (skills?.technical?.length > 0) completenessScore += 10;\n    if (projects?.length > 0) completenessScore += 5;\n    completenessScore = Math.min(95, completenessScore);\n    // Calculate overall weighted score\n    const overall = Math.round(keywordScore * 0.25 + formattingScore * 0.25 + contentScore * 0.25 + completenessScore * 0.25);\n    // Generate realistic improvements\n    const improvements = generateImprovements(keywordScore, formattingScore, contentScore, completenessScore, formData);\n    return {\n        overall,\n        breakdown: {\n            keywords: keywordScore,\n            formatting: formattingScore,\n            content: contentScore,\n            completeness: completenessScore\n        },\n        improvements\n    };\n}\nfunction generateImprovements(keywordScore, formattingScore, contentScore, completenessScore, formData) {\n    const improvements = [];\n    if (keywordScore < 70) {\n        improvements.push('Add more technical skills and industry-relevant keywords');\n    }\n    if (keywordScore < 60) {\n        improvements.push('Include action verbs in experience descriptions (Led, Developed, Implemented)');\n    }\n    if (formattingScore < 80) {\n        improvements.push('Complete contact information (phone, LinkedIn profile)');\n    }\n    if (contentScore < 70) {\n        improvements.push('Add quantified achievements with specific metrics and results');\n    }\n    if (contentScore < 60) {\n        improvements.push('Expand experience descriptions with more detailed accomplishments');\n    }\n    if (completenessScore < 80) {\n        improvements.push('Add professional summary highlighting key strengths and experience');\n    }\n    if (completenessScore < 70) {\n        improvements.push('Include relevant projects to showcase technical abilities');\n    }\n    // Specific improvements based on missing content\n    if (!formData.personal?.summary) {\n        improvements.push('Write a compelling professional summary (2-3 sentences)');\n    }\n    if (!formData.skills?.certifications?.length) {\n        improvements.push('Add relevant professional certifications to boost credibility');\n    }\n    if (!formData.personal?.linkedin) {\n        improvements.push('Include LinkedIn profile URL for better networking opportunities');\n    }\n    return improvements.slice(0, 4); // Limit to 4 most important improvements\n}\nfunction extractKeywords(formData) {\n    const keywords = [];\n    // Extract from technical skills\n    if (formData.skills?.technical) {\n        keywords.push(...formData.skills.technical);\n    }\n    // Extract from experience titles and companies\n    formData.experience?.forEach((exp)=>{\n        if (exp.title) keywords.push(exp.title);\n        if (exp.company) keywords.push(exp.company);\n    });\n    // Extract from education\n    formData.education?.forEach((edu)=>{\n        if (edu.degree) keywords.push(edu.degree);\n        if (edu.institution) keywords.push(edu.institution);\n    });\n    // Extract from projects\n    formData.projects?.forEach((proj)=>{\n        if (proj.technologies) {\n            keywords.push(...proj.technologies.split(',').map((t)=>t.trim()));\n        }\n    });\n    return [\n        ...new Set(keywords)\n    ].slice(0, 15); // Unique keywords, limit to 15\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/generate-resume/route.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendors"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-resume%2Froute&page=%2Fapi%2Fgenerate-resume%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-resume%2Froute.js&appDir=C%3A%5CProjects%5CNewBlinkFindAI%5Cfrontend-master%5Csrc%5Capp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&rootDir=C%3A%5CProjects%5CNewBlinkFindAI%5Cfrontend-master&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();
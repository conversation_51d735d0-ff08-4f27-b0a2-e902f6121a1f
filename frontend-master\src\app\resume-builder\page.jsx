"use client";
import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { 
  User, 
  GraduationCap, 
  Briefcase, 
  Award,
  FileText, 
  ArrowLeft,
  ArrowRight,
  Sparkles,
  Eye,
  Wand2,
  Plus,
  Trash2,
  MapPin,
  Calendar,
  Globe,
  Mail,
  Phone,
  Link,
  Upload,
  PlusCircle,
  <PERSON>lette,
  Settings,
  Zap
} from "lucide-react";
import { SparklesIcon } from '@heroicons/react/24/solid';
import { toast } from "react-hot-toast";
import ProgressBar from "@/components/common/ProgressBar";
import ATSScoreCircle from "@/components/resume/ats/ScoreCircle";
import EnhancedResumePreview from "@/components/resume/ResumePreview";
import PDFDownload, { ViewResumeButton } from "@/components/resume/PDFDownload";
import ResumeBuilderModeToggle from "@/components/resume/ResumeBuilder";
import ResumeUpload from "@/components/resume/ResumeUpload";
import ATSAnalysisDisplay from "@/components/resume/ats/AnalysisDisplay";
import JobDescriptionInput from "@/components/resume/JobDescriptionInput";
import BeforeAfterComparison from "@/components/resume/BeforeAfterComparison";
import AIContentSuggestions from "@/components/resume/AIContentSuggestions";
import ATSOptimizationPanel from "@/components/resume/ats/OptimizationPanel";
import TemplateSelector from "@/components/resume/TemplateSelector";
import UploadEnhancementWorkflow from "@/components/resume/UploadWorkflow";
import useATSAnalysis from "@/hooks/useATSAnalysis";
import ATSFieldIndicator from "@/components/resume/ats/FieldIndicator";
import ClientOnly from "@/components/common/ClientOnly";
import ATSTooltip from "@/components/resume/ats/Tooltip";
import StepIndicator from "@/components/resume/StepNavigation";
import StickyNavigation from "@/components/resume/StepNavigation";
import { ExperienceForm, SkillsProjectsForm, ReviewForm, PersonalInfoForm, EducationForm } from "@/components/resume/forms/ResumeFormComponents";
import EnhancedResumeBuilder from "@/components/resume/ResumeBuilder";
import ClassicMode from "@/components/resume/ClassicMode";
import RedesignedResumeBuilder from "@/components/resume/RedesignedResumeBuilder";

const ResumeBuilder = () => {
  // Mode selection: 'classic', 'enhanced', 'universal'
  const [currentMode, setCurrentMode] = useState('universal');

  // Universal Mode - Show redesigned version by default
  if (currentMode === 'universal') {
    return <RedesignedResumeBuilder />;
  }

  // Enhanced Mode Toggle - Show enhanced version
  if (currentMode === 'enhanced') {
    return (
      <div className="min-h-screen bg-gradient-to-b from-black to-[#0A0A0A] text-white relative">
        {/* Background Elements */}
        <div className="absolute inset-0 opacity-20">
          <div className="absolute inset-0 bg-[url('/grid.svg')] [mask-image:radial-gradient(ellipse_at_center,white,transparent)]" />
        </div>        <div className="relative z-10">
          {/* Header with Mode Toggle */}
          <motion.div
            className="text-center pt-24 pb-16 px-4 sm:px-6 lg:px-8 relative z-20"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <div className="flex items-center justify-center gap-3 mb-6">
              <div className="relative">
                <SparklesIcon className="h-8 w-8 sm:h-10 sm:w-10 text-neural-pink animate-pulse" />
                <div className="absolute inset-0 bg-neural-pink opacity-20 rounded-full blur-md"></div>
              </div>
              <h1 className="text-3xl sm:text-4xl lg:text-5xl font-extrabold text-white">
                Supercharge Your Job Hunt with AI-Powered Resumes
              </h1>
            </div>
            <p className="text-gray-300 text-base sm:text-lg max-w-2xl mx-auto mb-8 px-4">
              Effortlessly craft ATS-optimized resumes in minutes using smart AI suggestions, real-time feedback, and one-click downloads.
            </p>

            {/* Mode Toggle */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="flex items-center justify-center"
            >              <div className="bg-gray-900/60 backdrop-blur-md rounded-2xl p-3 border border-white/10 max-w-lg mx-auto">
                <div className="flex items-center gap-2 flex-wrap justify-center">
                  <button
                    onClick={() => setCurrentMode('classic')}
                    className="px-3 sm:px-4 py-2 rounded-lg text-xs sm:text-sm font-medium transition-colors bg-gray-800 text-gray-300 hover:bg-gray-700"
                  >
                    Classic
                  </button>
                  <button
                    onClick={() => setCurrentMode('enhanced')}
                    className="px-3 sm:px-4 py-2 rounded-lg text-xs sm:text-sm font-medium transition-colors bg-gradient-to-r from-neural-purple to-neural-pink text-white"
                  >
                    <div className="flex items-center gap-1 sm:gap-2">
                      <Sparkles className="h-3 w-3 sm:h-4 sm:w-4" />
                      <span className="hidden sm:inline">AI Enhanced</span>
                      <span className="sm:hidden">AI</span>
                    </div>
                  </button>
                  <button
                    onClick={() => setCurrentMode('universal')}
                    className="px-3 sm:px-4 py-2 rounded-lg text-xs sm:text-sm font-medium transition-colors bg-gradient-to-r from-blue-500 to-green-500 text-white"
                  >
                    <div className="flex items-center gap-1 sm:gap-2">
                      <Globe className="h-3 w-3 sm:h-4 sm:w-4" />
                      <span className="hidden sm:inline">Universal</span>
                      <span className="sm:hidden">New</span>
                    </div>
                  </button>
                </div>                <div className="mt-3 pt-3 border-t border-gray-700">
                  <div className="flex items-center gap-2 text-xs sm:text-sm text-blue-400 justify-center">
                    <Globe className="h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
                    <span className="text-center">Universal design for all industries • Simplified UX • Multi-professional templates</span>
                  </div>
                </div>
              </div>
            </motion.div>
          </motion.div>

          {/* Enhanced Resume Builder Component */}
          <EnhancedResumeBuilder hideHeader={true} />
        </div>
      </div>
    );
  }

  // Classic mode
  return (
    <div className="min-h-screen bg-gradient-to-b from-black to-[#0A0A0A] text-white relative">
      <div className="absolute inset-0 opacity-20">
        <div className="absolute inset-0 bg-[url('/grid.svg')] [mask-image:radial-gradient(ellipse_at_center,white,transparent)]" />
      </div>      <div className="relative z-10 container mx-auto px-4 pt-24 pb-16">
        <motion.div
          className="text-center"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <div className="flex items-center justify-center gap-3 mb-6">
            <div className="relative">
              <SparklesIcon className="h-8 w-8 sm:h-10 sm:w-10 text-neural-pink animate-pulse" />
              <div className="absolute inset-0 bg-neural-pink opacity-20 rounded-full blur-md"></div>
            </div>
            <h1 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-white">
              Resume Builder
            </h1>
          </div>          <p className="text-gray-300 text-lg max-w-2xl mx-auto mb-8">
            Create professional, ATS-optimized resumes with our AI-powered builder. 
            <span className="block mt-2 text-green-400 text-base font-semibold">
              ✅ Updated with 2024/2025 ATS best practices for maximum compatibility
            </span>
          </p>

          {/* Mode Toggle */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="flex items-center justify-center mb-12"
          >
            <div className="bg-gray-900/60 backdrop-blur-md rounded-2xl p-3 border border-white/10">
              <div className="flex items-center gap-2 flex-wrap justify-center">
                <button
                  onClick={() => setCurrentMode('classic')}
                  className="px-4 py-2 rounded-lg text-sm font-medium transition-colors bg-neural-purple text-white"
                >
                  Classic Mode
                </button>
                <button
                  onClick={() => setCurrentMode('enhanced')}
                  className="px-4 py-2 rounded-lg text-sm font-medium transition-colors bg-gray-800 text-gray-300 hover:bg-gray-700"
                >
                  <div className="flex items-center gap-2">
                    <Sparkles className="h-4 w-4" />
                    AI Enhanced
                  </div>
                </button>
                <button
                  onClick={() => setCurrentMode('universal')}
                  className="px-4 py-2 rounded-lg text-sm font-medium transition-colors bg-gradient-to-r from-blue-500 to-green-500 text-white"
                >
                  <div className="flex items-center gap-2">
                    <Globe className="h-4 w-4" />
                    Universal
                  </div>
                </button>
              </div>
            </div>
          </motion.div>

          <ClassicMode />
        </motion.div>
      </div>
    </div>
  );
};

export default ResumeBuilder;

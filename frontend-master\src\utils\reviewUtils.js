/**
 * Review System Utilities
 * 
 * Helper functions for managing the review system
 */

// Check if a user has already submitted a review
export const hasUserReviewed = (userId) => {
  if (!userId) return false;
  const reviewedUsers = JSON.parse(localStorage.getItem('reviewedUsers') || '[]');
  return reviewedUsers.includes(userId);
};

// Mark a user as having reviewed
export const markUserAsReviewed = (userId) => {
  if (!userId) return;
  const reviewedUsers = JSON.parse(localStorage.getItem('reviewedUsers') || '[]');
  if (!reviewedUsers.includes(userId)) {
    reviewedUsers.push(userId);
    localStorage.setItem('reviewedUsers', JSON.stringify(reviewedUsers));
  }
};

// Get user's review status for analytics
export const getUserReviewStatus = (userId) => {
  return {
    userId,
    hasReviewed: hasUserReviewed(userId),
    reviewedAt: hasUserReviewed(userId) ? 'stored-locally' : null,
    requiresReview: !hasUserReviewed(userId)
  };
};

// Clear review status (for testing or admin purposes)
export const clearReviewStatus = (userId = null) => {
  if (userId) {
    const reviewedUsers = JSON.parse(localStorage.getItem('reviewedUsers') || '[]');
    const filtered = reviewedUsers.filter(id => id !== userId);
    localStorage.setItem('reviewedUsers', JSON.stringify(filtered));
  } else {
    localStorage.removeItem('reviewedUsers');
  }
};

// Get review statistics from API
export const getReviewStats = async () => {
  try {
    const response = await fetch('/api/reviews');
    const data = await response.json();
    
    if (data.reviews) {
      const stats = {
        totalReviews: data.reviews.length,
        averageRating: data.reviews.reduce((sum, review) => sum + review.rating, 0) / data.reviews.length,
        ratingDistribution: {
          5: data.reviews.filter(r => r.rating === 5).length,
          4: data.reviews.filter(r => r.rating === 4).length,
          3: data.reviews.filter(r => r.rating === 3).length,
          2: data.reviews.filter(r => r.rating === 2).length,
          1: data.reviews.filter(r => r.rating === 1).length,
        },
        mostRecentReview: data.reviews[0]?.date || null
      };
      
      return stats;
    }
    
    return null;
  } catch (error) {
    console.error('Error fetching review stats:', error);
    return null;
  }
};

export default {
  hasUserReviewed,
  markUserAsReviewed,
  getUserReviewStatus,
  clearReviewStatus,
  getReviewStats
};

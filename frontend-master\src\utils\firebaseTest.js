// Test Firebase connection and create sample data
import { collection, addDoc, doc, setDoc } from 'firebase/firestore';
import { firestore } from '@/firebase/config';

export const testFirebaseConnection = async () => {
  try {
    console.log('🔥 Testing Firebase connection...');
    
    // Test adding a user
    const userRef = await addDoc(collection(firestore, 'users'), {
      name: 'Test User',
      email: '<EMAIL>',
      createdAt: new Date()
    });
    console.log('✅ User added with ID:', userRef.id);

    // Test adding a review
    const reviewRef = await addDoc(collection(firestore, 'reviews'), {
      rating: 5,
      comment: 'Great platform!',
      userId: userRef.id,
      createdAt: new Date()
    });
    console.log('✅ Review added with ID:', reviewRef.id);

    return { success: true, message: 'Firebase connection successful!' };
  } catch (error) {
    console.error('❌ Firebase connection failed:', error);
    return { success: false, error: error.message };
  }
};

export const addSampleData = async () => {
  try {
    console.log('📊 Adding sample data...');
    
    // Add multiple users
    const users = [
      { name: '<PERSON>', email: '<EMAIL>' },
      { name: '<PERSON>', email: '<EMAIL>' },
      { name: 'Bob <PERSON>', email: '<EMAIL>' },
      { name: 'Alice Brown', email: '<EMAIL>' },
      { name: 'Charlie Wilson', email: '<EMAIL>' }
    ];

    for (const user of users) {
      await addDoc(collection(firestore, 'users'), {
        ...user,
        createdAt: new Date()
      });
    }

    // Add multiple reviews
    const reviews = [
      { rating: 5, comment: 'Excellent service!' },
      { rating: 4, comment: 'Very good platform' },
      { rating: 5, comment: 'Highly recommended' },
      { rating: 4, comment: 'Great experience' },
      { rating: 5, comment: 'Love the features' },
      { rating: 4, comment: 'Easy to use' },
      { rating: 5, comment: 'Outstanding support' }
    ];

    for (const review of reviews) {
      await addDoc(collection(firestore, 'reviews'), {
        ...review,
        createdAt: new Date()
      });
    }

    console.log('✅ Sample data added successfully!');
    return { success: true, message: 'Sample data added!' };
  } catch (error) {
    console.error('❌ Error adding sample data:', error);
    return { success: false, error: error.message };
  }
};

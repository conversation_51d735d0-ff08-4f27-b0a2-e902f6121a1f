// Pricing configuration for BlinkFind AI
export const PRICING_PLANS = {
  free: {
    id: 'free',
    name: 'Free',
    price: 0,
    currency: 'USD',
    interval: 'forever',
    features: [
      '1 Resume Generation',
      'Basic ATS Analysis',
      '3 Template Options',
      'Basic AI Suggestions',
      'PDF Download'
    ],
    limitations: {
      resumeGenerations: 1,
      templates: 3,
      aiSuggestions: 5,
      atsAnalysis: 'basic'
    },
    popular: false,
    color: 'gray'
  },
  
  basic: {
    id: 'basic',
    name: 'Basic Pro',
    price: 9.99,
    currency: 'USD',
    interval: 'month',
    yearlyPrice: 99.99,
    features: [
      '10 Resume Generations/month',
      'Advanced ATS Analysis',
      '10 Template Options',
      'AI Content Enhancement',
      'Cover Letter Generator',
      'PDF & Word Downloads',
      'Email Support'
    ],
    limitations: {
      resumeGenerations: 10,
      templates: 10,
      aiSuggestions: 50,
      atsAnalysis: 'advanced',
      coverLetters: 5
    },
    popular: true,
    color: 'neural-purple',
    stripeProductId: 'prod_basic_monthly',
    stripePriceId: 'price_basic_monthly',
    razorpayPlanId: 'plan_basic_monthly'
  },
  
  premium: {
    id: 'premium',
    name: 'Premium Pro',
    price: 19.99,
    currency: 'USD',
    interval: 'month',
    yearlyPrice: 199.99,
    features: [
      'Unlimited Resume Generations',
      'Premium ATS Analysis',
      'All 21 Templates',
      'Advanced AI Enhancement',
      'Unlimited Cover Letters',
      'Job Application AI',
      'LinkedIn Profile Optimization',
      'Priority Support',
      'Custom Branding'
    ],
    limitations: {
      resumeGenerations: 'unlimited',
      templates: 'all',
      aiSuggestions: 'unlimited',
      atsAnalysis: 'premium',
      coverLetters: 'unlimited',
      jobApplicationAI: true
    },
    popular: false,
    color: 'neural-pink',
    stripeProductId: 'prod_premium_monthly',
    stripePriceId: 'price_premium_monthly',
    razorpayPlanId: 'plan_premium_monthly'
  },
  
  enterprise: {
    id: 'enterprise',
    name: 'Enterprise',
    price: 49.99,
    currency: 'USD',
    interval: 'month',
    yearlyPrice: 499.99,
    features: [
      'Everything in Premium',
      'Team Management (up to 10 users)',
      'Bulk Resume Processing',
      'API Access',
      'Custom Templates',
      'White-label Solution',
      'Dedicated Account Manager',
      '24/7 Phone Support',
      'Custom Integrations'
    ],
    limitations: {
      resumeGenerations: 'unlimited',
      templates: 'all',
      aiSuggestions: 'unlimited',
      atsAnalysis: 'enterprise',
      coverLetters: 'unlimited',
      jobApplicationAI: true,
      teamMembers: 10,
      apiAccess: true
    },
    popular: false,
    color: 'gradient',
    stripeProductId: 'prod_enterprise_monthly',
    stripePriceId: 'price_enterprise_monthly',
    razorpayPlanId: 'plan_enterprise_monthly'
  }
};

// Indian pricing (in INR)
export const PRICING_PLANS_INR = {
  basic: {
    ...PRICING_PLANS.basic,
    price: 799,
    currency: 'INR',
    yearlyPrice: 7999,
    razorpayPlanId: 'plan_basic_monthly_inr'
  },
  premium: {
    ...PRICING_PLANS.premium,
    price: 1599,
    currency: 'INR',
    yearlyPrice: 15999,
    razorpayPlanId: 'plan_premium_monthly_inr'
  },
  enterprise: {
    ...PRICING_PLANS.enterprise,
    price: 3999,
    currency: 'INR',
    yearlyPrice: 39999,
    razorpayPlanId: 'plan_enterprise_monthly_inr'
  }
};

// One-time purchase options
export const ONE_TIME_PURCHASES = {
  resume_pack_5: {
    id: 'resume_pack_5',
    name: '5 Resume Pack',
    price: 14.99,
    priceINR: 1199,
    features: [
      '5 Resume Generations',
      'All Templates',
      'Advanced ATS Analysis',
      'AI Enhancement',
      '30-day Access'
    ],
    credits: 5,
    validityDays: 30
  },
  
  resume_pack_10: {
    id: 'resume_pack_10',
    name: '10 Resume Pack',
    price: 24.99,
    priceINR: 1999,
    features: [
      '10 Resume Generations',
      'All Templates',
      'Premium ATS Analysis',
      'AI Enhancement',
      'Cover Letter Generator',
      '60-day Access'
    ],
    credits: 10,
    validityDays: 60,
    popular: true
  },
  
  lifetime_access: {
    id: 'lifetime_access',
    name: 'Lifetime Access',
    price: 199.99,
    priceINR: 15999,
    features: [
      'Unlimited Resume Generations',
      'All Templates Forever',
      'Premium Features',
      'Future Updates Included',
      'Lifetime Support'
    ],
    credits: 'unlimited',
    validityDays: 'lifetime'
  }
};

// Payment gateway configuration
export const PAYMENT_GATEWAYS = {
  stripe: {
    enabled: true,
    publicKey: process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY,
    currencies: ['USD', 'EUR', 'GBP', 'CAD', 'AUD'],
    methods: ['card', 'google_pay', 'apple_pay']
  },
  
  razorpay: {
    enabled: true,
    keyId: process.env.NEXT_PUBLIC_RAZORPAY_KEY_ID,
    currencies: ['INR'],
    methods: ['card', 'netbanking', 'wallet', 'upi']
  }
};

// Utility functions
export const getPricingForRegion = (region = 'US') => {
  if (region === 'IN' || region === 'India') {
    return { ...PRICING_PLANS, ...PRICING_PLANS_INR };
  }
  return PRICING_PLANS;
};

export const formatPrice = (price, currency = 'USD') => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: currency === 'INR' ? 0 : 2
  }).format(price);
};

export const calculateYearlySavings = (monthlyPrice, yearlyPrice) => {
  const monthlyCost = monthlyPrice * 12;
  const savings = monthlyCost - yearlyPrice;
  const percentage = Math.round((savings / monthlyCost) * 100);
  return { savings, percentage };
};

export default PRICING_PLANS;

'use client';
import { motion, AnimatePresence } from 'framer-motion';
import { useState } from 'react';
import { 
  Briefcase,
  Plus,
  Trash2,
  Calendar,
  Building,
  MapPin,
  Sparkles,
  Save,
  Wand2,
  AlertTriangle,
  CheckCircle,
  Clock,
  Target
} from 'lucide-react';
import Smart<PERSON>orm<PERSON>ield from './SmartFormField';
import ClientOnly from '../../common/ClientOnly';
import ATSFieldIndicator from '../ats/FieldIndicator';

export const EnhancedExperienceForm = ({ 
  formData, 
  updateFormData, 
  addArrayItem, 
  removeArrayItem, 
  atsAnalysis, 
  validationErrors = {}, 
  showValidationErrors = false,
  onSave,
  onAISuggest
}) => {
  const [expandedItems, setExpandedItems] = useState(new Set([0]));
  const [showAIHelper, setShowAIHelper] = useState(false);

  const toggleExpanded = (index) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(index)) {
      newExpanded.delete(index);
    } else {
      newExpanded.add(index);
    }
    setExpandedItems(newExpanded);
  };

  const addExperience = () => {
    const newIndex = formData.experience.length;
    addArrayItem('experience', {
      title: "",
      company: "",
      location: "",
      startDate: "",
      endDate: "",
      current: false,
      description: ""
    });
    setExpandedItems(new Set([...expandedItems, newIndex]));
  };

  const aiSuggestions = {
    title: [
      "Senior Software Engineer",
      "Full Stack Developer",
      "Frontend Developer",
      "Backend Developer",
      "DevOps Engineer",
      "Product Manager",
      "Data Scientist",
      "UI/UX Designer"
    ],
    company: [
      "Google",
      "Microsoft",
      "Amazon",
      "Meta",
      "Apple",
      "Netflix",
      "Uber",
      "Airbnb"
    ],
    description: [
      "• Led development of scalable web applications serving 100K+ users daily",
      "• Implemented microservices architecture reducing system latency by 40%",
      "• Collaborated with cross-functional teams to deliver features on time",
      "• Mentored junior developers and conducted code reviews",
      "• Optimized database queries improving performance by 60%",
      "• Built responsive user interfaces using React and TypeScript",
      "• Deployed applications using Docker and Kubernetes",
      "• Established CI/CD pipelines reducing deployment time by 50%"
    ]
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="space-y-6"
    >
      {/* Header with Actions */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-neural-purple/20 rounded-xl flex items-center justify-center">
            <Briefcase className="h-5 w-5 text-neural-purple" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-white">Work Experience</h2>
            <p className="text-gray-400 text-sm">Add your professional work history</p>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <button
            onClick={() => setShowAIHelper(!showAIHelper)}
            className="flex items-center gap-2 px-3 py-2 bg-neural-purple/20 hover:bg-neural-purple/30 border border-neural-purple/50 rounded-lg transition-colors text-sm"
          >
            <Sparkles className="h-4 w-4" />
            AI Helper
          </button>
          <button
            onClick={addExperience}
            className="flex items-center gap-2 px-4 py-2 bg-neural-purple hover:bg-neural-purple/80 text-white rounded-xl transition-colors"
          >
            <Plus className="h-4 w-4" />
            Add Experience
          </button>
        </div>
      </div>

      {/* Validation Error Summary */}
      <AnimatePresence>
        {showValidationErrors && validationErrors.experience && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="bg-red-500/10 border border-red-500/20 rounded-xl p-4"
          >
            <div className="flex items-center gap-2 mb-2">
              <AlertTriangle className="h-5 w-5 text-red-400" />
              <h4 className="text-red-400 font-medium">Work Experience Required</h4>
            </div>
            <p className="text-red-300 text-sm">{validationErrors.experience}</p>
          </motion.div>
        )}
      </AnimatePresence>

      {/* AI Helper Panel */}
      <AnimatePresence>
        {showAIHelper && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="bg-gradient-to-r from-neural-purple/10 to-neural-pink/10 border border-neural-purple/30 rounded-xl p-4"
          >
            <div className="flex items-center gap-2 mb-3">
              <Wand2 className="h-5 w-5 text-neural-purple" />
              <h3 className="font-semibold text-white">AI Experience Assistant</h3>
            </div>
            <p className="text-gray-300 text-sm mb-3">
              Get AI-powered suggestions for job titles, companies, and achievement descriptions that will make your experience stand out.
            </p>
            <div className="flex gap-2">
              <button
                onClick={() => onAISuggest?.('experience')}
                className="px-3 py-2 bg-neural-purple hover:bg-neural-purple/80 text-white rounded-lg text-sm transition-colors"
              >
                Generate Suggestions
              </button>
              <button
                onClick={() => setShowAIHelper(false)}
                className="px-3 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg text-sm transition-colors"
              >
                Close
              </button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Experience Items */}
      <div className="space-y-4">
        {formData.experience.map((exp, index) => (
          <motion.div
            key={exp.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="bg-gray-900/40 backdrop-blur-sm rounded-2xl border border-white/10 overflow-hidden"
          >
            {/* Header */}
            <div 
              className="p-4 cursor-pointer hover:bg-gray-800/30 transition-colors"
              onClick={() => toggleExpanded(index)}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-neural-purple/20 rounded-lg flex items-center justify-center">
                    <span className="text-neural-purple font-semibold text-sm">{index + 1}</span>
                  </div>
                  <div>
                    <h3 className="font-semibold text-white">
                      {exp.title || `Experience ${index + 1}`}
                    </h3>
                    <p className="text-gray-400 text-sm">
                      {exp.company || 'Company not specified'} 
                      {exp.startDate && (
                        <span className="ml-2">
                          • {exp.startDate} - {exp.current ? 'Present' : exp.endDate || 'End date not set'}
                        </span>
                      )}
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  {/* Status Indicator */}
                  {exp.title && exp.company && exp.description ? (
                    <div className="flex items-center gap-1 text-green-400">
                      <CheckCircle className="h-4 w-4" />
                      <span className="text-xs">Complete</span>
                    </div>
                  ) : (
                    <div className="flex items-center gap-1 text-yellow-400">
                      <Clock className="h-4 w-4" />
                      <span className="text-xs">Incomplete</span>
                    </div>
                  )}
                  
                  {formData.experience.length > 1 && (
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        removeArrayItem('experience', exp.id);
                      }}
                      className="p-2 text-red-400 hover:text-red-300 hover:bg-red-500/10 rounded-lg transition-colors"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  )}
                  <motion.div
                    animate={{ rotate: expandedItems.has(index) ? 180 : 0 }}
                    transition={{ duration: 0.2 }}
                  >
                    <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </motion.div>
                </div>
              </div>
            </div>

            {/* Expanded Content */}
            <AnimatePresence>
              {expandedItems.has(index) && (
                <motion.div
                  initial={{ height: 0, opacity: 0 }}
                  animate={{ height: 'auto', opacity: 1 }}
                  exit={{ height: 0, opacity: 0 }}
                  transition={{ duration: 0.3 }}
                  className="border-t border-gray-700"
                >
                  <div className="p-6 space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {/* Job Title */}
                      <SmartFormField
                        label="Job Title"
                        type="text"
                        value={exp.title}
                        onChange={(e) => updateFormData('experience', 'title', e.target.value, index)}
                        placeholder="Senior Software Engineer"
                        required
                        error={showValidationErrors && validationErrors.experience && !exp.title.trim() ? 'Job title is required' : null}
                        success={exp.title && !validationErrors.experience ? "Great job title!" : null}
                        icon={Briefcase}
                        aiSuggestions={aiSuggestions.title}
                        onAISuggestionApply={(suggestion) => updateFormData('experience', 'title', suggestion, index)}
                        hint="Use specific, industry-standard job titles"
                      />

                      {/* Company */}
                      <SmartFormField
                        label="Company"
                        type="text"
                        value={exp.company}
                        onChange={(e) => updateFormData('experience', 'company', e.target.value, index)}
                        placeholder="Google Inc."
                        required
                        error={showValidationErrors && validationErrors.experience && !exp.company.trim() ? 'Company is required' : null}
                        success={exp.company && !validationErrors.experience ? "Company added!" : null}
                        icon={Building}
                        aiSuggestions={aiSuggestions.company}
                        onAISuggestionApply={(suggestion) => updateFormData('experience', 'company', suggestion, index)}
                      />

                      {/* Location */}
                      <SmartFormField
                        label="Location"
                        type="text"
                        value={exp.location}
                        onChange={(e) => updateFormData('experience', 'location', e.target.value, index)}
                        placeholder="San Francisco, CA"
                        icon={MapPin}
                        suggestions={["San Francisco, CA", "New York, NY", "Seattle, WA", "Austin, TX", "Remote"]}
                      />

                      {/* Current Position Toggle */}
                      <div className="flex items-center space-x-4">
                        <label className="flex items-center space-x-2 cursor-pointer">
                          <input
                            type="checkbox"
                            checked={exp.current}
                            onChange={(e) => updateFormData('experience', 'current', e.target.checked, index)}
                            className="w-4 h-4 text-neural-purple bg-gray-800 border-gray-600 rounded focus:ring-neural-purple focus:ring-2"
                          />
                          <span className="text-sm text-gray-300">Currently working here</span>
                        </label>
                      </div>

                      {/* Start Date */}
                      <SmartFormField
                        label="Start Date"
                        type="month"
                        value={exp.startDate}
                        onChange={(e) => updateFormData('experience', 'startDate', e.target.value, index)}
                        icon={Calendar}
                        hint="When did you start this position?"
                      />

                      {/* End Date */}
                      <SmartFormField
                        label="End Date"
                        type="month"
                        value={exp.endDate}
                        onChange={(e) => updateFormData('experience', 'endDate', e.target.value, index)}
                        disabled={exp.current}
                        icon={Calendar}
                        hint={exp.current ? "End date disabled for current position" : "When did you leave this position?"}
                      />

                      {/* Job Description */}
                      <div className="md:col-span-2">
                        <SmartFormField
                          label="Job Description & Achievements"
                          type="textarea"
                          value={exp.description}
                          onChange={(e) => updateFormData('experience', 'description', e.target.value, index)}
                          placeholder="• Developed and maintained web applications using React and Node.js&#10;• Led a team of 5 developers in implementing new features&#10;• Improved application performance by 40% and reduced load time by 2 seconds&#10;• Collaborated with cross-functional teams to deliver projects on time"
                          rows={6}
                          maxLength={1000}
                          showCharCount
                          aiSuggestions={aiSuggestions.description}
                          onAISuggestionApply={(suggestion) => updateFormData('experience', 'description', suggestion, index)}
                          hint="Use bullet points and include quantified achievements (numbers, percentages, metrics)"
                        />
                        <ClientOnly>
                          <ATSFieldIndicator
                            fieldName="experience_description"
                            value={exp.description}
                            analysis={atsAnalysis?.fieldAnalysis?.[`experience_description_${index}`]}
                            showDetails={true}
                          />
                        </ClientOnly>
                      </div>
                    </div>

                    {/* Quick Actions */}
                    <div className="flex items-center justify-between pt-4 border-t border-gray-700">
                      <div className="flex items-center gap-2 text-sm text-gray-400">
                        <Target className="h-4 w-4" />
                        <span>ATS Optimization: {exp.description ? 'Good' : 'Add description for better ATS score'}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <button
                          onClick={() => onAISuggest?.(`experience_${index}`)}
                          className="px-3 py-1.5 bg-neural-purple/20 hover:bg-neural-purple/30 border border-neural-purple/50 rounded-lg text-neural-purple text-sm transition-colors"
                        >
                          <Sparkles className="h-3 w-3 inline mr-1" />
                          AI Enhance
                        </button>
                        <button
                          onClick={onSave}
                          className="px-3 py-1.5 bg-gray-700 hover:bg-gray-600 text-white rounded-lg text-sm transition-colors"
                        >
                          <Save className="h-3 w-3 inline mr-1" />
                          Save
                        </button>
                      </div>
                    </div>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>
        ))}
      </div>

      {/* Empty State */}
      {formData.experience.length === 0 && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="text-center py-12 bg-gray-900/20 rounded-2xl border border-dashed border-gray-600"
        >
          <Briefcase className="h-12 w-12 text-gray-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-400 mb-2">No Work Experience Added</h3>
          <p className="text-gray-500 mb-4">Add your professional work history to strengthen your resume</p>
          <button
            onClick={addExperience}
            className="px-6 py-3 bg-neural-purple hover:bg-neural-purple/80 text-white rounded-xl transition-colors"
          >
            Add Your First Experience
          </button>
        </motion.div>
      )}

      {/* Progress Indicator */}
      <div className="bg-gray-900/40 backdrop-blur-sm rounded-xl p-4 border border-white/10">
        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-400">Experience Section Progress</span>
          <span className="text-neural-purple font-medium">
            {formData.experience.filter(exp => exp.title && exp.company && exp.description).length} / {formData.experience.length} completed
          </span>
        </div>
        <div className="w-full bg-gray-700 rounded-full h-2 mt-2">
          <motion.div
            className="bg-gradient-to-r from-neural-purple to-neural-pink h-2 rounded-full"
            initial={{ width: 0 }}
            animate={{ 
              width: `${(formData.experience.filter(exp => exp.title && exp.company && exp.description).length / Math.max(formData.experience.length, 1)) * 100}%` 
            }}
            transition={{ duration: 0.5 }}
          />
        </div>
      </div>
    </motion.div>
  );
};

export default EnhancedExperienceForm;
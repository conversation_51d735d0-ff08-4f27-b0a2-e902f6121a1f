'use client'
import { motion } from 'framer-motion'
import { SparklesIcon } from '@heroicons/react/24/solid'
import { useState, useEffect } from 'react'
import Link from 'next/link'
import { 
  FileText, 
  Search, 
  Shield, 
  Smartphone, 
  Globe,   Bot,
  ArrowRight
} from 'lucide-react'
import PlatformStats from '@/components/features/PlatformStats'

const Hero = () => {
  const [currentSlide, setCurrentSlide] = useState(0)
  // AI Products data  // AI Products data (all marked as upcoming)
  const aiProducts = [    {
      icon: <FileText className="h-8 w-8" />,
      title: "AI Resume Builder",
      description: "Create ATS-optimized resumes in minutes with AI-powered suggestions",
      features: ["Create Resume in 5 minutes", "ATS Scoring", "Job-Specific Content"],
      color: "from-neural-purple to-neural-pink",
      link: "/resume-builder",
      upcoming: true
    },
    {
      icon: <Search className="h-8 w-8" />,
      title: "QuickFind",
      description: "Lost & Found platform with AI-powered matching and recovery",
      features: ["Instant Search", "AI Matching", "Secure Recovery"],
      color: "from-neural-blue to-neural-purple",
      link: "/quickfind",
      upcoming: true
    }
  ]

  // Auto-rotate carousel
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % aiProducts.length)
    }, 4000)
    return () => clearInterval(interval)
  }, [aiProducts.length])

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % aiProducts.length)
  }

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + aiProducts.length) % aiProducts.length)
  }
  return (    <div className="relative overflow-hidden bg-gradient-to-b from-black to-[#0A0A0A] min-h-screen">
      {/* Animated background elements */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute inset-0 bg-[url('/grid.svg')] [mask-image:radial-gradient(ellipse_at_center,white,transparent)]" />
      </div>      {/* Floating AI nodes */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Desktop floating elements */}
        {[...Array(6)].map((_, i) => (
          <motion.div
            key={i}
            className="hidden sm:block absolute rounded-full bg-neural-purple opacity-15 lg:opacity-20 blur-xl"
            initial={{
              x: Math.random() * 100 - 50,
              y: Math.random() * 100 - 50,
              width: Math.random() * 120 + 60, // Desktop: 60-180px
              height: Math.random() * 120 + 60, // Desktop: 60-180px
            }}
            animate={{
              x: Math.random() * 100 - 50,
              y: Math.random() * 100 - 50,
              transition: {
                duration: Math.random() * 15 + 15, // Slower animation
                repeat: Infinity,
                repeatType: 'reverse',
              },
            }}
          />
        ))}
        {/* Smaller mobile-only elements */}
        {[...Array(3)].map((_, i) => (
          <motion.div
            key={`mobile-${i}`}
            className="block sm:hidden absolute rounded-full bg-neural-purple opacity-8 blur-lg"
            initial={{
              x: Math.random() * 100 - 50,
              y: Math.random() * 100 - 50,
              width: Math.random() * 40 + 20, // Mobile: 20-60px
              height: Math.random() * 40 + 20, // Mobile: 20-60px
            }}
            animate={{
              x: Math.random() * 100 - 50,
              y: Math.random() * 100 - 50,
              transition: {
                duration: Math.random() * 20 + 20,
                repeat: Infinity,
                repeatType: 'reverse',
              },
            }}
          />
        ))}
      </div><div className="relative mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 pt-12 pb-12 sm:pt-16 sm:pb-16 lg:pt-20 lg:pb-20 xl:pt-24 xl:pb-24 min-h-screen flex items-center">
        <div className="w-full lg:flex lg:items-center lg:gap-x-12">          {/* Main Content */}
          <div className="mx-auto max-w-2xl lg:mx-0 lg:flex-auto lg:w-[65%] text-center lg:text-left">            <div className="flex items-center gap-x-2 mb-4 hidden sm:flex justify-center lg:justify-start">
            <SparklesIcon className="h-5 w-5 text-neural-pink" />
            <span className="text-sm font-semibold leading-6 text-neural-blue">
              Your Everyday AI for Smarter Living & Working
            </span>
          </div>          <motion.h1
            className="text-2xl font-bold tracking-tight text-white sm:text-3xl lg:text-4xl xl:text-5xl leading-tight relative"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            Turn <span className="bg-gradient-to-r from-neural-purple to-neural-pink bg-clip-text text-transparent relative">
              Hours of Work
              <svg 
                aria-hidden="true" 
                viewBox="0 0 418 42" 
                className="absolute left-0 top-2/3 h-[0.5em] w-full fill-neural-purple/40 opacity-70" 
                preserveAspectRatio="none"
              >
                <path d="M203.371.916c-26.013-2.078-76.686 1.963-124.73 9.946L67.3 12.749C35.421 18.062 18.2 21.766 6.004 25.934 1.244 27.561.828 27.778.874 28.61c.07 1.214.828 1.121 9.595-1.176 9.072-2.377 17.15-3.92 39.246-7.496C123.565 7.986 157.869 4.492 195.942 5.046c7.461.108 19.25 1.696 19.17 2.582-.107 1.183-7.874 4.31-25.75 10.366-21.992 7.45-35.43 12.534-36.701 13.884-2.173 2.308-.202 4.407 4.442 4.734 2.654.187 3.263.157 15.593-.78 35.401-2.686 57.944-3.488 88.365-3.143 46.327.526 75.721 2.23 130.788 7.584 19.787 1.924 20.814 1.98 24.557 1.332l.066-.011c1.201-.203 1.53-1.825.399-2.335-2.911-1.31-4.893-1.604-22.048-3.261-57.509-5.556-87.871-7.36-132.059-7.842-23.239-.254-33.617-.116-50.627.674-11.629.54-42.371 2.494-46.696 2.967-2.359.259 8.133-3.625 26.504-9.81 23.239-7.825 27.934-10.149 28.304-14.005.417-4.348-3.529-6-16.878-7.066Z"></path>
              </svg>
            </span> Into <span className="bg-gradient-to-r from-neural-blue to-neural-purple bg-clip-text text-transparent relative">
              Minutes
              <svg 
                aria-hidden="true" 
                viewBox="0 0 418 42" 
                className="absolute left-0 top-2/3 h-[0.4em] w-full fill-neural-blue/30 opacity-60" 
                preserveAspectRatio="none"
              >
                <path d="M203.371.916c-26.013-2.078-76.686 1.963-124.73 9.946L67.3 12.749C35.421 18.062 18.2 21.766 6.004 25.934 1.244 27.561.828 27.778.874 28.61c.07 1.214.828 1.121 9.595-1.176 9.072-2.377 17.15-3.92 39.246-7.496C123.565 7.986 157.869 4.492 195.942 5.046c7.461.108 19.25 1.696 19.17 2.582-.107 1.183-7.874 4.31-25.75 10.366-21.992 7.45-35.43 12.534-36.701 13.884-2.173 2.308-.202 4.407 4.442 4.734 2.654.187 3.263.157 15.593-.78 35.401-2.686 57.944-3.488 88.365-3.143 46.327.526 75.721 2.23 130.788 7.584 19.787 1.924 20.814 1.98 24.557 1.332l.066-.011c1.201-.203 1.53-1.825.399-2.335-2.911-1.31-4.893-1.604-22.048-3.261-57.509-5.556-87.871-7.36-132.059-7.842-23.239-.254-33.617-.116-50.627.674-11.629.54-42.371 2.494-46.696 2.967-2.359.259 8.133-3.625 26.504-9.81 23.239-7.825 27.934-10.149 28.304-14.005.417-4.348-3.529-6-16.878-7.066Z"></path>
              </svg>
            </span> — With <span className="bg-gradient-to-r from-neural-pink to-neural-blue bg-clip-text text-transparent">AI</span>
          </motion.h1>
            <motion.p 
            className="mt-4 text-base leading-6 text-gray-300 max-w-xl mx-auto lg:mx-0"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            Apply for jobs, plan your day, automate your business — all without switching between tools.
          </motion.p>

          {/* Subheadline */}
          <motion.p 
            className="mt-3 text-xs text-gray-400 max-w-xl mx-auto lg:mx-0"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            Built for busy people who want real results.          </motion.p>          {/* CTA Buttons */}
          <motion.div 
            className="mt-6 flex flex-row items-center gap-3 justify-center lg:justify-start"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
          ><Link href="/resume-builder">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="rounded-lg bg-gradient-to-r from-neural-purple to-neural-pink px-4 sm:px-6 py-2 sm:py-2.5 text-xs sm:text-sm font-semibold text-white shadow-lg hover:opacity-90 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-neural-purple transition-all duration-300"
              >
                Start Free
              </motion.button>
            </Link>


          </motion.div>          
          {/* Platform Statistics */}
          <PlatformStats />
        </div>        {/* AI Products Carousel */}
        <div className="mt-8 sm:mt-12 lg:mt-0 lg:w-[30%] hidden sm:block">
          <motion.div 
            className="relative"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            <div className="absolute inset-0 rounded-xl bg-gradient-to-tr from-neural-purple to-neural-blue opacity-30 blur-xl" />
            <div className="relative rounded-xl bg-gray-900/10 p-2 ring-1 ring-white/10 backdrop-blur-md">
              <div className="h-[300px] sm:h-[320px] lg:h-[340px] w-full rounded-lg bg-gradient-to-br from-black/50 to-gray-900/50 p-4 overflow-hidden">                
                {/* Carousel Header */}
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-2 text-gray-300">
                    <Bot className="h-4 w-4 text-neural-blue" />
                    <span className="text-xs font-medium">AI Products</span>
                  </div>
                  <div className="flex gap-2">
                    <button 
                      onClick={prevSlide}
                      className="p-1 rounded-full bg-gray-800/50 hover:bg-gray-700/50 transition-colors"
                    >
                      <ArrowRight className="h-3 w-3 text-gray-400 rotate-180" />
                    </button>
                    <button 
                      onClick={nextSlide}
                      className="p-1 rounded-full bg-gray-800/50 hover:bg-gray-700/50 transition-colors"
                    >
                      <ArrowRight className="h-3 w-3 text-gray-400" />
                    </button>
                  </div>
                </div>

                {/* Carousel Content */}
                <div className="relative h-full">
                  {aiProducts.map((product, index) => (
                    <motion.div
                      key={index}
                      className={`absolute inset-0 ${index === currentSlide ? 'opacity-100' : 'opacity-0'} transition-opacity duration-500`}
                    >                      <div className="bg-gray-900/60 backdrop-blur-sm rounded-lg p-4 h-full border border-white/10">
                        <div className={`inline-flex p-2 rounded-lg bg-gradient-to-r ${product.color} mb-3`}>
                          <FileText className="h-6 w-6" />
                        </div>
                        
                        <h3 className="text-lg font-bold text-white mb-2">
                          {product.title}
                        </h3>
                        
                        <p className="text-gray-300 text-xs mb-4 leading-relaxed">
                          {product.description}
                        </p>

                        <div className="space-y-2 mb-4">
                          {product.features.map((feature, idx) => (
                            <div key={idx} className="flex items-center gap-2">
                              <div className="h-1 w-1 rounded-full bg-neural-purple"></div>
                              <span className="text-xs text-gray-400">{feature}</span>
                            </div>
                          ))}
                        </div>

                        <Link href={product.link}>
                          <motion.button
                            whileHover={{ scale: 1.02 }}
                            whileTap={{ scale: 0.98 }}
                            className={`w-full py-2.5 px-3 rounded-lg bg-gradient-to-r ${product.color} text-white font-medium text-xs hover:opacity-90 transition-opacity`}
                          >
                            Explore {product.title}
                          </motion.button>
                        </Link>
                      </div>
                    </motion.div>
                  ))}
                </div>                {/* Carousel Indicators */}
                <div className="absolute bottom-3 left-1/2 transform -translate-x-1/2 flex gap-1.5">
                  {aiProducts.map((_, index) => (
                    <button
                      key={index}
                      onClick={() => setCurrentSlide(index)}
                      className={`h-1.5 w-1.5 rounded-full transition-colors ${
                        index === currentSlide ? 'bg-neural-purple' : 'bg-gray-600'
                      }`}
                    />
                  ))}
                </div>
              </div>
            </div>
          </motion.div>
        </div>
        </div>
      </div>
    </div>
  )
}

export default Hero
'use client'
import { motion } from 'framer-motion'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTrigger } from "@/components/ui/dialog"
import { Database, Lock, Shield, User } from 'lucide-react' // Updated icon imports

const PrivacyPolicy = () => {
  const sections = [
    {
      icon: Database, // Changed from Data to Database
      title: "Information We Collect",
      content: "We collect information you provide directly to us, including:",
      items: [
        "Name and contact information",
        "Account credentials",
        "Usage data and analytics",
        "Device information and IP address"
      ]
    },
    {
      icon: Shield,
      title: "How We Use Your Information",
      content: "We use the information we collect to:",
      items: [
        "Provide and improve our services",
        "Send important updates and notifications",
        "Analyze usage patterns",
        "Protect against fraud"
      ]
    },
    {
      icon: Lock,
      title: "Data Security",
      content: "We implement industry-standard security measures to protect your personal information. This includes encryption, secure servers, and regular security audits."
    },
    {
      icon: User,
      title: "Your Rights",
      content: "You have the right to:",
      items: [
        "Access your personal data",
        "Request data correction",
        "Delete your account"
      ]
    }
  ]

  return (
    <Dialog>
      <DialogTrigger asChild>
        <motion.button
          whileHover={{ scale: 1.05 }}
          className="inline-flex items-center text-primary hover:text-primary/80 transition-colors"
        >
          Privacy Policy
        </motion.button>
      </DialogTrigger>
      <DialogContent className="glass-effect max-w-2xl max-h-[80vh] overflow-y-auto border-white/10">
        <DialogHeader>
          <DialogTitle className="text-2xl font-bold text-white mb-6">
            Privacy Policy
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-8">
          {sections.map((section, index) => (
            <motion.section
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="space-y-2"
            >
              <div className="flex items-center gap-3">
                <section.icon className="h-5 w-5 text-primary" />
                <h3 className="text-lg font-semibold text-white">{section.title}</h3>
              </div>
              <p className="text-gray-400">{section.content}</p>
              {section.items && (
                <ul className="list-disc pl-5 mt-2 space-y-1 text-gray-400">
                  {section.items.map((item, i) => (
                    <li key={i}>{item}</li>
                  ))}
                </ul>
              )}
            </motion.section>
          ))}
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.4 }}
          viewport={{ once: true }}
          className="mt-8 text-center"
        >
          <a
            href="/privacy-policy"
            className="inline-flex items-center text-primary hover:text-primary/80 transition-colors"
          >
            View Full Privacy Policy
            <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
            </svg>
          </a>
        </motion.div>
      </DialogContent>
    </Dialog>
  )
}

export default PrivacyPolicy
'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  CreditCard, 
  Smartphone, 
  Globe, 
  Wallet,
  Lock, 
  Loader2, 
  AlertTriangle,
  Check,
  ArrowRight
} from 'lucide-react';
import { formatPrice } from '@/config/pricing';

const RazorpayPayment = ({ 
  plan, 
  amount, 
  currency, 
  billingInterval,
  onSuccess, 
  onError,
  isProcessing,
  setIsProcessing 
}) => {
  const [selectedMethod, setSelectedMethod] = useState('upi');
  const [upiId, setUpiId] = useState('');
  const [isRazorpayLoaded, setIsRazorpayLoaded] = useState(false);

  useEffect(() => {
    // Load Razorpay script
    const loadRazorpay = async () => {
      if (window.Razorpay) {
        setIsRazorpayLoaded(true);
        return;
      }

      const script = document.createElement('script');
      script.src = 'https://checkout.razorpay.com/v1/checkout.js';
      script.onload = () => setIsRazorpayLoaded(true);
      script.onerror = () => {
        console.error('Failed to load Razorpay');
        onError(new Error('Failed to load payment gateway'));
      };
      document.head.appendChild(script);
    };

    loadRazorpay();
  }, [onError]);

  const paymentMethods = [
    {
      id: 'upi',
      name: 'UPI',
      description: 'Pay using UPI ID or QR code',
      icon: <Smartphone className="h-5 w-5" />,
      popular: true
    },
    {
      id: 'card',
      name: 'Credit/Debit Card',
      description: 'Visa, Mastercard, RuPay',
      icon: <CreditCard className="h-5 w-5" />
    },
    {
      id: 'netbanking',
      name: 'Net Banking',
      description: 'All major banks supported',
      icon: <Globe className="h-5 w-5" />
    },
    {
      id: 'wallet',
      name: 'Wallets',
      description: 'Paytm, PhonePe, Google Pay',
      icon: <Wallet className="h-5 w-5" />
    }
  ];

  const handlePayment = async () => {
    if (!isRazorpayLoaded) {
      onError(new Error('Payment gateway not loaded'));
      return;
    }

    if (selectedMethod === 'upi' && !upiId.trim()) {
      onError(new Error('Please enter your UPI ID'));
      return;
    }

    setIsProcessing(true);

    try {
      // Create order on backend (simulated)
      const orderData = {
        amount: amount * 100, // Razorpay uses paise
        currency: 'INR',
        receipt: `receipt_${Date.now()}`,
        notes: {
          plan_id: plan.id,
          billing_interval: billingInterval
        }
      };

      // Simulate order creation
      await new Promise(resolve => setTimeout(resolve, 1000));
      const orderId = 'order_' + Math.random().toString(36).substr(2, 9);

      const options = {
        key: process.env.NEXT_PUBLIC_RAZORPAY_KEY_ID || 'rzp_test_key',
        amount: orderData.amount,
        currency: orderData.currency,
        name: 'BlinkFind AI',
        description: `${plan.name} - ${billingInterval === 'yearly' ? 'Annual' : 'Monthly'} Subscription`,
        order_id: orderId,
        image: '/logo.png',
        prefill: {
          name: 'User Name', // Would come from user context
          email: '<EMAIL>', // Would come from user context
          contact: '**********' // Would come from user context
        },
        notes: orderData.notes,
        theme: {
          color: '#8B5CF6' // neural-purple
        },
        method: {
          upi: selectedMethod === 'upi',
          card: selectedMethod === 'card',
          netbanking: selectedMethod === 'netbanking',
          wallet: selectedMethod === 'wallet'
        },
        handler: function (response) {
          // Payment successful
          const paymentData = {
            id: response.razorpay_payment_id,
            orderId: response.razorpay_order_id,
            signature: response.razorpay_signature,
            amount: amount,
            currency: currency,
            status: 'captured',
            plan: plan.id,
            billingInterval,
            paymentMethod: 'razorpay',
            method: selectedMethod
          };

          onSuccess(paymentData);
        },
        modal: {
          ondismiss: function() {
            setIsProcessing(false);
          }
        }
      };

      // Add UPI ID if selected
      if (selectedMethod === 'upi' && upiId) {
        options.prefill.vpa = upiId;
      }

      const rzp = new window.Razorpay(options);
      
      rzp.on('payment.failed', function (response) {
        onError(new Error(response.error.description || 'Payment failed'));
      });

      rzp.open();

    } catch (error) {
      onError(error);
      setIsProcessing(false);
    }
  };

  if (!isRazorpayLoaded) {
    return (
      <div className="flex items-center justify-center py-8">
        <Loader2 className="h-6 w-6 animate-spin text-neural-purple" />
        <span className="ml-2 text-gray-400">Loading payment gateway...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Payment Summary */}
      <div className="bg-gray-800/50 rounded-xl p-4 border border-gray-700">
        <div className="flex items-center justify-between">
          <div>
            <div className="font-medium text-white">{plan.name}</div>
            <div className="text-sm text-gray-400">
              {billingInterval === 'yearly' ? 'Annual' : 'Monthly'} subscription
            </div>
          </div>
          <div className="text-right">
            <div className="text-xl font-bold text-white">
              {formatPrice(amount, currency)}
            </div>
            <div className="text-sm text-gray-400">
              {billingInterval === 'yearly' ? '/year' : '/month'}
            </div>
          </div>
        </div>
      </div>

      {/* Payment Methods */}
      <div className="space-y-3">
        <h4 className="font-medium text-white">Choose Payment Method</h4>
        <div className="grid grid-cols-2 gap-3">
          {paymentMethods.map((method) => (
            <button
              key={method.id}
              onClick={() => setSelectedMethod(method.id)}
              className={`p-4 rounded-xl border transition-all duration-200 text-left ${
                selectedMethod === method.id
                  ? 'border-neural-purple bg-neural-purple/10'
                  : 'border-gray-700 bg-gray-800/30 hover:border-gray-600'
              }`}
            >
              <div className="flex items-center gap-3 mb-2">
                <div className={`${
                  selectedMethod === method.id ? 'text-neural-purple' : 'text-gray-400'
                }`}>
                  {method.icon}
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <span className="font-medium text-white text-sm">{method.name}</span>
                    {method.popular && (
                      <span className="px-1.5 py-0.5 bg-neural-purple/20 text-neural-purple text-xs rounded">
                        Popular
                      </span>
                    )}
                  </div>
                </div>
                <div className={`w-3 h-3 rounded-full border ${
                  selectedMethod === method.id
                    ? 'border-neural-purple bg-neural-purple'
                    : 'border-gray-600'
                }`} />
              </div>
              <p className="text-xs text-gray-400">{method.description}</p>
            </button>
          ))}
        </div>
      </div>

      {/* UPI ID Input */}
      {selectedMethod === 'upi' && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          className="space-y-3"
        >
          <label className="block text-sm font-medium text-gray-300">
            UPI ID (Optional)
          </label>
          <input
            type="text"
            value={upiId}
            onChange={(e) => setUpiId(e.target.value)}
            placeholder="yourname@paytm"
            className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400"
          />
          <p className="text-xs text-gray-400">
            Leave empty to choose UPI app during payment
          </p>
        </motion.div>
      )}

      {/* Payment Features */}
      <div className="bg-blue-500/10 border border-blue-500/20 rounded-xl p-4">
        <div className="flex items-center gap-2 mb-2">
          <Check className="h-4 w-4 text-blue-400" />
          <span className="text-blue-400 font-medium text-sm">Secure Payment</span>
        </div>
        <ul className="text-xs text-gray-400 space-y-1">
          <li>• Bank-grade security with 256-bit encryption</li>
          <li>• Instant payment confirmation</li>
          <li>• Refund protection as per policy</li>
          <li>• 24/7 customer support</li>
        </ul>
      </div>

      {/* Pay Button */}
      <button
        onClick={handlePayment}
        disabled={isProcessing}
        className="w-full py-3 px-4 bg-neural-purple hover:bg-neural-purple/80 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-xl font-semibold transition-colors flex items-center justify-center gap-2"
      >
        {isProcessing ? (
          <>
            <Loader2 className="h-4 w-4 animate-spin" />
            Processing...
          </>
        ) : (
          <>
            <Lock className="h-4 w-4" />
            Pay {formatPrice(amount, currency)}
            <ArrowRight className="h-4 w-4" />
          </>
        )}
      </button>

      {/* Powered by Razorpay */}
      <div className="text-center text-xs text-gray-500">
        Powered by <span className="font-medium">Razorpay</span>
      </div>
    </div>
  );
};

export default RazorpayPayment;

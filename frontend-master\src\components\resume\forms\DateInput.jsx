'use client';

import { useState } from 'react';
import { Calendar, CalendarDays } from 'lucide-react';

const DateInput = ({ 
  label, 
  value, 
  onChange, 
  placeholder = "dd/mm/yyyy",
  required = false,
  error = null,
  className = ""
}) => {
  const [inputType, setInputType] = useState('text');
  const [showCalendar, setShowCalendar] = useState(false);

  // Convert date formats
  const formatDateForDisplay = (dateValue) => {
    if (!dateValue) return '';
    
    // If it's already in dd/mm/yyyy format, return as is
    if (dateValue.includes('/')) return dateValue;
    
    // If it's in yyyy-mm format (from month input), convert to dd/mm/yyyy
    if (dateValue.includes('-') && dateValue.length === 7) {
      const [year, month] = dateValue.split('-');
      return `01/${month}/${year}`;
    }
    
    // If it's in yyyy-mm-dd format, convert to dd/mm/yyyy
    if (dateValue.includes('-') && dateValue.length === 10) {
      const [year, month, day] = dateValue.split('-');
      return `${day}/${month}/${year}`;
    }
    
    return dateValue;
  };

  const formatDateForInput = (dateValue) => {
    if (!dateValue) return '';
    
    // If it's in dd/mm/yyyy format, convert to yyyy-mm-dd for date input
    if (dateValue.includes('/')) {
      const parts = dateValue.split('/');
      if (parts.length === 3) {
        const [day, month, year] = parts;
        return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
      }
    }
    
    return dateValue;
  };

  const validateDate = (dateStr) => {
    if (!dateStr) return true;
    
    // Check dd/mm/yyyy format
    const dateRegex = /^(\d{1,2})\/(\d{1,2})\/(\d{4})$/;
    const match = dateStr.match(dateRegex);
    
    if (!match) return false;
    
    const [, day, month, year] = match;
    const dayNum = parseInt(day, 10);
    const monthNum = parseInt(month, 10);
    const yearNum = parseInt(year, 10);
    
    // Basic validation
    if (monthNum < 1 || monthNum > 12) return false;
    if (dayNum < 1 || dayNum > 31) return false;
    if (yearNum < 1900 || yearNum > 2030) return false;
    
    return true;
  };

  const handleTextChange = (e) => {
    const newValue = e.target.value;
    
    // Auto-format as user types
    let formatted = newValue.replace(/\D/g, ''); // Remove non-digits
    
    if (formatted.length >= 2) {
      formatted = formatted.substring(0, 2) + '/' + formatted.substring(2);
    }
    if (formatted.length >= 5) {
      formatted = formatted.substring(0, 5) + '/' + formatted.substring(5, 9);
    }
    
    onChange({ target: { value: formatted } });
  };

  const handleDateChange = (e) => {
    const dateValue = e.target.value;
    if (dateValue) {
      // Convert yyyy-mm-dd to dd/mm/yyyy
      const [year, month, day] = dateValue.split('-');
      const formatted = `${day}/${month}/${year}`;
      onChange({ target: { value: formatted } });
    } else {
      onChange({ target: { value: '' } });
    }
    setShowCalendar(false);
  };

  const displayValue = formatDateForDisplay(value);
  const inputValue = formatDateForInput(value);
  const isValid = validateDate(displayValue);

  return (
    <div className={`space-y-2 ${className}`}>
      <label className="block text-sm font-medium text-gray-300">
        {label} {required && <span className="text-red-400">*</span>}
      </label>
      
      <div className="relative">
        <div className="flex gap-2">
          {/* Text Input */}
          <div className="flex-1 relative">
            <input
              type="text"
              value={displayValue}
              onChange={handleTextChange}
              placeholder={placeholder}
              className={`w-full px-4 py-3 bg-gray-800/50 border rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400 pr-10 ${
                error || (!isValid && displayValue) 
                  ? 'border-red-500 focus:ring-red-500 focus:border-red-500' 
                  : 'border-gray-700'
              }`}
              maxLength={10}
            />
            <Calendar className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          </div>
          
          {/* Calendar Picker Button */}
          <button
            type="button"
            onClick={() => setShowCalendar(!showCalendar)}
            className="px-3 py-3 bg-gray-700 hover:bg-gray-600 border border-gray-600 rounded-lg transition-colors"
            title="Open calendar picker"
          >
            <CalendarDays className="h-4 w-4 text-gray-300" />
          </button>
        </div>

        {/* Calendar Input (Hidden) */}
        {showCalendar && (
          <div className="absolute top-full left-0 mt-1 z-10 bg-gray-800 border border-gray-600 rounded-lg p-2 shadow-lg">
            <input
              type="date"
              value={inputValue}
              onChange={handleDateChange}
              className="bg-gray-700 border border-gray-600 rounded text-white p-2"
            />
          </div>
        )}
      </div>

      {/* Validation Messages */}
      {error && (
        <p className="text-red-400 text-sm flex items-center gap-1">
          <span className="w-1 h-1 bg-red-400 rounded-full"></span>
          {error}
        </p>
      )}
      
      {!isValid && displayValue && !error && (
        <p className="text-yellow-400 text-sm flex items-center gap-1">
          <span className="w-1 h-1 bg-yellow-400 rounded-full"></span>
          Please enter a valid date in dd/mm/yyyy format
        </p>
      )}
      
      {isValid && displayValue && (
        <p className="text-green-400 text-sm flex items-center gap-1">
          <span className="w-1 h-1 bg-green-400 rounded-full"></span>
          Valid date format
        </p>
      )}
      
      <p className="text-gray-500 text-xs">
        Enter date manually (dd/mm/yyyy) or use calendar picker
      </p>
    </div>
  );
};

export default DateInput;

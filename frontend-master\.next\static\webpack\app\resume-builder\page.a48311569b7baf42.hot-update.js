"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/resume-builder/page",{

/***/ "(app-pages-browser)/./src/components/resume/EnhancedTemplateSelector.jsx":
/*!************************************************************!*\
  !*** ./src/components/resume/EnhancedTemplateSelector.jsx ***!
  \************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Award_CheckCircle_Code_Crown_Eye_Filter_Globe_Grid_Heart_List_Palette_Search_Shield_Star_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Award,CheckCircle,Code,Crown,Eye,Filter,Globe,Grid,Heart,List,Palette,Search,Shield,Star,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/grid-3x3.js\");\n/* harmony import */ var _barrel_optimize_names_Award_CheckCircle_Code_Crown_Eye_Filter_Globe_Grid_Heart_List_Palette_Search_Shield_Star_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Award,CheckCircle,Code,Crown,Eye,Filter,Globe,Grid,Heart,List,Palette,Search,Shield,Star,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Award_CheckCircle_Code_Crown_Eye_Filter_Globe_Grid_Heart_List_Palette_Search_Shield_Star_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Award,CheckCircle,Code,Crown,Eye,Filter,Globe,Grid,Heart,List,Palette,Search,Shield,Star,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Award_CheckCircle_Code_Crown_Eye_Filter_Globe_Grid_Heart_List_Palette_Search_Shield_Star_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Award,CheckCircle,Code,Crown,Eye,Filter,Globe,Grid,Heart,List,Palette,Search,Shield,Star,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_Award_CheckCircle_Code_Crown_Eye_Filter_Globe_Grid_Heart_List_Palette_Search_Shield_Star_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Award,CheckCircle,Code,Crown,Eye,Filter,Globe,Grid,Heart,List,Palette,Search,Shield,Star,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_Award_CheckCircle_Code_Crown_Eye_Filter_Globe_Grid_Heart_List_Palette_Search_Shield_Star_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Award,CheckCircle,Code,Crown,Eye,Filter,Globe,Grid,Heart,List,Palette,Search,Shield,Star,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/palette.js\");\n/* harmony import */ var _barrel_optimize_names_Award_CheckCircle_Code_Crown_Eye_Filter_Globe_Grid_Heart_List_Palette_Search_Shield_Star_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Award,CheckCircle,Code,Crown,Eye,Filter,Globe,Grid,Heart,List,Palette,Search,Shield,Star,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Award_CheckCircle_Code_Crown_Eye_Filter_Globe_Grid_Heart_List_Palette_Search_Shield_Star_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Award,CheckCircle,Code,Crown,Eye,Filter,Globe,Grid,Heart,List,Palette,Search,Shield,Star,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Award_CheckCircle_Code_Crown_Eye_Filter_Globe_Grid_Heart_List_Palette_Search_Shield_Star_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Award,CheckCircle,Code,Crown,Eye,Filter,Globe,Grid,Heart,List,Palette,Search,Shield,Star,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Award_CheckCircle_Code_Crown_Eye_Filter_Globe_Grid_Heart_List_Palette_Search_Shield_Star_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Award,CheckCircle,Code,Crown,Eye,Filter,Globe,Grid,Heart,List,Palette,Search,Shield,Star,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Award_CheckCircle_Code_Crown_Eye_Filter_Globe_Grid_Heart_List_Palette_Search_Shield_Star_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Award,CheckCircle,Code,Crown,Eye,Filter,Globe,Grid,Heart,List,Palette,Search,Shield,Star,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Award_CheckCircle_Code_Crown_Eye_Filter_Globe_Grid_Heart_List_Palette_Search_Shield_Star_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Award,CheckCircle,Code,Crown,Eye,Filter,Globe,Grid,Heart,List,Palette,Search,Shield,Star,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/list.js\");\n/* harmony import */ var _barrel_optimize_names_Award_CheckCircle_Code_Crown_Eye_Filter_Globe_Grid_Heart_List_Palette_Search_Shield_Star_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Award,CheckCircle,Code,Crown,Eye,Filter,Globe,Grid,Heart,List,Palette,Search,Shield,Star,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Award_CheckCircle_Code_Crown_Eye_Filter_Globe_Grid_Heart_List_Palette_Search_Shield_Star_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Award,CheckCircle,Code,Crown,Eye,Filter,Globe,Grid,Heart,List,Palette,Search,Shield,Star,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _templates_EnhancedTemplateSystem__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./templates/EnhancedTemplateSystem */ \"(app-pages-browser)/./src/components/resume/templates/EnhancedTemplateSystem.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst EnhancedTemplateSelector = (param)=>{\n    let { selectedTemplate, onTemplateSelect, formData, showPreview = true, variant = 'grid' // 'grid', 'list', 'carousel'\n     } = param;\n    _s();\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('ats-score'); // 'ats-score', 'name', 'category'\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(variant);\n    const [previewTemplate, setPreviewTemplate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const categories = [\n        {\n            id: 'all',\n            name: 'All Templates',\n            icon: _barrel_optimize_names_Award_CheckCircle_Code_Crown_Eye_Filter_Globe_Grid_Heart_List_Palette_Search_Shield_Star_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n        },\n        {\n            id: 'ats-optimized',\n            name: 'ATS Optimized',\n            icon: _barrel_optimize_names_Award_CheckCircle_Code_Crown_Eye_Filter_Globe_Grid_Heart_List_Palette_Search_Shield_Star_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n        },\n        {\n            id: 'modern',\n            name: 'Modern',\n            icon: _barrel_optimize_names_Award_CheckCircle_Code_Crown_Eye_Filter_Globe_Grid_Heart_List_Palette_Search_Shield_Star_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n        },\n        {\n            id: 'executive',\n            name: 'Executive',\n            icon: _barrel_optimize_names_Award_CheckCircle_Code_Crown_Eye_Filter_Globe_Grid_Heart_List_Palette_Search_Shield_Star_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n        },\n        {\n            id: 'tech',\n            name: 'Technology',\n            icon: _barrel_optimize_names_Award_CheckCircle_Code_Crown_Eye_Filter_Globe_Grid_Heart_List_Palette_Search_Shield_Star_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n        },\n        {\n            id: 'creative',\n            name: 'Creative',\n            icon: _barrel_optimize_names_Award_CheckCircle_Code_Crown_Eye_Filter_Globe_Grid_Heart_List_Palette_Search_Shield_Star_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n        },\n        {\n            id: 'healthcare',\n            name: 'Healthcare',\n            icon: _barrel_optimize_names_Award_CheckCircle_Code_Crown_Eye_Filter_Globe_Grid_Heart_List_Palette_Search_Shield_Star_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n        },\n        {\n            id: 'regional',\n            name: 'Regional',\n            icon: _barrel_optimize_names_Award_CheckCircle_Code_Crown_Eye_Filter_Globe_Grid_Heart_List_Palette_Search_Shield_Star_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n        },\n        {\n            id: 'minimal',\n            name: 'Minimal',\n            icon: _barrel_optimize_names_Award_CheckCircle_Code_Crown_Eye_Filter_Globe_Grid_Heart_List_Palette_Search_Shield_Star_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n        }\n    ];\n    const filteredTemplates = Object.values(_templates_EnhancedTemplateSystem__WEBPACK_IMPORTED_MODULE_2__.ENHANCED_TEMPLATES).filter((template)=>{\n        const matchesSearch = template.name.toLowerCase().includes(searchTerm.toLowerCase()) || template.description.toLowerCase().includes(searchTerm.toLowerCase());\n        const matchesCategory = selectedCategory === 'all' || template.category === selectedCategory;\n        return matchesSearch && matchesCategory;\n    }).sort((a, b)=>{\n        switch(sortBy){\n            case 'ats-score':\n                return b.atsScore - a.atsScore;\n            case 'name':\n                return a.name.localeCompare(b.name);\n            case 'category':\n                return a.category.localeCompare(b.category);\n            default:\n                return 0;\n        }\n    });\n    const getCategoryIcon = (categoryId)=>{\n        const category = categories.find((cat)=>cat.id === categoryId);\n        return category ? category.icon : _barrel_optimize_names_Award_CheckCircle_Code_Crown_Eye_Filter_Globe_Grid_Heart_List_Palette_Search_Shield_Star_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"];\n    };\n    const getATSScoreColor = (score)=>{\n        if (score >= 95) return 'text-green-400 bg-green-400/20';\n        if (score >= 90) return 'text-yellow-400 bg-yellow-400/20';\n        return 'text-red-400 bg-red-400/20';\n    };\n    const renderTemplateCard = (template)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n            layout: true,\n            initial: {\n                opacity: 0,\n                scale: 0.9\n            },\n            animate: {\n                opacity: 1,\n                scale: 1\n            },\n            exit: {\n                opacity: 0,\n                scale: 0.9\n            },\n            className: \"relative bg-gray-800/50 backdrop-blur-sm rounded-xl border transition-all duration-300 cursor-pointer group hover:scale-105 \".concat(selectedTemplate === template.id ? 'border-neural-purple shadow-lg shadow-neural-purple/20' : 'border-gray-700 hover:border-gray-600'),\n            onClick: ()=>onTemplateSelect(template.id),\n            children: [\n                template.isPremium && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute -top-2 -right-2 z-10\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-r from-neural-purple to-neural-pink p-2 rounded-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_Code_Crown_Eye_Filter_Globe_Grid_Heart_List_Palette_Search_Shield_Star_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"h-3 w-3 text-white\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                            lineNumber: 83,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                        lineNumber: 82,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                    lineNumber: 81,\n                    columnNumber: 9\n                }, undefined),\n                selectedTemplate === template.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute -top-2 -left-2 z-10\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-green-500 p-2 rounded-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_Code_Crown_Eye_Filter_Globe_Grid_Heart_List_Palette_Search_Shield_Star_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            className: \"h-3 w-3 text-white\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                            lineNumber: 92,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                        lineNumber: 91,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                    lineNumber: 90,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"aspect-[8.5/11] bg-white rounded-t-xl overflow-hidden relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full h-full bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center text-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs font-medium mb-1\",\n                                        children: template.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs opacity-60\",\n                                        children: \"Preview\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                                lineNumber: 101,\n                                columnNumber: 11\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                            lineNumber: 100,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors flex items-center justify-center opacity-0 group-hover:opacity-100\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: (e)=>{\n                                    e.stopPropagation();\n                                    setPreviewTemplate(template);\n                                },\n                                className: \"bg-white/90 text-gray-900 px-3 py-2 rounded-lg font-medium flex items-center gap-2 transform scale-90 group-hover:scale-100 transition-transform\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_Code_Crown_Eye_Filter_Globe_Grid_Heart_List_Palette_Search_Shield_Star_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Preview\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                                lineNumber: 109,\n                                columnNumber: 11\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                            lineNumber: 108,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                    lineNumber: 98,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start justify-between mb-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold text-white text-sm\",\n                                    children: template.name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(getATSScoreColor(template.atsScore)),\n                                    children: [\n                                        template.atsScore,\n                                        \"%\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                            lineNumber: 124,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400 text-xs mb-3 line-clamp-2\",\n                            children: template.description\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                            lineNumber: 131,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-1 mb-3\",\n                            children: [\n                                template.features.slice(0, 2).map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"px-2 py-1 bg-gray-700/50 text-gray-300 text-xs rounded\",\n                                        children: feature\n                                    }, index, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 13\n                                    }, undefined)),\n                                template.features.length > 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"px-2 py-1 bg-gray-700/50 text-gray-400 text-xs rounded\",\n                                    children: [\n                                        \"+\",\n                                        template.features.length - 2\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                            lineNumber: 134,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                (()=>{\n                                    const CategoryIcon = getCategoryIcon(template.category);\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CategoryIcon, {\n                                        className: \"h-3 w-3 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 20\n                                    }, undefined);\n                                })(),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-400 text-xs capitalize\",\n                                    children: template.category\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                            lineNumber: 151,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                    lineNumber: 123,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, template.id, true, {\n            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n            lineNumber: 66,\n            columnNumber: 5\n        }, undefined);\n    const renderListView = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: filteredTemplates.map((template)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                    layout: true,\n                    initial: {\n                        opacity: 0,\n                        x: -20\n                    },\n                    animate: {\n                        opacity: 1,\n                        x: 0\n                    },\n                    exit: {\n                        opacity: 0,\n                        x: -20\n                    },\n                    className: \"flex items-center gap-4 p-4 bg-gray-800/50 backdrop-blur-sm rounded-xl border transition-all duration-300 cursor-pointer hover:bg-gray-800/70 \".concat(selectedTemplate === template.id ? 'border-neural-purple shadow-lg shadow-neural-purple/20' : 'border-gray-700 hover:border-gray-600'),\n                    onClick: ()=>onTemplateSelect(template.id),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-16 h-20 bg-white rounded flex-shrink-0 flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-gray-600 text-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"font-medium\",\n                                    children: template.name.split(' ')[0]\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                                lineNumber: 180,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                            lineNumber: 179,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 mb-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-white\",\n                                            children: template.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        template.isPremium && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_Code_Crown_Eye_Filter_Globe_Grid_Heart_List_Palette_Search_Shield_Star_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-4 w-4 text-neural-purple\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                                            lineNumber: 189,\n                                            columnNumber: 38\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(getATSScoreColor(template.atsScore)),\n                                            children: [\n                                                template.atsScore,\n                                                \"% ATS\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400 text-sm mb-2\",\n                                    children: template.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-4 text-xs text-gray-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"capitalize\",\n                                            children: template.category\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                template.features.length,\n                                                \" features\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                            lineNumber: 186,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        setPreviewTemplate(template);\n                                    },\n                                    className: \"p-2 text-gray-400 hover:text-white transition-colors\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_Code_Crown_Eye_Filter_Globe_Grid_Heart_List_Palette_Search_Shield_Star_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 13\n                                }, undefined),\n                                selectedTemplate === template.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_Code_Crown_Eye_Filter_Globe_Grid_Heart_List_Palette_Search_Shield_Star_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"h-5 w-5 text-green-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                            lineNumber: 202,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, template.id, true, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                    lineNumber: 165,\n                    columnNumber: 9\n                }, undefined))\n        }, void 0, false, {\n            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n            lineNumber: 163,\n            columnNumber: 5\n        }, undefined);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-white mb-2\",\n                                children: \"Choose Your Template\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                                lineNumber: 226,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"Select a professional template optimized for ATS systems\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                                lineNumber: 227,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                        lineNumber: 225,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 bg-gray-800/50 rounded-lg p-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setViewMode('grid'),\n                                className: \"p-2 rounded transition-colors \".concat(viewMode === 'grid' ? 'bg-neural-purple text-white' : 'text-gray-400 hover:text-white'),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_Code_Crown_Eye_Filter_Globe_Grid_Heart_List_Palette_Search_Shield_Star_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                                    lineNumber: 238,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                                lineNumber: 232,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setViewMode('list'),\n                                className: \"p-2 rounded transition-colors \".concat(viewMode === 'list' ? 'bg-neural-purple text-white' : 'text-gray-400 hover:text-white'),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_Code_Crown_Eye_Filter_Globe_Grid_Heart_List_Palette_Search_Shield_Star_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                                lineNumber: 240,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                        lineNumber: 231,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                lineNumber: 224,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col lg:flex-row gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_Code_Crown_Eye_Filter_Globe_Grid_Heart_List_Palette_Search_Shield_Star_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                                lineNumber: 255,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                placeholder: \"Search templates...\",\n                                value: searchTerm,\n                                onChange: (e)=>setSearchTerm(e.target.value),\n                                className: \"w-full pl-10 pr-4 py-2 bg-gray-800/50 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-neural-purple focus:border-transparent\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                                lineNumber: 256,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                        lineNumber: 254,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                        value: selectedCategory,\n                        onChange: (e)=>setSelectedCategory(e.target.value),\n                        className: \"px-4 py-2 bg-gray-800/50 border border-gray-700 rounded-lg text-white focus:ring-2 focus:ring-neural-purple focus:border-transparent\",\n                        children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: category.id,\n                                children: category.name\n                            }, category.id, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                                lineNumber: 272,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                        lineNumber: 266,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                        value: sortBy,\n                        onChange: (e)=>setSortBy(e.target.value),\n                        className: \"px-4 py-2 bg-gray-800/50 border border-gray-700 rounded-lg text-white focus:ring-2 focus:ring-neural-purple focus:border-transparent\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"ats-score\",\n                                children: \"ATS Score\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                                lineNumber: 284,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"name\",\n                                children: \"Name\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                                lineNumber: 285,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"category\",\n                                children: \"Category\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                                lineNumber: 286,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                        lineNumber: 279,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                lineNumber: 252,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-gray-400 text-sm\",\n                        children: [\n                            filteredTemplates.length,\n                            \" template\",\n                            filteredTemplates.length !== 1 ? 's' : '',\n                            \" found\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                        lineNumber: 292,\n                        columnNumber: 9\n                    }, undefined),\n                    selectedCategory !== 'all' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setSelectedCategory('all'),\n                        className: \"text-neural-purple hover:text-neural-purple/80 text-sm transition-colors\",\n                        children: \"Clear filters\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                        lineNumber: 296,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                lineNumber: 291,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_17__.AnimatePresence, {\n                mode: \"wait\",\n                children: viewMode === 'grid' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\",\n                    children: filteredTemplates.map(renderTemplateCard)\n                }, \"grid\", false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                    lineNumber: 308,\n                    columnNumber: 11\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    children: renderListView()\n                }, \"list\", false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                    lineNumber: 318,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                lineNumber: 306,\n                columnNumber: 7\n            }, undefined),\n            filteredTemplates.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_Code_Crown_Eye_Filter_Globe_Grid_Heart_List_Palette_Search_Shield_Star_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                        className: \"h-12 w-12 text-gray-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                        lineNumber: 332,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-400 mb-2\",\n                        children: \"No templates found\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                        lineNumber: 333,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-500\",\n                        children: \"Try adjusting your search or filter criteria\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                        lineNumber: 334,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                lineNumber: 331,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_17__.AnimatePresence, {\n                children: previewTemplate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    className: \"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4\",\n                    onClick: ()=>setPreviewTemplate(null),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            scale: 0.9\n                        },\n                        animate: {\n                            opacity: 1,\n                            scale: 1\n                        },\n                        exit: {\n                            opacity: 0,\n                            scale: 0.9\n                        },\n                        className: \"bg-gray-900 rounded-2xl border border-gray-700 p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto\",\n                        onClick: (e)=>e.stopPropagation(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-bold text-white\",\n                                                children: previewTemplate.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                                                lineNumber: 357,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400\",\n                                                children: previewTemplate.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                                                lineNumber: 358,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                                        lineNumber: 356,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setPreviewTemplate(null),\n                                        className: \"text-gray-400 hover:text-white transition-colors\",\n                                        children: \"✕\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                                        lineNumber: 360,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                                lineNumber: 355,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg p-8 min-h-[600px] flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center text-gray-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-medium mb-2\",\n                                            children: [\n                                                previewTemplate.name,\n                                                \" Preview\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                                            lineNumber: 371,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"Full template preview would be rendered here\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                                            lineNumber: 372,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                                    lineNumber: 370,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                                lineNumber: 369,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mt-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"px-3 py-1 rounded-full text-sm font-medium \".concat(getATSScoreColor(previewTemplate.atsScore)),\n                                                children: [\n                                                    previewTemplate.atsScore,\n                                                    \"% ATS Score\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                                                lineNumber: 378,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            previewTemplate.isPremium && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-1 text-neural-purple\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_CheckCircle_Code_Crown_Eye_Filter_Globe_Grid_Heart_List_Palette_Search_Shield_Star_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                                                        lineNumber: 383,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: \"Premium\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                                                        lineNumber: 384,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                                                lineNumber: 382,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                                        lineNumber: 377,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            onTemplateSelect(previewTemplate.id);\n                                            setPreviewTemplate(null);\n                                        },\n                                        className: \"px-6 py-2 bg-neural-purple hover:bg-neural-purple/80 text-white rounded-lg transition-colors\",\n                                        children: \"Use This Template\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                                        lineNumber: 388,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                                lineNumber: 376,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                        lineNumber: 348,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                    lineNumber: 341,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n                lineNumber: 339,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\EnhancedTemplateSelector.jsx\",\n        lineNumber: 222,\n        columnNumber: 5\n    }, undefined);\n};\n_s(EnhancedTemplateSelector, \"+XRCZkFJV5l5fcBdc4m7Po5NrAk=\");\n_c = EnhancedTemplateSelector;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (EnhancedTemplateSelector);\nvar _c;\n$RefreshReg$(_c, \"EnhancedTemplateSelector\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/resume/EnhancedTemplateSelector.jsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/resume/forms/ReviewForm.jsx":
/*!****************************************************!*\
  !*** ./src/components/resume/forms/ReviewForm.jsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EnhancedReviewForm: () => (/* binding */ EnhancedReviewForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowRight,Award,Briefcase,Building,Calendar,CheckCircle,Clock,Code,Crown,Download,Edit3,Eye,FileText,Folder,Globe,GraduationCap,Info,Link,Mail,MapPin,Palette,Phone,Save,Shield,Sparkles,Star,Target,TrendingUp,User,Wand2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowRight,Award,Briefcase,Building,Calendar,CheckCircle,Clock,Code,Crown,Download,Edit3,Eye,FileText,Folder,Globe,GraduationCap,Info,Link,Mail,MapPin,Palette,Phone,Save,Shield,Sparkles,Star,Target,TrendingUp,User,Wand2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowRight,Award,Briefcase,Building,Calendar,CheckCircle,Clock,Code,Crown,Download,Edit3,Eye,FileText,Folder,Globe,GraduationCap,Info,Link,Mail,MapPin,Palette,Phone,Save,Shield,Sparkles,Star,Target,TrendingUp,User,Wand2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowRight,Award,Briefcase,Building,Calendar,CheckCircle,Clock,Code,Crown,Download,Edit3,Eye,FileText,Folder,Globe,GraduationCap,Info,Link,Mail,MapPin,Palette,Phone,Save,Shield,Sparkles,Star,Target,TrendingUp,User,Wand2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowRight,Award,Briefcase,Building,Calendar,CheckCircle,Clock,Code,Crown,Download,Edit3,Eye,FileText,Folder,Globe,GraduationCap,Info,Link,Mail,MapPin,Palette,Phone,Save,Shield,Sparkles,Star,Target,TrendingUp,User,Wand2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/graduation-cap.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowRight,Award,Briefcase,Building,Calendar,CheckCircle,Clock,Code,Crown,Download,Edit3,Eye,FileText,Folder,Globe,GraduationCap,Info,Link,Mail,MapPin,Palette,Phone,Save,Shield,Sparkles,Star,Target,TrendingUp,User,Wand2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowRight,Award,Briefcase,Building,Calendar,CheckCircle,Clock,Code,Crown,Download,Edit3,Eye,FileText,Folder,Globe,GraduationCap,Info,Link,Mail,MapPin,Palette,Phone,Save,Shield,Sparkles,Star,Target,TrendingUp,User,Wand2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowRight,Award,Briefcase,Building,Calendar,CheckCircle,Clock,Code,Crown,Download,Edit3,Eye,FileText,Folder,Globe,GraduationCap,Info,Link,Mail,MapPin,Palette,Phone,Save,Shield,Sparkles,Star,Target,TrendingUp,User,Wand2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowRight,Award,Briefcase,Building,Calendar,CheckCircle,Clock,Code,Crown,Download,Edit3,Eye,FileText,Folder,Globe,GraduationCap,Info,Link,Mail,MapPin,Palette,Phone,Save,Shield,Sparkles,Star,Target,TrendingUp,User,Wand2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowRight,Award,Briefcase,Building,Calendar,CheckCircle,Clock,Code,Crown,Download,Edit3,Eye,FileText,Folder,Globe,GraduationCap,Info,Link,Mail,MapPin,Palette,Phone,Save,Shield,Sparkles,Star,Target,TrendingUp,User,Wand2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowRight,Award,Briefcase,Building,Calendar,CheckCircle,Clock,Code,Crown,Download,Edit3,Eye,FileText,Folder,Globe,GraduationCap,Info,Link,Mail,MapPin,Palette,Phone,Save,Shield,Sparkles,Star,Target,TrendingUp,User,Wand2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowRight,Award,Briefcase,Building,Calendar,CheckCircle,Clock,Code,Crown,Download,Edit3,Eye,FileText,Folder,Globe,GraduationCap,Info,Link,Mail,MapPin,Palette,Phone,Save,Shield,Sparkles,Star,Target,TrendingUp,User,Wand2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _EnhancedFormField__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./EnhancedFormField */ \"(app-pages-browser)/./src/components/resume/forms/EnhancedFormField.jsx\");\n/* harmony import */ var _EnhancedTemplateSelector__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../EnhancedTemplateSelector */ \"(app-pages-browser)/./src/components/resume/EnhancedTemplateSelector.jsx\");\n/* __next_internal_client_entry_do_not_use__ EnhancedReviewForm auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst EnhancedReviewForm = (param)=>{\n    let { formData, updateFormData, atsAnalysis, onSave, onAISuggest, selectedTemplate, onTemplateSelect } = param;\n    var _formData_personal_summary, _formData_personal_summary1;\n    _s();\n    const [showTemplateSelector, setShowTemplateSelector] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [jobDescription, setJobDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(formData.jobDescription || '');\n    // Update job description in parent state\n    const handleJobDescriptionChange = (e)=>{\n        setJobDescription(e.target.value);\n        updateFormData('jobDescription', '', e.target.value);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n        initial: {\n            opacity: 0,\n            y: 20\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        exit: {\n            opacity: 0,\n            y: -20\n        },\n        className: \"space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-10 h-10 bg-neural-purple/20 rounded-xl flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"h-5 w-5 text-neural-purple\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                    lineNumber: 37,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                lineNumber: 36,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-white\",\n                                        children: \"Review & Generate\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                        lineNumber: 40,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Review your information and generate your professional resume\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                        lineNumber: 41,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                lineNumber: 39,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onSave,\n                            className: \"flex items-center gap-2 px-3 py-2 bg-gray-800 hover:bg-gray-700 border border-gray-600 rounded-lg transition-colors text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                    lineNumber: 46,\n                                    columnNumber: 13\n                                }, undefined),\n                                \" Save Draft\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                            lineNumber: 45,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                        lineNumber: 44,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setShowTemplateSelector(true),\n                        className: \"px-4 py-2 bg-gradient-to-r from-neural-purple to-neural-pink text-white rounded-lg font-medium transition-all duration-300 shadow-lg hover:shadow-xl\",\n                        children: \"Choose Template\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                        lineNumber: 53,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"ml-4 text-sm text-gray-400\",\n                        children: [\n                            \"Selected: \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-white font-medium\",\n                                children: selectedTemplate\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                lineNumber: 56,\n                                columnNumber: 64\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                        lineNumber: 56,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, undefined),\n            showTemplateSelector && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TemplateSelector, {\n                selectedTemplate: selectedTemplate,\n                onTemplateSelect: (templateId)=>{\n                    onTemplateSelect(templateId);\n                    setShowTemplateSelector(false);\n                },\n                onClose: ()=>setShowTemplateSelector(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                lineNumber: 59,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-neural-purple/10 to-neural-pink/10 border border-neural-purple/30 rounded-xl p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold mb-2 flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"h-5 w-5 text-neural-purple\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                lineNumber: 72,\n                                columnNumber: 11\n                            }, undefined),\n                            \"Target Job Description (Optional)\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                        lineNumber: 71,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-300 text-sm mb-3\",\n                        children: \"Paste a job description to optimize your resume for a specific role. The AI will tailor your summary, skills, and experience accordingly.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                        lineNumber: 75,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                        value: jobDescription,\n                        onChange: handleJobDescriptionChange,\n                        rows: 6,\n                        className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400 resize-none mb-2\",\n                        placeholder: \"Paste the job description here for AI-powered resume optimization...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                        lineNumber: 76,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-400\",\n                        children: \"\\uD83D\\uDCA1 Adding a job description helps our AI tailor your resume to match specific requirements and keywords.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                lineNumber: 70,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-900/40 backdrop-blur-sm rounded-2xl p-6 border border-white/10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-semibold text-white mb-4\",\n                        children: \"Your Resume Information\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                        lineNumber: 88,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-semibold text-neural-purple mb-2 flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                lineNumber: 91,\n                                                columnNumber: 91\n                                            }, undefined),\n                                            \" Personal Info\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-300\",\n                                        children: [\n                                            formData.personal.firstName,\n                                            \" \",\n                                            formData.personal.lastName,\n                                            \" | \",\n                                            formData.personal.email,\n                                            \" | \",\n                                            formData.personal.phone\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-400\",\n                                        children: [\n                                            formData.personal.location,\n                                            \" | \",\n                                            formData.personal.linkedin,\n                                            \" | \",\n                                            formData.personal.portfolio\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-2 text-gray-200 text-sm\",\n                                        children: formData.personal.summary\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                lineNumber: 90,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-semibold text-neural-purple mb-2 flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                lineNumber: 97,\n                                                columnNumber: 91\n                                            }, undefined),\n                                            \" Education\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    formData.education.map((edu, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-1 text-sm text-gray-300\",\n                                            children: [\n                                                edu.degree,\n                                                \" - \",\n                                                edu.institution,\n                                                \" (\",\n                                                edu.startDate,\n                                                \" - \",\n                                                edu.endDate,\n                                                \") | \",\n                                                edu.location,\n                                                edu.gpa && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-2 text-xs text-gray-400\",\n                                                    children: [\n                                                        \"GPA: \",\n                                                        edu.gpa\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                    lineNumber: 101,\n                                                    columnNumber: 29\n                                                }, undefined),\n                                                edu.relevant && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-gray-400\",\n                                                    children: edu.relevant\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                    lineNumber: 102,\n                                                    columnNumber: 34\n                                                }, undefined)\n                                            ]\n                                        }, edu.id, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 15\n                                        }, undefined))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                lineNumber: 96,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-semibold text-neural-purple mb-2 flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                lineNumber: 107,\n                                                columnNumber: 91\n                                            }, undefined),\n                                            \" Experience\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    formData.experience.map((exp, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-1 text-sm text-gray-300\",\n                                            children: [\n                                                exp.title,\n                                                \" at \",\n                                                exp.company,\n                                                \" (\",\n                                                exp.startDate,\n                                                \" - \",\n                                                exp.current ? 'Present' : exp.endDate,\n                                                \") | \",\n                                                exp.location,\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-gray-400 whitespace-pre-line\",\n                                                    children: exp.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                    lineNumber: 111,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, exp.id, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 15\n                                        }, undefined))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                lineNumber: 106,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-semibold text-neural-purple mb-2 flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                lineNumber: 116,\n                                                columnNumber: 91\n                                            }, undefined),\n                                            \" Skills & Projects\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-400 mb-1\",\n                                        children: [\n                                            \"Technical: \",\n                                            formData.skills.technical.join(', ')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-400 mb-1\",\n                                        children: [\n                                            \"Languages: \",\n                                            formData.skills.languages.join(', ')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-400 mb-1\",\n                                        children: [\n                                            \"Certifications: \",\n                                            formData.skills.certifications.join(', ')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-2\",\n                                        children: formData.projects.map((proj, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-1 text-sm text-gray-300\",\n                                                children: [\n                                                    proj.name,\n                                                    \" (\",\n                                                    proj.technologies,\n                                                    \") \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-gray-400\",\n                                                        children: proj.link\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                        lineNumber: 123,\n                                                        columnNumber: 53\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-400 whitespace-pre-line\",\n                                                        children: proj.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                        lineNumber: 124,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, proj.id, true, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                lineNumber: 115,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                        lineNumber: 89,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                lineNumber: 87,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-neural-purple/10 to-neural-pink/10 border border-neural-purple/30 rounded-xl p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3 mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-10 h-10 bg-neural-purple/20 rounded-xl flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"h-5 w-5 text-neural-purple\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                lineNumber: 135,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold text-white\",\n                                        children: \"Ready to Generate Your Resume?\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                        lineNumber: 139,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-300 text-sm\",\n                                        children: \"Review your information above and generate your professional resume\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                lineNumber: 138,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                        lineNumber: 134,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-sm font-medium text-white mb-3\",\n                                children: \"Content Requirements:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                lineNumber: 146,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            formData.personal.firstName && formData.personal.lastName ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-4 w-4 text-green-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 17\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"h-4 w-4 text-yellow-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                lineNumber: 152,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm \".concat(formData.personal.firstName && formData.personal.lastName ? 'text-green-400' : 'text-yellow-400'),\n                                                children: \"Personal Information Complete\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                lineNumber: 154,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            formData.education.some((edu)=>edu.degree && edu.institution) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-4 w-4 text-green-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 17\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"h-4 w-4 text-yellow-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm \".concat(formData.education.some((edu)=>edu.degree && edu.institution) ? 'text-green-400' : 'text-yellow-400'),\n                                                children: \"Education Information\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                lineNumber: 165,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            formData.experience.some((exp)=>exp.title && exp.company && exp.description && exp.description.length >= 50) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-4 w-4 text-green-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 17\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"h-4 w-4 text-yellow-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm \".concat(formData.experience.some((exp)=>exp.title && exp.company && exp.description && exp.description.length >= 50) ? 'text-green-400' : 'text-yellow-400'),\n                                                children: \"Work Experience (minimum 50 characters per description)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                lineNumber: 176,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            formData.personal.summary && formData.personal.summary.length >= 100 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-4 w-4 text-green-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 17\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"h-4 w-4 text-yellow-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm \".concat(formData.personal.summary && formData.personal.summary.length >= 100 ? 'text-green-400' : 'text-yellow-400'),\n                                                children: \"Professional Summary (minimum 100 characters)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                        lineNumber: 181,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                lineNumber: 147,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                        lineNumber: 145,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-800/30 rounded-lg p-4 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-sm font-medium text-white mb-2\",\n                                children: \"Content Summary:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                lineNumber: 196,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 gap-4 text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-400\",\n                                                children: \"Professional Summary:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2 font-medium \".concat(((_formData_personal_summary = formData.personal.summary) === null || _formData_personal_summary === void 0 ? void 0 : _formData_personal_summary.length) >= 100 ? 'text-green-400' : 'text-yellow-400'),\n                                                children: [\n                                                    ((_formData_personal_summary1 = formData.personal.summary) === null || _formData_personal_summary1 === void 0 ? void 0 : _formData_personal_summary1.length) || 0,\n                                                    \" characters\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-400\",\n                                                children: \"Total Experience Descriptions:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                lineNumber: 205,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2 font-medium \".concat(formData.experience.reduce((total, exp)=>{\n                                                    var _exp_description;\n                                                    return total + (((_exp_description = exp.description) === null || _exp_description === void 0 ? void 0 : _exp_description.length) || 0);\n                                                }, 0) >= 200 ? 'text-green-400' : 'text-yellow-400'),\n                                                children: [\n                                                    formData.experience.reduce((total, exp)=>{\n                                                        var _exp_description;\n                                                        return total + (((_exp_description = exp.description) === null || _exp_description === void 0 ? void 0 : _exp_description.length) || 0);\n                                                    }, 0),\n                                                    \" characters\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                lineNumber: 197,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                        lineNumber: 195,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onSave,\n                                className: \"flex items-center justify-center gap-2 px-6 py-3 bg-gray-800 hover:bg-gray-700 text-white font-medium rounded-lg transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Save Progress\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                lineNumber: 215,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    var _formData_personal_summary;\n                                    // Check minimum requirements\n                                    const hasMinimumContent = formData.personal.firstName && formData.personal.lastName && ((_formData_personal_summary = formData.personal.summary) === null || _formData_personal_summary === void 0 ? void 0 : _formData_personal_summary.length) >= 100 && formData.education.some((edu)=>edu.degree && edu.institution) && formData.experience.some((exp)=>{\n                                        var _exp_description;\n                                        return exp.title && exp.company && ((_exp_description = exp.description) === null || _exp_description === void 0 ? void 0 : _exp_description.length) >= 50;\n                                    });\n                                    if (!hasMinimumContent) {\n                                        alert('Please ensure all required fields are completed with minimum content requirements before generating your resume.');\n                                        return;\n                                    }\n                                    // Trigger resume generation\n                                    if (true) {\n                                        const event = new CustomEvent('generateResume');\n                                        window.dispatchEvent(event);\n                                    }\n                                },\n                                className: \"flex-1 flex items-center justify-center gap-2 px-6 py-3 bg-gradient-to-r from-neural-purple to-neural-pink text-white font-semibold rounded-lg hover:opacity-90 transition-opacity shadow-lg hover:shadow-xl\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Generate AI-Enhanced Resume\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                lineNumber: 223,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                        lineNumber: 214,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 p-3 bg-blue-500/10 border border-blue-500/20 rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-blue-300 text-sm flex items-start gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    className: \"h-4 w-4 mt-0.5 flex-shrink-0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                    lineNumber: 255,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Tip:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                            lineNumber: 257,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \" Make sure your professional summary is at least 100 characters and each work experience description is at least 50 characters for the best AI enhancement results.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                            lineNumber: 254,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                        lineNumber: 253,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                lineNumber: 133,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, undefined);\n};\n_s(EnhancedReviewForm, \"rxUno9qBiVscLNyEcKXUgN2zQfU=\");\n_c = EnhancedReviewForm;\nvar _c;\n$RefreshReg$(_c, \"EnhancedReviewForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/resume/forms/ReviewForm.jsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/resume/templates/EnhancedTemplateSystem.jsx":
/*!********************************************************************!*\
  !*** ./src/components/resume/templates/EnhancedTemplateSystem.jsx ***!
  \********************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ENHANCED_TEMPLATES: () => (/* binding */ ENHANCED_TEMPLATES),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   renderEnhancedTemplate: () => (/* binding */ renderEnhancedTemplate)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ ENHANCED_TEMPLATES,renderEnhancedTemplate,default auto */ \n\n// Enhanced Template System with Modern ATS-Optimized Designs\nconst ENHANCED_TEMPLATES = {\n    // Classic ATS-Optimized Templates\n    classic_ats: {\n        id: 'classic_ats',\n        name: 'Classic ATS',\n        category: 'ats-optimized',\n        atsScore: 100,\n        description: 'Maximum ATS compatibility with traditional formatting',\n        features: [\n            'Single column',\n            'Standard fonts',\n            'Clear sections',\n            'No graphics'\n        ],\n        preview: '/templates/classic-ats.png',\n        isPremium: false\n    },\n    modern_minimal: {\n        id: 'modern_minimal',\n        name: 'Modern Minimal',\n        category: 'modern',\n        atsScore: 98,\n        description: 'Clean, contemporary design with subtle styling',\n        features: [\n            'Minimal design',\n            'Modern typography',\n            'Subtle accents',\n            'ATS-friendly'\n        ],\n        preview: '/templates/modern-minimal.png',\n        isPremium: false\n    },\n    professional_executive: {\n        id: 'professional_executive',\n        name: 'Professional Executive',\n        category: 'executive',\n        atsScore: 96,\n        description: 'Sophisticated layout for senior-level positions',\n        features: [\n            'Executive styling',\n            'Leadership focus',\n            'Premium look',\n            'ATS-compatible'\n        ],\n        preview: '/templates/professional-executive.png',\n        isPremium: true\n    },\n    // Industry-Specific Templates\n    tech_developer: {\n        id: 'tech_developer',\n        name: 'Tech Developer',\n        category: 'tech',\n        atsScore: 97,\n        description: 'Optimized for software development roles',\n        features: [\n            'Skills-focused',\n            'Project highlights',\n            'Tech-friendly',\n            'GitHub integration'\n        ],\n        preview: '/templates/tech-developer.png',\n        isPremium: false\n    },\n    creative_professional: {\n        id: 'creative_professional',\n        name: 'Creative Professional',\n        category: 'creative',\n        atsScore: 90,\n        description: 'Balanced creativity with ATS compatibility',\n        features: [\n            'Creative elements',\n            'Portfolio focus',\n            'Visual appeal',\n            'ATS-safe'\n        ],\n        preview: '/templates/creative-professional.png',\n        isPremium: true\n    },\n    healthcare_medical: {\n        id: 'healthcare_medical',\n        name: 'Healthcare Medical',\n        category: 'healthcare',\n        atsScore: 95,\n        description: 'Tailored for medical and healthcare professionals',\n        features: [\n            'Certification focus',\n            'Medical formatting',\n            'Clean layout',\n            'ATS-optimized'\n        ],\n        preview: '/templates/healthcare-medical.png',\n        isPremium: true\n    },\n    // Regional Templates\n    european_cv: {\n        id: 'european_cv',\n        name: 'European CV',\n        category: 'regional',\n        atsScore: 94,\n        description: 'European CV format with photo placement',\n        features: [\n            'Photo section',\n            'EU standards',\n            'Language skills',\n            'Europass compatible'\n        ],\n        preview: '/templates/european-cv.png',\n        isPremium: false\n    },\n    uk_professional: {\n        id: 'uk_professional',\n        name: 'UK Professional',\n        category: 'regional',\n        atsScore: 96,\n        description: 'British CV format with personal statement',\n        features: [\n            'Personal statement',\n            'UK standards',\n            'A4 optimized',\n            'Traditional'\n        ],\n        preview: '/templates/uk-professional.png',\n        isPremium: false\n    },\n    // Modern Variants\n    contemporary_edge: {\n        id: 'contemporary_edge',\n        name: 'Contemporary Edge',\n        category: 'modern',\n        atsScore: 92,\n        description: 'Modern design with subtle visual elements',\n        features: [\n            'Contemporary style',\n            'Visual hierarchy',\n            'Color accents',\n            'ATS-friendly'\n        ],\n        preview: '/templates/contemporary-edge.png',\n        isPremium: true\n    },\n    minimalist_pro: {\n        id: 'minimalist_pro',\n        name: 'Minimalist Pro',\n        category: 'minimal',\n        atsScore: 99,\n        description: 'Ultra-clean design focusing on content',\n        features: [\n            'Minimal styling',\n            'Content focus',\n            'Perfect spacing',\n            'Maximum ATS'\n        ],\n        preview: '/templates/minimalist-pro.png',\n        isPremium: false\n    }\n};\n// Template rendering function with enhanced formatting\nconst renderEnhancedTemplate = (templateId, formData)=>{\n    const template = ENHANCED_TEMPLATES[templateId];\n    if (!template) return null;\n    // Common styles for all templates\n    const commonStyles = {\n        fontFamily: \"'Inter', 'Helvetica Neue', Arial, sans-serif\",\n        fontSize: '11pt',\n        lineHeight: '1.4',\n        color: '#000000',\n        margin: 0,\n        padding: '0.5in',\n        backgroundColor: '#ffffff'\n    };\n    // Template-specific rendering\n    switch(templateId){\n        case 'classic_ats':\n            return renderClassicATS(formData, commonStyles);\n        case 'modern_minimal':\n            return renderModernMinimal(formData, commonStyles);\n        case 'professional_executive':\n            return renderProfessionalExecutive(formData, commonStyles);\n        case 'tech_developer':\n            return renderTechDeveloper(formData, commonStyles);\n        case 'creative_professional':\n            return renderCreativeProfessional(formData, commonStyles);\n        case 'healthcare_medical':\n            return renderHealthcareMedical(formData, commonStyles);\n        case 'european_cv':\n            return renderEuropeanCV(formData, commonStyles);\n        case 'uk_professional':\n            return renderUKProfessional(formData, commonStyles);\n        case 'contemporary_edge':\n            return renderContemporaryEdge(formData, commonStyles);\n        case 'minimalist_pro':\n            return renderMinimalistPro(formData, commonStyles);\n        default:\n            return renderClassicATS(formData, commonStyles);\n    }\n};\n// Classic ATS Template - Maximum compatibility\nconst renderClassicATS = (formData, baseStyles)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            ...baseStyles,\n            maxWidth: '8.5in',\n            minHeight: '11in',\n            margin: '0 auto',\n            padding: '0.75in'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    textAlign: 'center',\n                    marginBottom: '20pt'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        style: {\n                            fontSize: '18pt',\n                            fontWeight: 'bold',\n                            margin: '0 0 8pt 0',\n                            textTransform: 'uppercase',\n                            letterSpacing: '1pt'\n                        },\n                        children: [\n                            formData.personal.firstName,\n                            \" \",\n                            formData.personal.lastName\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                        lineNumber: 176,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: '10pt',\n                            color: '#333333',\n                            lineHeight: '1.3'\n                        },\n                        children: [\n                            formData.personal.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: formData.personal.email\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                                lineNumber: 191,\n                                columnNumber: 39\n                            }, undefined),\n                            formData.personal.phone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \" • \",\n                                    formData.personal.phone\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                                lineNumber: 192,\n                                columnNumber: 39\n                            }, undefined),\n                            formData.personal.location && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \" • \",\n                                    formData.personal.location\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                                lineNumber: 193,\n                                columnNumber: 42\n                            }, undefined),\n                            formData.personal.linkedin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \" • \",\n                                    formData.personal.linkedin\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                                lineNumber: 194,\n                                columnNumber: 42\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                        lineNumber: 186,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                lineNumber: 175,\n                columnNumber: 7\n            }, undefined),\n            formData.personal.summary && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    marginBottom: '16pt'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            fontSize: '12pt',\n                            fontWeight: 'bold',\n                            textTransform: 'uppercase',\n                            borderBottom: '1pt solid #000000',\n                            paddingBottom: '2pt',\n                            marginBottom: '8pt'\n                        },\n                        children: \"Professional Summary\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                        lineNumber: 201,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        style: {\n                            fontSize: '11pt',\n                            lineHeight: '1.4',\n                            textAlign: 'justify',\n                            margin: 0\n                        },\n                        children: formData.personal.summary\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                        lineNumber: 211,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                lineNumber: 200,\n                columnNumber: 9\n            }, undefined),\n            formData.experience && formData.experience.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    marginBottom: '16pt'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            fontSize: '12pt',\n                            fontWeight: 'bold',\n                            textTransform: 'uppercase',\n                            borderBottom: '1pt solid #000000',\n                            paddingBottom: '2pt',\n                            marginBottom: '8pt'\n                        },\n                        children: \"Professional Experience\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                        lineNumber: 225,\n                        columnNumber: 11\n                    }, undefined),\n                    formData.experience.map((exp, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                marginBottom: '12pt'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        justifyContent: 'space-between',\n                                        alignItems: 'baseline',\n                                        marginBottom: '2pt'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            style: {\n                                                fontSize: '11pt',\n                                                fontWeight: 'bold',\n                                                margin: 0\n                                            },\n                                            children: exp.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                fontSize: '10pt',\n                                                color: '#333333'\n                                            },\n                                            children: [\n                                                exp.startDate,\n                                                \" - \",\n                                                exp.current ? 'Present' : exp.endDate\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                                            lineNumber: 250,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                                    lineNumber: 237,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        fontSize: '11pt',\n                                        fontWeight: '600',\n                                        color: '#333333',\n                                        marginBottom: '4pt'\n                                    },\n                                    children: [\n                                        exp.company,\n                                        \" \",\n                                        exp.location && \"• \".concat(exp.location)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                                    lineNumber: 257,\n                                    columnNumber: 15\n                                }, undefined),\n                                exp.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        fontSize: '11pt',\n                                        lineHeight: '1.4',\n                                        textAlign: 'justify'\n                                    },\n                                    children: exp.description.split('\\n').map((line, lineIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                marginBottom: '2pt'\n                                            },\n                                            children: line.trim().startsWith('•') ? line : \"• \".concat(line)\n                                        }, lineIndex, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                                            lineNumber: 272,\n                                            columnNumber: 21\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, index, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                            lineNumber: 236,\n                            columnNumber: 13\n                        }, undefined))\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                lineNumber: 224,\n                columnNumber: 9\n            }, undefined),\n            formData.education && formData.education.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    marginBottom: '16pt'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            fontSize: '12pt',\n                            fontWeight: 'bold',\n                            textTransform: 'uppercase',\n                            borderBottom: '1pt solid #000000',\n                            paddingBottom: '2pt',\n                            marginBottom: '8pt'\n                        },\n                        children: \"Education\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                        lineNumber: 286,\n                        columnNumber: 11\n                    }, undefined),\n                    formData.education.map((edu, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                marginBottom: '8pt'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        justifyContent: 'space-between',\n                                        alignItems: 'baseline'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontSize: '11pt',\n                                                        fontWeight: 'bold'\n                                                    },\n                                                    children: edu.degree\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                                                    lineNumber: 304,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontSize: '11pt',\n                                                        color: '#333333'\n                                                    },\n                                                    children: edu.field && \" in \".concat(edu.field)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                                                    lineNumber: 310,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                                            lineNumber: 303,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                fontSize: '10pt',\n                                                color: '#333333'\n                                            },\n                                            children: edu.graduationDate\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                                            lineNumber: 317,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                                    lineNumber: 298,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        fontSize: '11pt',\n                                        color: '#333333'\n                                    },\n                                    children: [\n                                        edu.institution,\n                                        \" \",\n                                        edu.location && \"• \".concat(edu.location)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                                    lineNumber: 324,\n                                    columnNumber: 15\n                                }, undefined),\n                                edu.gpa && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        fontSize: '10pt',\n                                        color: '#333333'\n                                    },\n                                    children: [\n                                        \"GPA: \",\n                                        edu.gpa\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                                    lineNumber: 331,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, index, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                            lineNumber: 297,\n                            columnNumber: 13\n                        }, undefined))\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                lineNumber: 285,\n                columnNumber: 9\n            }, undefined),\n            formData.skills && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    marginBottom: '16pt'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            fontSize: '12pt',\n                            fontWeight: 'bold',\n                            textTransform: 'uppercase',\n                            borderBottom: '1pt solid #000000',\n                            paddingBottom: '2pt',\n                            marginBottom: '8pt'\n                        },\n                        children: \"Skills\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                        lineNumber: 346,\n                        columnNumber: 11\n                    }, undefined),\n                    formData.skills.technical && formData.skills.technical.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            marginBottom: '6pt'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                style: {\n                                    fontWeight: 'bold',\n                                    fontSize: '11pt'\n                                },\n                                children: \"Technical: \"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                                lineNumber: 359,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                style: {\n                                    fontSize: '11pt'\n                                },\n                                children: formData.skills.technical.join(', ')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                                lineNumber: 360,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                        lineNumber: 358,\n                        columnNumber: 13\n                    }, undefined),\n                    formData.skills.languages && formData.skills.languages.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            marginBottom: '6pt'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                style: {\n                                    fontWeight: 'bold',\n                                    fontSize: '11pt'\n                                },\n                                children: \"Languages: \"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                                lineNumber: 368,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                style: {\n                                    fontSize: '11pt'\n                                },\n                                children: formData.skills.languages.join(', ')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                                lineNumber: 369,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                        lineNumber: 367,\n                        columnNumber: 13\n                    }, undefined),\n                    formData.skills.certifications && formData.skills.certifications.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            marginBottom: '6pt'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                style: {\n                                    fontWeight: 'bold',\n                                    fontSize: '11pt'\n                                },\n                                children: \"Certifications: \"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                                lineNumber: 377,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                style: {\n                                    fontSize: '11pt'\n                                },\n                                children: formData.skills.certifications.join(', ')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                                lineNumber: 378,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                        lineNumber: 376,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                lineNumber: 345,\n                columnNumber: 9\n            }, undefined),\n            formData.projects && formData.projects.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    marginBottom: '16pt'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            fontSize: '12pt',\n                            fontWeight: 'bold',\n                            textTransform: 'uppercase',\n                            borderBottom: '1pt solid #000000',\n                            paddingBottom: '2pt',\n                            marginBottom: '8pt'\n                        },\n                        children: \"Projects\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                        lineNumber: 389,\n                        columnNumber: 11\n                    }, undefined),\n                    formData.projects.map((project, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                marginBottom: '8pt'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    style: {\n                                        fontSize: '11pt',\n                                        fontWeight: 'bold',\n                                        margin: '0 0 2pt 0'\n                                    },\n                                    children: [\n                                        project.name,\n                                        project.technologies && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                fontSize: '10pt',\n                                                fontWeight: 'normal',\n                                                color: '#333333'\n                                            },\n                                            children: [\n                                                ' ',\n                                                \"(\",\n                                                project.technologies,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                                            lineNumber: 408,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                                    lineNumber: 401,\n                                    columnNumber: 15\n                                }, undefined),\n                                project.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    style: {\n                                        fontSize: '11pt',\n                                        lineHeight: '1.4',\n                                        margin: '0 0 4pt 0',\n                                        textAlign: 'justify'\n                                    },\n                                    children: project.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                                    lineNumber: 418,\n                                    columnNumber: 17\n                                }, undefined),\n                                (project.link || project.github) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        fontSize: '10pt',\n                                        color: '#333333'\n                                    },\n                                    children: [\n                                        project.link && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"Demo: \",\n                                                project.link\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                                            lineNumber: 432,\n                                            columnNumber: 36\n                                        }, undefined),\n                                        project.link && project.github && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \" • \"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                                            lineNumber: 433,\n                                            columnNumber: 54\n                                        }, undefined),\n                                        project.github && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"Code: \",\n                                                project.github\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                                            lineNumber: 434,\n                                            columnNumber: 38\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                                    lineNumber: 428,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, index, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                            lineNumber: 400,\n                            columnNumber: 13\n                        }, undefined))\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                lineNumber: 388,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n        lineNumber: 167,\n        columnNumber: 5\n    }, undefined);\n};\n// Modern Minimal Template - Clean contemporary design\nconst renderModernMinimal = (formData, baseStyles)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            ...baseStyles,\n            maxWidth: '8.5in',\n            minHeight: '11in',\n            margin: '0 auto',\n            padding: '0.75in',\n            fontFamily: \"'Inter', 'Segoe UI', sans-serif\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    textAlign: 'center',\n                    marginBottom: '24pt',\n                    borderBottom: '2pt solid #f0f0f0',\n                    paddingBottom: '16pt'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        style: {\n                            fontSize: '24pt',\n                            fontWeight: '300',\n                            margin: '0 0 8pt 0',\n                            color: '#1a1a1a',\n                            letterSpacing: '0.5pt'\n                        },\n                        children: [\n                            formData.personal.firstName,\n                            \" \",\n                            formData.personal.lastName\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                        lineNumber: 463,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: '11pt',\n                            color: '#666666',\n                            lineHeight: '1.5',\n                            fontWeight: '400'\n                        },\n                        children: [\n                            formData.personal.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: formData.personal.email\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                                lineNumber: 479,\n                                columnNumber: 39\n                            }, undefined),\n                            formData.personal.phone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \" • \",\n                                    formData.personal.phone\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                                lineNumber: 480,\n                                columnNumber: 39\n                            }, undefined),\n                            formData.personal.location && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \" • \",\n                                    formData.personal.location\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                                lineNumber: 481,\n                                columnNumber: 42\n                            }, undefined),\n                            formData.personal.linkedin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \" • \",\n                                    formData.personal.linkedin\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                                lineNumber: 482,\n                                columnNumber: 42\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                        lineNumber: 473,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                lineNumber: 457,\n                columnNumber: 7\n            }, undefined),\n            formData.personal.summary && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    marginBottom: '20pt'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            fontSize: '14pt',\n                            fontWeight: '500',\n                            color: '#2563eb',\n                            marginBottom: '10pt',\n                            letterSpacing: '0.3pt'\n                        },\n                        children: \"Professional Summary\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                        lineNumber: 489,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        style: {\n                            fontSize: '11pt',\n                            lineHeight: '1.6',\n                            color: '#333333',\n                            margin: 0,\n                            textAlign: 'justify'\n                        },\n                        children: formData.personal.summary\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                        lineNumber: 498,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                lineNumber: 488,\n                columnNumber: 9\n            }, undefined),\n            formData.experience && formData.experience.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    marginBottom: '20pt'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            fontSize: '14pt',\n                            fontWeight: '500',\n                            color: '#2563eb',\n                            marginBottom: '12pt',\n                            letterSpacing: '0.3pt'\n                        },\n                        children: \"Experience\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                        lineNumber: 513,\n                        columnNumber: 11\n                    }, undefined),\n                    formData.experience.map((exp, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                marginBottom: '16pt',\n                                paddingLeft: '12pt',\n                                borderLeft: '3pt solid #f0f0f0'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        justifyContent: 'space-between',\n                                        alignItems: 'baseline',\n                                        marginBottom: '4pt'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            style: {\n                                                fontSize: '12pt',\n                                                fontWeight: '600',\n                                                margin: 0,\n                                                color: '#1a1a1a'\n                                            },\n                                            children: exp.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                                            lineNumber: 534,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                fontSize: '10pt',\n                                                color: '#666666',\n                                                fontWeight: '500'\n                                            },\n                                            children: [\n                                                exp.startDate,\n                                                \" - \",\n                                                exp.current ? 'Present' : exp.endDate\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                                            lineNumber: 542,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                                    lineNumber: 528,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        fontSize: '11pt',\n                                        fontWeight: '500',\n                                        color: '#2563eb',\n                                        marginBottom: '6pt'\n                                    },\n                                    children: [\n                                        exp.company,\n                                        \" \",\n                                        exp.location && \"• \".concat(exp.location)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                                    lineNumber: 550,\n                                    columnNumber: 15\n                                }, undefined),\n                                exp.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        fontSize: '11pt',\n                                        lineHeight: '1.5',\n                                        color: '#333333'\n                                    },\n                                    children: exp.description.split('\\n').map((line, lineIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                marginBottom: '3pt'\n                                            },\n                                            children: line.trim().startsWith('•') ? line : \"• \".concat(line)\n                                        }, lineIndex, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                                            lineNumber: 565,\n                                            columnNumber: 21\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                                    lineNumber: 559,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, index, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                            lineNumber: 523,\n                            columnNumber: 13\n                        }, undefined))\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                lineNumber: 512,\n                columnNumber: 9\n            }, undefined),\n            formData.education && formData.education.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    marginBottom: '20pt'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            fontSize: '14pt',\n                            fontWeight: '500',\n                            color: '#2563eb',\n                            marginBottom: '12pt',\n                            letterSpacing: '0.3pt'\n                        },\n                        children: \"Education\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                        lineNumber: 579,\n                        columnNumber: 11\n                    }, undefined),\n                    formData.education.map((edu, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                marginBottom: '12pt',\n                                paddingLeft: '12pt',\n                                borderLeft: '3pt solid #f0f0f0'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        justifyContent: 'space-between',\n                                        alignItems: 'baseline',\n                                        marginBottom: '2pt'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontSize: '12pt',\n                                                        fontWeight: '600',\n                                                        color: '#1a1a1a'\n                                                    },\n                                                    children: edu.degree\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                                                    lineNumber: 601,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontSize: '11pt',\n                                                        color: '#666666'\n                                                    },\n                                                    children: edu.field && \" in \".concat(edu.field)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                                                    lineNumber: 608,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                                            lineNumber: 600,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                fontSize: '10pt',\n                                                color: '#666666',\n                                                fontWeight: '500'\n                                            },\n                                            children: edu.graduationDate\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                                            lineNumber: 615,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                                    lineNumber: 594,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        fontSize: '11pt',\n                                        color: '#2563eb',\n                                        fontWeight: '500'\n                                    },\n                                    children: [\n                                        edu.institution,\n                                        \" \",\n                                        edu.location && \"• \".concat(edu.location)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                                    lineNumber: 623,\n                                    columnNumber: 15\n                                }, undefined),\n                                edu.gpa && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        fontSize: '10pt',\n                                        color: '#666666',\n                                        marginTop: '2pt'\n                                    },\n                                    children: [\n                                        \"GPA: \",\n                                        edu.gpa\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                                    lineNumber: 631,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, index, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                            lineNumber: 589,\n                            columnNumber: 13\n                        }, undefined))\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                lineNumber: 578,\n                columnNumber: 9\n            }, undefined),\n            formData.skills && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    marginBottom: '20pt'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            fontSize: '14pt',\n                            fontWeight: '500',\n                            color: '#2563eb',\n                            marginBottom: '12pt',\n                            letterSpacing: '0.3pt'\n                        },\n                        children: \"Skills\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                        lineNumber: 647,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            paddingLeft: '12pt',\n                            borderLeft: '3pt solid #f0f0f0'\n                        },\n                        children: [\n                            formData.skills.technical && formData.skills.technical.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginBottom: '8pt'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        style: {\n                                            fontWeight: '600',\n                                            fontSize: '11pt',\n                                            color: '#1a1a1a'\n                                        },\n                                        children: \"Technical:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                                        lineNumber: 660,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        style: {\n                                            fontSize: '11pt',\n                                            color: '#333333',\n                                            marginLeft: '6pt'\n                                        },\n                                        children: formData.skills.technical.join(' • ')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                                        lineNumber: 667,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                                lineNumber: 659,\n                                columnNumber: 15\n                            }, undefined),\n                            formData.skills.languages && formData.skills.languages.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginBottom: '8pt'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        style: {\n                                            fontWeight: '600',\n                                            fontSize: '11pt',\n                                            color: '#1a1a1a'\n                                        },\n                                        children: \"Languages:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                                        lineNumber: 679,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        style: {\n                                            fontSize: '11pt',\n                                            color: '#333333',\n                                            marginLeft: '6pt'\n                                        },\n                                        children: formData.skills.languages.join(' • ')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                                        lineNumber: 686,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                                lineNumber: 678,\n                                columnNumber: 15\n                            }, undefined),\n                            formData.skills.certifications && formData.skills.certifications.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginBottom: '8pt'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        style: {\n                                            fontWeight: '600',\n                                            fontSize: '11pt',\n                                            color: '#1a1a1a'\n                                        },\n                                        children: \"Certifications:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                                        lineNumber: 698,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        style: {\n                                            fontSize: '11pt',\n                                            color: '#333333',\n                                            marginLeft: '6pt'\n                                        },\n                                        children: formData.skills.certifications.join(' • ')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                                        lineNumber: 705,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                                lineNumber: 697,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                        lineNumber: 657,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                lineNumber: 646,\n                columnNumber: 9\n            }, undefined),\n            formData.projects && formData.projects.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    marginBottom: '20pt'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            fontSize: '14pt',\n                            fontWeight: '500',\n                            color: '#2563eb',\n                            marginBottom: '12pt',\n                            letterSpacing: '0.3pt'\n                        },\n                        children: \"Projects\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                        lineNumber: 721,\n                        columnNumber: 11\n                    }, undefined),\n                    formData.projects.map((project, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                marginBottom: '12pt',\n                                paddingLeft: '12pt',\n                                borderLeft: '3pt solid #f0f0f0'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    style: {\n                                        fontSize: '12pt',\n                                        fontWeight: '600',\n                                        margin: '0 0 4pt 0',\n                                        color: '#1a1a1a'\n                                    },\n                                    children: [\n                                        project.name,\n                                        project.technologies && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                fontSize: '10pt',\n                                                fontWeight: '500',\n                                                color: '#666666',\n                                                marginLeft: '8pt'\n                                            },\n                                            children: [\n                                                \"(\",\n                                                project.technologies,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                                            lineNumber: 744,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                                    lineNumber: 736,\n                                    columnNumber: 15\n                                }, undefined),\n                                project.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    style: {\n                                        fontSize: '11pt',\n                                        lineHeight: '1.5',\n                                        margin: '0 0 6pt 0',\n                                        color: '#333333',\n                                        textAlign: 'justify'\n                                    },\n                                    children: project.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                                    lineNumber: 755,\n                                    columnNumber: 17\n                                }, undefined),\n                                (project.link || project.github) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        fontSize: '10pt',\n                                        color: '#2563eb',\n                                        fontWeight: '500'\n                                    },\n                                    children: [\n                                        project.link && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"Demo: \",\n                                                project.link\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                                            lineNumber: 771,\n                                            columnNumber: 36\n                                        }, undefined),\n                                        project.link && project.github && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \" • \"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                                            lineNumber: 772,\n                                            columnNumber: 54\n                                        }, undefined),\n                                        project.github && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"Code: \",\n                                                project.github\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                                            lineNumber: 773,\n                                            columnNumber: 38\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                                    lineNumber: 766,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, index, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                            lineNumber: 731,\n                            columnNumber: 13\n                        }, undefined))\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n                lineNumber: 720,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\templates\\\\EnhancedTemplateSystem.jsx\",\n        lineNumber: 448,\n        columnNumber: 5\n    }, undefined);\n};\nconst renderProfessionalExecutive = (formData, baseStyles)=>{\n    // Implementation for professional executive template\n    return renderClassicATS(formData, baseStyles); // Temporary fallback\n};\nconst renderTechDeveloper = (formData, baseStyles)=>{\n    // Implementation for tech developer template\n    return renderClassicATS(formData, baseStyles); // Temporary fallback\n};\nconst renderCreativeProfessional = (formData, baseStyles)=>{\n    // Implementation for creative professional template\n    return renderClassicATS(formData, baseStyles); // Temporary fallback\n};\nconst renderHealthcareMedical = (formData, baseStyles)=>{\n    // Implementation for healthcare medical template\n    return renderClassicATS(formData, baseStyles); // Temporary fallback\n};\nconst renderEuropeanCV = (formData, baseStyles)=>{\n    // Implementation for European CV template\n    return renderClassicATS(formData, baseStyles); // Temporary fallback\n};\nconst renderUKProfessional = (formData, baseStyles)=>{\n    // Implementation for UK professional template\n    return renderClassicATS(formData, baseStyles); // Temporary fallback\n};\nconst renderContemporaryEdge = (formData, baseStyles)=>{\n    // Implementation for contemporary edge template\n    return renderClassicATS(formData, baseStyles); // Temporary fallback\n};\nconst renderMinimalistPro = (formData, baseStyles)=>{\n    // Implementation for minimalist pro template\n    return renderClassicATS(formData, baseStyles); // Temporary fallback\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\n    ENHANCED_TEMPLATES,\n    renderEnhancedTemplate\n});\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/resume/templates/EnhancedTemplateSystem.jsx\n"));

/***/ })

});
"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/resume-builder/page",{

/***/ "(app-pages-browser)/./src/components/resume/ResumeBuilder.jsx":
/*!*************************************************!*\
  !*** ./src/components/resume/ResumeBuilder.jsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Briefcase,CheckCircle,Clock,Download,Eye,FileText,GraduationCap,Lightbulb,RotateCcw,Save,Sparkles,Target,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Briefcase,CheckCircle,Clock,Download,Eye,FileText,GraduationCap,Lightbulb,RotateCcw,Save,Sparkles,Target,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/graduation-cap.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Briefcase,CheckCircle,Clock,Download,Eye,FileText,GraduationCap,Lightbulb,RotateCcw,Save,Sparkles,Target,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Briefcase,CheckCircle,Clock,Download,Eye,FileText,GraduationCap,Lightbulb,RotateCcw,Save,Sparkles,Target,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Briefcase,CheckCircle,Clock,Download,Eye,FileText,GraduationCap,Lightbulb,RotateCcw,Save,Sparkles,Target,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Briefcase,CheckCircle,Clock,Download,Eye,FileText,GraduationCap,Lightbulb,RotateCcw,Save,Sparkles,Target,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Briefcase,CheckCircle,Clock,Download,Eye,FileText,GraduationCap,Lightbulb,RotateCcw,Save,Sparkles,Target,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _StepNavigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./StepNavigation */ \"(app-pages-browser)/./src/components/resume/StepNavigation.jsx\");\n/* harmony import */ var _layout_SmartNavigationBar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../layout/SmartNavigationBar */ \"(app-pages-browser)/./src/components/layout/SmartNavigationBar.jsx\");\n/* harmony import */ var _forms_ResumeFormComponents__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./forms/ResumeFormComponents */ \"(app-pages-browser)/./src/components/resume/forms/ResumeFormComponents.jsx\");\n/* harmony import */ var _forms_ExperienceForm__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./forms/ExperienceForm */ \"(app-pages-browser)/./src/components/resume/forms/ExperienceForm.jsx\");\n/* harmony import */ var _forms_SkillsProjectsForm__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./forms/SkillsProjectsForm */ \"(app-pages-browser)/./src/components/resume/forms/SkillsProjectsForm.jsx\");\n/* harmony import */ var _forms_ProjectsForm__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./forms/ProjectsForm */ \"(app-pages-browser)/./src/components/resume/forms/ProjectsForm.jsx\");\n/* harmony import */ var _forms_SkillsForm__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./forms/SkillsForm */ \"(app-pages-browser)/./src/components/resume/forms/SkillsForm.jsx\");\n/* harmony import */ var _forms_ReviewForm__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./forms/ReviewForm */ \"(app-pages-browser)/./src/components/resume/forms/ReviewForm.jsx\");\n/* harmony import */ var _EnhancedProgressIndicator__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./EnhancedProgressIndicator */ \"(app-pages-browser)/./src/components/resume/EnhancedProgressIndicator.jsx\");\n/* harmony import */ var _EnhancedNavigation__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./EnhancedNavigation */ \"(app-pages-browser)/./src/components/resume/EnhancedNavigation.jsx\");\n/* harmony import */ var _common_ProgressBar__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../common/ProgressBar */ \"(app-pages-browser)/./src/components/common/ProgressBar.jsx\");\n/* harmony import */ var _ResumePreview__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./ResumePreview */ \"(app-pages-browser)/./src/components/resume/ResumePreview.jsx\");\n/* harmony import */ var _SimpleResumePreview__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./SimpleResumePreview */ \"(app-pages-browser)/./src/components/resume/SimpleResumePreview.jsx\");\n/* harmony import */ var _SuccessScreen__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./SuccessScreen */ \"(app-pages-browser)/./src/components/resume/SuccessScreen.jsx\");\n/* harmony import */ var _hooks_useATSAnalysis__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/hooks/useATSAnalysis */ \"(app-pages-browser)/./src/hooks/useATSAnalysis.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst EnhancedResumeBuilder = (param)=>{\n    let { hideHeader = false } = param;\n    _s();\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [selectedTemplate, setSelectedTemplate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('modern');\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        personal: {\n            firstName: \"\",\n            lastName: \"\",\n            email: \"\",\n            phone: \"\",\n            location: \"\",\n            linkedin: \"\",\n            portfolio: \"\",\n            summary: \"\",\n            profileImage: \"\"\n        },\n        education: [\n            {\n                id: 1,\n                degree: \"\",\n                institution: \"\",\n                location: \"\",\n                startDate: \"\",\n                endDate: \"\",\n                gpa: \"\",\n                relevant: \"\"\n            }\n        ],\n        experience: [\n            {\n                id: 1,\n                title: \"\",\n                company: \"\",\n                location: \"\",\n                startDate: \"\",\n                endDate: \"\",\n                current: false,\n                description: \"\"\n            }\n        ],\n        skills: {\n            technical: [],\n            languages: [],\n            certifications: []\n        },\n        projects: [\n            {\n                id: 1,\n                name: \"\",\n                description: \"\",\n                technologies: \"\",\n                link: \"\"\n            }\n        ],\n        jobDescription: \"\"\n    });\n    const [showPreview, setShowPreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showProgressBar, setShowProgressBar] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [resumeGenerated, setResumeGenerated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [validationErrors, setValidationErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [showValidationErrors, setShowValidationErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [completedSteps, setCompletedSteps] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [autoSaveEnabled, setAutoSaveEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [lastSaved, setLastSaved] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [resumeData, setResumeData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [resumeUrl, setResumeUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [atsScore, setAtsScore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [suggestions, setSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [aiSuggestionsEnabled, setAiSuggestionsEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [smartValidation, setSmartValidation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showHints, setShowHints] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const steps = [\n        {\n            id: 0,\n            title: \"Personal Information\",\n            icon: _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n            description: \"Tell us about yourself\",\n            estimatedTime: 3,\n            required: true\n        },\n        {\n            id: 1,\n            title: \"Education\",\n            icon: _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n            description: \"Your academic background\",\n            estimatedTime: 5,\n            required: true\n        },\n        {\n            id: 2,\n            title: \"Experience\",\n            icon: _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n            description: \"Your work experience\",\n            estimatedTime: 8,\n            required: true\n        },\n        {\n            id: 3,\n            title: \"Projects\",\n            icon: _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n            description: \"Showcase your projects\",\n            estimatedTime: 4,\n            required: false\n        },\n        {\n            id: 4,\n            title: \"Skills\",\n            icon: _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"],\n            description: \"Your technical & soft skills\",\n            estimatedTime: 3,\n            required: false\n        },\n        {\n            id: 5,\n            title: \"Review & Generate\",\n            icon: _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n            description: \"Finalize your resume\",\n            estimatedTime: 2,\n            required: false\n        }\n    ];\n    const atsAnalysis = (0,_hooks_useATSAnalysis__WEBPACK_IMPORTED_MODULE_17__[\"default\"])(formData);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EnhancedResumeBuilder.useEffect\": ()=>{\n            // Check for backup data from auth flow first\n            const backupData = localStorage.getItem('resumeFormDataBackup');\n            const authTimestamp = localStorage.getItem('authFlowTimestamp');\n            const savedData = localStorage.getItem('resumeFormData');\n            // If backup data exists and is recent (within 10 minutes), use it\n            if (backupData && authTimestamp) {\n                const timeDiff = Date.now() - parseInt(authTimestamp);\n                if (timeDiff < 10 * 60 * 1000) {\n                    try {\n                        const parsed = JSON.parse(backupData);\n                        setFormData(parsed);\n                        setLastSaved(new Date());\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success('Your work has been restored after sign-in!');\n                        // Clean up backup data\n                        localStorage.removeItem('resumeFormDataBackup');\n                        localStorage.removeItem('authFlowTimestamp');\n                        // Update main storage\n                        localStorage.setItem('resumeFormData', backupData);\n                        localStorage.setItem('resumeLastSaved', new Date().toISOString());\n                        return;\n                    } catch (error) {\n                        console.error('Failed to load backup data:', error);\n                    }\n                }\n            }\n            // Fallback to regular saved data\n            if (savedData) {\n                try {\n                    const parsed = JSON.parse(savedData);\n                    setFormData(parsed);\n                    setLastSaved(new Date(localStorage.getItem('resumeLastSaved') || Date.now()));\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success('Previous work restored!');\n                } catch (error) {\n                    console.error('Failed to load saved data:', error);\n                }\n            }\n            // Clean up old backup data\n            localStorage.removeItem('resumeFormDataBackup');\n            localStorage.removeItem('authFlowTimestamp');\n        }\n    }[\"EnhancedResumeBuilder.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EnhancedResumeBuilder.useEffect\": ()=>{\n            if (autoSaveEnabled && formData) {\n                // Make form data globally accessible for auth flow\n                if (true) {\n                    window.formData = formData;\n                }\n                const timeoutId = setTimeout({\n                    \"EnhancedResumeBuilder.useEffect.timeoutId\": ()=>{\n                        localStorage.setItem('resumeFormData', JSON.stringify(formData));\n                        localStorage.setItem('resumeLastSaved', new Date().toISOString());\n                        setLastSaved(new Date());\n                    }\n                }[\"EnhancedResumeBuilder.useEffect.timeoutId\"], 2000);\n                return ({\n                    \"EnhancedResumeBuilder.useEffect\": ()=>clearTimeout(timeoutId)\n                })[\"EnhancedResumeBuilder.useEffect\"];\n            }\n        }\n    }[\"EnhancedResumeBuilder.useEffect\"], [\n        formData,\n        autoSaveEnabled\n    ]);\n    const validateStep = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[validateStep]\": (stepIndex)=>{\n            const errors = {};\n            switch(stepIndex){\n                case 0:\n                    var _formData_personal_firstName, _formData_personal_lastName, _formData_personal_email;\n                    if (!((_formData_personal_firstName = formData.personal.firstName) === null || _formData_personal_firstName === void 0 ? void 0 : _formData_personal_firstName.trim())) errors.firstName = 'First name is required';\n                    if (!((_formData_personal_lastName = formData.personal.lastName) === null || _formData_personal_lastName === void 0 ? void 0 : _formData_personal_lastName.trim())) errors.lastName = 'Last name is required';\n                    if (!((_formData_personal_email = formData.personal.email) === null || _formData_personal_email === void 0 ? void 0 : _formData_personal_email.trim())) errors.email = 'Email is required';\n                    else if (!/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(formData.personal.email)) errors.email = 'Please enter a valid email address';\n                    break;\n                case 1:\n                    var _formData_education;\n                    const validEducation = (_formData_education = formData.education) === null || _formData_education === void 0 ? void 0 : _formData_education.filter({\n                        \"EnhancedResumeBuilder.useCallback[validateStep]\": (edu)=>{\n                            var _edu_degree, _edu_institution;\n                            return ((_edu_degree = edu.degree) === null || _edu_degree === void 0 ? void 0 : _edu_degree.trim()) && ((_edu_institution = edu.institution) === null || _edu_institution === void 0 ? void 0 : _edu_institution.trim());\n                        }\n                    }[\"EnhancedResumeBuilder.useCallback[validateStep]\"]);\n                    if (!validEducation || validEducation.length === 0) errors.education = 'At least one education entry with degree and institution is required';\n                    break;\n                case 2:\n                    var _formData_experience;\n                    const validExperience = (_formData_experience = formData.experience) === null || _formData_experience === void 0 ? void 0 : _formData_experience.filter({\n                        \"EnhancedResumeBuilder.useCallback[validateStep]\": (exp)=>{\n                            var _exp_title, _exp_company;\n                            return ((_exp_title = exp.title) === null || _exp_title === void 0 ? void 0 : _exp_title.trim()) && ((_exp_company = exp.company) === null || _exp_company === void 0 ? void 0 : _exp_company.trim());\n                        }\n                    }[\"EnhancedResumeBuilder.useCallback[validateStep]\"]);\n                    if (!validExperience || validExperience.length === 0) errors.experience = 'At least one work experience entry with job title and company is required';\n                    break;\n                case 3:\n                    break;\n                case 4:\n                    break;\n                default:\n                    break;\n            }\n            return errors;\n        }\n    }[\"EnhancedResumeBuilder.useCallback[validateStep]\"], [\n        formData\n    ]);\n    const canProceedToNextStep = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[canProceedToNextStep]\": (stepIndex)=>{\n            const errors = validateStep(stepIndex);\n            return Object.keys(errors).length === 0;\n        }\n    }[\"EnhancedResumeBuilder.useCallback[canProceedToNextStep]\"], [\n        validateStep\n    ]);\n    const getCompletedSteps = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[getCompletedSteps]\": ()=>{\n            const completed = [];\n            for(let i = 0; i < currentStep; i++){\n                const errors = validateStep(i);\n                if (Object.keys(errors).length === 0) {\n                    completed.push(i);\n                }\n            }\n            return completed;\n        }\n    }[\"EnhancedResumeBuilder.useCallback[getCompletedSteps]\"], [\n        currentStep,\n        validateStep\n    ]);\n    const getStepCompletionStatus = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[getStepCompletionStatus]\": ()=>{\n            const status = {};\n            steps.forEach({\n                \"EnhancedResumeBuilder.useCallback[getStepCompletionStatus]\": (step, index)=>{\n                    const errors = validateStep(index);\n                    status[index] = Object.keys(errors).length === 0;\n                }\n            }[\"EnhancedResumeBuilder.useCallback[getStepCompletionStatus]\"]);\n            return status;\n        }\n    }[\"EnhancedResumeBuilder.useCallback[getStepCompletionStatus]\"], [\n        steps,\n        validateStep\n    ]);\n    const canGenerateResume = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[canGenerateResume]\": ()=>{\n            for(let i = 0; i < steps.length - 1; i++){\n                if (!canProceedToNextStep(i)) return false;\n            }\n            return true;\n        }\n    }[\"EnhancedResumeBuilder.useCallback[canGenerateResume]\"], [\n        canProceedToNextStep,\n        steps.length\n    ]);\n    const updateFormData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[updateFormData]\": function(section, field, value) {\n            let index = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : null;\n            setFormData({\n                \"EnhancedResumeBuilder.useCallback[updateFormData]\": (prev)=>{\n                    let newData = {\n                        ...prev\n                    };\n                    if (index !== null && Array.isArray(prev[section])) {\n                        const newArray = [\n                            ...prev[section]\n                        ];\n                        newArray[index] = {\n                            ...newArray[index],\n                            [field]: value\n                        };\n                        newData[section] = newArray;\n                    } else if (typeof prev[section] === 'object' && !Array.isArray(prev[section])) {\n                        newData[section] = {\n                            ...prev[section],\n                            [field]: value\n                        };\n                    } else {\n                        newData[field] = value;\n                    }\n                    return newData;\n                }\n            }[\"EnhancedResumeBuilder.useCallback[updateFormData]\"]);\n            if (validationErrors[field]) {\n                setValidationErrors({\n                    \"EnhancedResumeBuilder.useCallback[updateFormData]\": (prev)=>{\n                        const newErrors = {\n                            ...prev\n                        };\n                        delete newErrors[field];\n                        return newErrors;\n                    }\n                }[\"EnhancedResumeBuilder.useCallback[updateFormData]\"]);\n            }\n        }\n    }[\"EnhancedResumeBuilder.useCallback[updateFormData]\"], [\n        validationErrors\n    ]);\n    const addArrayItem = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[addArrayItem]\": (section, template)=>{\n            setFormData({\n                \"EnhancedResumeBuilder.useCallback[addArrayItem]\": (prev)=>({\n                        ...prev,\n                        [section]: [\n                            ...prev[section],\n                            {\n                                ...template,\n                                id: Math.random().toString(36).substring(2, 11)\n                            }\n                        ]\n                    })\n            }[\"EnhancedResumeBuilder.useCallback[addArrayItem]\"]);\n        }\n    }[\"EnhancedResumeBuilder.useCallback[addArrayItem]\"], []);\n    const removeArrayItem = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[removeArrayItem]\": (section, id)=>{\n            setFormData({\n                \"EnhancedResumeBuilder.useCallback[removeArrayItem]\": (prev)=>({\n                        ...prev,\n                        [section]: prev[section].filter({\n                            \"EnhancedResumeBuilder.useCallback[removeArrayItem]\": (item)=>item.id !== id\n                        }[\"EnhancedResumeBuilder.useCallback[removeArrayItem]\"])\n                    })\n            }[\"EnhancedResumeBuilder.useCallback[removeArrayItem]\"]);\n        }\n    }[\"EnhancedResumeBuilder.useCallback[removeArrayItem]\"], []);\n    const nextStep = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[nextStep]\": ()=>{\n            const errors = validateStep(currentStep);\n            if (Object.keys(errors).length > 0) {\n                setValidationErrors(errors);\n                setShowValidationErrors(true);\n                const errorMessages = Object.values(errors);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(errorMessages[0]);\n                return;\n            }\n            setCompletedSteps({\n                \"EnhancedResumeBuilder.useCallback[nextStep]\": (prev)=>[\n                        ...new Set([\n                            ...prev,\n                            currentStep\n                        ])\n                    ]\n            }[\"EnhancedResumeBuilder.useCallback[nextStep]\"]);\n            setValidationErrors({});\n            setShowValidationErrors(false);\n            if (currentStep < steps.length - 1) {\n                setCurrentStep(currentStep + 1);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"\".concat(steps[currentStep].title, \" completed!\"));\n            }\n        }\n    }[\"EnhancedResumeBuilder.useCallback[nextStep]\"], [\n        currentStep,\n        validateStep,\n        steps\n    ]);\n    const prevStep = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[prevStep]\": ()=>{\n            setValidationErrors({});\n            setShowValidationErrors(false);\n            if (currentStep > 0) setCurrentStep(currentStep - 1);\n        }\n    }[\"EnhancedResumeBuilder.useCallback[prevStep]\"], [\n        currentStep\n    ]);\n    const handleStepClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[handleStepClick]\": (stepIndex)=>{\n            if (stepIndex <= currentStep || completedSteps.includes(stepIndex)) {\n                setCurrentStep(stepIndex);\n                setValidationErrors({});\n                setShowValidationErrors(false);\n            }\n        }\n    }[\"EnhancedResumeBuilder.useCallback[handleStepClick]\"], [\n        currentStep,\n        completedSteps\n    ]);\n    const generateResume = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[generateResume]\": async ()=>{\n            setValidationErrors({});\n            setShowValidationErrors(false);\n            if (!canGenerateResume()) {\n                const allErrors = {};\n                for(let i = 0; i < steps.length - 1; i++){\n                    const stepErrors = validateStep(i);\n                    Object.assign(allErrors, stepErrors);\n                }\n                setValidationErrors(allErrors);\n                setShowValidationErrors(true);\n                for(let i = 0; i < steps.length - 1; i++){\n                    const stepErrors = validateStep(i);\n                    if (Object.keys(stepErrors).length > 0) {\n                        setCurrentStep(i);\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Please complete all required fields in \".concat(steps[i].title));\n                        return;\n                    }\n                }\n                return;\n            }\n            try {\n                setIsGenerating(true);\n                setShowProgressBar(true);\n                setResumeGenerated(false);\n                const response = await fetch('/api/generate-resume', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        formData,\n                        templateId: selectedTemplate\n                    })\n                });\n                const contentType = response.headers.get('content-type');\n                if (!contentType || !contentType.includes('application/json')) throw new Error('Server returned non-JSON response');\n                const data = await response.json();\n                if (!response.ok) throw new Error(data.error || 'Failed to generate resume');\n                if (!data.resumeData || !data.downloadUrl) throw new Error('Generated resume data is incomplete');\n                setResumeUrl(data.downloadUrl);\n                setResumeData(data.resumeData);\n                setAtsScore(data.atsScore || 75);\n                setSuggestions(data.suggestions || []);\n                setResumeGenerated(true);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success('Resume generated successfully!');\n            } catch (error) {\n                console.error('Resume generation error:', error);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(error.message || 'Failed to generate resume');\n            } finally{\n                setIsGenerating(false);\n                setShowProgressBar(false);\n            }\n        }\n    }[\"EnhancedResumeBuilder.useCallback[generateResume]\"], [\n        canGenerateResume,\n        formData,\n        steps,\n        validateStep,\n        selectedTemplate\n    ]);\n    const handleSave = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[handleSave]\": ()=>{\n            localStorage.setItem('resumeFormData', JSON.stringify(formData));\n            localStorage.setItem('resumeLastSaved', new Date().toISOString());\n            setLastSaved(new Date());\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success('Progress saved!');\n        }\n    }[\"EnhancedResumeBuilder.useCallback[handleSave]\"], [\n        formData\n    ]);\n    const handleAISuggest = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[handleAISuggest]\": async (section)=>{\n            if (!aiSuggestionsEnabled) return;\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.loading('Getting AI suggestions...');\n            try {\n                await new Promise({\n                    \"EnhancedResumeBuilder.useCallback[handleAISuggest]\": (resolve)=>setTimeout(resolve, 1500)\n                }[\"EnhancedResumeBuilder.useCallback[handleAISuggest]\"]);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.dismiss();\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success('AI suggestions applied!');\n            } catch (error) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.dismiss();\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error('Failed to get AI suggestions');\n            }\n        }\n    }[\"EnhancedResumeBuilder.useCallback[handleAISuggest]\"], [\n        aiSuggestionsEnabled\n    ]);\n    // Set up event listener for resume generation from ReviewForm\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EnhancedResumeBuilder.useEffect\": ()=>{\n            const handleGenerateResume = {\n                \"EnhancedResumeBuilder.useEffect.handleGenerateResume\": ()=>{\n                    // Check if we can generate resume and call the function\n                    if (canGenerateResume()) {\n                        generateResume();\n                    } else {\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error('Please complete all required sections before generating your resume.');\n                    }\n                }\n            }[\"EnhancedResumeBuilder.useEffect.handleGenerateResume\"];\n            if (true) {\n                window.addEventListener('generateResume', handleGenerateResume);\n                return ({\n                    \"EnhancedResumeBuilder.useEffect\": ()=>window.removeEventListener('generateResume', handleGenerateResume)\n                })[\"EnhancedResumeBuilder.useEffect\"];\n            }\n        }\n    }[\"EnhancedResumeBuilder.useEffect\"], [\n        canGenerateResume,\n        generateResume\n    ]);\n    const getStepValidation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[getStepValidation]\": ()=>{\n            const validation = {};\n            steps.forEach({\n                \"EnhancedResumeBuilder.useCallback[getStepValidation]\": (step, index)=>{\n                    const errors = validateStep(index);\n                    validation[index] = Object.keys(errors).length === 0;\n                }\n            }[\"EnhancedResumeBuilder.useCallback[getStepValidation]\"]);\n            return validation;\n        }\n    }[\"EnhancedResumeBuilder.useCallback[getStepValidation]\"], [\n        steps,\n        validateStep\n    ]);\n    const renderStepContent = ()=>{\n        const commonProps = {\n            formData,\n            updateFormData,\n            addArrayItem,\n            removeArrayItem,\n            atsAnalysis,\n            validationErrors,\n            showValidationErrors,\n            onSave: handleSave,\n            onAISuggest: handleAISuggest\n        };\n        switch(currentStep){\n            case 0:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_ResumeFormComponents__WEBPACK_IMPORTED_MODULE_5__.PersonalInfoForm, {\n                    ...commonProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                    lineNumber: 383,\n                    columnNumber: 16\n                }, undefined);\n            case 1:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_ResumeFormComponents__WEBPACK_IMPORTED_MODULE_5__.EducationForm, {\n                    ...commonProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                    lineNumber: 385,\n                    columnNumber: 16\n                }, undefined);\n            case 2:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_ExperienceForm__WEBPACK_IMPORTED_MODULE_6__.EnhancedExperienceForm, {\n                    ...commonProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                    lineNumber: 387,\n                    columnNumber: 16\n                }, undefined);\n            case 3:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_ProjectsForm__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    ...commonProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                    lineNumber: 389,\n                    columnNumber: 16\n                }, undefined);\n            case 4:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_SkillsForm__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    ...commonProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                    lineNumber: 391,\n                    columnNumber: 16\n                }, undefined);\n            case 5:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_ReviewForm__WEBPACK_IMPORTED_MODULE_10__.EnhancedReviewForm, {\n                    ...commonProps,\n                    selectedTemplate: selectedTemplate,\n                    onTemplateSelect: setSelectedTemplate\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                    lineNumber: 393,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return null;\n        }\n    };\n    if (resumeGenerated && resumeData && resumeUrl) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SuccessScreen__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n            formData: formData,\n            resumeData: resumeData,\n            onStartOver: ()=>{\n                setResumeGenerated(false);\n                setResumeData(null);\n                setResumeUrl(\"\");\n                setCurrentStep(0);\n            },\n            onEditResume: ()=>{\n                setResumeGenerated(false);\n                setCurrentStep(4); // Go back to review step\n            }\n        }, void 0, false, {\n            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n            lineNumber: 401,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-b from-black to-[#0A0A0A] text-white relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 opacity-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 bg-[url('/grid.svg')] [mask-image:radial-gradient(ellipse_at_center,white,transparent)]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                    lineNumber: 421,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                lineNumber: 420,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_common_ProgressBar__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                isVisible: showProgressBar,\n                onComplete: ()=>setShowProgressBar(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                lineNumber: 423,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 pb-32\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 py-8\",\n                    children: [\n                        !hideHeader && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_23__.motion.div, {\n                            className: \"text-center mb-8\",\n                            initial: {\n                                opacity: 0,\n                                y: -20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center gap-3 mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    className: \"h-10 w-10 text-neural-pink animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                    lineNumber: 430,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-neural-pink opacity-20 rounded-full blur-md\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                    lineNumber: 431,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                            lineNumber: 429,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-4xl lg:text-5xl font-bold text-white\",\n                                            children: \"Enhanced Resume Builder\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                            lineNumber: 433,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                    lineNumber: 428,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 text-lg max-w-2xl mx-auto\",\n                                    children: \"Create professional, ATS-friendly resumes with our enhanced AI-powered builder\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                    lineNumber: 435,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                            lineNumber: 427,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col xl:flex-row gap-8 max-w-7xl mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 max-w-4xl\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"sticky top-4 z-30 bg-gray-900/95 backdrop-blur-sm rounded-2xl border border-gray-700/50 mb-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EnhancedProgressIndicator__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                steps: steps,\n                                                currentStep: currentStep,\n                                                completedSteps: completedSteps,\n                                                onStepClick: handleStepClick,\n                                                variant: \"minimal\",\n                                                showLabels: true,\n                                                showProgress: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                lineNumber: 443,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                            lineNumber: 442,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_25__.AnimatePresence, {\n                                                mode: \"wait\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_23__.motion.div, {\n                                                    initial: {\n                                                        opacity: 0,\n                                                        x: 20\n                                                    },\n                                                    animate: {\n                                                        opacity: 1,\n                                                        x: 0\n                                                    },\n                                                    exit: {\n                                                        opacity: 0,\n                                                        x: -20\n                                                    },\n                                                    transition: {\n                                                        duration: 0.3\n                                                    },\n                                                    children: renderStepContent()\n                                                }, currentStep, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                    lineNumber: 457,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                lineNumber: 456,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                            lineNumber: 455,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                    lineNumber: 440,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden xl:block xl:w-80 xl:flex-shrink-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"sticky top-4 h-[calc(100vh-2rem)]\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SimpleResumePreview__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            formData: formData,\n                                            selectedTemplate: selectedTemplate,\n                                            showPreview: showPreview,\n                                            onTogglePreview: ()=>setShowPreview(!showPreview)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                            lineNumber: 468,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                        lineNumber: 467,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                    lineNumber: 465,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"xl:hidden mt-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_23__.motion.div, {\n                                        className: \"bg-gray-900/60 backdrop-blur-md rounded-2xl p-6 border border-white/10\",\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            delay: 0.2\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                className: \"h-5 w-5 text-neural-blue\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                                lineNumber: 487,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            \"Resume Preview\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                        lineNumber: 486,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setShowPreview(!showPreview),\n                                                        className: \"px-3 py-1.5 rounded-lg text-sm font-medium transition-colors \".concat(showPreview ? 'bg-neural-purple text-white' : 'bg-gray-800 text-gray-300 hover:bg-gray-700'),\n                                                        children: showPreview ? 'Hide' : 'Show'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                        lineNumber: 490,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                lineNumber: 485,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            showPreview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"max-h-[600px] overflow-y-auto border border-white/10 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ResumePreview__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    formData: formData,\n                                                    selectedTemplate: selectedTemplate\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                    lineNumber: 501,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                lineNumber: 500,\n                                                columnNumber: 19\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-12 text-gray-400\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                        className: \"h-12 w-12 mx-auto mb-3 opacity-30\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                        lineNumber: 505,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm\",\n                                                        children: 'Click \"Show\" to preview your resume'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                        lineNumber: 506,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                lineNumber: 504,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                        lineNumber: 479,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                    lineNumber: 478,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                            lineNumber: 438,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                    lineNumber: 425,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                lineNumber: 424,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_layout_SmartNavigationBar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                currentStep: currentStep,\n                totalSteps: steps.length,\n                onPrevious: prevStep,\n                onNext: nextStep,\n                onGenerate: generateResume,\n                onSave: handleSave,\n                onPreview: ()=>setShowPreview(!showPreview),\n                isGenerating: isGenerating,\n                canProceed: currentStep === steps.length - 1 ? canGenerateResume() : canProceedToNextStep(currentStep),\n                showPreview: showPreview,\n                steps: steps,\n                formData: formData,\n                atsScore: atsAnalysis.overallScore,\n                autoSaveEnabled: autoSaveEnabled\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                lineNumber: 514,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n        lineNumber: 419,\n        columnNumber: 5\n    }, undefined);\n};\n_s(EnhancedResumeBuilder, \"fDIam3iIEkONwVfjFA/qcKl+co8=\", false, function() {\n    return [\n        _hooks_useATSAnalysis__WEBPACK_IMPORTED_MODULE_17__[\"default\"]\n    ];\n});\n_c = EnhancedResumeBuilder;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (EnhancedResumeBuilder);\nvar _c;\n$RefreshReg$(_c, \"EnhancedResumeBuilder\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/resume/ResumeBuilder.jsx\n"));

/***/ })

});
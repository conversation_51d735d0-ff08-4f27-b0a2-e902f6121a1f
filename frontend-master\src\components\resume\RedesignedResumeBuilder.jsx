'use client';
import { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { toast } from 'react-hot-toast';

// Import new components
import SimplifiedNavigation from './SimplifiedNavigation';
import StreamlinedPreview from './StreamlinedPreview';
import FullscreenPreviewModal from './FullscreenPreviewModal';
import UniversalTemplateSelector from './UniversalTemplateSelector';
import {
  UniversalPersonalForm,
  UniversalExperienceForm,
  UniversalEducationForm,
  UniversalSkillsForm
} from './forms/UniversalFormFields';

// Import template system
import { INDUSTRY_TEMPLATES } from './templates/MultiIndustryTemplates';

const RedesignedResumeBuilder = ({ hideHeader = false }) => {
  // Core state
  const [currentStep, setCurrentStep] = useState(0);
  const [selectedTemplate, setSelectedTemplate] = useState('business_executive');
  const [showTemplateSelector, setShowTemplateSelector] = useState(false);
  const [showFullscreenPreview, setShowFullscreenPreview] = useState(false);
  
  // Form data with universal structure
  const [formData, setFormData] = useState({
    personal: {
      firstName: "",
      lastName: "",
      email: "",
      phone: "",
      location: "",
      summary: ""
    },
    experience: [
      { 
        id: 1, 
        title: "", 
        company: "", 
        location: "", 
        startDate: "", 
        endDate: "", 
        current: false, 
        description: "" 
      }
    ],
    education: [
      { 
        id: 1, 
        degree: "", 
        field: "", 
        institution: "", 
        location: "", 
        graduationDate: "", 
        gpa: "" 
      }
    ],
    skills: { 
      technical: [], 
      languages: [], 
      certifications: [] 
    }
  });

  // UI state
  const [validationErrors, setValidationErrors] = useState({});
  const [isSaving, setIsSaving] = useState(false);
  const [completedSteps, setCompletedSteps] = useState([]);

  // Step configuration - simplified and universal
  const steps = [
    { 
      id: 0, 
      title: "Personal Info", 
      description: "Your contact information and professional summary"
    },
    { 
      id: 1, 
      title: "Experience", 
      description: "Your work history and achievements"
    },
    { 
      id: 2, 
      title: "Education", 
      description: "Your educational background and qualifications"
    },
    { 
      id: 3, 
      title: "Skills", 
      description: "Your core competencies and abilities"
    },
    { 
      id: 4, 
      title: "Review", 
      description: "Review and download your resume"
    }
  ];

  // Auto-save functionality
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      localStorage.setItem('universalResumeData', JSON.stringify(formData));
      localStorage.setItem('selectedTemplate', selectedTemplate);
    }, 2000);
    return () => clearTimeout(timeoutId);
  }, [formData, selectedTemplate]);

  // Load saved data
  useEffect(() => {
    const savedData = localStorage.getItem('universalResumeData');
    const savedTemplate = localStorage.getItem('selectedTemplate');
    
    if (savedData) {
      try {
        setFormData(JSON.parse(savedData));
        toast.success('Previous work restored!');
      } catch (error) {
        console.error('Failed to load saved data:', error);
      }
    }
    
    if (savedTemplate && INDUSTRY_TEMPLATES[savedTemplate]) {
      setSelectedTemplate(savedTemplate);
    }
  }, []);

  // Validation logic
  const validateStep = useCallback((stepIndex) => {
    const errors = {};
    
    switch (stepIndex) {
      case 0: // Personal Info
        if (!formData.personal.firstName?.trim()) errors.firstName = 'First name is required';
        if (!formData.personal.lastName?.trim()) errors.lastName = 'Last name is required';
        if (!formData.personal.email?.trim()) errors.email = 'Email is required';
        else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.personal.email)) {
          errors.email = 'Please enter a valid email address';
        }
        break;
        
      case 1: // Experience
        const validExperience = formData.experience?.filter(exp => 
          exp.title?.trim() && exp.company?.trim()
        );
        if (!validExperience || validExperience.length === 0) {
          errors.experience = 'At least one work experience entry is required';
        }
        break;
        
      case 2: // Education
        const validEducation = formData.education?.filter(edu => 
          edu.degree?.trim() && edu.institution?.trim()
        );
        if (!validEducation || validEducation.length === 0) {
          errors.education = 'At least one education entry is required';
        }
        break;
        
      case 3: // Skills - optional
        break;
        
      default:
        break;
    }
    
    return errors;
  }, [formData]);

  // Navigation logic
  const canProceedToNextStep = useCallback((stepIndex) => {
    const errors = validateStep(stepIndex);
    return Object.keys(errors).length === 0;
  }, [validateStep]);

  const nextStep = useCallback(() => {
    const errors = validateStep(currentStep);
    if (Object.keys(errors).length > 0) {
      setValidationErrors(errors);
      const errorMessages = Object.values(errors);
      toast.error(errorMessages[0]);
      return;
    }
    
    setCompletedSteps(prev => [...new Set([...prev, currentStep])]);
    setValidationErrors({});
    
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
      toast.success(`${steps[currentStep].title} completed!`);
    }
  }, [currentStep, validateStep, steps]);

  const prevStep = useCallback(() => {
    setValidationErrors({});
    if (currentStep > 0) setCurrentStep(currentStep - 1);
  }, [currentStep]);

  // Form data management
  const updateFormData = useCallback((section, field, value, index = null) => {
    setFormData(prev => {
      let newData = { ...prev };
      
      if (index !== null && Array.isArray(prev[section])) {
        const newArray = [...prev[section]];
        newArray[index] = { ...newArray[index], [field]: value };
        newData[section] = newArray;
      } else if (typeof prev[section] === 'object' && !Array.isArray(prev[section])) {
        newData[section] = { ...prev[section], [field]: value };
      } else {
        newData[field] = value;
      }
      
      return newData;
    });
    
    // Clear validation errors for this field
    if (validationErrors[field]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  }, [validationErrors]);

  const addArrayItem = useCallback((section, template) => {
    setFormData(prev => ({
      ...prev,
      [section]: [...prev[section], { 
        ...template, 
        id: Math.random().toString(36).substring(2, 11) 
      }]
    }));
  }, []);

  const removeArrayItem = useCallback((section, id) => {
    setFormData(prev => ({
      ...prev,
      [section]: prev[section].filter(item => item.id !== id)
    }));
  }, []);

  // Save functionality
  const handleSave = useCallback(() => {
    setIsSaving(true);
    localStorage.setItem('universalResumeData', JSON.stringify(formData));
    localStorage.setItem('selectedTemplate', selectedTemplate);
    
    setTimeout(() => {
      setIsSaving(false);
      toast.success('Progress saved!');
    }, 1000);
  }, [formData, selectedTemplate]);

  // Download functionality
  const handleDownload = useCallback(() => {
    toast.success('Download feature coming soon!');
  }, []);

  // Calculate completion percentage
  const getCompletionPercentage = useCallback(() => {
    let totalFields = 0;
    let completedFields = 0;

    // Personal info (4 required fields)
    totalFields += 4;
    if (formData.personal.firstName) completedFields++;
    if (formData.personal.lastName) completedFields++;
    if (formData.personal.email) completedFields++;
    if (formData.personal.summary) completedFields++;

    // Experience (at least 1 entry with title and company)
    totalFields += 2;
    const validExp = formData.experience?.filter(exp => exp.title && exp.company);
    if (validExp?.length > 0) completedFields += 2;

    // Education (at least 1 entry with degree and institution)
    totalFields += 2;
    const validEdu = formData.education?.filter(edu => edu.degree && edu.institution);
    if (validEdu?.length > 0) completedFields += 2;

    // Skills (optional but counts if present)
    if (formData.skills?.technical?.length > 0) {
      totalFields += 1;
      completedFields += 1;
    }

    return Math.round((completedFields / totalFields) * 100);
  }, [formData]);

  // Render step content
  const renderStepContent = () => {
    const commonProps = {
      formData,
      updateFormData,
      addArrayItem,
      removeArrayItem,
      validationErrors
    };

    switch (currentStep) {
      case 0:
        return <UniversalPersonalForm {...commonProps} />;
      case 1:
        return <UniversalExperienceForm {...commonProps} />;
      case 2:
        return <UniversalEducationForm {...commonProps} />;
      case 3:
        return <UniversalSkillsForm {...commonProps} />;
      case 4:
        return (
          <div className="space-y-6">
            <motion.div
              className="bg-gray-900/60 backdrop-blur-md rounded-2xl border border-gray-700/50 p-8 text-center"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
            >
              <div className="flex items-center justify-center gap-3 mb-6">
                <div className="relative">
                  <motion.div
                    className="h-8 w-8 text-neural-pink"
                    animate={{ scale: [1, 1.2, 1] }}
                    transition={{ duration: 2, repeat: Infinity }}
                  >
                    🎉
                  </motion.div>
                  <div className="absolute inset-0 bg-neural-pink opacity-20 rounded-full blur-md"></div>
                </div>
                <h2 className="text-2xl font-semibold text-white">
                  Your Resume is Ready!
                </h2>
              </div>

              <p className="text-gray-300 mb-8 max-w-md mx-auto">
                Review your professional resume in the preview panel. Upgrade to download and access premium features.
              </p>

              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button
                  onClick={() => setShowTemplateSelector(true)}
                  className="px-6 py-3 bg-gray-800 hover:bg-gray-700 text-white rounded-lg transition-colors border border-gray-600"
                >
                  Change Template
                </button>
                <button
                  onClick={() => setShowFullscreenPreview(true)}
                  className="px-6 py-3 bg-gradient-to-r from-neural-purple to-neural-pink text-white rounded-lg hover:opacity-90 transition-opacity"
                >
                  Preview Fullscreen
                </button>
                <button
                  onClick={() => toast.info('Upgrade to Pro to download your resume!')}
                  className="px-6 py-3 bg-gradient-to-r from-yellow-500 to-orange-500 text-white rounded-lg hover:opacity-90 transition-opacity"
                >
                  Upgrade to Download
                </button>
              </div>

              <div className="mt-6 p-4 bg-yellow-500/10 border border-yellow-500/20 rounded-lg">
                <p className="text-yellow-400 text-sm">
                  💡 Upgrade to Pro to download PDF, access premium templates, and unlock AI enhancements
                </p>
              </div>
            </motion.div>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-black to-[#0A0A0A] text-white relative">
      {/* Background Elements */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute inset-0 bg-[url('/grid.svg')] [mask-image:radial-gradient(ellipse_at_center,white,transparent)]" />
      </div>

      {/* Header */}
      {!hideHeader && (
        <div className="relative z-10">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <motion.div
              className="text-center"
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
            >
              <div className="flex items-center justify-center gap-3 mb-6">
                <div className="relative">
                  <motion.div
                    className="h-10 w-10 text-neural-pink"
                    animate={{ rotate: 360 }}
                    transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                  >
                    ✨
                  </motion.div>
                  <div className="absolute inset-0 bg-neural-pink opacity-20 rounded-full blur-md"></div>
                </div>
                <h1 className="text-3xl lg:text-4xl font-bold text-white">
                  Universal Resume Builder
                </h1>
              </div>
              <p className="text-gray-300 text-lg max-w-2xl mx-auto">
                Create professional resumes for any industry with our intelligent builder
              </p>
            </motion.div>
          </div>
        </div>
      )}

      {/* Main Content */}
      <div className="relative z-10 pb-32">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex flex-col xl:flex-row gap-8">
            {/* Form Content - 2/3 width */}
            <div className="flex-1 max-w-4xl">
              <AnimatePresence mode="wait">
                <motion.div
                  key={currentStep}
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  transition={{ duration: 0.3 }}
                >
                  {renderStepContent()}
                </motion.div>
              </AnimatePresence>
            </div>

            {/* Preview Sidebar - 1/3 width */}
            <div className="hidden xl:block xl:w-80 xl:flex-shrink-0">
              <div className="sticky top-8">
                <StreamlinedPreview
                  formData={formData}
                  selectedTemplate={selectedTemplate}
                  onOpenFullscreen={() => setShowFullscreenPreview(true)}
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <SimplifiedNavigation
        currentStep={currentStep}
        totalSteps={steps.length}
        onPrevious={prevStep}
        onNext={nextStep}
        onSave={handleSave}
        onDownload={handleDownload}
        onHome={() => window.location.href = '/'}
        canProceed={canProceedToNextStep(currentStep)}
        isSaving={isSaving}
        completionPercentage={getCompletionPercentage()}
        stepTitles={steps.map(step => step.title)}
      />

      {/* Modals */}
      <AnimatePresence>
        {showTemplateSelector && (
          <UniversalTemplateSelector
            selectedTemplate={selectedTemplate}
            onTemplateSelect={setSelectedTemplate}
            onClose={() => setShowTemplateSelector(false)}
            formData={formData}
          />
        )}
      </AnimatePresence>

      <AnimatePresence>
        {showFullscreenPreview && (
          <FullscreenPreviewModal
            isOpen={showFullscreenPreview}
            onClose={() => setShowFullscreenPreview(false)}
            formData={formData}
            selectedTemplate={selectedTemplate}
            onTemplateChange={setSelectedTemplate}
          />
        )}
      </AnimatePresence>
    </div>
  );
};

export default RedesignedResumeBuilder;

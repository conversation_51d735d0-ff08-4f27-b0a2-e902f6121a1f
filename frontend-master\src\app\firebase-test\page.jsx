'use client';

import { useState } from 'react';
import { testFirebaseConnection, addSampleData } from '@/utils/firebaseTest';

export default function FirebaseTestPage() {
  const [results, setResults] = useState([]);
  const [loading, setLoading] = useState(false);

  const handleTestConnection = async () => {
    setLoading(true);
    try {
      const result = await testFirebaseConnection();
      setResults(prev => [...prev, {
        timestamp: new Date().toLocaleTimeString(),
        type: 'connection',
        ...result
      }]);
    } catch (error) {
      setResults(prev => [...prev, {
        timestamp: new Date().toLocaleTimeString(),
        type: 'error',
        success: false,
        error: error.message
      }]);
    }
    setLoading(false);
  };

  const handleAddSampleData = async () => {
    setLoading(true);
    try {
      const result = await addSampleData();
      setResults(prev => [...prev, {
        timestamp: new Date().toLocaleTimeString(),
        type: 'sample_data',
        ...result
      }]);
    } catch (error) {
      setResults(prev => [...prev, {
        timestamp: new Date().toLocaleTimeString(),
        type: 'error',
        success: false,
        error: error.message
      }]);
    }
    setLoading(false);
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-black to-gray-900 text-white p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8 text-center">
          🔥 Firebase Real-Time Data Test
        </h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          <button
            onClick={handleTestConnection}
            disabled={loading}
            className="bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 disabled:opacity-50 px-6 py-3 rounded-lg font-medium transition-all"
          >
            {loading ? 'Testing...' : 'Test Firebase Connection'}
          </button>
          
          <button
            onClick={handleAddSampleData}
            disabled={loading}
            className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 disabled:opacity-50 px-6 py-3 rounded-lg font-medium transition-all"
          >
            {loading ? 'Adding...' : 'Add Sample Data'}
          </button>
        </div>

        <div className="bg-gray-800 rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4">Test Results:</h2>
          {results.length === 0 ? (
            <p className="text-gray-400">No tests run yet. Click a button above to start testing.</p>
          ) : (
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {results.map((result, index) => (
                <div 
                  key={index}
                  className={`p-3 rounded border-l-4 ${
                    result.success 
                      ? 'border-green-400 bg-green-900/20' 
                      : 'border-red-400 bg-red-900/20'
                  }`}
                >
                  <div className="flex justify-between items-start mb-2">
                    <span className="text-sm text-gray-300">{result.timestamp}</span>
                    <span className={`text-xs px-2 py-1 rounded ${
                      result.success ? 'bg-green-600' : 'bg-red-600'
                    }`}>
                      {result.success ? 'SUCCESS' : 'ERROR'}
                    </span>
                  </div>
                  <p className="text-sm">
                    {result.message || result.error}
                  </p>
                </div>
              ))}
            </div>
          )}
        </div>

        <div className="mt-8 bg-gray-800 rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4">Instructions:</h2>
          <ol className="list-decimal list-inside space-y-2 text-gray-300">
            <li>First, test the Firebase connection to see if it's working</li>
            <li>If successful, add sample data to populate the collections</li>
            <li>Go back to the home page to see the real-time stats updating</li>
            <li>Check the browser console for detailed Firebase logs</li>
          </ol>
        </div>

        <div className="mt-6 text-center">
          <a 
            href="/"
            className="inline-block bg-gradient-to-r from-green-500 to-teal-500 hover:from-green-600 hover:to-teal-600 px-6 py-3 rounded-lg font-medium transition-all"
          >
            ← Back to Home (View Stats)
          </a>
        </div>
      </div>
    </div>
  );
}

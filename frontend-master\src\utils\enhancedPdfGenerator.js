import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';

// Enhanced PDF Generator with improved formatting and ATS optimization
export class EnhancedPDFGenerator {
  constructor() {
    this.doc = null;
    this.pageWidth = 210; // A4 width in mm
    this.pageHeight = 297; // A4 height in mm
    this.margins = {
      top: 20,
      right: 20,
      bottom: 20,
      left: 20
    };
    this.currentY = this.margins.top;
    this.lineHeight = 5;
    this.fontSize = {
      title: 16,
      heading: 12,
      subheading: 10,
      body: 9,
      small: 8
    };
  }

  // Initialize PDF document
  initDocument() {
    this.doc = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: 'a4'
    });
    
    // Set default font
    this.doc.setFont('helvetica', 'normal');
    this.currentY = this.margins.top;
  }

  // Add text with proper formatting and bullet point handling
  addText(text, options = {}) {
    const {
      fontSize = this.fontSize.body,
      fontStyle = 'normal',
      color = [0, 0, 0],
      align = 'left',
      maxWidth = this.pageWidth - this.margins.left - this.margins.right,
      indent = 0,
      isBullet = false
    } = options;

    this.doc.setFontSize(fontSize);
    this.doc.setFont('helvetica', fontStyle);
    this.doc.setTextColor(...color);

    // Handle bullet points properly
    let processedText = text;
    if (isBullet && !text.trim().startsWith('•')) {
      processedText = `• ${text.trim()}`;
    }

    // Remove duplicate bullet points
    processedText = processedText.replace(/^•\s*•\s*/, '• ');

    // Split text into lines that fit the width
    const lines = this.doc.splitTextToSize(processedText, maxWidth - indent);
    
    lines.forEach((line, index) => {
      // Check if we need a new page
      if (this.currentY + this.lineHeight > this.pageHeight - this.margins.bottom) {
        this.doc.addPage();
        this.currentY = this.margins.top;
      }

      // Add the text
      this.doc.text(line, this.margins.left + indent, this.currentY, { align });
      this.currentY += this.lineHeight;
    });

    return this;
  }

  // Add section heading with consistent formatting
  addSectionHeading(title) {
    this.currentY += 3; // Add space before heading
    
    this.addText(title.toUpperCase(), {
      fontSize: this.fontSize.heading,
      fontStyle: 'bold',
      color: [0, 0, 0]
    });

    // Add underline
    const textWidth = this.doc.getTextWidth(title.toUpperCase());
    this.doc.setLineWidth(0.5);
    this.doc.line(
      this.margins.left,
      this.currentY - 2,
      this.margins.left + textWidth,
      this.currentY - 2
    );

    this.currentY += 2; // Add space after heading
    return this;
  }

  // Add contact information in header
  addContactHeader(personalInfo) {
    // Name
    this.addText(`${personalInfo.firstName} ${personalInfo.lastName}`, {
      fontSize: this.fontSize.title,
      fontStyle: 'bold',
      align: 'center'
    });

    this.currentY += 2;

    // Contact details
    const contactDetails = [];
    if (personalInfo.email) contactDetails.push(personalInfo.email);
    if (personalInfo.phone) contactDetails.push(personalInfo.phone);
    if (personalInfo.location) contactDetails.push(personalInfo.location);
    if (personalInfo.linkedin) contactDetails.push(personalInfo.linkedin);

    if (contactDetails.length > 0) {
      this.addText(contactDetails.join(' • '), {
        fontSize: this.fontSize.small,
        align: 'center',
        color: [60, 60, 60]
      });
    }

    this.currentY += 5; // Add space after header
    return this;
  }

  // Add experience section with proper formatting
  addExperience(experiences) {
    if (!experiences || experiences.length === 0) return this;

    this.addSectionHeading('Professional Experience');

    experiences.forEach((exp, index) => {
      // Job title and dates
      const titleLine = exp.title;
      const dateLine = `${exp.startDate} - ${exp.current ? 'Present' : exp.endDate}`;
      
      // Add job title
      this.addText(titleLine, {
        fontSize: this.fontSize.subheading,
        fontStyle: 'bold'
      });

      // Add company and location on same line as dates
      const companyInfo = exp.company + (exp.location ? ` • ${exp.location}` : '');
      this.currentY -= this.lineHeight; // Move back up
      
      this.addText(dateLine, {
        fontSize: this.fontSize.small,
        align: 'right',
        color: [60, 60, 60]
      });

      this.addText(companyInfo, {
        fontSize: this.fontSize.body,
        fontStyle: 'bold',
        color: [60, 60, 60]
      });

      // Add description with proper bullet formatting
      if (exp.description) {
        const descriptions = exp.description.split('\n').filter(line => line.trim());
        descriptions.forEach(desc => {
          this.addText(desc.trim(), {
            fontSize: this.fontSize.body,
            indent: 5,
            isBullet: true
          });
        });
      }

      // Add space between experiences
      if (index < experiences.length - 1) {
        this.currentY += 3;
      }
    });

    this.currentY += 5;
    return this;
  }

  // Add education section
  addEducation(education) {
    if (!education || education.length === 0) return this;

    this.addSectionHeading('Education');

    education.forEach((edu, index) => {
      // Degree and graduation date
      const degreeLine = edu.degree + (edu.field ? ` in ${edu.field}` : '');
      const dateLine = edu.graduationDate;

      this.addText(degreeLine, {
        fontSize: this.fontSize.subheading,
        fontStyle: 'bold'
      });

      this.currentY -= this.lineHeight; // Move back up
      
      this.addText(dateLine, {
        fontSize: this.fontSize.small,
        align: 'right',
        color: [60, 60, 60]
      });

      // Institution and location
      const institutionInfo = edu.institution + (edu.location ? ` • ${edu.location}` : '');
      this.addText(institutionInfo, {
        fontSize: this.fontSize.body,
        color: [60, 60, 60]
      });

      // GPA if provided
      if (edu.gpa) {
        this.addText(`GPA: ${edu.gpa}`, {
          fontSize: this.fontSize.small,
          color: [60, 60, 60]
        });
      }

      // Add space between education entries
      if (index < education.length - 1) {
        this.currentY += 2;
      }
    });

    this.currentY += 5;
    return this;
  }

  // Add skills section with proper formatting
  addSkills(skills) {
    if (!skills) return this;

    this.addSectionHeading('Skills');

    if (skills.technical && skills.technical.length > 0) {
      this.addText('Technical: ' + skills.technical.join(', '), {
        fontSize: this.fontSize.body
      });
    }

    if (skills.languages && skills.languages.length > 0) {
      this.addText('Languages: ' + skills.languages.join(', '), {
        fontSize: this.fontSize.body
      });
    }

    if (skills.certifications && skills.certifications.length > 0) {
      this.addText('Certifications: ' + skills.certifications.join(', '), {
        fontSize: this.fontSize.body
      });
    }

    this.currentY += 5;
    return this;
  }

  // Add projects section
  addProjects(projects) {
    if (!projects || projects.length === 0) return this;

    this.addSectionHeading('Projects');

    projects.forEach((project, index) => {
      // Project name and technologies
      let projectTitle = project.name;
      if (project.technologies) {
        projectTitle += ` (${project.technologies})`;
      }

      this.addText(projectTitle, {
        fontSize: this.fontSize.subheading,
        fontStyle: 'bold'
      });

      // Description
      if (project.description) {
        this.addText(project.description, {
          fontSize: this.fontSize.body,
          indent: 0
        });
      }

      // Links
      const links = [];
      if (project.link) links.push(`Demo: ${project.link}`);
      if (project.github) links.push(`Code: ${project.github}`);
      
      if (links.length > 0) {
        this.addText(links.join(' • '), {
          fontSize: this.fontSize.small,
          color: [60, 60, 60]
        });
      }

      // Add space between projects
      if (index < projects.length - 1) {
        this.currentY += 3;
      }
    });

    this.currentY += 5;
    return this;
  }

  // Generate complete resume PDF
  generateResume(formData, templateId = 'classic_ats') {
    this.initDocument();

    // Add header with contact information
    this.addContactHeader(formData.personal);

    // Add professional summary
    if (formData.personal.summary) {
      this.addSectionHeading('Professional Summary');
      this.addText(formData.personal.summary, {
        fontSize: this.fontSize.body
      });
      this.currentY += 5;
    }

    // Add sections in order
    this.addExperience(formData.experience);
    this.addEducation(formData.education);
    this.addSkills(formData.skills);
    this.addProjects(formData.projects);

    return this.doc;
  }

  // Save PDF with proper filename
  savePDF(formData, filename) {
    const doc = this.generateResume(formData);
    const name = `${formData.personal.firstName}_${formData.personal.lastName}`;
    const finalFilename = filename || `${name}_Resume.pdf`;
    doc.save(finalFilename);
  }

  // Get PDF as blob for preview
  getPDFBlob(formData) {
    const doc = this.generateResume(formData);
    return doc.output('blob');
  }

  // Get PDF as data URL for preview
  getPDFDataURL(formData) {
    const doc = this.generateResume(formData);
    return doc.output('dataurlstring');
  }
}

// Enhanced HTML to PDF converter with better formatting
export class EnhancedHTMLToPDF {
  constructor() {
    this.options = {
      scale: 2,
      useCORS: true,
      allowTaint: true,
      backgroundColor: '#ffffff',
      width: 794, // A4 width in pixels at 96 DPI
      height: 1123, // A4 height in pixels at 96 DPI
      scrollX: 0,
      scrollY: 0
    };
  }

  async convertElementToPDF(element, filename = 'resume.pdf') {
    try {
      // Create canvas from HTML element
      const canvas = await html2canvas(element, this.options);
      
      // Create PDF
      const pdf = new jsPDF({
        orientation: 'portrait',
        unit: 'mm',
        format: 'a4'
      });

      // Calculate dimensions
      const imgWidth = 210; // A4 width in mm
      const imgHeight = (canvas.height * imgWidth) / canvas.width;
      
      // Add image to PDF
      const imgData = canvas.toDataURL('image/png');
      pdf.addImage(imgData, 'PNG', 0, 0, imgWidth, imgHeight);

      // Save PDF
      pdf.save(filename);
      
      return pdf;
    } catch (error) {
      console.error('Error converting HTML to PDF:', error);
      throw error;
    }
  }

  async getPDFBlob(element) {
    try {
      const canvas = await html2canvas(element, this.options);
      const pdf = new jsPDF({
        orientation: 'portrait',
        unit: 'mm',
        format: 'a4'
      });

      const imgWidth = 210;
      const imgHeight = (canvas.height * imgWidth) / canvas.width;
      const imgData = canvas.toDataURL('image/png');
      
      pdf.addImage(imgData, 'PNG', 0, 0, imgWidth, imgHeight);
      
      return pdf.output('blob');
    } catch (error) {
      console.error('Error generating PDF blob:', error);
      throw error;
    }
  }
}

// Utility functions
export const downloadPDF = (formData, templateId, filename) => {
  const generator = new EnhancedPDFGenerator();
  generator.savePDF(formData, filename);
};

export const generatePDFPreview = (formData, templateId) => {
  const generator = new EnhancedPDFGenerator();
  return generator.getPDFDataURL(formData);
};

export const convertHTMLToPDF = async (element, filename) => {
  const converter = new EnhancedHTMLToPDF();
  return await converter.convertElementToPDF(element, filename);
};

export default EnhancedPDFGenerator;

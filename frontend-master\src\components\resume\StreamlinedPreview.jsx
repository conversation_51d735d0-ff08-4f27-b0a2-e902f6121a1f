'use client';
import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Eye, EyeOff, Maximize2, FileText, User, Mail, Phone, MapPin } from 'lucide-react';

const StreamlinedPreview = ({ 
  formData, 
  selectedTemplate = 'professional',
  onOpenFullscreen
}) => {
  const [isVisible, setIsVisible] = useState(true);

  // Check if we have meaningful data to preview
  const hasContent = formData && (
    formData.personal?.firstName ||
    formData.personal?.email ||
    formData.experience?.some(exp => exp.title || exp.company) ||
    formData.education?.some(edu => edu.degree || edu.institution)
  );

  const renderEmptyState = () => (
    <div className="flex flex-col items-center justify-center h-full text-gray-400 p-6">
      <FileText className="h-12 w-12 mb-3 opacity-50" />
      <h3 className="text-sm font-medium mb-1">Resume Preview</h3>
      <p className="text-xs text-center opacity-75">
        Your resume will appear here as you fill out the form
      </p>
    </div>
  );

  const renderPreviewContent = () => {
    if (!hasContent) return renderEmptyState();

    return (
      <div className="p-4 space-y-4 text-sm">
        {/* Header Section */}
        <div className="text-center border-b border-gray-200 pb-3">
          <h1 className="text-lg font-bold text-gray-900 mb-2">
            {formData.personal?.firstName || 'Your Name'} {formData.personal?.lastName || ''}
          </h1>
          
          <div className="flex flex-wrap justify-center gap-3 text-xs text-gray-600">
            {formData.personal?.email && (
              <div className="flex items-center gap-1">
                <Mail className="h-3 w-3" />
                <span>{formData.personal.email}</span>
              </div>
            )}
            {formData.personal?.phone && (
              <div className="flex items-center gap-1">
                <Phone className="h-3 w-3" />
                <span>{formData.personal.phone}</span>
              </div>
            )}
            {formData.personal?.location && (
              <div className="flex items-center gap-1">
                <MapPin className="h-3 w-3" />
                <span>{formData.personal.location}</span>
              </div>
            )}
          </div>
        </div>

        {/* Professional Summary */}
        {formData.personal?.summary && (
          <div>
            <h2 className="text-sm font-semibold text-gray-900 mb-2">Professional Summary</h2>
            <p className="text-xs text-gray-700 leading-relaxed">
              {formData.personal.summary.length > 150 
                ? `${formData.personal.summary.substring(0, 150)}...` 
                : formData.personal.summary}
            </p>
          </div>
        )}

        {/* Experience Section */}
        {formData.experience?.some(exp => exp.title || exp.company) && (
          <div>
            <h2 className="text-sm font-semibold text-gray-900 mb-2">Experience</h2>
            <div className="space-y-3">
              {formData.experience
                .filter(exp => exp.title || exp.company)
                .slice(0, 2)
                .map((exp, index) => (
                  <div key={index} className="border-l-2 border-blue-100 pl-3">
                    <div className="flex justify-between items-start mb-1">
                      <h3 className="text-xs font-medium text-gray-900">
                        {exp.title || 'Job Title'}
                      </h3>
                      <span className="text-xs text-gray-500">
                        {exp.startDate} {exp.startDate && (exp.endDate || exp.current) && ' - '}
                        {exp.current ? 'Present' : exp.endDate}
                      </span>
                    </div>
                    <p className="text-xs text-gray-600 mb-1">
                      {exp.company || 'Company Name'}
                      {exp.location && ` • ${exp.location}`}
                    </p>
                    {exp.description && (
                      <p className="text-xs text-gray-600">
                        {exp.description.length > 100 
                          ? `${exp.description.substring(0, 100)}...` 
                          : exp.description}
                      </p>
                    )}
                  </div>
                ))}
              
              {formData.experience.filter(exp => exp.title || exp.company).length > 2 && (
                <p className="text-xs text-gray-500 italic pl-3">
                  +{formData.experience.filter(exp => exp.title || exp.company).length - 2} more positions
                </p>
              )}
            </div>
          </div>
        )}

        {/* Education Section */}
        {formData.education?.some(edu => edu.degree || edu.institution) && (
          <div>
            <h2 className="text-sm font-semibold text-gray-900 mb-2">Education</h2>
            <div className="space-y-2">
              {formData.education
                .filter(edu => edu.degree || edu.institution)
                .slice(0, 2)
                .map((edu, index) => (
                  <div key={index} className="border-l-2 border-green-100 pl-3">
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="text-xs font-medium text-gray-900">
                          {edu.degree || 'Degree'} {edu.field && `in ${edu.field}`}
                        </h3>
                        <p className="text-xs text-gray-600">
                          {edu.institution || 'Institution'}
                        </p>
                      </div>
                      <span className="text-xs text-gray-500">
                        {edu.graduationDate}
                      </span>
                    </div>
                  </div>
                ))}
            </div>
          </div>
        )}

        {/* Skills Section */}
        {(formData.skills?.technical?.length > 0 || formData.skills?.languages?.length > 0) && (
          <div>
            <h2 className="text-sm font-semibold text-gray-900 mb-2">Skills</h2>
            <div className="space-y-1">
              {formData.skills.technical?.length > 0 && (
                <div>
                  <span className="text-xs font-medium text-gray-700">Technical: </span>
                  <span className="text-xs text-gray-600">
                    {formData.skills.technical.slice(0, 8).join(', ')}
                    {formData.skills.technical.length > 8 && '...'}
                  </span>
                </div>
              )}
              {formData.skills.languages?.length > 0 && (
                <div>
                  <span className="text-xs font-medium text-gray-700">Languages: </span>
                  <span className="text-xs text-gray-600">
                    {formData.skills.languages.join(', ')}
                  </span>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="bg-white border border-gray-200 rounded-lg shadow-sm h-full flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-3 border-b border-gray-100 bg-gray-50">
        <div className="flex items-center gap-2">
          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
          <span className="text-sm font-medium text-gray-700">Live Preview</span>
        </div>
        
        <div className="flex items-center gap-1">
          {isVisible && onOpenFullscreen && (
            <button
              onClick={onOpenFullscreen}
              className="p-1.5 text-gray-500 hover:text-gray-700 hover:bg-gray-200 rounded transition-colors"
              title="View Fullscreen"
            >
              <Maximize2 className="h-4 w-4" />
            </button>
          )}
          <button
            onClick={() => setIsVisible(!isVisible)}
            className="p-1.5 text-gray-500 hover:text-gray-700 hover:bg-gray-200 rounded transition-colors"
            title={isVisible ? 'Hide Preview' : 'Show Preview'}
          >
            {isVisible ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
          </button>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-hidden">
        <AnimatePresence mode="wait">
          {isVisible ? (
            <motion.div
              key="preview"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="h-full overflow-y-auto"
            >
              {renderPreviewContent()}
            </motion.div>
          ) : (
            <motion.div
              key="hidden"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="flex items-center justify-center h-full text-gray-400"
            >
              <div className="text-center">
                <Eye className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p className="text-sm">Preview Hidden</p>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Footer */}
      {isVisible && hasContent && (
        <div className="p-2 border-t border-gray-100 bg-gray-50">
          <div className="flex items-center justify-between text-xs text-gray-500">
            <span>Updates automatically</span>
            <span className="capitalize">{selectedTemplate.replace('_', ' ')}</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default StreamlinedPreview;

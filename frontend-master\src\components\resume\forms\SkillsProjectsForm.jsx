'use client';
import { motion, AnimatePresence } from 'framer-motion';
import { useState } from 'react';
import { 
  Award,
  Plus,
  Trash2,
  Code,
  Globe,
  Folder,
  Link,
  Sparkles,
  Save,
  Wand2,
  AlertTriangle,
  CheckCircle,
  Target,
  Lightbulb,
  X
} from 'lucide-react';
import SmartForm<PERSON>ield from './SmartFormField';
import ClientOnly from '../../common/ClientOnly';
import ATSFieldIndicator from '../ats/FieldIndicator';

export const EnhancedSkillsProjectsForm = ({ 
  formData, 
  updateFormData, 
  addArrayItem, 
  removeArrayItem, 
  atsAnalysis, 
  validationErrors = {}, 
  showValidationErrors = false,
  onSave,
  onAISuggest
}) => {
  const [expandedProjects, setExpandedProjects] = useState(new Set([0]));
  const [showAIHelper, setShowAIHelper] = useState(false);
  const [activeSkillCategory, setActiveSkillCategory] = useState('technical');

  const toggleProjectExpanded = (index) => {
    const newExpanded = new Set(expandedProjects);
    if (newExpanded.has(index)) {
      newExpanded.delete(index);
    } else {
      newExpanded.add(index);
    }
    setExpandedProjects(newExpanded);
  };

  const addProject = () => {
    const newIndex = formData.projects.length;
    addArrayItem('projects', {
      name: "",
      description: "",
      technologies: "",
      link: ""
    });
    setExpandedProjects(new Set([...expandedProjects, newIndex]));
  };

  const addSkill = (category, skill) => {
    if (skill.trim() && !formData.skills[category].includes(skill.trim())) {
      const newSkills = [...formData.skills[category], skill.trim()];
      updateFormData('skills', category, newSkills);
    }
  };

  const removeSkill = (category, skillToRemove) => {
    const newSkills = formData.skills[category].filter(skill => skill !== skillToRemove);
    updateFormData('skills', category, newSkills);
  };

  const aiSuggestions = {
    technical: [
      "JavaScript", "TypeScript", "React", "Node.js", "Python", "Java", "C++", "Go",
      "Docker", "Kubernetes", "AWS", "Azure", "MongoDB", "PostgreSQL", "Redis",
      "Git", "Jenkins", "Terraform", "GraphQL", "REST APIs", "Microservices"
    ],
    languages: [
      "English (Native)", "Spanish (Fluent)", "French (Conversational)", 
      "German (Basic)", "Mandarin (Intermediate)", "Japanese (Basic)",
      "Portuguese (Fluent)", "Italian (Conversational)"
    ],
    certifications: [
      "AWS Certified Solutions Architect", "Google Cloud Professional", 
      "Microsoft Azure Fundamentals", "Certified Kubernetes Administrator",
      "Oracle Java Certified", "Scrum Master Certified", "PMP Certified",
      "CompTIA Security+", "Cisco CCNA", "Salesforce Certified"
    ],
    projects: [
      "E-commerce Platform", "Task Management App", "Social Media Dashboard",
      "Real-time Chat Application", "Data Analytics Platform", "Mobile Banking App",
      "Inventory Management System", "Learning Management System"
    ],
    technologies: [
      "React, Node.js, MongoDB", "Python, Django, PostgreSQL", "Java, Spring Boot, MySQL",
      "Vue.js, Express.js, Redis", "Angular, .NET Core, SQL Server", "Flutter, Firebase",
      "React Native, GraphQL, AWS", "Next.js, Prisma, Vercel"
    ]
  };

  const SkillInput = ({ category, placeholder }) => {
    const [inputValue, setInputValue] = useState('');

    const handleKeyPress = (e) => {
      if (e.key === 'Enter' && inputValue.trim()) {
        addSkill(category, inputValue);
        setInputValue('');
      }
    };

    return (
      <div className="space-y-3">
        <div className="relative">
          <input
            type="text"
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={handleKeyPress}
            className={`w-full px-4 py-3 bg-gray-800/50 border rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400 ${
              showValidationErrors && validationErrors.skills ? 'border-red-500 focus:ring-red-500' : 'border-gray-700'
            }`}
            placeholder={`${placeholder} (Press Enter to add)`}
          />
          {inputValue && (
            <button
              onClick={() => {
                addSkill(category, inputValue);
                setInputValue('');
              }}
              className="absolute right-2 top-1/2 transform -translate-y-1/2 px-3 py-1 bg-neural-purple hover:bg-neural-purple/80 text-white rounded text-sm transition-colors"
            >
              Add
            </button>
          )}
        </div>

        {/* AI Suggestions */}
        {aiSuggestions[category] && (
          <div className="flex flex-wrap gap-1">
            {aiSuggestions[category].slice(0, 6).map((suggestion, index) => (
              <button
                key={index}
                onClick={() => addSkill(category, suggestion)}
                className="px-2 py-1 bg-gray-700 hover:bg-neural-purple/20 text-gray-300 hover:text-neural-purple rounded text-xs transition-colors border border-gray-600 hover:border-neural-purple/50"
              >
                + {suggestion}
              </button>
            ))}
          </div>
        )}

        {/* Skills Display */}
        <div className="flex flex-wrap gap-2">
          {formData.skills[category].map((skill, index) => (
            <motion.span
              key={index}
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              className="inline-flex items-center gap-1 px-3 py-1.5 bg-neural-purple/20 border border-neural-purple/50 rounded-full text-sm text-neural-purple"
            >
              {skill}
              <button
                onClick={() => removeSkill(category, skill)}
                className="text-neural-pink hover:text-red-400 transition-colors ml-1"
              >
                <X className="h-3 w-3" />
              </button>
            </motion.span>
          ))}
        </div>
      </div>
    );
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="space-y-8"
    >
      {/* Header with Actions */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-neural-purple/20 rounded-xl flex items-center justify-center">
            <Award className="h-5 w-5 text-neural-purple" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-white">Skills & Projects</h2>
            <p className="text-gray-400 text-sm">Showcase your technical abilities and project experience</p>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <button
            onClick={() => setShowAIHelper(!showAIHelper)}
            className="flex items-center gap-2 px-3 py-2 bg-neural-purple/20 hover:bg-neural-purple/30 border border-neural-purple/50 rounded-lg transition-colors text-sm"
          >
            <Sparkles className="h-4 w-4" />
            AI Helper
          </button>
          <button
            onClick={onSave}
            className="flex items-center gap-2 px-3 py-2 bg-gray-800 hover:bg-gray-700 border border-gray-600 rounded-lg transition-colors text-sm"
          >
            <Save className="h-4 w-4" />
            Save
          </button>
        </div>
      </div>

      {/* Validation Error Summary */}
      <AnimatePresence>
        {showValidationErrors && validationErrors.skills && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="bg-red-500/10 border border-red-500/20 rounded-xl p-4"
          >
            <div className="flex items-center gap-2 mb-2">
              <AlertTriangle className="h-5 w-5 text-red-400" />
              <h4 className="text-red-400 font-medium">Skills Required</h4>
            </div>
            <p className="text-red-300 text-sm">{validationErrors.skills}</p>
          </motion.div>
        )}
      </AnimatePresence>

      {/* AI Helper Panel */}
      <AnimatePresence>
        {showAIHelper && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="bg-gradient-to-r from-neural-purple/10 to-neural-pink/10 border border-neural-purple/30 rounded-xl p-4"
          >
            <div className="flex items-center gap-2 mb-3">
              <Wand2 className="h-5 w-5 text-neural-purple" />
              <h3 className="font-semibold text-white">AI Skills & Projects Assistant</h3>
            </div>
            <p className="text-gray-300 text-sm mb-3">
              Get AI-powered suggestions for relevant skills and project ideas based on your experience and target roles.
            </p>
            <div className="flex gap-2">
              <button
                onClick={() => onAISuggest?.('skills')}
                className="px-3 py-2 bg-neural-purple hover:bg-neural-purple/80 text-white rounded-lg text-sm transition-colors"
              >
                Suggest Skills
              </button>
              <button
                onClick={() => onAISuggest?.('projects')}
                className="px-3 py-2 bg-neural-pink hover:bg-neural-pink/80 text-white rounded-lg text-sm transition-colors"
              >
                Suggest Projects
              </button>
              <button
                onClick={() => setShowAIHelper(false)}
                className="px-3 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg text-sm transition-colors"
              >
                Close
              </button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Skills Section */}
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h3 className="text-xl font-semibold text-white flex items-center gap-2">
            <Code className="h-5 w-5 text-neural-purple" />
            Skills
          </h3>
          <div className="text-sm text-gray-400">
            Total: {Object.values(formData.skills).flat().length} skills
          </div>
        </div>

        {/* Skill Category Tabs */}
        <div className="flex space-x-1 bg-gray-800/50 p-1 rounded-lg">
          {[
            { key: 'technical', label: 'Technical', icon: Code },
            { key: 'languages', label: 'Languages', icon: Globe },
            { key: 'certifications', label: 'Certifications', icon: Award }
          ].map(({ key, label, icon: Icon }) => (
            <button
              key={key}
              onClick={() => setActiveSkillCategory(key)}
              className={`flex items-center gap-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                activeSkillCategory === key
                  ? 'bg-neural-purple text-white'
                  : 'text-gray-400 hover:text-white hover:bg-gray-700'
              }`}
            >
              <Icon className="h-4 w-4" />
              {label}
              <span className="bg-gray-600 text-xs px-1.5 py-0.5 rounded-full">
                {formData.skills[key].length}
              </span>
            </button>
          ))}
        </div>

        {/* Active Skill Category */}
        <div className="bg-gray-900/40 backdrop-blur-sm rounded-xl p-6 border border-white/10">
          <div className="mb-4">
            <h4 className="font-semibold text-white mb-2">
              {activeSkillCategory === 'technical' && 'Technical Skills'}
              {activeSkillCategory === 'languages' && 'Languages'}
              {activeSkillCategory === 'certifications' && 'Certifications'}
            </h4>
            <p className="text-gray-400 text-sm">
              {activeSkillCategory === 'technical' && 'Programming languages, frameworks, tools, and technologies'}
              {activeSkillCategory === 'languages' && 'Spoken languages with proficiency levels'}
              {activeSkillCategory === 'certifications' && 'Professional certifications and credentials'}
            </p>
          </div>

          <SkillInput
            category={activeSkillCategory}
            placeholder={
              activeSkillCategory === 'technical' ? 'JavaScript, React, Node.js...' :
              activeSkillCategory === 'languages' ? 'English (Native), Spanish (Fluent)...' :
              'AWS Certified, Google Cloud Professional...'
            }
          />

          <ClientOnly>
            <ATSFieldIndicator
              fieldName="technical_skills"
              value={formData.skills[activeSkillCategory]}
              analysis={atsAnalysis?.fieldAnalysis?.technical_skills}
              showDetails={true}
            />
          </ClientOnly>
        </div>
      </div>

      {/* Projects Section */}
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h3 className="text-xl font-semibold text-white flex items-center gap-2">
            <Folder className="h-5 w-5 text-neural-purple" />
            Projects
          </h3>
          <button
            onClick={addProject}
            className="flex items-center gap-2 px-4 py-2 bg-neural-purple/20 hover:bg-neural-purple/30 border border-neural-purple/50 rounded-lg transition-colors"
          >
            <Plus className="h-4 w-4" />
            Add Project
          </button>
        </div>

        <div className="space-y-4">
          {formData.projects.map((project, index) => (
            <motion.div
              key={project.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="bg-gray-900/40 backdrop-blur-sm rounded-2xl border border-white/10 overflow-hidden"
            >
              {/* Project Header */}
              <div 
                className="p-4 cursor-pointer hover:bg-gray-800/30 transition-colors"
                onClick={() => toggleProjectExpanded(index)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-neural-purple/20 rounded-lg flex items-center justify-center">
                      <span className="text-neural-purple font-semibold text-sm">{index + 1}</span>
                    </div>
                    <div>
                      <h4 className="font-semibold text-white">
                        {project.name || `Project ${index + 1}`}
                      </h4>
                      <p className="text-gray-400 text-sm">
                        {project.technologies || 'Technologies not specified'}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    {project.name && project.description ? (
                      <div className="flex items-center gap-1 text-green-400">
                        <CheckCircle className="h-4 w-4" />
                        <span className="text-xs">Complete</span>
                      </div>
                    ) : (
                      <div className="flex items-center gap-1 text-yellow-400">
                        <Lightbulb className="h-4 w-4" />
                        <span className="text-xs">Incomplete</span>
                      </div>
                    )}
                    
                    {formData.projects.length > 1 && (
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          removeArrayItem('projects', project.id);
                        }}
                        className="p-2 text-red-400 hover:text-red-300 hover:bg-red-500/10 rounded-lg transition-colors"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    )}
                    <motion.div
                      animate={{ rotate: expandedProjects.has(index) ? 180 : 0 }}
                      transition={{ duration: 0.2 }}
                    >
                      <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </motion.div>
                  </div>
                </div>
              </div>

              {/* Expanded Project Content */}
              <AnimatePresence>
                {expandedProjects.has(index) && (
                  <motion.div
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: 'auto', opacity: 1 }}
                    exit={{ height: 0, opacity: 0 }}
                    transition={{ duration: 0.3 }}
                    className="border-t border-gray-700"
                  >
                    <div className="p-6 space-y-6">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        {/* Project Name */}
                        <SmartFormField
                          label="Project Name"
                          type="text"
                          value={project.name}
                          onChange={(e) => updateFormData('projects', 'name', e.target.value, index)}
                          placeholder="E-commerce Platform"
                          required
                          icon={Folder}
                          aiSuggestions={aiSuggestions.projects}
                          onAISuggestionApply={(suggestion) => updateFormData('projects', 'name', suggestion, index)}
                        />

                        {/* Project Link */}
                        <SmartFormField
                          label="Project Link"
                          type="url"
                          value={project.link}
                          onChange={(e) => updateFormData('projects', 'link', e.target.value, index)}
                          placeholder="https://github.com/username/project"
                          icon={Link}
                          hint="GitHub, live demo, or portfolio link"
                        />

                        {/* Technologies Used */}
                        <SmartFormField
                          label="Technologies Used"
                          type="text"
                          value={project.technologies}
                          onChange={(e) => updateFormData('projects', 'technologies', e.target.value, index)}
                          placeholder="React, Node.js, MongoDB, Express.js"
                          icon={Code}
                          aiSuggestions={aiSuggestions.technologies}
                          onAISuggestionApply={(suggestion) => updateFormData('projects', 'technologies', suggestion, index)}
                          hint="List the main technologies and frameworks used"
                        />

                        {/* Project Description */}
                        <div className="md:col-span-2">
                          <SmartFormField
                            label="Project Description"
                            type="textarea"
                            value={project.description}
                            onChange={(e) => updateFormData('projects', 'description', e.target.value, index)}
                            placeholder="Developed a full-stack e-commerce platform with user authentication, payment processing, and inventory management. Implemented responsive design and achieved 99.9% uptime with automated testing and CI/CD pipeline."
                            rows={4}
                            maxLength={500}
                            showCharCount
                            hint="Describe the project goals, your role, technologies used, and measurable outcomes"
                          />
                          <ClientOnly>
                            <ATSFieldIndicator
                              fieldName="project_description"
                              value={project.description}
                              analysis={atsAnalysis?.fieldAnalysis?.[`project_description_${index}`]}
                              showDetails={true}
                            />
                          </ClientOnly>
                        </div>
                      </div>

                      {/* Quick Actions */}
                      <div className="flex items-center justify-between pt-4 border-t border-gray-700">
                        <div className="flex items-center gap-2 text-sm text-gray-400">
                          <Target className="h-4 w-4" />
                          <span>Impact: {project.description ? 'Well described' : 'Add description to show impact'}</span>
                        </div>
                        <button
                          onClick={() => onAISuggest?.(`project_${index}`)}
                          className="px-3 py-1.5 bg-neural-purple/20 hover:bg-neural-purple/30 border border-neural-purple/50 rounded-lg text-neural-purple text-sm transition-colors"
                        >
                          <Sparkles className="h-3 w-3 inline mr-1" />
                          AI Enhance
                        </button>
                      </div>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>
          ))}
        </div>

        {/* Empty State for Projects */}
        {formData.projects.length === 0 && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="text-center py-12 bg-gray-900/20 rounded-2xl border border-dashed border-gray-600"
          >
            <Folder className="h-12 w-12 text-gray-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-400 mb-2">No Projects Added</h3>
            <p className="text-gray-500 mb-4">Showcase your best work and personal projects</p>
            <button
              onClick={addProject}
              className="px-6 py-3 bg-neural-purple hover:bg-neural-purple/80 text-white rounded-xl transition-colors"
            >
              Add Your First Project
            </button>
          </motion.div>
        )}
      </div>

      {/* Progress Indicator */}
      <div className="bg-gray-900/40 backdrop-blur-sm rounded-xl p-4 border border-white/10">
        <div className="flex items-center justify-between text-sm mb-2">
          <span className="text-gray-400">Skills & Projects Progress</span>
          <span className="text-neural-purple font-medium">
            {Object.values(formData.skills).flat().length > 0 ? 'Skills added' : 'No skills yet'} • 
            {formData.projects.filter(p => p.name && p.description).length} / {formData.projects.length} projects completed
          </span>
        </div>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <div className="text-xs text-gray-500 mb-1">Skills ({Object.values(formData.skills).flat().length})</div>
            <div className="w-full bg-gray-700 rounded-full h-2">
              <motion.div
                className="bg-gradient-to-r from-neural-purple to-neural-pink h-2 rounded-full"
                initial={{ width: 0 }}
                animate={{ 
                  width: `${Math.min((Object.values(formData.skills).flat().length / 10) * 100, 100)}%` 
                }}
                transition={{ duration: 0.5 }}
              />
            </div>
          </div>
          <div>
            <div className="text-xs text-gray-500 mb-1">Projects ({formData.projects.filter(p => p.name && p.description).length})</div>
            <div className="w-full bg-gray-700 rounded-full h-2">
              <motion.div
                className="bg-gradient-to-r from-neural-pink to-neural-purple h-2 rounded-full"
                initial={{ width: 0 }}
                animate={{ 
                  width: `${(formData.projects.filter(p => p.name && p.description).length / Math.max(formData.projects.length, 1)) * 100}%` 
                }}
                transition={{ duration: 0.5 }}
              />
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

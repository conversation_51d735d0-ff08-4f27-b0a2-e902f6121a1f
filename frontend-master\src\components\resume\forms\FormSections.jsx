'use client';
import { motion, AnimatePresence } from 'framer-motion';
import { useState } from 'react';
import {
  User,
  Mail,
  Phone,
  MapPin,
  Globe,
  Link,
  GraduationCap,
  Briefcase,
  Award,
  Plus,
  Trash2,
  Calendar,
  Building,
  FileText,
  Sparkles,
  Target,
  Camera,
  X,
  CheckCircle,
  AlertTriangle,
  Info,
  Copy,
  Wand2,
  Save,
  RotateCcw
} from 'lucide-react';
import SmartForm<PERSON>ield from './SmartFormField';
import ClientOnly from '../../common/ClientOnly';
import ATSFieldIndicator from './ATSFieldIndicator';

// Enhanced Personal Information Form
export const EnhancedPersonalInfoForm = ({ 
  formData, 
  updateFormData, 
  atsAnalysis, 
  validationErrors = {}, 
  showValidationErrors = false,
  onSave,
  onAISuggest
}) => {
  const [showAIHelper, setShowAIHelper] = useState(false);
  const [savedData, setSavedData] = useState(null);

  const handleSave = () => {
    setSavedData({ ...formData.personal });
    onSave?.();
  };

  const handleReset = () => {
    if (savedData) {
      Object.keys(savedData).forEach(key => {
        updateFormData('personal', key, savedData[key]);
      });
    }
  };

  const aiSuggestions = {
    summary: [
      "Experienced software developer with 5+ years in full-stack development, specializing in React and Node.js",
      "Results-driven engineer with expertise in cloud technologies and agile methodologies",
      "Passionate developer focused on creating scalable solutions and improving user experiences"
    ]
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="space-y-6"
    >
      {/* Header with Actions */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-neural-purple/20 rounded-xl flex items-center justify-center">
            <User className="h-5 w-5 text-neural-purple" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-white">Personal Information</h2>
            <p className="text-gray-400 text-sm">Tell us about yourself to get started</p>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <button
            onClick={() => setShowAIHelper(!showAIHelper)}
            className="flex items-center gap-2 px-3 py-2 bg-neural-purple/20 hover:bg-neural-purple/30 border border-neural-purple/50 rounded-lg transition-colors text-sm"
          >
            <Sparkles className="h-4 w-4" />
            AI Helper
          </button>
          <button
            onClick={handleSave}
            className="flex items-center gap-2 px-3 py-2 bg-gray-800 hover:bg-gray-700 border border-gray-600 rounded-lg transition-colors text-sm"
          >
            <Save className="h-4 w-4" />
            Save
          </button>
        </div>
      </div>

      {/* Validation Error Summary */}
      <AnimatePresence>
        {showValidationErrors && (validationErrors.firstName || validationErrors.lastName || validationErrors.email) && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="bg-red-500/10 border border-red-500/20 rounded-xl p-4"
          >
            <div className="flex items-center gap-2 mb-2">
              <AlertTriangle className="h-5 w-5 text-red-400" />
              <h4 className="text-red-400 font-medium">Required Information Missing</h4>
            </div>
            <div className="space-y-1 text-sm">
              {validationErrors.firstName && <p className="text-red-300">• {validationErrors.firstName}</p>}
              {validationErrors.lastName && <p className="text-red-300">• {validationErrors.lastName}</p>}
              {validationErrors.email && <p className="text-red-300">• {validationErrors.email}</p>}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* AI Helper Panel */}
      <AnimatePresence>
        {showAIHelper && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="bg-gradient-to-r from-neural-purple/10 to-neural-pink/10 border border-neural-purple/30 rounded-xl p-4"
          >
            <div className="flex items-center gap-2 mb-3">
              <Wand2 className="h-5 w-5 text-neural-purple" />
              <h3 className="font-semibold text-white">AI Writing Assistant</h3>
            </div>
            <p className="text-gray-300 text-sm mb-3">
              Get AI-powered suggestions to make your personal information more professional and ATS-friendly.
            </p>
            <div className="flex gap-2">
              <button
                onClick={() => onAISuggest?.('personal')}
                className="px-3 py-2 bg-neural-purple hover:bg-neural-purple/80 text-white rounded-lg text-sm transition-colors"
              >
                Generate Suggestions
              </button>
              <button
                onClick={() => setShowAIHelper(false)}
                className="px-3 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg text-sm transition-colors"
              >
                Close
              </button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Form Fields */}
      <div className="bg-gray-900/40 backdrop-blur-sm rounded-2xl p-6 border border-white/10">
        {/* Profile Image Upload */}
        <div className="mb-6 flex justify-center">
          <div className="relative">
            <div className="w-32 h-32 bg-gray-800/50 border-2 border-dashed border-gray-600 rounded-full flex items-center justify-center overflow-hidden">
              {formData.personal.profileImage ? (
                <img
                  src={formData.personal.profileImage}
                  alt="Profile"
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="text-center">
                  <User className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                  <p className="text-xs text-gray-400">Optional Photo</p>
                </div>
              )}
            </div>
            <label className="absolute bottom-0 right-0 bg-neural-purple hover:bg-neural-purple/80 text-white p-2 rounded-full cursor-pointer transition-colors">
              <Camera className="h-4 w-4" />
              <input
                type="file"
                accept="image/png,image/jpeg,image/jpg"
                onChange={(e) => {
                  const file = e.target.files[0];
                  if (file) {
                    // Validate file size (2MB limit for images)
                    if (file.size > 2 * 1024 * 1024) {
                      alert('Image size must be less than 2MB');
                      return;
                    }
                    // Convert to base64 for storage
                    const reader = new FileReader();
                    reader.onload = (event) => {
                      updateFormData('personal', 'profileImage', event.target.result);
                    };
                    reader.readAsDataURL(file);
                  }
                }}
                className="hidden"
              />
            </label>
            {formData.personal.profileImage && (
              <button
                onClick={() => updateFormData('personal', 'profileImage', '')}
                className="absolute top-0 right-0 bg-red-500 hover:bg-red-600 text-white p-1 rounded-full transition-colors"
                title="Remove image"
              >
                <X className="h-3 w-3" />
              </button>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* First Name */}
          <SmartFormField
            label="First Name"
            type="text"
            value={formData.personal.firstName}
            onChange={(e) => updateFormData('personal', 'firstName', e.target.value)}
            placeholder="John"
            required
            error={showValidationErrors ? validationErrors.firstName : null}
            success={formData.personal.firstName && !validationErrors.firstName ? "Looks good!" : null}
            icon={User}
            autoComplete="given-name"
          />

          {/* Last Name */}
          <SmartFormField
            label="Last Name"
            type="text"
            value={formData.personal.lastName}
            onChange={(e) => updateFormData('personal', 'lastName', e.target.value)}
            placeholder="Doe"
            required
            error={showValidationErrors ? validationErrors.lastName : null}
            success={formData.personal.lastName && !validationErrors.lastName ? "Looks good!" : null}
            icon={User}
            autoComplete="family-name"
          />

          {/* Email */}
          <SmartFormField
            label="Email Address"
            type="email"
            value={formData.personal.email}
            onChange={(e) => updateFormData('personal', 'email', e.target.value)}
            placeholder="<EMAIL>"
            required
            error={showValidationErrors ? validationErrors.email : null}
            success={formData.personal.email && !validationErrors.email ? "Valid email format" : null}
            icon={Mail}
            autoComplete="email"
            hint="Use a professional email address for better impression"
          />

          {/* Phone */}
          <SmartFormField
            label="Phone Number"
            type="tel"
            value={formData.personal.phone}
            onChange={(e) => updateFormData('personal', 'phone', e.target.value)}
            placeholder="+****************"
            icon={Phone}
            autoComplete="tel"
            hint="Include country code for international applications"
          />

          {/* Location */}
          <SmartFormField
            label="Location"
            type="text"
            value={formData.personal.location}
            onChange={(e) => updateFormData('personal', 'location', e.target.value)}
            placeholder="San Francisco, CA"
            icon={MapPin}
            autoComplete="address-level2"
            suggestions={["San Francisco, CA", "New York, NY", "Austin, TX", "Seattle, WA"]}
          />

          {/* LinkedIn */}
          <SmartFormField
            label="LinkedIn Profile"
            type="url"
            value={formData.personal.linkedin}
            onChange={(e) => updateFormData('personal', 'linkedin', e.target.value)}
            placeholder="https://linkedin.com/in/johndoe"
            icon={Link}
            hint="Make sure your LinkedIn profile is up to date"
          />

          {/* Portfolio */}
          <div className="md:col-span-2">
            <SmartFormField
              label="Portfolio/Website"
              type="url"
              value={formData.personal.portfolio}
              onChange={(e) => updateFormData('personal', 'portfolio', e.target.value)}
              placeholder="https://johndoe.com"
              icon={Globe}
              hint="Showcase your best work with a professional portfolio"
            />
          </div>

          {/* Professional Summary */}
          <div className="md:col-span-2">
            <SmartFormField
              label="Professional Summary"
              type="textarea"
              value={formData.personal.summary}
              onChange={(e) => updateFormData('personal', 'summary', e.target.value)}
              placeholder="Write a compelling summary that highlights your key skills, experience, and career objectives..."
              rows={4}
              maxLength={500}
              showCharCount
              aiSuggestions={aiSuggestions.summary}
              onAISuggestionApply={(suggestion) => updateFormData('personal', 'summary', suggestion)}
              hint="2-3 sentences that grab the hiring manager's attention"
            />
            <ClientOnly>
              <ATSFieldIndicator
                fieldName="summary"
                value={formData.personal.summary}
                analysis={atsAnalysis?.fieldAnalysis?.summary}
                showDetails={true}
              />
            </ClientOnly>
          </div>
        </div>
      </div>

      {/* Progress Indicator */}
      <div className="bg-gray-900/40 backdrop-blur-sm rounded-xl p-4 border border-white/10">
        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-400">Section Progress</span>
          <span className="text-neural-purple font-medium">
            {Object.values(formData.personal).filter(val => val && val.toString().trim()).length} / 8 fields completed
          </span>
        </div>
        <div className="w-full bg-gray-700 rounded-full h-2 mt-2">
          <motion.div
            className="bg-gradient-to-r from-neural-purple to-neural-pink h-2 rounded-full"
            initial={{ width: 0 }}
            animate={{ 
              width: `${(Object.values(formData.personal).filter(val => val && val.toString().trim()).length / 8) * 100}%` 
            }}
            transition={{ duration: 0.5 }}
          />
        </div>
      </div>
    </motion.div>
  );
};

// Enhanced Education Form
export const EnhancedEducationForm = ({ 
  formData, 
  updateFormData, 
  addArrayItem, 
  removeArrayItem, 
  atsAnalysis, 
  validationErrors = {}, 
  showValidationErrors = false 
}) => {
  const [expandedItems, setExpandedItems] = useState(new Set([0]));

  const toggleExpanded = (index) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(index)) {
      newExpanded.delete(index);
    } else {
      newExpanded.add(index);
    }
    setExpandedItems(newExpanded);
  };

  const addEducation = () => {
    const newIndex = formData.education.length;
    addArrayItem('education', {
      degree: "",
      institution: "",
      location: "",
      startDate: "",
      endDate: "",
      gpa: "",
      relevant: ""
    });
    setExpandedItems(new Set([...expandedItems, newIndex]));
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="space-y-6"
    >
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-neural-purple/20 rounded-xl flex items-center justify-center">
            <GraduationCap className="h-5 w-5 text-neural-purple" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-white">Education</h2>
            <p className="text-gray-400 text-sm">Add your educational background</p>
          </div>
        </div>
        
        <button
          onClick={addEducation}
          className="flex items-center gap-2 px-4 py-2 bg-neural-purple hover:bg-neural-purple/80 text-white rounded-xl transition-colors"
        >
          <Plus className="h-4 w-4" />
          Add Education
        </button>
      </div>

      {/* Validation Error */}
      <AnimatePresence>
        {showValidationErrors && validationErrors.education && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="bg-red-500/10 border border-red-500/20 rounded-xl p-4"
          >
            <div className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-red-400" />
              <span className="text-red-400 font-medium">{validationErrors.education}</span>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Education Items */}
      <div className="space-y-4">
        {formData.education.map((edu, index) => (
          <motion.div
            key={edu.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="bg-gray-900/40 backdrop-blur-sm rounded-2xl border border-white/10 overflow-hidden"
          >
            {/* Header */}
            <div 
              className="p-4 cursor-pointer hover:bg-gray-800/30 transition-colors"
              onClick={() => toggleExpanded(index)}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-neural-purple/20 rounded-lg flex items-center justify-center">
                    <span className="text-neural-purple font-semibold text-sm">{index + 1}</span>
                  </div>
                  <div>
                    <h3 className="font-semibold text-white">
                      {edu.degree || `Education ${index + 1}`}
                    </h3>
                    <p className="text-gray-400 text-sm">
                      {edu.institution || 'Institution not specified'}
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  {formData.education.length > 1 && (
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        removeArrayItem('education', edu.id);
                      }}
                      className="p-2 text-red-400 hover:text-red-300 hover:bg-red-500/10 rounded-lg transition-colors"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  )}
                  <motion.div
                    animate={{ rotate: expandedItems.has(index) ? 180 : 0 }}
                    transition={{ duration: 0.2 }}
                  >
                    <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </motion.div>
                </div>
              </div>
            </div>

            {/* Expanded Content */}
            <AnimatePresence>
              {expandedItems.has(index) && (
                <motion.div
                  initial={{ height: 0, opacity: 0 }}
                  animate={{ height: 'auto', opacity: 1 }}
                  exit={{ height: 0, opacity: 0 }}
                  transition={{ duration: 0.3 }}
                  className="border-t border-gray-700"
                >
                  <div className="p-6 space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {/* Degree */}
                      <SmartFormField
                        label="Degree/Qualification"
                        type="text"
                        value={edu.degree}
                        onChange={(e) => updateFormData('education', 'degree', e.target.value, index)}
                        placeholder="Bachelor of Science in Computer Science"
                        required
                        error={showValidationErrors && validationErrors.education && !edu.degree.trim() ? 'Degree is required' : null}
                        icon={GraduationCap}
                        suggestions={[
                          "Bachelor of Science in Computer Science",
                          "Master of Science in Software Engineering",
                          "Bachelor of Technology in Information Technology"
                        ]}
                      />

                      {/* Institution */}
                      <SmartFormField
                        label="Institution/University"
                        type="text"
                        value={edu.institution}
                        onChange={(e) => updateFormData('education', 'institution', e.target.value, index)}
                        placeholder="Stanford University"
                        required
                        error={showValidationErrors && validationErrors.education && !edu.institution.trim() ? 'Institution is required' : null}
                        icon={Building}
                      />

                      {/* Location */}
                      <SmartFormField
                        label="Location"
                        type="text"
                        value={edu.location}
                        onChange={(e) => updateFormData('education', 'location', e.target.value, index)}
                        placeholder="Palo Alto, CA"
                        icon={MapPin}
                      />

                      {/* GPA */}
                      <SmartFormField
                        label="GPA (Optional)"
                        type="text"
                        value={edu.gpa}
                        onChange={(e) => updateFormData('education', 'gpa', e.target.value, index)}
                        placeholder="3.8/4.0"
                        hint="Only include if 3.5 or higher"
                      />

                      {/* Start Date */}
                      <SmartFormField
                        label="Start Date"
                        type="month"
                        value={edu.startDate}
                        onChange={(e) => updateFormData('education', 'startDate', e.target.value, index)}
                        icon={Calendar}
                      />

                      {/* End Date */}
                      <SmartFormField
                        label="End Date"
                        type="month"
                        value={edu.endDate}
                        onChange={(e) => updateFormData('education', 'endDate', e.target.value, index)}
                        icon={Calendar}
                      />

                      {/* Relevant Coursework */}
                      <div className="md:col-span-2">
                        <SmartFormField
                          label="Relevant Coursework, Projects, or Achievements"
                          type="textarea"
                          value={edu.relevant}
                          onChange={(e) => updateFormData('education', 'relevant', e.target.value, index)}
                          placeholder="• Data Structures and Algorithms, Software Engineering, Database Systems&#10;• Dean's List (Fall 2019, Spring 2020)&#10;• Capstone Project: Built a machine learning model for fraud detection"
                          rows={3}
                          hint="Highlight coursework and achievements relevant to your target role"
                        />
                      </div>
                    </div>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>
        ))}
      </div>

      {/* Empty State */}
      {formData.education.length === 0 && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="text-center py-12 bg-gray-900/20 rounded-2xl border border-dashed border-gray-600"
        >
          <GraduationCap className="h-12 w-12 text-gray-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-400 mb-2">No Education Added</h3>
          <p className="text-gray-500 mb-4">Add your educational background to strengthen your resume</p>
          <button
            onClick={addEducation}
            className="px-6 py-3 bg-neural-purple hover:bg-neural-purple/80 text-white rounded-xl transition-colors"
          >
            Add Your First Education
          </button>
        </motion.div>
      )}
    </motion.div>
  );
};

const EnhancedFormSections = { EnhancedPersonalInfoForm, EnhancedEducationForm }; export default EnhancedFormSections;

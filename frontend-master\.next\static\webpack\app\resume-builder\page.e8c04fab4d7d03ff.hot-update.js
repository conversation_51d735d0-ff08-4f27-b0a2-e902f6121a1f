"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/resume-builder/page",{

/***/ "(app-pages-browser)/./src/components/resume/RedesignedResumeBuilder.jsx":
/*!***********************************************************!*\
  !*** ./src/components/resume/RedesignedResumeBuilder.jsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _SimplifiedNavigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./SimplifiedNavigation */ \"(app-pages-browser)/./src/components/resume/SimplifiedNavigation.jsx\");\n/* harmony import */ var _StreamlinedPreview__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./StreamlinedPreview */ \"(app-pages-browser)/./src/components/resume/StreamlinedPreview.jsx\");\n/* harmony import */ var _FullscreenPreviewModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./FullscreenPreviewModal */ \"(app-pages-browser)/./src/components/resume/FullscreenPreviewModal.jsx\");\n/* harmony import */ var _UniversalTemplateSelector__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./UniversalTemplateSelector */ \"(app-pages-browser)/./src/components/resume/UniversalTemplateSelector.jsx\");\n/* harmony import */ var _forms_UniversalFormFields__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./forms/UniversalFormFields */ \"(app-pages-browser)/./src/components/resume/forms/UniversalFormFields.jsx\");\n/* harmony import */ var _templates_MultiIndustryTemplates__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./templates/MultiIndustryTemplates */ \"(app-pages-browser)/./src/components/resume/templates/MultiIndustryTemplates.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n// Import new components\n\n\n\n\n\n// Import template system\n\nconst RedesignedResumeBuilder = (param)=>{\n    let { hideHeader = false } = param;\n    _s();\n    // Core state\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [selectedTemplate, setSelectedTemplate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('business_executive');\n    const [showTemplateSelector, setShowTemplateSelector] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showFullscreenPreview, setShowFullscreenPreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Form data with universal structure\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        personal: {\n            firstName: \"\",\n            lastName: \"\",\n            email: \"\",\n            phone: \"\",\n            location: \"\",\n            summary: \"\"\n        },\n        experience: [\n            {\n                id: 1,\n                title: \"\",\n                company: \"\",\n                location: \"\",\n                startDate: \"\",\n                endDate: \"\",\n                current: false,\n                description: \"\"\n            }\n        ],\n        education: [\n            {\n                id: 1,\n                degree: \"\",\n                field: \"\",\n                institution: \"\",\n                location: \"\",\n                graduationDate: \"\",\n                gpa: \"\"\n            }\n        ],\n        skills: {\n            technical: [],\n            languages: [],\n            certifications: []\n        }\n    });\n    // UI state\n    const [validationErrors, setValidationErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isSaving, setIsSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [completedSteps, setCompletedSteps] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Step configuration - simplified and universal\n    const steps = [\n        {\n            id: 0,\n            title: \"Personal Info\",\n            description: \"Your contact information and professional summary\"\n        },\n        {\n            id: 1,\n            title: \"Experience\",\n            description: \"Your work history and achievements\"\n        },\n        {\n            id: 2,\n            title: \"Education\",\n            description: \"Your educational background and qualifications\"\n        },\n        {\n            id: 3,\n            title: \"Skills\",\n            description: \"Your core competencies and abilities\"\n        },\n        {\n            id: 4,\n            title: \"Review\",\n            description: \"Review and download your resume\"\n        }\n    ];\n    // Auto-save functionality\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RedesignedResumeBuilder.useEffect\": ()=>{\n            const timeoutId = setTimeout({\n                \"RedesignedResumeBuilder.useEffect.timeoutId\": ()=>{\n                    localStorage.setItem('universalResumeData', JSON.stringify(formData));\n                    localStorage.setItem('selectedTemplate', selectedTemplate);\n                }\n            }[\"RedesignedResumeBuilder.useEffect.timeoutId\"], 2000);\n            return ({\n                \"RedesignedResumeBuilder.useEffect\": ()=>clearTimeout(timeoutId)\n            })[\"RedesignedResumeBuilder.useEffect\"];\n        }\n    }[\"RedesignedResumeBuilder.useEffect\"], [\n        formData,\n        selectedTemplate\n    ]);\n    // Load saved data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RedesignedResumeBuilder.useEffect\": ()=>{\n            const savedData = localStorage.getItem('universalResumeData');\n            const savedTemplate = localStorage.getItem('selectedTemplate');\n            if (savedData) {\n                try {\n                    setFormData(JSON.parse(savedData));\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success('Previous work restored!');\n                } catch (error) {\n                    console.error('Failed to load saved data:', error);\n                }\n            }\n            if (savedTemplate && _templates_MultiIndustryTemplates__WEBPACK_IMPORTED_MODULE_8__.INDUSTRY_TEMPLATES[savedTemplate]) {\n                setSelectedTemplate(savedTemplate);\n            }\n        }\n    }[\"RedesignedResumeBuilder.useEffect\"], []);\n    // Validation logic\n    const validateStep = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"RedesignedResumeBuilder.useCallback[validateStep]\": (stepIndex)=>{\n            const errors = {};\n            switch(stepIndex){\n                case 0:\n                    var _formData_personal_firstName, _formData_personal_lastName, _formData_personal_email;\n                    if (!((_formData_personal_firstName = formData.personal.firstName) === null || _formData_personal_firstName === void 0 ? void 0 : _formData_personal_firstName.trim())) errors.firstName = 'First name is required';\n                    if (!((_formData_personal_lastName = formData.personal.lastName) === null || _formData_personal_lastName === void 0 ? void 0 : _formData_personal_lastName.trim())) errors.lastName = 'Last name is required';\n                    if (!((_formData_personal_email = formData.personal.email) === null || _formData_personal_email === void 0 ? void 0 : _formData_personal_email.trim())) errors.email = 'Email is required';\n                    else if (!/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(formData.personal.email)) {\n                        errors.email = 'Please enter a valid email address';\n                    }\n                    break;\n                case 1:\n                    var _formData_experience;\n                    const validExperience = (_formData_experience = formData.experience) === null || _formData_experience === void 0 ? void 0 : _formData_experience.filter({\n                        \"RedesignedResumeBuilder.useCallback[validateStep]\": (exp)=>{\n                            var _exp_title, _exp_company;\n                            return ((_exp_title = exp.title) === null || _exp_title === void 0 ? void 0 : _exp_title.trim()) && ((_exp_company = exp.company) === null || _exp_company === void 0 ? void 0 : _exp_company.trim());\n                        }\n                    }[\"RedesignedResumeBuilder.useCallback[validateStep]\"]);\n                    if (!validExperience || validExperience.length === 0) {\n                        errors.experience = 'At least one work experience entry is required';\n                    }\n                    break;\n                case 2:\n                    var _formData_education;\n                    const validEducation = (_formData_education = formData.education) === null || _formData_education === void 0 ? void 0 : _formData_education.filter({\n                        \"RedesignedResumeBuilder.useCallback[validateStep]\": (edu)=>{\n                            var _edu_degree, _edu_institution;\n                            return ((_edu_degree = edu.degree) === null || _edu_degree === void 0 ? void 0 : _edu_degree.trim()) && ((_edu_institution = edu.institution) === null || _edu_institution === void 0 ? void 0 : _edu_institution.trim());\n                        }\n                    }[\"RedesignedResumeBuilder.useCallback[validateStep]\"]);\n                    if (!validEducation || validEducation.length === 0) {\n                        errors.education = 'At least one education entry is required';\n                    }\n                    break;\n                case 3:\n                    break;\n                default:\n                    break;\n            }\n            return errors;\n        }\n    }[\"RedesignedResumeBuilder.useCallback[validateStep]\"], [\n        formData\n    ]);\n    // Navigation logic\n    const canProceedToNextStep = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"RedesignedResumeBuilder.useCallback[canProceedToNextStep]\": (stepIndex)=>{\n            const errors = validateStep(stepIndex);\n            return Object.keys(errors).length === 0;\n        }\n    }[\"RedesignedResumeBuilder.useCallback[canProceedToNextStep]\"], [\n        validateStep\n    ]);\n    const nextStep = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"RedesignedResumeBuilder.useCallback[nextStep]\": ()=>{\n            const errors = validateStep(currentStep);\n            if (Object.keys(errors).length > 0) {\n                setValidationErrors(errors);\n                const errorMessages = Object.values(errors);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(errorMessages[0]);\n                return;\n            }\n            setCompletedSteps({\n                \"RedesignedResumeBuilder.useCallback[nextStep]\": (prev)=>[\n                        ...new Set([\n                            ...prev,\n                            currentStep\n                        ])\n                    ]\n            }[\"RedesignedResumeBuilder.useCallback[nextStep]\"]);\n            setValidationErrors({});\n            if (currentStep < steps.length - 1) {\n                setCurrentStep(currentStep + 1);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"\".concat(steps[currentStep].title, \" completed!\"));\n            }\n        }\n    }[\"RedesignedResumeBuilder.useCallback[nextStep]\"], [\n        currentStep,\n        validateStep,\n        steps\n    ]);\n    const prevStep = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"RedesignedResumeBuilder.useCallback[prevStep]\": ()=>{\n            setValidationErrors({});\n            if (currentStep > 0) setCurrentStep(currentStep - 1);\n        }\n    }[\"RedesignedResumeBuilder.useCallback[prevStep]\"], [\n        currentStep\n    ]);\n    // Form data management\n    const updateFormData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"RedesignedResumeBuilder.useCallback[updateFormData]\": function(section, field, value) {\n            let index = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : null;\n            setFormData({\n                \"RedesignedResumeBuilder.useCallback[updateFormData]\": (prev)=>{\n                    let newData = {\n                        ...prev\n                    };\n                    if (index !== null && Array.isArray(prev[section])) {\n                        const newArray = [\n                            ...prev[section]\n                        ];\n                        newArray[index] = {\n                            ...newArray[index],\n                            [field]: value\n                        };\n                        newData[section] = newArray;\n                    } else if (typeof prev[section] === 'object' && !Array.isArray(prev[section])) {\n                        newData[section] = {\n                            ...prev[section],\n                            [field]: value\n                        };\n                    } else {\n                        newData[field] = value;\n                    }\n                    return newData;\n                }\n            }[\"RedesignedResumeBuilder.useCallback[updateFormData]\"]);\n            // Clear validation errors for this field\n            if (validationErrors[field]) {\n                setValidationErrors({\n                    \"RedesignedResumeBuilder.useCallback[updateFormData]\": (prev)=>{\n                        const newErrors = {\n                            ...prev\n                        };\n                        delete newErrors[field];\n                        return newErrors;\n                    }\n                }[\"RedesignedResumeBuilder.useCallback[updateFormData]\"]);\n            }\n        }\n    }[\"RedesignedResumeBuilder.useCallback[updateFormData]\"], [\n        validationErrors\n    ]);\n    const addArrayItem = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"RedesignedResumeBuilder.useCallback[addArrayItem]\": (section, template)=>{\n            setFormData({\n                \"RedesignedResumeBuilder.useCallback[addArrayItem]\": (prev)=>({\n                        ...prev,\n                        [section]: [\n                            ...prev[section],\n                            {\n                                ...template,\n                                id: Math.random().toString(36).substring(2, 11)\n                            }\n                        ]\n                    })\n            }[\"RedesignedResumeBuilder.useCallback[addArrayItem]\"]);\n        }\n    }[\"RedesignedResumeBuilder.useCallback[addArrayItem]\"], []);\n    const removeArrayItem = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"RedesignedResumeBuilder.useCallback[removeArrayItem]\": (section, id)=>{\n            setFormData({\n                \"RedesignedResumeBuilder.useCallback[removeArrayItem]\": (prev)=>({\n                        ...prev,\n                        [section]: prev[section].filter({\n                            \"RedesignedResumeBuilder.useCallback[removeArrayItem]\": (item)=>item.id !== id\n                        }[\"RedesignedResumeBuilder.useCallback[removeArrayItem]\"])\n                    })\n            }[\"RedesignedResumeBuilder.useCallback[removeArrayItem]\"]);\n        }\n    }[\"RedesignedResumeBuilder.useCallback[removeArrayItem]\"], []);\n    // Save functionality\n    const handleSave = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"RedesignedResumeBuilder.useCallback[handleSave]\": ()=>{\n            setIsSaving(true);\n            localStorage.setItem('universalResumeData', JSON.stringify(formData));\n            localStorage.setItem('selectedTemplate', selectedTemplate);\n            setTimeout({\n                \"RedesignedResumeBuilder.useCallback[handleSave]\": ()=>{\n                    setIsSaving(false);\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success('Progress saved!');\n                }\n            }[\"RedesignedResumeBuilder.useCallback[handleSave]\"], 1000);\n        }\n    }[\"RedesignedResumeBuilder.useCallback[handleSave]\"], [\n        formData,\n        selectedTemplate\n    ]);\n    // Download functionality\n    const handleDownload = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"RedesignedResumeBuilder.useCallback[handleDownload]\": ()=>{\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success('Download feature coming soon!');\n        }\n    }[\"RedesignedResumeBuilder.useCallback[handleDownload]\"], []);\n    // Calculate completion percentage\n    const getCompletionPercentage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"RedesignedResumeBuilder.useCallback[getCompletionPercentage]\": ()=>{\n            var _formData_experience, _formData_education, _formData_skills_technical, _formData_skills;\n            let totalFields = 0;\n            let completedFields = 0;\n            // Personal info (4 required fields)\n            totalFields += 4;\n            if (formData.personal.firstName) completedFields++;\n            if (formData.personal.lastName) completedFields++;\n            if (formData.personal.email) completedFields++;\n            if (formData.personal.summary) completedFields++;\n            // Experience (at least 1 entry with title and company)\n            totalFields += 2;\n            const validExp = (_formData_experience = formData.experience) === null || _formData_experience === void 0 ? void 0 : _formData_experience.filter({\n                \"RedesignedResumeBuilder.useCallback[getCompletionPercentage]\": (exp)=>exp.title && exp.company\n            }[\"RedesignedResumeBuilder.useCallback[getCompletionPercentage]\"]);\n            if ((validExp === null || validExp === void 0 ? void 0 : validExp.length) > 0) completedFields += 2;\n            // Education (at least 1 entry with degree and institution)\n            totalFields += 2;\n            const validEdu = (_formData_education = formData.education) === null || _formData_education === void 0 ? void 0 : _formData_education.filter({\n                \"RedesignedResumeBuilder.useCallback[getCompletionPercentage]\": (edu)=>edu.degree && edu.institution\n            }[\"RedesignedResumeBuilder.useCallback[getCompletionPercentage]\"]);\n            if ((validEdu === null || validEdu === void 0 ? void 0 : validEdu.length) > 0) completedFields += 2;\n            // Skills (optional but counts if present)\n            if (((_formData_skills = formData.skills) === null || _formData_skills === void 0 ? void 0 : (_formData_skills_technical = _formData_skills.technical) === null || _formData_skills_technical === void 0 ? void 0 : _formData_skills_technical.length) > 0) {\n                totalFields += 1;\n                completedFields += 1;\n            }\n            return Math.round(completedFields / totalFields * 100);\n        }\n    }[\"RedesignedResumeBuilder.useCallback[getCompletionPercentage]\"], [\n        formData\n    ]);\n    // Render step content\n    const renderStepContent = ()=>{\n        const commonProps = {\n            formData,\n            updateFormData,\n            addArrayItem,\n            removeArrayItem,\n            validationErrors\n        };\n        switch(currentStep){\n            case 0:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_UniversalFormFields__WEBPACK_IMPORTED_MODULE_7__.UniversalPersonalForm, {\n                    ...commonProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                    lineNumber: 306,\n                    columnNumber: 16\n                }, undefined);\n            case 1:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_UniversalFormFields__WEBPACK_IMPORTED_MODULE_7__.UniversalExperienceForm, {\n                    ...commonProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                    lineNumber: 308,\n                    columnNumber: 16\n                }, undefined);\n            case 2:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_UniversalFormFields__WEBPACK_IMPORTED_MODULE_7__.UniversalEducationForm, {\n                    ...commonProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                    lineNumber: 310,\n                    columnNumber: 16\n                }, undefined);\n            case 3:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_UniversalFormFields__WEBPACK_IMPORTED_MODULE_7__.UniversalSkillsForm, {\n                    ...commonProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                    lineNumber: 312,\n                    columnNumber: 16\n                }, undefined);\n            case 4:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                        className: \"bg-gray-900/60 backdrop-blur-md rounded-2xl border border-gray-700/50 p-8 text-center\",\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center gap-3 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                                className: \"h-8 w-8 text-neural-pink\",\n                                                animate: {\n                                                    scale: [\n                                                        1,\n                                                        1.2,\n                                                        1\n                                                    ]\n                                                },\n                                                transition: {\n                                                    duration: 2,\n                                                    repeat: Infinity\n                                                },\n                                                children: \"⚡\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                                lineNumber: 323,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-neural-pink opacity-20 rounded-full blur-md\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-semibold text-white\",\n                                        children: \"Resume Created in Minutes!\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                        lineNumber: 332,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                lineNumber: 321,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-300 mb-4 max-w-md mx-auto\",\n                                children: \"You've turned hours of work into minutes with AI. Your professional resume is ready for job applications.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                lineNumber: 337,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center gap-4 text-sm text-gray-400 mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"flex items-center gap-1\",\n                                        children: \"✅ ATS-Optimized\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                        lineNumber: 342,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"flex items-center gap-1\",\n                                        children: \"✅ Job-Ready Format\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                        lineNumber: 345,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"flex items-center gap-1\",\n                                        children: \"✅ Professional Design\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                        lineNumber: 348,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                lineNumber: 341,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowTemplateSelector(true),\n                                        className: \"px-6 py-3 bg-gray-800 hover:bg-gray-700 text-white rounded-lg transition-colors border border-gray-600\",\n                                        children: \"Change Template\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                        lineNumber: 354,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowFullscreenPreview(true),\n                                        className: \"px-6 py-3 bg-gradient-to-r from-neural-purple to-neural-pink text-white rounded-lg hover:opacity-90 transition-opacity\",\n                                        children: \"Preview Fullscreen\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                        lineNumber: 360,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.info('Upgrade to Pro to download your resume!'),\n                                        className: \"px-6 py-3 bg-gradient-to-r from-yellow-500 to-orange-500 text-white rounded-lg hover:opacity-90 transition-opacity\",\n                                        children: \"Upgrade to Download\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                        lineNumber: 366,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                lineNumber: 353,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 p-4 bg-yellow-500/10 border border-yellow-500/20 rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-yellow-400 text-sm\",\n                                    children: \"\\uD83D\\uDCA1 Upgrade to Pro to download PDF, access premium templates, and unlock AI enhancements\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                    lineNumber: 375,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                lineNumber: 374,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                        lineNumber: 316,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                    lineNumber: 315,\n                    columnNumber: 11\n                }, undefined);\n            default:\n                return null;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-b from-black to-[#0A0A0A] text-white relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 opacity-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 bg-[url('/grid.svg')] [mask-image:radial-gradient(ellipse_at_center,white,transparent)]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                    lineNumber: 391,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                lineNumber: 390,\n                columnNumber: 7\n            }, undefined),\n            !hideHeader && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 pt-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                        className: \"text-center\",\n                        initial: {\n                            opacity: 0,\n                            y: -20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center gap-2 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                                className: \"h-8 w-8 text-neural-pink\",\n                                                animate: {\n                                                    rotate: 360\n                                                },\n                                                transition: {\n                                                    duration: 20,\n                                                    repeat: Infinity,\n                                                    ease: \"linear\"\n                                                },\n                                                children: \"✨\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                                lineNumber: 405,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-neural-pink opacity-20 rounded-full blur-md\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                                lineNumber: 412,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                        lineNumber: 404,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-semibold text-neural-blue\",\n                                        children: \"Turn Hours Into Minutes — With AI\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                        lineNumber: 414,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                lineNumber: 403,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl lg:text-4xl font-bold text-white mb-4\",\n                                children: \"AI Resume Builder\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                lineNumber: 419,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-300 text-lg max-w-2xl mx-auto mb-2\",\n                                children: \"Create ATS-optimized resumes in minutes with AI-powered suggestions\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                lineNumber: 423,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap items-center justify-center gap-4 text-sm text-gray-400\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"flex items-center gap-1\",\n                                        children: \"⚡ Create Resume in 5 minutes\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                        lineNumber: 428,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"flex items-center gap-1\",\n                                        children: \"\\uD83C\\uDFAF ATS Scoring\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                        lineNumber: 431,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"flex items-center gap-1\",\n                                        children: \"\\uD83D\\uDE80 Job-Specific Content\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                        lineNumber: 434,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                lineNumber: 427,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                        lineNumber: 398,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                    lineNumber: 397,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                lineNumber: 396,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 pb-32\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col xl:flex-row gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 max-w-4xl\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.AnimatePresence, {\n                                    mode: \"wait\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            x: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        exit: {\n                                            opacity: 0,\n                                            x: -20\n                                        },\n                                        transition: {\n                                            duration: 0.3\n                                        },\n                                        children: renderStepContent()\n                                    }, currentStep, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                        lineNumber: 450,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                    lineNumber: 449,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                lineNumber: 448,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden xl:block xl:w-80 xl:flex-shrink-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"sticky top-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StreamlinedPreview__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        formData: formData,\n                                        selectedTemplate: selectedTemplate,\n                                        onOpenFullscreen: ()=>setShowFullscreenPreview(true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                        lineNumber: 465,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                    lineNumber: 464,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                lineNumber: 463,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                        lineNumber: 446,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                    lineNumber: 445,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                lineNumber: 444,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SimplifiedNavigation__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                currentStep: currentStep,\n                totalSteps: steps.length,\n                onPrevious: prevStep,\n                onNext: nextStep,\n                onSave: handleSave,\n                onDownload: handleDownload,\n                onHome: ()=>window.location.href = '/',\n                canProceed: canProceedToNextStep(currentStep),\n                isSaving: isSaving,\n                completionPercentage: getCompletionPercentage(),\n                stepTitles: steps.map((step)=>step.title)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                lineNumber: 477,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.AnimatePresence, {\n                children: showTemplateSelector && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_UniversalTemplateSelector__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    selectedTemplate: selectedTemplate,\n                    onTemplateSelect: setSelectedTemplate,\n                    onClose: ()=>setShowTemplateSelector(false),\n                    formData: formData\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                    lineNumber: 494,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                lineNumber: 492,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.AnimatePresence, {\n                children: showFullscreenPreview && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FullscreenPreviewModal__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    isOpen: showFullscreenPreview,\n                    onClose: ()=>setShowFullscreenPreview(false),\n                    formData: formData,\n                    selectedTemplate: selectedTemplate,\n                    onTemplateChange: setSelectedTemplate\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                    lineNumber: 505,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                lineNumber: 503,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n        lineNumber: 388,\n        columnNumber: 5\n    }, undefined);\n};\n_s(RedesignedResumeBuilder, \"DLu6Gwuw9dLx2aADXayQf6m5jMU=\");\n_c = RedesignedResumeBuilder;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (RedesignedResumeBuilder);\nvar _c;\n$RefreshReg$(_c, \"RedesignedResumeBuilder\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/resume/RedesignedResumeBuilder.jsx\n"));

/***/ })

});
'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Upload, 
  Zap, 
  FileText, 
  CheckCircle, 
  AlertCircle, 
  BarChart3,
  Download,
  Eye,
  Sparkles
} from 'lucide-react';
import { toast } from 'react-hot-toast';

const ClassicMode = () => {
  const [uploadedFile, setUploadedFile] = useState(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisResult, setAnalysisResult] = useState(null);
  const [analysisType, setAnalysisType] = useState(null); // 'quick' or 'improvement'

  const handleFileUpload = (event, type) => {
    const file = event.target.files[0];
    
    if (!file) return;

    // Validate file type - only PDF allowed as per requirements
    if (file.type !== 'application/pdf') {
      toast.error('Please upload PDF files only');
      return;
    }

    // Validate file size (10MB limit)
    if (file.size > 10 * 1024 * 1024) {
      toast.error('File size must be less than 10MB');
      return;
    }

    setUploadedFile(file);
    performAnalysis(file, type);
  };

  const performAnalysis = async (file, type) => {
    setIsAnalyzing(true);
    setAnalysisType(type);
    setAnalysisResult(null);

    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('analysisType', type === 'quick' ? 'quick' : 'full');

      const response = await fetch('/api/upload-resume', {
        method: 'POST',
        body: formData,
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Analysis failed');
      }

      setAnalysisResult(result);
      toast.success(`${type === 'quick' ? 'Quick ATS Check' : 'Resume Analysis'} completed!`);
    } catch (error) {
      console.error('Analysis error:', error);
      toast.error(error.message || 'Failed to analyze resume');
    } finally {
      setIsAnalyzing(false);
    }
  };

  const resetAnalysis = () => {
    setUploadedFile(null);
    setAnalysisResult(null);
    setAnalysisType(null);
  };

  const getScoreColor = (score) => {
    if (score >= 80) return 'text-green-400';
    if (score >= 60) return 'text-yellow-400';
    return 'text-red-400';
  };

  const getScoreBackground = (score) => {
    if (score >= 80) return 'from-green-500/20 to-green-600/20 border-green-500/30';
    if (score >= 60) return 'from-yellow-500/20 to-yellow-600/20 border-yellow-500/30';
    return 'from-red-500/20 to-red-600/20 border-red-500/30';
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-black via-gray-950 to-black">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0 bg-[url('/grid.svg')] [mask-image:radial-gradient(ellipse_at_center,white,transparent)]" />
      </div>

      <div className="relative container mx-auto px-6 py-12">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-neural-purple to-neural-pink bg-clip-text text-transparent mb-4">
            Classic Resume Builder
          </h1>
          <p className="text-gray-400 text-lg max-w-2xl mx-auto">
            Upload your existing resume for instant ATS analysis and improvement suggestions
          </p>
        </motion.div>

        {!analysisResult ? (
          /* Upload Section */
          <div className="max-w-4xl mx-auto">
            <div className="grid md:grid-cols-2 gap-8">
              {/* Button 1: Upload for ATS Improvement */}
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.1 }}
                className="bg-gray-900/20 backdrop-blur-md rounded-2xl p-8 border border-white/10 hover:border-neural-purple/30 transition-all duration-300"
              >
                <div className="text-center">
                  <div className="w-16 h-16 bg-neural-purple/20 rounded-xl flex items-center justify-center mx-auto mb-6">
                    <Upload className="h-8 w-8 text-neural-purple" />
                  </div>
                  <h3 className="text-2xl font-bold text-white mb-4">Upload for ATS Improvement</h3>
                  <p className="text-gray-300 mb-6">
                    Get detailed analysis and improvement suggestions to make your resume ATS-friendly
                  </p>
                  
                  <label className="block">
                    <input
                      type="file"
                      accept=".pdf"
                      onChange={(e) => handleFileUpload(e, 'improvement')}
                      className="hidden"
                      disabled={isAnalyzing}
                    />
                    <div className="bg-gradient-to-r from-neural-purple to-neural-pink text-white px-6 py-3 rounded-lg font-medium hover:opacity-90 transition-opacity cursor-pointer inline-flex items-center gap-2">
                      {isAnalyzing && analysisType === 'improvement' ? (
                        <>
                          <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                          Analyzing...
                        </>
                      ) : (
                        <>
                          <Upload className="h-4 w-4" />
                          Upload PDF Resume
                        </>
                      )}
                    </div>
                  </label>
                </div>
              </motion.div>

              {/* Button 2: Quick ATS Check */}
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="bg-gray-900/20 backdrop-blur-md rounded-2xl p-8 border border-white/10 hover:border-neural-blue/30 transition-all duration-300"
              >
                <div className="text-center">
                  <div className="w-16 h-16 bg-neural-blue/20 rounded-xl flex items-center justify-center mx-auto mb-6">
                    <Zap className="h-8 w-8 text-neural-blue" />
                  </div>
                  <h3 className="text-2xl font-bold text-white mb-4">Quick ATS Check</h3>
                  <p className="text-gray-300 mb-6">
                    Get instant ATS score and quick analysis of your resume's compatibility
                  </p>
                  
                  <label className="block">
                    <input
                      type="file"
                      accept=".pdf"
                      onChange={(e) => handleFileUpload(e, 'quick')}
                      className="hidden"
                      disabled={isAnalyzing}
                    />
                    <div className="bg-gradient-to-r from-neural-blue to-cyan-500 text-white px-6 py-3 rounded-lg font-medium hover:opacity-90 transition-opacity cursor-pointer inline-flex items-center gap-2">
                      {isAnalyzing && analysisType === 'quick' ? (
                        <>
                          <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                          Analyzing...
                        </>
                      ) : (
                        <>
                          <Zap className="h-4 w-4" />
                          Quick Check PDF
                        </>
                      )}
                    </div>
                  </label>
                </div>
              </motion.div>
            </div>

            {/* File Format Notice */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="mt-8 text-center"
            >
              <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4 max-w-md mx-auto">
                <div className="flex items-center gap-2 justify-center text-blue-400">
                  <FileText className="h-4 w-4" />
                  <span className="text-sm font-medium">PDF Format Only</span>
                </div>
                <p className="text-xs text-gray-400 mt-1">
                  For best results, upload your resume in PDF format
                </p>
              </div>
            </motion.div>
          </div>
        ) : (
          /* Results Section */
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="max-w-4xl mx-auto"
          >
            {/* File Info */}
            <div className="bg-gray-900/20 backdrop-blur-md rounded-2xl p-6 border border-white/10 mb-8">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <FileText className="h-6 w-6 text-neural-purple" />
                  <div>
                    <h3 className="text-white font-medium">{uploadedFile?.name}</h3>
                    <p className="text-gray-400 text-sm">
                      {(uploadedFile?.size / 1024 / 1024).toFixed(2)} MB • {analysisType === 'quick' ? 'Quick ATS Check' : 'Full Analysis'}
                    </p>
                  </div>
                </div>
                <button
                  onClick={resetAnalysis}
                  className="text-gray-400 hover:text-white transition-colors"
                >
                  Upload New Resume
                </button>
              </div>
            </div>

            {/* ATS Score */}
            <div className={`bg-gradient-to-r ${getScoreBackground(analysisResult.atsScore?.overall || 0)} backdrop-blur-md rounded-2xl p-8 border mb-8`}>
              <div className="text-center">
                <h2 className="text-2xl font-bold text-white mb-4">ATS Score</h2>
                <div className={`text-6xl font-bold ${getScoreColor(analysisResult.atsScore?.overall || 0)} mb-4`}>
                  {analysisResult.atsScore?.overall || 0}%
                </div>
                <p className="text-gray-300">
                  {analysisResult.atsScore?.overall >= 80 ? 'Excellent ATS compatibility!' :
                   analysisResult.atsScore?.overall >= 60 ? 'Good, but room for improvement' :
                   'Needs significant improvement'}
                </p>
              </div>
            </div>

            {/* Analysis Results */}
            {analysisResult.analysis && (
              <div className="grid md:grid-cols-2 gap-6 mb-8">
                {/* Strengths */}
                <div className="bg-green-500/10 border border-green-500/20 rounded-2xl p-6">
                  <h3 className="text-xl font-bold text-green-400 mb-4 flex items-center gap-2">
                    <CheckCircle className="h-5 w-5" />
                    Strengths
                  </h3>
                  <ul className="space-y-2">
                    {analysisResult.analysis.strengths?.map((strength, index) => (
                      <li key={index} className="text-gray-300 text-sm flex items-start gap-2">
                        <div className="w-1.5 h-1.5 bg-green-400 rounded-full mt-2 flex-shrink-0"></div>
                        {strength}
                      </li>
                    ))}
                  </ul>
                </div>

                {/* Recommendations */}
                <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-2xl p-6">
                  <h3 className="text-xl font-bold text-yellow-400 mb-4 flex items-center gap-2">
                    <AlertCircle className="h-5 w-5" />
                    Recommendations
                  </h3>
                  <ul className="space-y-2">
                    {analysisResult.analysis.recommendations?.map((rec, index) => (
                      <li key={index} className="text-gray-300 text-sm flex items-start gap-2">
                        <div className="w-1.5 h-1.5 bg-yellow-400 rounded-full mt-2 flex-shrink-0"></div>
                        {rec}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex flex-wrap gap-4 justify-center">
              <button
                onClick={resetAnalysis}
                className="bg-gray-700 hover:bg-gray-600 text-white px-6 py-3 rounded-lg font-medium transition-colors"
              >
                Analyze Another Resume
              </button>
              <button
                onClick={() => window.location.href = '/resume-builder?mode=enhanced'}
                className="bg-gradient-to-r from-neural-purple to-neural-pink text-white px-6 py-3 rounded-lg font-medium hover:opacity-90 transition-opacity inline-flex items-center gap-2"
              >
                <Sparkles className="h-4 w-4" />
                Build New Resume
              </button>
            </div>
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default ClassicMode;

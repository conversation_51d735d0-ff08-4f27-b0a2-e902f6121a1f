/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/layout"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CNewBlinkFindAI%5C%5Cfrontend-master%5C%5Cnode_modules%5C%5C%40next%5C%5Cthird-parties%5C%5Cdist%5C%5Cgoogle%5C%5Cga.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CNewBlinkFindAI%5C%5Cfrontend-master%5C%5Cnode_modules%5C%5C%40next%5C%5Cthird-parties%5C%5Cdist%5C%5Cgoogle%5C%5Cgtm.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CNewBlinkFindAI%5C%5Cfrontend-master%5C%5Cnode_modules%5C%5C%40next%5C%5Cthird-parties%5C%5Cdist%5C%5CThirdPartyScriptEmbed.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CNewBlinkFindAI%5C%5Cfrontend-master%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CNewBlinkFindAI%5C%5Cfrontend-master%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CNewBlinkFindAI%5C%5Cfrontend-master%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CNewBlinkFindAI%5C%5Cfrontend-master%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CNewBlinkFindAI%5C%5Cfrontend-master%5C%5Csrc%5C%5Ccomponents%5C%5Ccommon%5C%5CVisitorTracker.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CNewBlinkFindAI%5C%5Cfrontend-master%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CNavbar.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CNewBlinkFindAI%5C%5Cfrontend-master%5C%5Csrc%5C%5Ccontexts%5C%5CAdminContext.jsx%22%2C%22ids%22%3A%5B%22AdminProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CNewBlinkFindAI%5C%5Cfrontend-master%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.jsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=false!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CNewBlinkFindAI%5C%5Cfrontend-master%5C%5Cnode_modules%5C%5C%40next%5C%5Cthird-parties%5C%5Cdist%5C%5Cgoogle%5C%5Cga.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CNewBlinkFindAI%5C%5Cfrontend-master%5C%5Cnode_modules%5C%5C%40next%5C%5Cthird-parties%5C%5Cdist%5C%5Cgoogle%5C%5Cgtm.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CNewBlinkFindAI%5C%5Cfrontend-master%5C%5Cnode_modules%5C%5C%40next%5C%5Cthird-parties%5C%5Cdist%5C%5CThirdPartyScriptEmbed.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CNewBlinkFindAI%5C%5Cfrontend-master%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CNewBlinkFindAI%5C%5Cfrontend-master%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CNewBlinkFindAI%5C%5Cfrontend-master%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CNewBlinkFindAI%5C%5Cfrontend-master%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CNewBlinkFindAI%5C%5Cfrontend-master%5C%5Csrc%5C%5Ccomponents%5C%5Ccommon%5C%5CVisitorTracker.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CNewBlinkFindAI%5C%5Cfrontend-master%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CNavbar.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CNewBlinkFindAI%5C%5Cfrontend-master%5C%5Csrc%5C%5Ccontexts%5C%5CAdminContext.jsx%22%2C%22ids%22%3A%5B%22AdminProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CNewBlinkFindAI%5C%5Cfrontend-master%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.jsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=false! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/@next/third-parties/dist/google/ga.js */ \"(app-pages-browser)/./node_modules/@next/third-parties/dist/google/ga.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/@next/third-parties/dist/google/gtm.js */ \"(app-pages-browser)/./node_modules/@next/third-parties/dist/google/gtm.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/@next/third-parties/dist/ThirdPartyScriptEmbed.js */ \"(app-pages-browser)/./node_modules/@next/third-parties/dist/ThirdPartyScriptEmbed.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/script.js */ \"(app-pages-browser)/./node_modules/next/dist/client/script.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/react-hot-toast/dist/index.mjs */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/globals.css */ \"(app-pages-browser)/./src/app/globals.css\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/common/VisitorTracker.jsx */ \"(app-pages-browser)/./src/components/common/VisitorTracker.jsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/Navbar.jsx */ \"(app-pages-browser)/./src/components/layout/Navbar.jsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AdminContext.jsx */ \"(app-pages-browser)/./src/contexts/AdminContext.jsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AuthContext.jsx */ \"(app-pages-browser)/./src/contexts/AuthContext.jsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CNewBlinkFindAI%5C%5Cfrontend-master%5C%5Cnode_modules%5C%5C%40next%5C%5Cthird-parties%5C%5Cdist%5C%5Cgoogle%5C%5Cga.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CNewBlinkFindAI%5C%5Cfrontend-master%5C%5Cnode_modules%5C%5C%40next%5C%5Cthird-parties%5C%5Cdist%5C%5Cgoogle%5C%5Cgtm.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CNewBlinkFindAI%5C%5Cfrontend-master%5C%5Cnode_modules%5C%5C%40next%5C%5Cthird-parties%5C%5Cdist%5C%5CThirdPartyScriptEmbed.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CNewBlinkFindAI%5C%5Cfrontend-master%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CNewBlinkFindAI%5C%5Cfrontend-master%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CNewBlinkFindAI%5C%5Cfrontend-master%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CNewBlinkFindAI%5C%5Cfrontend-master%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CNewBlinkFindAI%5C%5Cfrontend-master%5C%5Csrc%5C%5Ccomponents%5C%5Ccommon%5C%5CVisitorTracker.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CNewBlinkFindAI%5C%5Cfrontend-master%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CNavbar.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CNewBlinkFindAI%5C%5Cfrontend-master%5C%5Csrc%5C%5Ccontexts%5C%5CAdminContext.jsx%22%2C%22ids%22%3A%5B%22AdminProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CNewBlinkFindAI%5C%5Cfrontend-master%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.jsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"28bfc82491cd\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcUHJvamVjdHNcXE5ld0JsaW5rRmluZEFJXFxmcm9udGVuZC1tYXN0ZXJcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjI4YmZjODI0OTFjZFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/common/VisitorTracker.jsx":
/*!**************************************************!*\
  !*** ./src/components/common/VisitorTracker.jsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _services_VisitorService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/VisitorService */ \"(app-pages-browser)/./src/services/VisitorService.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ var _s = $RefreshSig$();\n\n\n\nconst VisitorTracker = ()=>{\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname)();\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"VisitorTracker.useEffect\": ()=>{\n            // Track visitor on mount (only once per session)\n            _services_VisitorService__WEBPACK_IMPORTED_MODULE_2__[\"default\"].trackVisitor();\n        }\n    }[\"VisitorTracker.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"VisitorTracker.useEffect\": ()=>{\n            // Track page view on route change\n            if (pathname) {\n                _services_VisitorService__WEBPACK_IMPORTED_MODULE_2__[\"default\"].trackPageView(pathname);\n            }\n        }\n    }[\"VisitorTracker.useEffect\"], [\n        pathname\n    ]);\n    // This component doesn't render anything\n    return null;\n};\n_s(VisitorTracker, \"tjXKfJWuFDa0epp0CJaCeazyqhM=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname\n    ];\n});\n_c = VisitorTracker;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (VisitorTracker);\nvar _c;\n$RefreshReg$(_c, \"VisitorTracker\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2NvbW1vbi9WaXNpdG9yVHJhY2tlci5qc3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBRWtDO0FBQ1k7QUFDUztBQUV2RCxNQUFNRyxpQkFBaUI7O0lBQ3JCLE1BQU1DLFdBQVdILDREQUFXQTtJQUU1QkQsZ0RBQVNBO29DQUFDO1lBQ1IsaURBQWlEO1lBQ2pERSxnRUFBY0EsQ0FBQ0csWUFBWTtRQUM3QjttQ0FBRyxFQUFFO0lBRUxMLGdEQUFTQTtvQ0FBQztZQUNSLGtDQUFrQztZQUNsQyxJQUFJSSxVQUFVO2dCQUNaRixnRUFBY0EsQ0FBQ0ksYUFBYSxDQUFDRjtZQUMvQjtRQUNGO21DQUFHO1FBQUNBO0tBQVM7SUFFYix5Q0FBeUM7SUFDekMsT0FBTztBQUNUO0dBakJNRDs7UUFDYUYsd0RBQVdBOzs7S0FEeEJFO0FBbUJOLGlFQUFlQSxjQUFjQSxFQUFDIiwic291cmNlcyI6WyJDOlxcUHJvamVjdHNcXE5ld0JsaW5rRmluZEFJXFxmcm9udGVuZC1tYXN0ZXJcXHNyY1xcY29tcG9uZW50c1xcY29tbW9uXFxWaXNpdG9yVHJhY2tlci5qc3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyB1c2VQYXRobmFtZSB9IGZyb20gJ25leHQvbmF2aWdhdGlvbic7XG5pbXBvcnQgdmlzaXRvclNlcnZpY2UgZnJvbSAnQC9zZXJ2aWNlcy9WaXNpdG9yU2VydmljZSc7XG5cbmNvbnN0IFZpc2l0b3JUcmFja2VyID0gKCkgPT4ge1xuICBjb25zdCBwYXRobmFtZSA9IHVzZVBhdGhuYW1lKCk7XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICAvLyBUcmFjayB2aXNpdG9yIG9uIG1vdW50IChvbmx5IG9uY2UgcGVyIHNlc3Npb24pXG4gICAgdmlzaXRvclNlcnZpY2UudHJhY2tWaXNpdG9yKCk7XG4gIH0sIFtdKTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIC8vIFRyYWNrIHBhZ2UgdmlldyBvbiByb3V0ZSBjaGFuZ2VcbiAgICBpZiAocGF0aG5hbWUpIHtcbiAgICAgIHZpc2l0b3JTZXJ2aWNlLnRyYWNrUGFnZVZpZXcocGF0aG5hbWUpO1xuICAgIH1cbiAgfSwgW3BhdGhuYW1lXSk7XG5cbiAgLy8gVGhpcyBjb21wb25lbnQgZG9lc24ndCByZW5kZXIgYW55dGhpbmdcbiAgcmV0dXJuIG51bGw7XG59O1xuXG5leHBvcnQgZGVmYXVsdCBWaXNpdG9yVHJhY2tlcjtcbiJdLCJuYW1lcyI6WyJ1c2VFZmZlY3QiLCJ1c2VQYXRobmFtZSIsInZpc2l0b3JTZXJ2aWNlIiwiVmlzaXRvclRyYWNrZXIiLCJwYXRobmFtZSIsInRyYWNrVmlzaXRvciIsInRyYWNrUGFnZVZpZXciXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/common/VisitorTracker.jsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/layout/Navbar.jsx":
/*!******************************************!*\
  !*** ./src/components/layout/Navbar.jsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_LogIn_LogOut_Menu_Sparkles_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=LogIn,LogOut,Menu,Sparkles,User,UserPlus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_LogIn_LogOut_Menu_Sparkles_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=LogIn,LogOut,Menu,Sparkles,User,UserPlus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_LogIn_LogOut_Menu_Sparkles_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=LogIn,LogOut,Menu,Sparkles,User,UserPlus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_LogIn_LogOut_Menu_Sparkles_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=LogIn,LogOut,Menu,Sparkles,User,UserPlus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_LogIn_LogOut_Menu_Sparkles_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=LogIn,LogOut,Menu,Sparkles,User,UserPlus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_LogIn_LogOut_Menu_Sparkles_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=LogIn,LogOut,Menu,Sparkles,User,UserPlus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-in.js\");\n/* harmony import */ var _barrel_optimize_names_LogIn_LogOut_Menu_Sparkles_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=LogIn,LogOut,Menu,Sparkles,User,UserPlus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.jsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst Navbar = ()=>{\n    _s();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { currentUser, logout } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const [role, setRole] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const links = [\n        {\n            name: 'Home',\n            href: '/'\n        },\n        {\n            name: 'About Us',\n            href: '/aboutus'\n        },\n        {\n            name: 'Resume Builder',\n            href: '/resume-builder'\n        }\n    ] // Check mounting state to avoid hydration mismatch\n    ;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Navbar.useEffect\": ()=>{\n            setMounted(true);\n            console.log('Navbar mounted, links:', links) // Debug log\n            ;\n            console.log('Current user:', currentUser) // Debug log\n            ;\n        }\n    }[\"Navbar.useEffect\"], []);\n    // Check if the user is logged in and set the role on initial render\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Navbar.useEffect\": ()=>{\n            if (mounted) {\n                const storedRole = localStorage.getItem('role');\n                setRole(storedRole);\n            }\n        }\n    }[\"Navbar.useEffect\"], [\n        currentUser,\n        mounted\n    ]);\n    const handleLoginLogout = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Navbar.useCallback[handleLoginLogout]\": async ()=>{\n            if (currentUser) {\n                // User is logged in, perform logout\n                try {\n                    await logout();\n                    if (mounted) {\n                        localStorage.removeItem('role');\n                        setRole(null);\n                    }\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].success('Logged out successfully');\n                    router.push('/');\n                    setIsOpen(false);\n                } catch (error) {\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error('Error logging out');\n                }\n            } else {\n                // User is not logged in, redirect to login\n                router.push('/login');\n                setIsOpen(false);\n            }\n        }\n    }[\"Navbar.useCallback[handleLoginLogout]\"], [\n        currentUser,\n        logout,\n        router,\n        mounted\n    ]);\n    const handleNavItemClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Navbar.useCallback[handleNavItemClick]\": (href)=>{\n            if (href.startsWith('#')) {\n                if (pathname !== '/') {\n                    router.push(\"/\".concat(href));\n                } else {\n                    // Scroll to section if on home page\n                    const element = document.querySelector(href);\n                    if (element) {\n                        element.scrollIntoView({\n                            behavior: 'smooth'\n                        });\n                    }\n                }\n            }\n            setIsOpen(false);\n        }\n    }[\"Navbar.useCallback[handleNavItemClick]\"], [\n        pathname,\n        router\n    ]);\n    // Prevent body scroll when menu is open and handle escape key\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Navbar.useEffect\": ()=>{\n            if (isOpen) {\n                document.body.style.overflow = 'hidden';\n                document.body.style.position = 'fixed';\n                document.body.style.top = '-' + window.scrollY + 'px';\n                document.body.style.width = '100%';\n                const handleEscape = {\n                    \"Navbar.useEffect.handleEscape\": (e)=>{\n                        if (e.key === 'Escape') {\n                            setIsOpen(false);\n                        }\n                    }\n                }[\"Navbar.useEffect.handleEscape\"];\n                document.addEventListener('keydown', handleEscape);\n                return ({\n                    \"Navbar.useEffect\": ()=>{\n                        const scrollY = document.body.style.top;\n                        document.body.style.overflow = '';\n                        document.body.style.position = '';\n                        document.body.style.top = '';\n                        document.body.style.width = '';\n                        window.scrollTo(0, parseInt(scrollY || '0') * -1);\n                        document.removeEventListener('keydown', handleEscape);\n                    }\n                })[\"Navbar.useEffect\"];\n            } else {\n                document.body.style.overflow = '';\n                document.body.style.position = '';\n                document.body.style.top = '';\n                document.body.style.width = '';\n            }\n        }\n    }[\"Navbar.useEffect\"], [\n        isOpen\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Navbar.useEffect\": ()=>{\n            const handleResize = {\n                \"Navbar.useEffect.handleResize\": ()=>{\n                    if (window.innerWidth >= 1024) {\n                        setIsOpen(false);\n                    }\n                }\n            }[\"Navbar.useEffect.handleResize\"];\n            const handleOrientationChange = {\n                \"Navbar.useEffect.handleOrientationChange\": ()=>{\n                    // Close menu on orientation change\n                    setIsOpen(false);\n                }\n            }[\"Navbar.useEffect.handleOrientationChange\"];\n            window.addEventListener('resize', handleResize);\n            window.addEventListener('orientationchange', handleOrientationChange);\n            return ({\n                \"Navbar.useEffect\": ()=>{\n                    window.removeEventListener('resize', handleResize);\n                    window.removeEventListener('orientationchange', handleOrientationChange);\n                }\n            })[\"Navbar.useEffect\"];\n        }\n    }[\"Navbar.useEffect\"], []);\n    if (!mounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n            className: \"fixed inset-x-0 top-0 z-50 backdrop-blur-md bg-black/50 border-b border-white/10\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"flex items-center justify-between px-4 py-3 sm:px-6 lg:px-8 min-h-[64px]\",\n                \"aria-label\": \"Global\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex lg:flex-1\",\n                    children: [\n                        \"            \",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/\",\n                            className: \"-m-1.5 p-1.5 flex items-center gap-2 touch-manipulation\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogIn_LogOut_Menu_Sparkles_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-6 w-6 sm:h-7 sm:w-7 text-purple-500 flex-shrink-0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Navbar.jsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-mono font-bold text-white text-base sm:text-lg\",\n                                    children: [\n                                        \"Blink\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-purple-500\",\n                                            children: \"Find\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Navbar.jsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 22\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Navbar.jsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Navbar.jsx\",\n                            lineNumber: 126,\n                            columnNumber: 55\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Navbar.jsx\",\n                    lineNumber: 126,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Navbar.jsx\",\n                lineNumber: 125,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Navbar.jsx\",\n            lineNumber: 124,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"fixed inset-x-0 top-0 z-50 backdrop-blur-md bg-black/50 border-b border-white/10\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"flex items-center justify-between px-4 py-3 sm:px-6 lg:px-8 min-h-[64px]\",\n                \"aria-label\": \"Global\",\n                children: [\n                    \"        \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex lg:flex-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/\",\n                            className: \"-m-1.5 p-1.5 flex items-center gap-2 touch-manipulation\",\n                            onClick: ()=>setIsOpen(false),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogIn_LogOut_Menu_Sparkles_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-6 w-6 sm:h-7 sm:w-7 text-purple-500 flex-shrink-0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Navbar.jsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-mono font-bold text-white text-base sm:text-lg\",\n                                    children: [\n                                        \"Blink\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-purple-500\",\n                                            children: \"Find\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Navbar.jsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 20\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Navbar.jsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Navbar.jsx\",\n                            lineNumber: 141,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Navbar.jsx\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex lg:hidden\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            className: \"-m-2.5 inline-flex items-center justify-center rounded-md p-2.5 text-gray-400 hover:text-white transition-colors touch-manipulation min-w-[44px] min-h-[44px]\",\n                            onClick: ()=>{\n                                console.log('Mobile menu button clicked, current state:', isOpen);\n                                setIsOpen(!isOpen);\n                            },\n                            \"aria-expanded\": isOpen,\n                            \"aria-label\": isOpen ? \"Close menu\" : \"Open menu\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"sr-only\",\n                                    children: [\n                                        isOpen ? \"Close\" : \"Open\",\n                                        \" main menu\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Navbar.jsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 13\n                                }, undefined),\n                                isOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogIn_LogOut_Menu_Sparkles_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"h-6 w-6\",\n                                    \"aria-hidden\": \"true\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Navbar.jsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogIn_LogOut_Menu_Sparkles_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-6 w-6\",\n                                    \"aria-hidden\": \"true\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Navbar.jsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Navbar.jsx\",\n                            lineNumber: 154,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Navbar.jsx\",\n                        lineNumber: 153,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden lg:flex lg:gap-x-8 xl:gap-x-12\",\n                        children: [\n                            links.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: item.href,\n                                    onClick: ()=>handleNavItemClick(item.href),\n                                    className: \"text-sm font-medium leading-6 transition-colors \".concat(pathname === item.href ? 'text-white' : 'text-gray-400 hover:text-white'),\n                                    children: item.name\n                                }, item.name, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Navbar.jsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 13\n                                }, undefined)),\n                            role === 'admin' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/admin/contact-forms\",\n                                className: \"text-sm font-medium leading-6 text-gray-400 hover:text-white transition-colors\",\n                                children: \"Admin\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Navbar.jsx\",\n                                lineNumber: 187,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Navbar.jsx\",\n                        lineNumber: 173,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden lg:flex lg:flex-1 lg:justify-end lg:items-center lg:gap-3 xl:gap-4\",\n                        children: [\n                            currentUser ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3 xl:gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/dashboard\",\n                                        className: \"flex items-center gap-2 text-sm font-medium leading-6 text-gray-400 hover:text-white transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogIn_LogOut_Menu_Sparkles_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Navbar.jsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden xl:inline\",\n                                                children: \"Dashboard\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Navbar.jsx\",\n                                                lineNumber: 205,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"xl:hidden\",\n                                                children: \"Profile\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Navbar.jsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Navbar.jsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleLoginLogout,\n                                        className: \"flex items-center gap-2 text-sm font-medium leading-6 text-gray-400 hover:text-white transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogIn_LogOut_Menu_Sparkles_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Navbar.jsx\",\n                                                lineNumber: 212,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Logout\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Navbar.jsx\",\n                                                lineNumber: 213,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Navbar.jsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Navbar.jsx\",\n                                lineNumber: 199,\n                                columnNumber: 13\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3 xl:gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/login\",\n                                        className: \"flex items-center gap-2 text-sm font-medium leading-6 text-gray-400 hover:text-white transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogIn_LogOut_Menu_Sparkles_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Navbar.jsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Login\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Navbar.jsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Navbar.jsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \"              \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/signup\",\n                                        className: \"flex items-center gap-2 bg-gradient-to-r from-purple-500 to-pink-500 text-white px-3 py-2 xl:px-4 xl:py-2 rounded-lg text-sm font-medium hover:opacity-90 transition-opacity\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogIn_LogOut_Menu_Sparkles_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Navbar.jsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Sign Up\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Navbar.jsx\",\n                                                lineNumber: 229,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Navbar.jsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 36\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Navbar.jsx\",\n                                lineNumber: 217,\n                                columnNumber: 13\n                            }, undefined),\n                            \"        \"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Navbar.jsx\",\n                        lineNumber: 197,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Navbar.jsx\",\n                lineNumber: 139,\n                columnNumber: 7\n            }, undefined),\n            \"      \",\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:hidden\",\n                children: [\n                    console.log('Mobile menu render, isOpen:', isOpen),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 z-40 bg-black/50 backdrop-blur-md\",\n                        onClick: ()=>setIsOpen(false),\n                        \"aria-hidden\": \"true\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Navbar.jsx\",\n                        lineNumber: 238,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-y-0 right-0 z-50 w-full max-w-sm bg-gray-900/95 backdrop-blur-xl border-l border-white/10 shadow-2xl\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex h-full flex-col\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between px-4 py-4 border-b border-gray-700/50 backdrop-blur-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/\",\n                                            className: \"flex items-center gap-2\",\n                                            onClick: ()=>setIsOpen(false),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogIn_LogOut_Menu_Sparkles_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"h-6 w-6 text-purple-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Navbar.jsx\",\n                                                    lineNumber: 254,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-mono font-bold text-white\",\n                                                    children: [\n                                                        \"BlinkFind\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-purple-500\",\n                                                            children: \"AI\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Navbar.jsx\",\n                                                            lineNumber: 256,\n                                                            columnNumber: 30\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Navbar.jsx\",\n                                                    lineNumber: 255,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Navbar.jsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            className: \"p-2 text-gray-400 hover:text-white rounded-lg hover:bg-white/10 transition-all\",\n                                            onClick: ()=>setIsOpen(false),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogIn_LogOut_Menu_Sparkles_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-6 w-6\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Navbar.jsx\",\n                                                lineNumber: 264,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Navbar.jsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Navbar.jsx\",\n                                    lineNumber: 248,\n                                    columnNumber: 15\n                                }, undefined),\n                                \"              \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 px-4 py-6 backdrop-blur-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-1\",\n                                            children: links.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: item.href,\n                                                    onClick: ()=>{\n                                                        handleNavItemClick(item.href);\n                                                        setIsOpen(false);\n                                                    },\n                                                    className: \"block px-3 py-2 text-sm font-medium text-gray-300 hover:bg-white/20 hover:text-white rounded-md backdrop-blur-lg transition-all border border-white/10 hover:border-white/30 bg-white/5\",\n                                                    children: item.name\n                                                }, item.name, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Navbar.jsx\",\n                                                    lineNumber: 271,\n                                                    columnNumber: 21\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Navbar.jsx\",\n                                            lineNumber: 269,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border-t border-gray-700/50 mt-4 pt-4 space-y-1\",\n                                            children: currentUser ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/dashboard\",\n                                                        onClick: ()=>setIsOpen(false),\n                                                        className: \"flex items-center gap-2 px-3 py-2 text-sm font-medium text-gray-300 hover:bg-white/20 hover:text-white rounded-md backdrop-blur-lg transition-all border border-white/10 hover:border-white/30 bg-white/5\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogIn_LogOut_Menu_Sparkles_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Navbar.jsx\",\n                                                                lineNumber: 294,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Dashboard\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Navbar.jsx\",\n                                                                lineNumber: 295,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Navbar.jsx\",\n                                                        lineNumber: 289,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleLoginLogout,\n                                                        className: \"flex items-center gap-2 px-3 py-2 text-sm font-medium text-gray-300 hover:bg-white/20 hover:text-white w-full text-left rounded-md backdrop-blur-lg transition-all border border-white/10 hover:border-white/30 bg-white/5\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogIn_LogOut_Menu_Sparkles_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Navbar.jsx\",\n                                                                lineNumber: 301,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Logout\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Navbar.jsx\",\n                                                                lineNumber: 302,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Navbar.jsx\",\n                                                        lineNumber: 297,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/login\",\n                                                        onClick: ()=>setIsOpen(false),\n                                                        className: \"flex items-center gap-2 px-3 py-2 text-sm font-medium text-gray-300 hover:bg-white/20 hover:text-white rounded-md backdrop-blur-lg transition-all border border-white/10 hover:border-white/30 bg-white/5\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogIn_LogOut_Menu_Sparkles_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Navbar.jsx\",\n                                                                lineNumber: 312,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Login\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Navbar.jsx\",\n                                                                lineNumber: 313,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Navbar.jsx\",\n                                                        lineNumber: 307,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/signup\",\n                                                        onClick: ()=>setIsOpen(false),\n                                                        className: \"flex items-center gap-2 px-3 py-2 text-sm font-medium bg-gradient-to-r from-purple-500/90 to-pink-500/90 text-white hover:from-purple-500 hover:to-pink-500 rounded-md backdrop-blur-lg transition-all shadow-md hover:shadow-purple-500/25 border border-purple-400/20\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogIn_LogOut_Menu_Sparkles_User_UserPlus_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Navbar.jsx\",\n                                                                lineNumber: 320,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Sign Up\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Navbar.jsx\",\n                                                                lineNumber: 321,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Navbar.jsx\",\n                                                        lineNumber: 315,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Navbar.jsx\",\n                                            lineNumber: 286,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Navbar.jsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Navbar.jsx\",\n                            lineNumber: 246,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Navbar.jsx\",\n                        lineNumber: 245,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Navbar.jsx\",\n                lineNumber: 235,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Navbar.jsx\",\n        lineNumber: 138,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Navbar, \"RBH89eSDy0Y/8oocG+8x/NRlZJo=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = Navbar;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Navbar);\nvar _c;\n$RefreshReg$(_c, \"Navbar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/layout/Navbar.jsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/contexts/AdminContext.jsx":
/*!***************************************!*\
  !*** ./src/contexts/AdminContext.jsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AdminProvider: () => (/* binding */ AdminProvider),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useAdmin: () => (/* binding */ useAdmin)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _firebase_config__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/firebase/config */ \"(app-pages-browser)/./src/firebase/config.js\");\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/auth */ \"(app-pages-browser)/./node_modules/firebase/auth/dist/esm/index.esm.js\");\n/* __next_internal_client_entry_do_not_use__ useAdmin,AdminProvider,default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\nconst AdminContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)();\n// Admin email addresses (from environment variables or fallback)\nconst getAdminEmails = ()=>{\n    if (true) {\n        // Client-side: use public env var\n        const adminEmailsEnv = \"<EMAIL>,<EMAIL>\";\n        if (adminEmailsEnv) {\n            return adminEmailsEnv.split(',').map((email)=>email.trim());\n        }\n        const adminDomain = \"blinkfind.com\" || 0;\n        return [\n            \"admin@\".concat(adminDomain),\n            \"superadmin@\".concat(adminDomain),\n            \"dev@\".concat(adminDomain)\n        ];\n    } else {}\n};\nconst ADMIN_EMAILS = getAdminEmails();\nconst useAdmin = ()=>{\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AdminContext);\n    if (!context) {\n        throw new Error('useAdmin must be used within an AdminProvider');\n    }\n    return context;\n};\n_s(useAdmin, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nconst AdminProvider = (param)=>{\n    let { children } = param;\n    _s1();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isAdmin, setIsAdmin] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminProvider.useEffect\": ()=>{\n            const unsubscribe = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_3__.onAuthStateChanged)(_firebase_config__WEBPACK_IMPORTED_MODULE_2__.auth, {\n                \"AdminProvider.useEffect.unsubscribe\": (currentUser)=>{\n                    setUser(currentUser);\n                    if (currentUser && currentUser.email) {\n                        // Check if user email is in admin list\n                        const isUserAdmin = ADMIN_EMAILS.includes(currentUser.email.toLowerCase());\n                        setIsAdmin(isUserAdmin);\n                        if (!isUserAdmin) {\n                            setError('Access denied. Admin privileges required.');\n                        } else {\n                            setError(null);\n                        }\n                    } else {\n                        setIsAdmin(false);\n                        setError(null);\n                    }\n                    setLoading(false);\n                }\n            }[\"AdminProvider.useEffect.unsubscribe\"]);\n            return unsubscribe;\n        }\n    }[\"AdminProvider.useEffect\"], []);\n    const adminLogin = async (email, password)=>{\n        try {\n            setError(null);\n            setLoading(true);\n            // Check if email is in admin list before attempting login\n            if (!ADMIN_EMAILS.includes(email.toLowerCase())) {\n                throw new Error('Access denied. Admin privileges required.');\n            }\n            const result = await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_3__.signInWithEmailAndPassword)(_firebase_config__WEBPACK_IMPORTED_MODULE_2__.auth, email, password);\n            // Double-check admin status after login\n            if (!ADMIN_EMAILS.includes(result.user.email.toLowerCase())) {\n                await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_3__.signOut)(_firebase_config__WEBPACK_IMPORTED_MODULE_2__.auth);\n                throw new Error('Access denied. Admin privileges required.');\n            }\n            return result;\n        } catch (error) {\n            setError(error.message);\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    const adminLogout = async ()=>{\n        try {\n            await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_3__.signOut)(_firebase_config__WEBPACK_IMPORTED_MODULE_2__.auth);\n            setError(null);\n        } catch (error) {\n            setError(error.message);\n            throw error;\n        }\n    };\n    const value = {\n        user,\n        isAdmin,\n        loading,\n        error,\n        adminLogin,\n        adminLogout\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AdminContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\contexts\\\\AdminContext.jsx\",\n        lineNumber: 121,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(AdminProvider, \"SDrvOAdfmOnf7mt80z0AufhjAss=\");\n_c = AdminProvider;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AdminContext);\nvar _c;\n$RefreshReg$(_c, \"AdminProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/AdminContext.jsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/services/VisitorService.js":
/*!****************************************!*\
  !*** ./src/services/VisitorService.js ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _firebase_config__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/firebase/config */ \"(app-pages-browser)/./src/firebase/config.js\");\n/* harmony import */ var firebase_database__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/database */ \"(app-pages-browser)/./node_modules/firebase/database/dist/esm/index.esm.js\");\n\n\nclass VisitorService {\n    // Track a new visitor (called once per browser session)\n    async trackVisitor() {\n        // Prevent multiple tracking in the same session\n        if (this.hasTrackedVisit || sessionStorage.getItem('blinkfind_visited')) {\n            return;\n        }\n        try {\n            // Get visitor's basic info (non-intrusive)\n            const visitorData = {\n                timestamp: (0,firebase_database__WEBPACK_IMPORTED_MODULE_1__.serverTimestamp)(),\n                userAgent: navigator.userAgent,\n                language: navigator.language,\n                referrer: document.referrer || 'direct',\n                viewport: \"\".concat(window.innerWidth, \"x\").concat(window.innerHeight),\n                timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,\n                sessionId: this.sessionKey\n            };\n            // Increment total visitor count\n            const visitorCountRef = (0,firebase_database__WEBPACK_IMPORTED_MODULE_1__.ref)(_firebase_config__WEBPACK_IMPORTED_MODULE_0__.database, 'analytics/totalVisitors');\n            await (0,firebase_database__WEBPACK_IMPORTED_MODULE_1__.set)(visitorCountRef, (0,firebase_database__WEBPACK_IMPORTED_MODULE_1__.increment)(1));\n            // Store visitor session data\n            const sessionRef = (0,firebase_database__WEBPACK_IMPORTED_MODULE_1__.ref)(_firebase_config__WEBPACK_IMPORTED_MODULE_0__.database, \"analytics/visits/\".concat(this.sessionKey));\n            await (0,firebase_database__WEBPACK_IMPORTED_MODULE_1__.set)(sessionRef, visitorData);\n            // Track daily visits\n            const today = new Date().toISOString().split('T')[0];\n            const dailyCountRef = (0,firebase_database__WEBPACK_IMPORTED_MODULE_1__.ref)(_firebase_config__WEBPACK_IMPORTED_MODULE_0__.database, \"analytics/dailyVisits/\".concat(today));\n            await (0,firebase_database__WEBPACK_IMPORTED_MODULE_1__.set)(dailyCountRef, (0,firebase_database__WEBPACK_IMPORTED_MODULE_1__.increment)(1));\n            // Mark as tracked in session storage\n            sessionStorage.setItem('blinkfind_visited', 'true');\n            this.hasTrackedVisit = true;\n            console.log('Visitor tracked successfully');\n        } catch (error) {\n            console.error('Error tracking visitor:', error);\n        }\n    }\n    // Track page views\n    async trackPageView(pagePath) {\n        try {\n            const pageViewData = {\n                timestamp: (0,firebase_database__WEBPACK_IMPORTED_MODULE_1__.serverTimestamp)(),\n                path: pagePath,\n                sessionId: this.sessionKey,\n                title: document.title\n            };\n            // Increment total page views\n            const pageViewCountRef = (0,firebase_database__WEBPACK_IMPORTED_MODULE_1__.ref)(_firebase_config__WEBPACK_IMPORTED_MODULE_0__.database, 'analytics/totalPageViews');\n            await (0,firebase_database__WEBPACK_IMPORTED_MODULE_1__.set)(pageViewCountRef, (0,firebase_database__WEBPACK_IMPORTED_MODULE_1__.increment)(1));\n            // Track specific page views\n            const pageRef = (0,firebase_database__WEBPACK_IMPORTED_MODULE_1__.ref)(_firebase_config__WEBPACK_IMPORTED_MODULE_0__.database, \"analytics/pageViews/\".concat(pagePath.replace(/\\//g, '_')));\n            await (0,firebase_database__WEBPACK_IMPORTED_MODULE_1__.set)(pageRef, (0,firebase_database__WEBPACK_IMPORTED_MODULE_1__.increment)(1));\n            // Store page view details\n            const sessionPageRef = (0,firebase_database__WEBPACK_IMPORTED_MODULE_1__.ref)(_firebase_config__WEBPACK_IMPORTED_MODULE_0__.database, \"analytics/pageViewDetails/\".concat(this.sessionKey, \"_\").concat(Date.now()));\n            await (0,firebase_database__WEBPACK_IMPORTED_MODULE_1__.set)(sessionPageRef, pageViewData);\n        } catch (error) {\n            console.error('Error tracking page view:', error);\n        }\n    }\n    // Get total visitor count\n    async getTotalVisitors() {\n        try {\n            const visitorCountRef = (0,firebase_database__WEBPACK_IMPORTED_MODULE_1__.ref)(_firebase_config__WEBPACK_IMPORTED_MODULE_0__.database, 'analytics/totalVisitors');\n            const snapshot = await (0,firebase_database__WEBPACK_IMPORTED_MODULE_1__.get)(visitorCountRef);\n            return snapshot.val() || 0;\n        } catch (error) {\n            console.error('Error getting visitor count:', error);\n            return 0;\n        }\n    }\n    // Get analytics data for admin dashboard\n    async getAnalyticsData() {\n        try {\n            var _data_dailyVisits, _data_dailyVisits1;\n            const analyticsRef = (0,firebase_database__WEBPACK_IMPORTED_MODULE_1__.ref)(_firebase_config__WEBPACK_IMPORTED_MODULE_0__.database, 'analytics');\n            const snapshot = await (0,firebase_database__WEBPACK_IMPORTED_MODULE_1__.get)(analyticsRef);\n            const data = snapshot.val() || {};\n            const today = new Date().toISOString().split('T')[0];\n            const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString().split('T')[0];\n            return {\n                totalVisitors: data.totalVisitors || 0,\n                totalPageViews: data.totalPageViews || 0,\n                todayVisits: ((_data_dailyVisits = data.dailyVisits) === null || _data_dailyVisits === void 0 ? void 0 : _data_dailyVisits[today]) || 0,\n                yesterdayVisits: ((_data_dailyVisits1 = data.dailyVisits) === null || _data_dailyVisits1 === void 0 ? void 0 : _data_dailyVisits1[yesterday]) || 0,\n                dailyVisits: data.dailyVisits || {},\n                pageViews: data.pageViews || {},\n                recentVisits: await this.getRecentVisits(data.visits || {})\n            };\n        } catch (error) {\n            console.error('Error getting analytics data:', error);\n            return {\n                totalVisitors: 0,\n                totalPageViews: 0,\n                todayVisits: 0,\n                yesterdayVisits: 0,\n                dailyVisits: {},\n                pageViews: {},\n                recentVisits: []\n            };\n        }\n    }\n    // Get recent visits for admin dashboard\n    async getRecentVisits(visitsData) {\n        try {\n            const visits = Object.entries(visitsData).map((param)=>{\n                let [sessionId, data] = param;\n                return {\n                    sessionId,\n                    ...data,\n                    timestamp: data.timestamp || Date.now()\n                };\n            }).sort((a, b)=>b.timestamp - a.timestamp).slice(0, 10); // Get last 10 visits\n            return visits;\n        } catch (error) {\n            console.error('Error processing recent visits:', error);\n            return [];\n        }\n    }\n    // Get daily visits for the last 7 days\n    async getWeeklyVisits() {\n        try {\n            const dailyVisitsRef = (0,firebase_database__WEBPACK_IMPORTED_MODULE_1__.ref)(_firebase_config__WEBPACK_IMPORTED_MODULE_0__.database, 'analytics/dailyVisits');\n            const snapshot = await (0,firebase_database__WEBPACK_IMPORTED_MODULE_1__.get)(dailyVisitsRef);\n            const dailyData = snapshot.val() || {};\n            const last7Days = [];\n            for(let i = 6; i >= 0; i--){\n                const date = new Date(Date.now() - i * 24 * 60 * 60 * 1000);\n                const dateString = date.toISOString().split('T')[0];\n                last7Days.push({\n                    date: dateString,\n                    visits: dailyData[dateString] || 0,\n                    label: date.toLocaleDateString('en-US', {\n                        month: 'short',\n                        day: 'numeric'\n                    })\n                });\n            }\n            return last7Days;\n        } catch (error) {\n            console.error('Error getting weekly visits:', error);\n            return [];\n        }\n    }\n    constructor(){\n        this.hasTrackedVisit = false;\n        this.sessionKey = \"blinkfind_visit_\".concat(Date.now());\n    }\n}\n// Create singleton instance\nconst visitorService = new VisitorService();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (visitorService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9zZXJ2aWNlcy9WaXNpdG9yU2VydmljZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBNkM7QUFDaUM7QUFFOUUsTUFBTU07SUFNSix3REFBd0Q7SUFDeEQsTUFBTUMsZUFBZTtRQUNuQixnREFBZ0Q7UUFDaEQsSUFBSSxJQUFJLENBQUNDLGVBQWUsSUFBSUMsZUFBZUMsT0FBTyxDQUFDLHNCQUFzQjtZQUN2RTtRQUNGO1FBRUEsSUFBSTtZQUNGLDJDQUEyQztZQUMzQyxNQUFNQyxjQUFjO2dCQUNsQkMsV0FBV1Asa0VBQWVBO2dCQUMxQlEsV0FBV0MsVUFBVUQsU0FBUztnQkFDOUJFLFVBQVVELFVBQVVDLFFBQVE7Z0JBQzVCQyxVQUFVQyxTQUFTRCxRQUFRLElBQUk7Z0JBQy9CRSxVQUFVLEdBQXdCQyxPQUFyQkEsT0FBT0MsVUFBVSxFQUFDLEtBQXNCLE9BQW5CRCxPQUFPRSxXQUFXO2dCQUNwREMsVUFBVUMsS0FBS0MsY0FBYyxHQUFHQyxlQUFlLEdBQUdDLFFBQVE7Z0JBQzFEQyxXQUFXLElBQUksQ0FBQ0MsVUFBVTtZQUM1QjtZQUVBLGdDQUFnQztZQUNoQyxNQUFNQyxrQkFBa0I1QixzREFBR0EsQ0FBQ0Qsc0RBQVFBLEVBQUU7WUFDdEMsTUFBTUcsc0RBQUdBLENBQUMwQixpQkFBaUJ6Qiw0REFBU0EsQ0FBQztZQUVyQyw2QkFBNkI7WUFDN0IsTUFBTTBCLGFBQWE3QixzREFBR0EsQ0FBQ0Qsc0RBQVFBLEVBQUUsb0JBQW9DLE9BQWhCLElBQUksQ0FBQzRCLFVBQVU7WUFDcEUsTUFBTXpCLHNEQUFHQSxDQUFDMkIsWUFBWW5CO1lBRXRCLHFCQUFxQjtZQUNyQixNQUFNb0IsUUFBUSxJQUFJQyxPQUFPQyxXQUFXLEdBQUdDLEtBQUssQ0FBQyxJQUFJLENBQUMsRUFBRTtZQUNwRCxNQUFNQyxnQkFBZ0JsQyxzREFBR0EsQ0FBQ0Qsc0RBQVFBLEVBQUUseUJBQStCLE9BQU4rQjtZQUM3RCxNQUFNNUIsc0RBQUdBLENBQUNnQyxlQUFlL0IsNERBQVNBLENBQUM7WUFFbkMscUNBQXFDO1lBQ3JDSyxlQUFlMkIsT0FBTyxDQUFDLHFCQUFxQjtZQUM1QyxJQUFJLENBQUM1QixlQUFlLEdBQUc7WUFFdkI2QixRQUFRQyxHQUFHLENBQUM7UUFDZCxFQUFFLE9BQU9DLE9BQU87WUFDZEYsUUFBUUUsS0FBSyxDQUFDLDJCQUEyQkE7UUFDM0M7SUFDRjtJQUVBLG1CQUFtQjtJQUNuQixNQUFNQyxjQUFjQyxRQUFRLEVBQUU7UUFDNUIsSUFBSTtZQUNGLE1BQU1DLGVBQWU7Z0JBQ25COUIsV0FBV1Asa0VBQWVBO2dCQUMxQnNDLE1BQU1GO2dCQUNOZCxXQUFXLElBQUksQ0FBQ0MsVUFBVTtnQkFDMUJnQixPQUFPM0IsU0FBUzJCLEtBQUs7WUFDdkI7WUFFQSw2QkFBNkI7WUFDN0IsTUFBTUMsbUJBQW1CNUMsc0RBQUdBLENBQUNELHNEQUFRQSxFQUFFO1lBQ3ZDLE1BQU1HLHNEQUFHQSxDQUFDMEMsa0JBQWtCekMsNERBQVNBLENBQUM7WUFFdEMsNEJBQTRCO1lBQzVCLE1BQU0wQyxVQUFVN0Msc0RBQUdBLENBQUNELHNEQUFRQSxFQUFFLHVCQUFvRCxPQUE3QnlDLFNBQVNNLE9BQU8sQ0FBQyxPQUFPO1lBQzdFLE1BQU01QyxzREFBR0EsQ0FBQzJDLFNBQVMxQyw0REFBU0EsQ0FBQztZQUU3QiwwQkFBMEI7WUFDMUIsTUFBTTRDLGlCQUFpQi9DLHNEQUFHQSxDQUFDRCxzREFBUUEsRUFBRSw2QkFBZ0RnQyxPQUFuQixJQUFJLENBQUNKLFVBQVUsRUFBQyxLQUFjLE9BQVhJLEtBQUtpQixHQUFHO1lBQzdGLE1BQU05QyxzREFBR0EsQ0FBQzZDLGdCQUFnQk47UUFFNUIsRUFBRSxPQUFPSCxPQUFPO1lBQ2RGLFFBQVFFLEtBQUssQ0FBQyw2QkFBNkJBO1FBQzdDO0lBQ0Y7SUFFQSwwQkFBMEI7SUFDMUIsTUFBTVcsbUJBQW1CO1FBQ3ZCLElBQUk7WUFDRixNQUFNckIsa0JBQWtCNUIsc0RBQUdBLENBQUNELHNEQUFRQSxFQUFFO1lBQ3RDLE1BQU1tRCxXQUFXLE1BQU1qRCxzREFBR0EsQ0FBQzJCO1lBQzNCLE9BQU9zQixTQUFTQyxHQUFHLE1BQU07UUFDM0IsRUFBRSxPQUFPYixPQUFPO1lBQ2RGLFFBQVFFLEtBQUssQ0FBQyxnQ0FBZ0NBO1lBQzlDLE9BQU87UUFDVDtJQUNGO0lBRUEseUNBQXlDO0lBQ3pDLE1BQU1jLG1CQUFtQjtRQUN2QixJQUFJO2dCQVdhQyxtQkFDSUE7WUFYbkIsTUFBTUMsZUFBZXRELHNEQUFHQSxDQUFDRCxzREFBUUEsRUFBRTtZQUNuQyxNQUFNbUQsV0FBVyxNQUFNakQsc0RBQUdBLENBQUNxRDtZQUMzQixNQUFNRCxPQUFPSCxTQUFTQyxHQUFHLE1BQU0sQ0FBQztZQUVoQyxNQUFNckIsUUFBUSxJQUFJQyxPQUFPQyxXQUFXLEdBQUdDLEtBQUssQ0FBQyxJQUFJLENBQUMsRUFBRTtZQUNwRCxNQUFNc0IsWUFBWSxJQUFJeEIsS0FBS0EsS0FBS2lCLEdBQUcsS0FBSyxLQUFLLEtBQUssS0FBSyxNQUFNaEIsV0FBVyxHQUFHQyxLQUFLLENBQUMsSUFBSSxDQUFDLEVBQUU7WUFFeEYsT0FBTztnQkFDTHVCLGVBQWVILEtBQUtHLGFBQWEsSUFBSTtnQkFDckNDLGdCQUFnQkosS0FBS0ksY0FBYyxJQUFJO2dCQUN2Q0MsYUFBYUwsRUFBQUEsb0JBQUFBLEtBQUtNLFdBQVcsY0FBaEJOLHdDQUFBQSxpQkFBa0IsQ0FBQ3ZCLE1BQU0sS0FBSTtnQkFDMUM4QixpQkFBaUJQLEVBQUFBLHFCQUFBQSxLQUFLTSxXQUFXLGNBQWhCTix5Q0FBQUEsa0JBQWtCLENBQUNFLFVBQVUsS0FBSTtnQkFDbERJLGFBQWFOLEtBQUtNLFdBQVcsSUFBSSxDQUFDO2dCQUNsQ0UsV0FBV1IsS0FBS1EsU0FBUyxJQUFJLENBQUM7Z0JBQzlCQyxjQUFjLE1BQU0sSUFBSSxDQUFDQyxlQUFlLENBQUNWLEtBQUtXLE1BQU0sSUFBSSxDQUFDO1lBQzNEO1FBQ0YsRUFBRSxPQUFPMUIsT0FBTztZQUNkRixRQUFRRSxLQUFLLENBQUMsaUNBQWlDQTtZQUMvQyxPQUFPO2dCQUNMa0IsZUFBZTtnQkFDZkMsZ0JBQWdCO2dCQUNoQkMsYUFBYTtnQkFDYkUsaUJBQWlCO2dCQUNqQkQsYUFBYSxDQUFDO2dCQUNkRSxXQUFXLENBQUM7Z0JBQ1pDLGNBQWMsRUFBRTtZQUNsQjtRQUNGO0lBQ0Y7SUFFQSx3Q0FBd0M7SUFDeEMsTUFBTUMsZ0JBQWdCRSxVQUFVLEVBQUU7UUFDaEMsSUFBSTtZQUNGLE1BQU1ELFNBQVNFLE9BQU9DLE9BQU8sQ0FBQ0YsWUFDM0JHLEdBQUcsQ0FBQztvQkFBQyxDQUFDMUMsV0FBVzJCLEtBQUs7dUJBQU07b0JBQzNCM0I7b0JBQ0EsR0FBRzJCLElBQUk7b0JBQ1AxQyxXQUFXMEMsS0FBSzFDLFNBQVMsSUFBSW9CLEtBQUtpQixHQUFHO2dCQUN2QztlQUNDcUIsSUFBSSxDQUFDLENBQUNDLEdBQUdDLElBQU1BLEVBQUU1RCxTQUFTLEdBQUcyRCxFQUFFM0QsU0FBUyxFQUN4QzZELEtBQUssQ0FBQyxHQUFHLEtBQUsscUJBQXFCO1lBRXRDLE9BQU9SO1FBQ1QsRUFBRSxPQUFPMUIsT0FBTztZQUNkRixRQUFRRSxLQUFLLENBQUMsbUNBQW1DQTtZQUNqRCxPQUFPLEVBQUU7UUFDWDtJQUNGO0lBRUEsdUNBQXVDO0lBQ3ZDLE1BQU1tQyxrQkFBa0I7UUFDdEIsSUFBSTtZQUNGLE1BQU1DLGlCQUFpQjFFLHNEQUFHQSxDQUFDRCxzREFBUUEsRUFBRTtZQUNyQyxNQUFNbUQsV0FBVyxNQUFNakQsc0RBQUdBLENBQUN5RTtZQUMzQixNQUFNQyxZQUFZekIsU0FBU0MsR0FBRyxNQUFNLENBQUM7WUFFckMsTUFBTXlCLFlBQVksRUFBRTtZQUNwQixJQUFLLElBQUlDLElBQUksR0FBR0EsS0FBSyxHQUFHQSxJQUFLO2dCQUMzQixNQUFNQyxPQUFPLElBQUkvQyxLQUFLQSxLQUFLaUIsR0FBRyxLQUFLNkIsSUFBSSxLQUFLLEtBQUssS0FBSztnQkFDdEQsTUFBTUUsYUFBYUQsS0FBSzlDLFdBQVcsR0FBR0MsS0FBSyxDQUFDLElBQUksQ0FBQyxFQUFFO2dCQUNuRDJDLFVBQVVJLElBQUksQ0FBQztvQkFDYkYsTUFBTUM7b0JBQ05mLFFBQVFXLFNBQVMsQ0FBQ0ksV0FBVyxJQUFJO29CQUNqQ0UsT0FBT0gsS0FBS0ksa0JBQWtCLENBQUMsU0FBUzt3QkFBRUMsT0FBTzt3QkFBU0MsS0FBSztvQkFBVTtnQkFDM0U7WUFDRjtZQUVBLE9BQU9SO1FBQ1QsRUFBRSxPQUFPdEMsT0FBTztZQUNkRixRQUFRRSxLQUFLLENBQUMsZ0NBQWdDQTtZQUM5QyxPQUFPLEVBQUU7UUFDWDtJQUNGO0lBaktBK0MsYUFBYztRQUNaLElBQUksQ0FBQzlFLGVBQWUsR0FBRztRQUN2QixJQUFJLENBQUNvQixVQUFVLEdBQUcsbUJBQThCLE9BQVhJLEtBQUtpQixHQUFHO0lBQy9DO0FBK0pGO0FBRUEsNEJBQTRCO0FBQzVCLE1BQU1zQyxpQkFBaUIsSUFBSWpGO0FBRTNCLGlFQUFlaUYsY0FBY0EsRUFBQyIsInNvdXJjZXMiOlsiQzpcXFByb2plY3RzXFxOZXdCbGlua0ZpbmRBSVxcZnJvbnRlbmQtbWFzdGVyXFxzcmNcXHNlcnZpY2VzXFxWaXNpdG9yU2VydmljZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBkYXRhYmFzZSB9IGZyb20gJ0AvZmlyZWJhc2UvY29uZmlnJztcbmltcG9ydCB7IHJlZiwgZ2V0LCBzZXQsIGluY3JlbWVudCwgc2VydmVyVGltZXN0YW1wIH0gZnJvbSAnZmlyZWJhc2UvZGF0YWJhc2UnO1xuXG5jbGFzcyBWaXNpdG9yU2VydmljZSB7XG4gIGNvbnN0cnVjdG9yKCkge1xuICAgIHRoaXMuaGFzVHJhY2tlZFZpc2l0ID0gZmFsc2U7XG4gICAgdGhpcy5zZXNzaW9uS2V5ID0gYGJsaW5rZmluZF92aXNpdF8ke0RhdGUubm93KCl9YDtcbiAgfVxuXG4gIC8vIFRyYWNrIGEgbmV3IHZpc2l0b3IgKGNhbGxlZCBvbmNlIHBlciBicm93c2VyIHNlc3Npb24pXG4gIGFzeW5jIHRyYWNrVmlzaXRvcigpIHtcbiAgICAvLyBQcmV2ZW50IG11bHRpcGxlIHRyYWNraW5nIGluIHRoZSBzYW1lIHNlc3Npb25cbiAgICBpZiAodGhpcy5oYXNUcmFja2VkVmlzaXQgfHwgc2Vzc2lvblN0b3JhZ2UuZ2V0SXRlbSgnYmxpbmtmaW5kX3Zpc2l0ZWQnKSkge1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIHRyeSB7XG4gICAgICAvLyBHZXQgdmlzaXRvcidzIGJhc2ljIGluZm8gKG5vbi1pbnRydXNpdmUpXG4gICAgICBjb25zdCB2aXNpdG9yRGF0YSA9IHtcbiAgICAgICAgdGltZXN0YW1wOiBzZXJ2ZXJUaW1lc3RhbXAoKSxcbiAgICAgICAgdXNlckFnZW50OiBuYXZpZ2F0b3IudXNlckFnZW50LFxuICAgICAgICBsYW5ndWFnZTogbmF2aWdhdG9yLmxhbmd1YWdlLFxuICAgICAgICByZWZlcnJlcjogZG9jdW1lbnQucmVmZXJyZXIgfHwgJ2RpcmVjdCcsXG4gICAgICAgIHZpZXdwb3J0OiBgJHt3aW5kb3cuaW5uZXJXaWR0aH14JHt3aW5kb3cuaW5uZXJIZWlnaHR9YCxcbiAgICAgICAgdGltZXpvbmU6IEludGwuRGF0ZVRpbWVGb3JtYXQoKS5yZXNvbHZlZE9wdGlvbnMoKS50aW1lWm9uZSxcbiAgICAgICAgc2Vzc2lvbklkOiB0aGlzLnNlc3Npb25LZXlcbiAgICAgIH07XG5cbiAgICAgIC8vIEluY3JlbWVudCB0b3RhbCB2aXNpdG9yIGNvdW50XG4gICAgICBjb25zdCB2aXNpdG9yQ291bnRSZWYgPSByZWYoZGF0YWJhc2UsICdhbmFseXRpY3MvdG90YWxWaXNpdG9ycycpO1xuICAgICAgYXdhaXQgc2V0KHZpc2l0b3JDb3VudFJlZiwgaW5jcmVtZW50KDEpKTtcblxuICAgICAgLy8gU3RvcmUgdmlzaXRvciBzZXNzaW9uIGRhdGFcbiAgICAgIGNvbnN0IHNlc3Npb25SZWYgPSByZWYoZGF0YWJhc2UsIGBhbmFseXRpY3MvdmlzaXRzLyR7dGhpcy5zZXNzaW9uS2V5fWApO1xuICAgICAgYXdhaXQgc2V0KHNlc3Npb25SZWYsIHZpc2l0b3JEYXRhKTtcblxuICAgICAgLy8gVHJhY2sgZGFpbHkgdmlzaXRzXG4gICAgICBjb25zdCB0b2RheSA9IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKS5zcGxpdCgnVCcpWzBdO1xuICAgICAgY29uc3QgZGFpbHlDb3VudFJlZiA9IHJlZihkYXRhYmFzZSwgYGFuYWx5dGljcy9kYWlseVZpc2l0cy8ke3RvZGF5fWApO1xuICAgICAgYXdhaXQgc2V0KGRhaWx5Q291bnRSZWYsIGluY3JlbWVudCgxKSk7XG5cbiAgICAgIC8vIE1hcmsgYXMgdHJhY2tlZCBpbiBzZXNzaW9uIHN0b3JhZ2VcbiAgICAgIHNlc3Npb25TdG9yYWdlLnNldEl0ZW0oJ2JsaW5rZmluZF92aXNpdGVkJywgJ3RydWUnKTtcbiAgICAgIHRoaXMuaGFzVHJhY2tlZFZpc2l0ID0gdHJ1ZTtcblxuICAgICAgY29uc29sZS5sb2coJ1Zpc2l0b3IgdHJhY2tlZCBzdWNjZXNzZnVsbHknKTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgdHJhY2tpbmcgdmlzaXRvcjonLCBlcnJvcik7XG4gICAgfVxuICB9XG5cbiAgLy8gVHJhY2sgcGFnZSB2aWV3c1xuICBhc3luYyB0cmFja1BhZ2VWaWV3KHBhZ2VQYXRoKSB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHBhZ2VWaWV3RGF0YSA9IHtcbiAgICAgICAgdGltZXN0YW1wOiBzZXJ2ZXJUaW1lc3RhbXAoKSxcbiAgICAgICAgcGF0aDogcGFnZVBhdGgsXG4gICAgICAgIHNlc3Npb25JZDogdGhpcy5zZXNzaW9uS2V5LFxuICAgICAgICB0aXRsZTogZG9jdW1lbnQudGl0bGVcbiAgICAgIH07XG5cbiAgICAgIC8vIEluY3JlbWVudCB0b3RhbCBwYWdlIHZpZXdzXG4gICAgICBjb25zdCBwYWdlVmlld0NvdW50UmVmID0gcmVmKGRhdGFiYXNlLCAnYW5hbHl0aWNzL3RvdGFsUGFnZVZpZXdzJyk7XG4gICAgICBhd2FpdCBzZXQocGFnZVZpZXdDb3VudFJlZiwgaW5jcmVtZW50KDEpKTtcblxuICAgICAgLy8gVHJhY2sgc3BlY2lmaWMgcGFnZSB2aWV3c1xuICAgICAgY29uc3QgcGFnZVJlZiA9IHJlZihkYXRhYmFzZSwgYGFuYWx5dGljcy9wYWdlVmlld3MvJHtwYWdlUGF0aC5yZXBsYWNlKC9cXC8vZywgJ18nKX1gKTtcbiAgICAgIGF3YWl0IHNldChwYWdlUmVmLCBpbmNyZW1lbnQoMSkpO1xuXG4gICAgICAvLyBTdG9yZSBwYWdlIHZpZXcgZGV0YWlsc1xuICAgICAgY29uc3Qgc2Vzc2lvblBhZ2VSZWYgPSByZWYoZGF0YWJhc2UsIGBhbmFseXRpY3MvcGFnZVZpZXdEZXRhaWxzLyR7dGhpcy5zZXNzaW9uS2V5fV8ke0RhdGUubm93KCl9YCk7XG4gICAgICBhd2FpdCBzZXQoc2Vzc2lvblBhZ2VSZWYsIHBhZ2VWaWV3RGF0YSk7XG5cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgdHJhY2tpbmcgcGFnZSB2aWV3OicsIGVycm9yKTtcbiAgICB9XG4gIH1cblxuICAvLyBHZXQgdG90YWwgdmlzaXRvciBjb3VudFxuICBhc3luYyBnZXRUb3RhbFZpc2l0b3JzKCkge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCB2aXNpdG9yQ291bnRSZWYgPSByZWYoZGF0YWJhc2UsICdhbmFseXRpY3MvdG90YWxWaXNpdG9ycycpO1xuICAgICAgY29uc3Qgc25hcHNob3QgPSBhd2FpdCBnZXQodmlzaXRvckNvdW50UmVmKTtcbiAgICAgIHJldHVybiBzbmFwc2hvdC52YWwoKSB8fCAwO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBnZXR0aW5nIHZpc2l0b3IgY291bnQ6JywgZXJyb3IpO1xuICAgICAgcmV0dXJuIDA7XG4gICAgfVxuICB9XG5cbiAgLy8gR2V0IGFuYWx5dGljcyBkYXRhIGZvciBhZG1pbiBkYXNoYm9hcmRcbiAgYXN5bmMgZ2V0QW5hbHl0aWNzRGF0YSgpIHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgYW5hbHl0aWNzUmVmID0gcmVmKGRhdGFiYXNlLCAnYW5hbHl0aWNzJyk7XG4gICAgICBjb25zdCBzbmFwc2hvdCA9IGF3YWl0IGdldChhbmFseXRpY3NSZWYpO1xuICAgICAgY29uc3QgZGF0YSA9IHNuYXBzaG90LnZhbCgpIHx8IHt9O1xuXG4gICAgICBjb25zdCB0b2RheSA9IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKS5zcGxpdCgnVCcpWzBdO1xuICAgICAgY29uc3QgeWVzdGVyZGF5ID0gbmV3IERhdGUoRGF0ZS5ub3coKSAtIDI0ICogNjAgKiA2MCAqIDEwMDApLnRvSVNPU3RyaW5nKCkuc3BsaXQoJ1QnKVswXTtcblxuICAgICAgcmV0dXJuIHtcbiAgICAgICAgdG90YWxWaXNpdG9yczogZGF0YS50b3RhbFZpc2l0b3JzIHx8IDAsXG4gICAgICAgIHRvdGFsUGFnZVZpZXdzOiBkYXRhLnRvdGFsUGFnZVZpZXdzIHx8IDAsXG4gICAgICAgIHRvZGF5VmlzaXRzOiBkYXRhLmRhaWx5VmlzaXRzPy5bdG9kYXldIHx8IDAsXG4gICAgICAgIHllc3RlcmRheVZpc2l0czogZGF0YS5kYWlseVZpc2l0cz8uW3llc3RlcmRheV0gfHwgMCxcbiAgICAgICAgZGFpbHlWaXNpdHM6IGRhdGEuZGFpbHlWaXNpdHMgfHwge30sXG4gICAgICAgIHBhZ2VWaWV3czogZGF0YS5wYWdlVmlld3MgfHwge30sXG4gICAgICAgIHJlY2VudFZpc2l0czogYXdhaXQgdGhpcy5nZXRSZWNlbnRWaXNpdHMoZGF0YS52aXNpdHMgfHwge30pXG4gICAgICB9O1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBnZXR0aW5nIGFuYWx5dGljcyBkYXRhOicsIGVycm9yKTtcbiAgICAgIHJldHVybiB7XG4gICAgICAgIHRvdGFsVmlzaXRvcnM6IDAsXG4gICAgICAgIHRvdGFsUGFnZVZpZXdzOiAwLFxuICAgICAgICB0b2RheVZpc2l0czogMCxcbiAgICAgICAgeWVzdGVyZGF5VmlzaXRzOiAwLFxuICAgICAgICBkYWlseVZpc2l0czoge30sXG4gICAgICAgIHBhZ2VWaWV3czoge30sXG4gICAgICAgIHJlY2VudFZpc2l0czogW11cbiAgICAgIH07XG4gICAgfVxuICB9XG5cbiAgLy8gR2V0IHJlY2VudCB2aXNpdHMgZm9yIGFkbWluIGRhc2hib2FyZFxuICBhc3luYyBnZXRSZWNlbnRWaXNpdHModmlzaXRzRGF0YSkge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCB2aXNpdHMgPSBPYmplY3QuZW50cmllcyh2aXNpdHNEYXRhKVxuICAgICAgICAubWFwKChbc2Vzc2lvbklkLCBkYXRhXSkgPT4gKHtcbiAgICAgICAgICBzZXNzaW9uSWQsXG4gICAgICAgICAgLi4uZGF0YSxcbiAgICAgICAgICB0aW1lc3RhbXA6IGRhdGEudGltZXN0YW1wIHx8IERhdGUubm93KClcbiAgICAgICAgfSkpXG4gICAgICAgIC5zb3J0KChhLCBiKSA9PiBiLnRpbWVzdGFtcCAtIGEudGltZXN0YW1wKVxuICAgICAgICAuc2xpY2UoMCwgMTApOyAvLyBHZXQgbGFzdCAxMCB2aXNpdHNcblxuICAgICAgcmV0dXJuIHZpc2l0cztcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgcHJvY2Vzc2luZyByZWNlbnQgdmlzaXRzOicsIGVycm9yKTtcbiAgICAgIHJldHVybiBbXTtcbiAgICB9XG4gIH1cblxuICAvLyBHZXQgZGFpbHkgdmlzaXRzIGZvciB0aGUgbGFzdCA3IGRheXNcbiAgYXN5bmMgZ2V0V2Vla2x5VmlzaXRzKCkge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCBkYWlseVZpc2l0c1JlZiA9IHJlZihkYXRhYmFzZSwgJ2FuYWx5dGljcy9kYWlseVZpc2l0cycpO1xuICAgICAgY29uc3Qgc25hcHNob3QgPSBhd2FpdCBnZXQoZGFpbHlWaXNpdHNSZWYpO1xuICAgICAgY29uc3QgZGFpbHlEYXRhID0gc25hcHNob3QudmFsKCkgfHwge307XG5cbiAgICAgIGNvbnN0IGxhc3Q3RGF5cyA9IFtdO1xuICAgICAgZm9yIChsZXQgaSA9IDY7IGkgPj0gMDsgaS0tKSB7XG4gICAgICAgIGNvbnN0IGRhdGUgPSBuZXcgRGF0ZShEYXRlLm5vdygpIC0gaSAqIDI0ICogNjAgKiA2MCAqIDEwMDApO1xuICAgICAgICBjb25zdCBkYXRlU3RyaW5nID0gZGF0ZS50b0lTT1N0cmluZygpLnNwbGl0KCdUJylbMF07XG4gICAgICAgIGxhc3Q3RGF5cy5wdXNoKHtcbiAgICAgICAgICBkYXRlOiBkYXRlU3RyaW5nLFxuICAgICAgICAgIHZpc2l0czogZGFpbHlEYXRhW2RhdGVTdHJpbmddIHx8IDAsXG4gICAgICAgICAgbGFiZWw6IGRhdGUudG9Mb2NhbGVEYXRlU3RyaW5nKCdlbi1VUycsIHsgbW9udGg6ICdzaG9ydCcsIGRheTogJ251bWVyaWMnIH0pXG4gICAgICAgIH0pO1xuICAgICAgfVxuXG4gICAgICByZXR1cm4gbGFzdDdEYXlzO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBnZXR0aW5nIHdlZWtseSB2aXNpdHM6JywgZXJyb3IpO1xuICAgICAgcmV0dXJuIFtdO1xuICAgIH1cbiAgfVxufVxuXG4vLyBDcmVhdGUgc2luZ2xldG9uIGluc3RhbmNlXG5jb25zdCB2aXNpdG9yU2VydmljZSA9IG5ldyBWaXNpdG9yU2VydmljZSgpO1xuXG5leHBvcnQgZGVmYXVsdCB2aXNpdG9yU2VydmljZTtcbiJdLCJuYW1lcyI6WyJkYXRhYmFzZSIsInJlZiIsImdldCIsInNldCIsImluY3JlbWVudCIsInNlcnZlclRpbWVzdGFtcCIsIlZpc2l0b3JTZXJ2aWNlIiwidHJhY2tWaXNpdG9yIiwiaGFzVHJhY2tlZFZpc2l0Iiwic2Vzc2lvblN0b3JhZ2UiLCJnZXRJdGVtIiwidmlzaXRvckRhdGEiLCJ0aW1lc3RhbXAiLCJ1c2VyQWdlbnQiLCJuYXZpZ2F0b3IiLCJsYW5ndWFnZSIsInJlZmVycmVyIiwiZG9jdW1lbnQiLCJ2aWV3cG9ydCIsIndpbmRvdyIsImlubmVyV2lkdGgiLCJpbm5lckhlaWdodCIsInRpbWV6b25lIiwiSW50bCIsIkRhdGVUaW1lRm9ybWF0IiwicmVzb2x2ZWRPcHRpb25zIiwidGltZVpvbmUiLCJzZXNzaW9uSWQiLCJzZXNzaW9uS2V5IiwidmlzaXRvckNvdW50UmVmIiwic2Vzc2lvblJlZiIsInRvZGF5IiwiRGF0ZSIsInRvSVNPU3RyaW5nIiwic3BsaXQiLCJkYWlseUNvdW50UmVmIiwic2V0SXRlbSIsImNvbnNvbGUiLCJsb2ciLCJlcnJvciIsInRyYWNrUGFnZVZpZXciLCJwYWdlUGF0aCIsInBhZ2VWaWV3RGF0YSIsInBhdGgiLCJ0aXRsZSIsInBhZ2VWaWV3Q291bnRSZWYiLCJwYWdlUmVmIiwicmVwbGFjZSIsInNlc3Npb25QYWdlUmVmIiwibm93IiwiZ2V0VG90YWxWaXNpdG9ycyIsInNuYXBzaG90IiwidmFsIiwiZ2V0QW5hbHl0aWNzRGF0YSIsImRhdGEiLCJhbmFseXRpY3NSZWYiLCJ5ZXN0ZXJkYXkiLCJ0b3RhbFZpc2l0b3JzIiwidG90YWxQYWdlVmlld3MiLCJ0b2RheVZpc2l0cyIsImRhaWx5VmlzaXRzIiwieWVzdGVyZGF5VmlzaXRzIiwicGFnZVZpZXdzIiwicmVjZW50VmlzaXRzIiwiZ2V0UmVjZW50VmlzaXRzIiwidmlzaXRzIiwidmlzaXRzRGF0YSIsIk9iamVjdCIsImVudHJpZXMiLCJtYXAiLCJzb3J0IiwiYSIsImIiLCJzbGljZSIsImdldFdlZWtseVZpc2l0cyIsImRhaWx5VmlzaXRzUmVmIiwiZGFpbHlEYXRhIiwibGFzdDdEYXlzIiwiaSIsImRhdGUiLCJkYXRlU3RyaW5nIiwicHVzaCIsImxhYmVsIiwidG9Mb2NhbGVEYXRlU3RyaW5nIiwibW9udGgiLCJkYXkiLCJjb25zdHJ1Y3RvciIsInZpc2l0b3JTZXJ2aWNlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/VisitorService.js\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["vendors","default-_app-pages-browser_src_contexts_AuthContext_jsx","main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CNewBlinkFindAI%5C%5Cfrontend-master%5C%5Cnode_modules%5C%5C%40next%5C%5Cthird-parties%5C%5Cdist%5C%5Cgoogle%5C%5Cga.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CNewBlinkFindAI%5C%5Cfrontend-master%5C%5Cnode_modules%5C%5C%40next%5C%5Cthird-parties%5C%5Cdist%5C%5Cgoogle%5C%5Cgtm.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CNewBlinkFindAI%5C%5Cfrontend-master%5C%5Cnode_modules%5C%5C%40next%5C%5Cthird-parties%5C%5Cdist%5C%5CThirdPartyScriptEmbed.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CNewBlinkFindAI%5C%5Cfrontend-master%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CNewBlinkFindAI%5C%5Cfrontend-master%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CNewBlinkFindAI%5C%5Cfrontend-master%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CNewBlinkFindAI%5C%5Cfrontend-master%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CNewBlinkFindAI%5C%5Cfrontend-master%5C%5Csrc%5C%5Ccomponents%5C%5Ccommon%5C%5CVisitorTracker.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CNewBlinkFindAI%5C%5Cfrontend-master%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CNavbar.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CNewBlinkFindAI%5C%5Cfrontend-master%5C%5Csrc%5C%5Ccontexts%5C%5CAdminContext.jsx%22%2C%22ids%22%3A%5B%22AdminProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CNewBlinkFindAI%5C%5Cfrontend-master%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.jsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);
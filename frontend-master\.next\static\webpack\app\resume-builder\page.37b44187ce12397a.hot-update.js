"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/resume-builder/page",{

/***/ "(app-pages-browser)/./src/components/resume/SimplifiedNavigation.jsx":
/*!********************************************************!*\
  !*** ./src/components/resume/SimplifiedNavigation.jsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_CheckCircle_Circle_Download_Eye_FileText_GraduationCap_Home_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,CheckCircle,Circle,Download,Eye,FileText,GraduationCap,Home,Save,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_CheckCircle_Circle_Download_Eye_FileText_GraduationCap_Home_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,CheckCircle,Circle,Download,Eye,FileText,GraduationCap,Home,Save,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/graduation-cap.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_CheckCircle_Circle_Download_Eye_FileText_GraduationCap_Home_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,CheckCircle,Circle,Download,Eye,FileText,GraduationCap,Home,Save,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_CheckCircle_Circle_Download_Eye_FileText_GraduationCap_Home_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,CheckCircle,Circle,Download,Eye,FileText,GraduationCap,Home,Save,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_CheckCircle_Circle_Download_Eye_FileText_GraduationCap_Home_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,CheckCircle,Circle,Download,Eye,FileText,GraduationCap,Home,Save,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_CheckCircle_Circle_Download_Eye_FileText_GraduationCap_Home_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,CheckCircle,Circle,Download,Eye,FileText,GraduationCap,Home,Save,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_CheckCircle_Circle_Download_Eye_FileText_GraduationCap_Home_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,CheckCircle,Circle,Download,Eye,FileText,GraduationCap,Home,Save,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_CheckCircle_Circle_Download_Eye_FileText_GraduationCap_Home_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,CheckCircle,Circle,Download,Eye,FileText,GraduationCap,Home,Save,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_CheckCircle_Circle_Download_Eye_FileText_GraduationCap_Home_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,CheckCircle,Circle,Download,Eye,FileText,GraduationCap,Home,Save,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_CheckCircle_Circle_Download_Eye_FileText_GraduationCap_Home_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,CheckCircle,Circle,Download,Eye,FileText,GraduationCap,Home,Save,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_CheckCircle_Circle_Download_Eye_FileText_GraduationCap_Home_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,CheckCircle,Circle,Download,Eye,FileText,GraduationCap,Home,Save,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_CheckCircle_Circle_Download_Eye_FileText_GraduationCap_Home_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Award,Briefcase,CheckCircle,Circle,Download,Eye,FileText,GraduationCap,Home,Save,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst SimplifiedNavigation = (param)=>{\n    let { currentStep, totalSteps, onPrevious, onNext, onSave, onDownload, onHome, canProceed = true, isSaving = false, completionPercentage = 0, stepTitles = [] } = param;\n    const isFirstStep = currentStep === 0;\n    const isLastStep = currentStep === totalSteps - 1;\n    const getStepIcon = (stepIndex)=>{\n        const icons = [\n            _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_CheckCircle_Circle_Download_Eye_FileText_GraduationCap_Home_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n            _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_CheckCircle_Circle_Download_Eye_FileText_GraduationCap_Home_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n            _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_CheckCircle_Circle_Download_Eye_FileText_GraduationCap_Home_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_CheckCircle_Circle_Download_Eye_FileText_GraduationCap_Home_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_CheckCircle_Circle_Download_Eye_FileText_GraduationCap_Home_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n        ];\n        const Icon = icons[stepIndex] || _barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_CheckCircle_Circle_Download_Eye_FileText_GraduationCap_Home_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\n        return Icon;\n    };\n    const getStepStatus = (stepIndex)=>{\n        if (stepIndex < currentStep) return 'completed';\n        if (stepIndex === currentStep) return 'current';\n        return 'upcoming';\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gray-900/95 backdrop-blur-sm border-t border-gray-700/50 shadow-2xl\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"hidden md:flex items-center justify-center py-4 border-b border-gray-700/30\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-8\",\n                        children: stepTitles.map((title, index)=>{\n                            const Icon = getStepIcon(index);\n                            const status = getStepStatus(index);\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"\\n                      w-10 h-10 rounded-full border-2 flex items-center justify-center transition-all duration-200\\n                      \".concat(status === 'completed' ? 'bg-neural-pink border-neural-pink text-white' : status === 'current' ? 'bg-neural-purple border-neural-purple text-white' : 'bg-gray-800 border-gray-600 text-gray-400', \"\\n                    \"),\n                                                children: status === 'completed' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_CheckCircle_Circle_Download_Eye_FileText_GraduationCap_Home_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimplifiedNavigation.jsx\",\n                                                    lineNumber: 60,\n                                                    columnNumber: 25\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimplifiedNavigation.jsx\",\n                                                    lineNumber: 62,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimplifiedNavigation.jsx\",\n                                                lineNumber: 50,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"\\n                      mt-2 text-xs font-medium\\n                      \".concat(status === 'current' ? 'text-neural-purple' : 'text-gray-400', \"\\n                    \"),\n                                                children: title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimplifiedNavigation.jsx\",\n                                                lineNumber: 65,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimplifiedNavigation.jsx\",\n                                        lineNumber: 49,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    index < stepTitles.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-16 h-0.5 mx-4 bg-gray-700 relative\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                            className: \"h-full bg-neural-pink\",\n                                            initial: {\n                                                width: 0\n                                            },\n                                            animate: {\n                                                width: index < currentStep ? '100%' : '0%'\n                                            },\n                                            transition: {\n                                                duration: 0.3\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimplifiedNavigation.jsx\",\n                                            lineNumber: 76,\n                                            columnNumber: 23\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimplifiedNavigation.jsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 21\n                                    }, undefined)\n                                ]\n                            }, index, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimplifiedNavigation.jsx\",\n                                lineNumber: 48,\n                                columnNumber: 17\n                            }, undefined);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimplifiedNavigation.jsx\",\n                        lineNumber: 42,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimplifiedNavigation.jsx\",\n                    lineNumber: 41,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"md:hidden py-3 border-b border-gray-700/30\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-medium text-white\",\n                                    children: [\n                                        \"Step \",\n                                        currentStep + 1,\n                                        \" of \",\n                                        totalSteps\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimplifiedNavigation.jsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-gray-400\",\n                                    children: [\n                                        Math.round(completionPercentage),\n                                        \"% Complete\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimplifiedNavigation.jsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimplifiedNavigation.jsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full bg-gray-700 rounded-full h-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                className: \"bg-gradient-to-r from-neural-purple to-neural-pink h-2 rounded-full\",\n                                initial: {\n                                    width: 0\n                                },\n                                animate: {\n                                    width: \"\".concat(completionPercentage, \"%\")\n                                },\n                                transition: {\n                                    duration: 0.5\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimplifiedNavigation.jsx\",\n                                lineNumber: 103,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimplifiedNavigation.jsx\",\n                            lineNumber: 102,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimplifiedNavigation.jsx\",\n                    lineNumber: 93,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between py-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onHome,\n                                    className: \"p-2 text-gray-400 hover:text-gray-600 transition-colors\",\n                                    title: \"Back to Home\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_CheckCircle_Circle_Download_Eye_FileText_GraduationCap_Home_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimplifiedNavigation.jsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimplifiedNavigation.jsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onPrevious,\n                                    disabled: isFirstStep,\n                                    className: \"\\n                flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-all duration-200\\n                \".concat(isFirstStep ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-gray-100 text-gray-700 hover:bg-gray-200', \"\\n              \"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_CheckCircle_Circle_Download_Eye_FileText_GraduationCap_Home_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimplifiedNavigation.jsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Previous\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimplifiedNavigation.jsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimplifiedNavigation.jsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimplifiedNavigation.jsx\",\n                            lineNumber: 115,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden sm:flex flex-col items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-900\",\n                                    children: stepTitles[currentStep] || \"Step \".concat(currentStep + 1)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimplifiedNavigation.jsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-500\",\n                                    children: isLastStep ? 'Review and download your resume' : 'Fill in your information'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimplifiedNavigation.jsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimplifiedNavigation.jsx\",\n                            lineNumber: 141,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onSave,\n                                    disabled: isSaving,\n                                    className: \"flex items-center space-x-2 px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg font-medium transition-colors disabled:opacity-50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_CheckCircle_Circle_Download_Eye_FileText_GraduationCap_Home_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimplifiedNavigation.jsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"hidden sm:inline\",\n                                            children: isSaving ? 'Saving...' : 'Save'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimplifiedNavigation.jsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimplifiedNavigation.jsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 13\n                                }, undefined),\n                                isLastStep && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onDownload,\n                                    className: \"flex items-center space-x-2 px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg font-medium transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_CheckCircle_Circle_Download_Eye_FileText_GraduationCap_Home_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimplifiedNavigation.jsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"hidden sm:inline\",\n                                            children: \"Download\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimplifiedNavigation.jsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimplifiedNavigation.jsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onNext,\n                                    disabled: !canProceed,\n                                    className: \"\\n                flex items-center space-x-2 px-6 py-2 rounded-lg font-medium transition-all duration-200\\n                \".concat(canProceed ? 'bg-blue-600 hover:bg-blue-700 text-white shadow-sm' : 'bg-gray-200 text-gray-400 cursor-not-allowed', \"\\n              \"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: isLastStep ? 'Complete' : 'Next'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimplifiedNavigation.jsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Award_Briefcase_CheckCircle_Circle_Download_Eye_FileText_GraduationCap_Home_Save_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimplifiedNavigation.jsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimplifiedNavigation.jsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimplifiedNavigation.jsx\",\n                            lineNumber: 151,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimplifiedNavigation.jsx\",\n                    lineNumber: 113,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimplifiedNavigation.jsx\",\n            lineNumber: 39,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\SimplifiedNavigation.jsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, undefined);\n};\n_c = SimplifiedNavigation;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SimplifiedNavigation);\nvar _c;\n$RefreshReg$(_c, \"SimplifiedNavigation\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/resume/SimplifiedNavigation.jsx\n"));

/***/ })

});
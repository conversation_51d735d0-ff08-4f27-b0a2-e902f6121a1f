/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/page"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CNewBlinkFindAI%5C%5Cfrontend-master%5C%5Csrc%5C%5Capp%5C%5Cpage.jsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CNewBlinkFindAI%5C%5Cfrontend-master%5C%5Csrc%5C%5Capp%5C%5Cpage.jsx%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.jsx */ \"(app-pages-browser)/./src/app/page.jsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1Byb2plY3RzJTVDJTVDTmV3QmxpbmtGaW5kQUklNUMlNUNmcm9udGVuZC1tYXN0ZXIlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNwYWdlLmpzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9ZmFsc2UhIiwibWFwcGluZ3MiOiJBQUFBLDhKQUFzRyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcUHJvamVjdHNcXFxcTmV3QmxpbmtGaW5kQUlcXFxcZnJvbnRlbmQtbWFzdGVyXFxcXHNyY1xcXFxhcHBcXFxccGFnZS5qc3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CNewBlinkFindAI%5C%5Cfrontend-master%5C%5Csrc%5C%5Capp%5C%5Cpage.jsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/page.jsx":
/*!**************************!*\
  !*** ./src/app/page.jsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_features_ContactUs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/features/ContactUs */ \"(app-pages-browser)/./src/components/features/ContactUs.jsx\");\n/* harmony import */ var _components_layout_Hero__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/Hero */ \"(app-pages-browser)/./src/components/layout/Hero.jsx\");\n/* harmony import */ var _components_features_ShowcaseProduct__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/features/ShowcaseProduct */ \"(app-pages-browser)/./src/components/features/ShowcaseProduct.jsx\");\n/* harmony import */ var _components_features_ReviewsSection__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/features/ReviewsSection */ \"(app-pages-browser)/./src/components/features/ReviewsSection.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n// import NewsAndUpdate from \"@/oldComponents/NewsAndUpdate\";\n// import SimplifiedTestimonialCarousel from \"@/components/QuickfindTestimonial\";\n// import FAQ from \"@/components/FAQ\";\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \" bg-[#F8FFF8]\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Hero__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\page.jsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_features_ShowcaseProduct__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\page.jsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_features_ReviewsSection__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\page.jsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_features_ContactUs__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\page.jsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\page.jsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvcGFnZS5qc3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUV3RDtBQUNaO0FBQ3dCO0FBQ0Y7QUFDbEUsNkRBQTZEO0FBQzdELGlGQUFpRjtBQUNqRixzQ0FBc0M7QUFFdkIsU0FBU0k7SUFDdEIscUJBQ0UsOERBQUNDO1FBQUlDLFdBQVU7OzBCQUNiLDhEQUFDTCwrREFBSUE7Ozs7OzBCQUNMLDhEQUFDQyw0RUFBZUE7Ozs7OzBCQUNoQiw4REFBQ0MsMkVBQWNBOzs7OzswQkFLZiw4REFBQ0gsc0VBQVNBOzs7Ozs7Ozs7OztBQUdoQjtLQWJ3QkkiLCJzb3VyY2VzIjpbIkM6XFxQcm9qZWN0c1xcTmV3QmxpbmtGaW5kQUlcXGZyb250ZW5kLW1hc3Rlclxcc3JjXFxhcHBcXHBhZ2UuanN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgQ29udGFjdFVzIGZyb20gXCJAL2NvbXBvbmVudHMvZmVhdHVyZXMvQ29udGFjdFVzXCI7XG5pbXBvcnQgSGVybyBmcm9tIFwiQC9jb21wb25lbnRzL2xheW91dC9IZXJvXCI7XG5pbXBvcnQgU2hvd2Nhc2VQcm9kdWN0IGZyb20gXCJAL2NvbXBvbmVudHMvZmVhdHVyZXMvU2hvd2Nhc2VQcm9kdWN0XCI7XG5pbXBvcnQgUmV2aWV3c1NlY3Rpb24gZnJvbSBcIkAvY29tcG9uZW50cy9mZWF0dXJlcy9SZXZpZXdzU2VjdGlvblwiO1xuLy8gaW1wb3J0IE5ld3NBbmRVcGRhdGUgZnJvbSBcIkAvb2xkQ29tcG9uZW50cy9OZXdzQW5kVXBkYXRlXCI7XG4vLyBpbXBvcnQgU2ltcGxpZmllZFRlc3RpbW9uaWFsQ2Fyb3VzZWwgZnJvbSBcIkAvY29tcG9uZW50cy9RdWlja2ZpbmRUZXN0aW1vbmlhbFwiO1xuLy8gaW1wb3J0IEZBUSBmcm9tIFwiQC9jb21wb25lbnRzL0ZBUVwiO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBIb21lKCkge1xuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiIGJnLVsjRjhGRkY4XVwiPlxuICAgICAgPEhlcm8gLz5cbiAgICAgIDxTaG93Y2FzZVByb2R1Y3QgLz5cbiAgICAgIDxSZXZpZXdzU2VjdGlvbiAvPlxuICAgICAgey8qIDxOZXdzQW5kVXBkYXRlLz4gKi99XG4gICAgICB7LyogPE91clRlYW0xLz4gKi99XG4gICAgICB7LyogPFNpbXBsaWZpZWRUZXN0aW1vbmlhbENhcm91c2VsIC8+ICovfVxuICAgICAgey8qIDxGQVEgLz4gKi99XG4gICAgICA8Q29udGFjdFVzIC8+XG4gICAgPC9kaXY+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiQ29udGFjdFVzIiwiSGVybyIsIlNob3djYXNlUHJvZHVjdCIsIlJldmlld3NTZWN0aW9uIiwiSG9tZSIsImRpdiIsImNsYXNzTmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.jsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/features/ContactUs.jsx":
/*!***********************************************!*\
  !*** ./src/components/features/ContactUs.jsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Mail,MapPin,Phone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Mail,MapPin,Phone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Mail,MapPin,Phone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst ContactUs = ()=>{\n    _s();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        email: '',\n        message: ''\n    });\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [submitStatus, setSubmitStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null) // 'success', 'error', null\n    ;\n    const handleChange = (e)=>{\n        setFormData({\n            ...formData,\n            [e.target.name]: e.target.value\n        });\n        // Clear status when user starts typing\n        if (submitStatus) {\n            setSubmitStatus(null);\n        }\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setIsSubmitting(true);\n        setSubmitStatus(null);\n        try {\n            const response = await fetch('/api/contact', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(formData)\n            });\n            const result = await response.json();\n            if (response.ok) {\n                setSubmitStatus('success');\n                // Reset form\n                setFormData({\n                    name: '',\n                    email: '',\n                    message: ''\n                });\n            } else {\n                setSubmitStatus('error');\n                console.error('Contact form error:', result.error);\n            }\n        } catch (error) {\n            console.error('Contact form submission failed:', error);\n            setSubmitStatus('error');\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-20 bg-black\",\n        id: \"contact\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.5\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                className: \"mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-medium text-primary\",\n                                    children: \"CONTACT US\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ContactUs.jsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ContactUs.jsx\",\n                                lineNumber: 67,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.h2, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.5,\n                                    delay: 0.1\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                className: \"text-3xl md:text-4xl font-bold text-white mb-6\",\n                                children: \"Let's build something amazing together\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ContactUs.jsx\",\n                                lineNumber: 77,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.p, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.5,\n                                    delay: 0.2\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                className: \"text-gray-400 mb-8\",\n                                children: \"Have a project in mind or want to learn more about our AI solutions? Reach out to our team.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ContactUs.jsx\",\n                                lineNumber: 87,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            duration: 0.5,\n                                            delay: 0.3\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        className: \"flex items-start gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                className: \"h-5 w-5 text-primary mt-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ContactUs.jsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-white font-medium\",\n                                                        children: \"Email\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ContactUs.jsx\",\n                                                        lineNumber: 107,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"mailto:<EMAIL>\",\n                                                        className: \"text-gray-400 hover:text-primary transition-colors\",\n                                                        children: \"<EMAIL>\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ContactUs.jsx\",\n                                                        lineNumber: 108,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ContactUs.jsx\",\n                                                lineNumber: 106,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ContactUs.jsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            duration: 0.5,\n                                            delay: 0.4\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        className: \"flex items-start gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"h-5 w-5 text-primary mt-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ContactUs.jsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-white font-medium\",\n                                                        children: \"Phone\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ContactUs.jsx\",\n                                                        lineNumber: 123,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"tel:+916390168645\",\n                                                        className: \"text-gray-400 hover:text-primary transition-colors\",\n                                                        children: \"+91 6390168645\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ContactUs.jsx\",\n                                                        lineNumber: 124,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ContactUs.jsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ContactUs.jsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            duration: 0.5,\n                                            delay: 0.5\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        className: \"flex items-start gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"h-5 w-5 text-primary mt-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ContactUs.jsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-white font-medium\",\n                                                        children: \"Location\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ContactUs.jsx\",\n                                                        lineNumber: 139,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-400\",\n                                                        children: \"India\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ContactUs.jsx\",\n                                                        lineNumber: 140,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ContactUs.jsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ContactUs.jsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ContactUs.jsx\",\n                                lineNumber: 97,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ContactUs.jsx\",\n                        lineNumber: 66,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.5\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"glass-effect p-8 rounded-xl\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"name\",\n                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                children: \"Your Name\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ContactUs.jsx\",\n                                                lineNumber: 155,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                id: \"name\",\n                                                name: \"name\",\n                                                value: formData.name,\n                                                onChange: handleChange,\n                                                className: \"w-full bg-gray-900/50 border border-white/10 rounded-lg px-4 py-3 text-white focus:ring-2 focus:ring-primary focus:border-transparent\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ContactUs.jsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ContactUs.jsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"email\",\n                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                children: \"Email Address\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ContactUs.jsx\",\n                                                lineNumber: 170,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"email\",\n                                                id: \"email\",\n                                                name: \"email\",\n                                                value: formData.email,\n                                                onChange: handleChange,\n                                                className: \"w-full bg-gray-900/50 border border-white/10 rounded-lg px-4 py-3 text-white focus:ring-2 focus:ring-primary focus:border-transparent\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ContactUs.jsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ContactUs.jsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"message\",\n                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                children: \"Your Message\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ContactUs.jsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                id: \"message\",\n                                                name: \"message\",\n                                                rows: \"5\",\n                                                value: formData.message,\n                                                onChange: handleChange,\n                                                className: \"w-full bg-gray-900/50 border border-white/10 rounded-lg px-4 py-3 text-white focus:ring-2 focus:ring-primary focus:border-transparent\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ContactUs.jsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ContactUs.jsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: isSubmitting,\n                                        className: \"w-full bg-gradient-to-r from-primary to-purple-600 text-white py-3 px-6 rounded-lg font-medium hover:opacity-90 transition-opacity disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        children: isSubmitting ? 'Sending...' : 'Send Message'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ContactUs.jsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    submitStatus === 'success' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 10\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        className: \"p-4 bg-green-500/20 border border-green-500/30 rounded-lg text-green-400 text-sm\",\n                                        children: \"✅ Thank you for your message! We'll get back to you soon.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ContactUs.jsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    submitStatus === 'error' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 10\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        className: \"p-4 bg-red-500/20 border border-red-500/30 rounded-lg text-red-400 text-sm\",\n                                        children: \"❌ Failed to send message. Please try again or contact us directly.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ContactUs.jsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ContactUs.jsx\",\n                                lineNumber: 153,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ContactUs.jsx\",\n                            lineNumber: 152,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ContactUs.jsx\",\n                        lineNumber: 146,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ContactUs.jsx\",\n                lineNumber: 65,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ContactUs.jsx\",\n            lineNumber: 64,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ContactUs.jsx\",\n        lineNumber: 63,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ContactUs, \"EP0YDpKvo9ZgpF2LMfIyL1dq2Yc=\");\n_c = ContactUs;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ContactUs);\nvar _c;\n$RefreshReg$(_c, \"ContactUs\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2ZlYXR1cmVzL0NvbnRhY3RVcy5qc3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUNnQztBQUNNO0FBQ1k7QUFFbEQsTUFBTUssWUFBWTs7SUFDaEIsTUFBTSxDQUFDQyxVQUFVQyxZQUFZLEdBQUdQLCtDQUFRQSxDQUFDO1FBQ3ZDUSxNQUFNO1FBQ05DLE9BQU87UUFDUEMsU0FBUztJQUNYO0lBQ0EsTUFBTSxDQUFDQyxjQUFjQyxnQkFBZ0IsR0FBR1osK0NBQVFBLENBQUM7SUFDakQsTUFBTSxDQUFDYSxjQUFjQyxnQkFBZ0IsR0FBR2QsK0NBQVFBLENBQUMsTUFBTSwyQkFBMkI7O0lBRWxGLE1BQU1lLGVBQWUsQ0FBQ0M7UUFDcEJULFlBQVk7WUFDVixHQUFHRCxRQUFRO1lBQ1gsQ0FBQ1UsRUFBRUMsTUFBTSxDQUFDVCxJQUFJLENBQUMsRUFBRVEsRUFBRUMsTUFBTSxDQUFDQyxLQUFLO1FBQ2pDO1FBQ0EsdUNBQXVDO1FBQ3ZDLElBQUlMLGNBQWM7WUFDaEJDLGdCQUFnQjtRQUNsQjtJQUNGO0lBRUEsTUFBTUssZUFBZSxPQUFPSDtRQUMxQkEsRUFBRUksY0FBYztRQUNoQlIsZ0JBQWdCO1FBQ2hCRSxnQkFBZ0I7UUFFaEIsSUFBSTtZQUNGLE1BQU1PLFdBQVcsTUFBTUMsTUFBTSxnQkFBZ0I7Z0JBQzNDQyxRQUFRO2dCQUNSQyxTQUFTO29CQUNQLGdCQUFnQjtnQkFDbEI7Z0JBQ0FDLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQ3JCO1lBQ3ZCO1lBRUEsTUFBTXNCLFNBQVMsTUFBTVAsU0FBU1EsSUFBSTtZQUVsQyxJQUFJUixTQUFTUyxFQUFFLEVBQUU7Z0JBQ2ZoQixnQkFBZ0I7Z0JBQ2hCLGFBQWE7Z0JBQ2JQLFlBQVk7b0JBQ1ZDLE1BQU07b0JBQ05DLE9BQU87b0JBQ1BDLFNBQVM7Z0JBQ1g7WUFDRixPQUFPO2dCQUNMSSxnQkFBZ0I7Z0JBQ2hCaUIsUUFBUUMsS0FBSyxDQUFDLHVCQUF1QkosT0FBT0ksS0FBSztZQUNuRDtRQUNGLEVBQUUsT0FBT0EsT0FBTztZQUNkRCxRQUFRQyxLQUFLLENBQUMsbUNBQW1DQTtZQUNqRGxCLGdCQUFnQjtRQUNsQixTQUFVO1lBQ1JGLGdCQUFnQjtRQUNsQjtJQUNGO0lBRUEscUJBQ0UsOERBQUNxQjtRQUFRQyxXQUFVO1FBQWlCQyxJQUFHO2tCQUNyQyw0RUFBQ0M7WUFBSUYsV0FBVTtzQkFDYiw0RUFBQ0U7Z0JBQUlGLFdBQVU7O2tDQUNiLDhEQUFDRTs7MENBQ0MsOERBQUNuQyxpREFBTUEsQ0FBQ21DLEdBQUc7Z0NBQ1RDLFNBQVM7b0NBQUVDLFNBQVM7b0NBQUdDLEdBQUc7Z0NBQUc7Z0NBQzdCQyxhQUFhO29DQUFFRixTQUFTO29DQUFHQyxHQUFHO2dDQUFFO2dDQUNoQ0UsWUFBWTtvQ0FBRUMsVUFBVTtnQ0FBSTtnQ0FDNUJDLFVBQVU7b0NBQUVDLE1BQU07Z0NBQUs7Z0NBQ3ZCVixXQUFVOzBDQUVWLDRFQUFDVztvQ0FBS1gsV0FBVTs4Q0FBbUM7Ozs7Ozs7Ozs7OzBDQUdyRCw4REFBQ2pDLGlEQUFNQSxDQUFDNkMsRUFBRTtnQ0FDUlQsU0FBUztvQ0FBRUMsU0FBUztvQ0FBR0MsR0FBRztnQ0FBRztnQ0FDN0JDLGFBQWE7b0NBQUVGLFNBQVM7b0NBQUdDLEdBQUc7Z0NBQUU7Z0NBQ2hDRSxZQUFZO29DQUFFQyxVQUFVO29DQUFLSyxPQUFPO2dDQUFJO2dDQUN4Q0osVUFBVTtvQ0FBRUMsTUFBTTtnQ0FBSztnQ0FDdkJWLFdBQVU7MENBQ1g7Ozs7OzswQ0FJRCw4REFBQ2pDLGlEQUFNQSxDQUFDK0MsQ0FBQztnQ0FDUFgsU0FBUztvQ0FBRUMsU0FBUztvQ0FBR0MsR0FBRztnQ0FBRztnQ0FDN0JDLGFBQWE7b0NBQUVGLFNBQVM7b0NBQUdDLEdBQUc7Z0NBQUU7Z0NBQ2hDRSxZQUFZO29DQUFFQyxVQUFVO29DQUFLSyxPQUFPO2dDQUFJO2dDQUN4Q0osVUFBVTtvQ0FBRUMsTUFBTTtnQ0FBSztnQ0FDdkJWLFdBQVU7MENBQ1g7Ozs7OzswQ0FJRCw4REFBQ0U7Z0NBQUlGLFdBQVU7O2tEQUNiLDhEQUFDakMsaURBQU1BLENBQUNtQyxHQUFHO3dDQUNUQyxTQUFTOzRDQUFFQyxTQUFTOzRDQUFHQyxHQUFHO3dDQUFHO3dDQUM3QkMsYUFBYTs0Q0FBRUYsU0FBUzs0Q0FBR0MsR0FBRzt3Q0FBRTt3Q0FDaENFLFlBQVk7NENBQUVDLFVBQVU7NENBQUtLLE9BQU87d0NBQUk7d0NBQ3hDSixVQUFVOzRDQUFFQyxNQUFNO3dDQUFLO3dDQUN2QlYsV0FBVTs7MERBRVYsOERBQUNoQyw2RkFBSUE7Z0RBQUNnQyxXQUFVOzs7Ozs7MERBQ2hCLDhEQUFDRTs7a0VBQ0MsOERBQUNhO3dEQUFHZixXQUFVO2tFQUF5Qjs7Ozs7O2tFQUN2Qyw4REFBQ2dCO3dEQUFFQyxNQUFLO3dEQUFxQ2pCLFdBQVU7a0VBQXFEOzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBTWhILDhEQUFDakMsaURBQU1BLENBQUNtQyxHQUFHO3dDQUNUQyxTQUFTOzRDQUFFQyxTQUFTOzRDQUFHQyxHQUFHO3dDQUFHO3dDQUM3QkMsYUFBYTs0Q0FBRUYsU0FBUzs0Q0FBR0MsR0FBRzt3Q0FBRTt3Q0FDaENFLFlBQVk7NENBQUVDLFVBQVU7NENBQUtLLE9BQU87d0NBQUk7d0NBQ3hDSixVQUFVOzRDQUFFQyxNQUFNO3dDQUFLO3dDQUN2QlYsV0FBVTs7MERBRVYsOERBQUMvQiw2RkFBS0E7Z0RBQUMrQixXQUFVOzs7Ozs7MERBQ2pCLDhEQUFDRTs7a0VBQ0MsOERBQUNhO3dEQUFHZixXQUFVO2tFQUF5Qjs7Ozs7O2tFQUN2Qyw4REFBQ2dCO3dEQUFFQyxNQUFLO3dEQUFvQmpCLFdBQVU7a0VBQXFEOzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBTS9GLDhEQUFDakMsaURBQU1BLENBQUNtQyxHQUFHO3dDQUNUQyxTQUFTOzRDQUFFQyxTQUFTOzRDQUFHQyxHQUFHO3dDQUFHO3dDQUM3QkMsYUFBYTs0Q0FBRUYsU0FBUzs0Q0FBR0MsR0FBRzt3Q0FBRTt3Q0FDaENFLFlBQVk7NENBQUVDLFVBQVU7NENBQUtLLE9BQU87d0NBQUk7d0NBQ3hDSixVQUFVOzRDQUFFQyxNQUFNO3dDQUFLO3dDQUN2QlYsV0FBVTs7MERBRVYsOERBQUM5Qiw2RkFBTUE7Z0RBQUM4QixXQUFVOzs7Ozs7MERBQ2xCLDhEQUFDRTs7a0VBQ0MsOERBQUNhO3dEQUFHZixXQUFVO2tFQUF5Qjs7Ozs7O2tFQUN2Qyw4REFBQ2M7d0RBQUVkLFdBQVU7a0VBQWdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBTXJDLDhEQUFDakMsaURBQU1BLENBQUNtQyxHQUFHO3dCQUNUQyxTQUFTOzRCQUFFQyxTQUFTOzRCQUFHQyxHQUFHO3dCQUFHO3dCQUM3QkMsYUFBYTs0QkFBRUYsU0FBUzs0QkFBR0MsR0FBRzt3QkFBRTt3QkFDaENFLFlBQVk7NEJBQUVDLFVBQVU7d0JBQUk7d0JBQzVCQyxVQUFVOzRCQUFFQyxNQUFNO3dCQUFLO2tDQUV2Qiw0RUFBQ1E7NEJBQUtDLFVBQVVsQzs0QkFBY2UsV0FBVTtzQ0FDdEMsNEVBQUNFO2dDQUFJRixXQUFVOztrREFDYiw4REFBQ0U7OzBEQUNDLDhEQUFDa0I7Z0RBQU1DLFNBQVE7Z0RBQU9yQixXQUFVOzBEQUErQzs7Ozs7OzBEQUcvRSw4REFBQ3NCO2dEQUNDQyxNQUFLO2dEQUNMdEIsSUFBRztnREFDSDNCLE1BQUs7Z0RBQ0xVLE9BQU9aLFNBQVNFLElBQUk7Z0RBQ3BCa0QsVUFBVTNDO2dEQUNWbUIsV0FBVTtnREFDVnlCLFFBQVE7Ozs7Ozs7Ozs7OztrREFJWiw4REFBQ3ZCOzswREFDQyw4REFBQ2tCO2dEQUFNQyxTQUFRO2dEQUFRckIsV0FBVTswREFBK0M7Ozs7OzswREFHaEYsOERBQUNzQjtnREFDQ0MsTUFBSztnREFDTHRCLElBQUc7Z0RBQ0gzQixNQUFLO2dEQUNMVSxPQUFPWixTQUFTRyxLQUFLO2dEQUNyQmlELFVBQVUzQztnREFDVm1CLFdBQVU7Z0RBQ1Z5QixRQUFROzs7Ozs7Ozs7Ozs7a0RBSVosOERBQUN2Qjs7MERBQ0MsOERBQUNrQjtnREFBTUMsU0FBUTtnREFBVXJCLFdBQVU7MERBQStDOzs7Ozs7MERBR2xGLDhEQUFDMEI7Z0RBQ0N6QixJQUFHO2dEQUNIM0IsTUFBSztnREFDTHFELE1BQUs7Z0RBQ0wzQyxPQUFPWixTQUFTSSxPQUFPO2dEQUN2QmdELFVBQVUzQztnREFDVm1CLFdBQVU7Z0RBQ1Z5QixRQUFROzs7Ozs7Ozs7Ozs7a0RBSVosOERBQUNHO3dDQUNDTCxNQUFLO3dDQUNMTSxVQUFVcEQ7d0NBQ1Z1QixXQUFVO2tEQUVUdkIsZUFBZSxlQUFlOzs7Ozs7b0NBSWhDRSxpQkFBaUIsMkJBQ2hCLDhEQUFDWixpREFBTUEsQ0FBQ21DLEdBQUc7d0NBQ1RDLFNBQVM7NENBQUVDLFNBQVM7NENBQUdDLEdBQUc7d0NBQUc7d0NBQzdCeUIsU0FBUzs0Q0FBRTFCLFNBQVM7NENBQUdDLEdBQUc7d0NBQUU7d0NBQzVCTCxXQUFVO2tEQUNYOzs7Ozs7b0NBS0ZyQixpQkFBaUIseUJBQ2hCLDhEQUFDWixpREFBTUEsQ0FBQ21DLEdBQUc7d0NBQ1RDLFNBQVM7NENBQUVDLFNBQVM7NENBQUdDLEdBQUc7d0NBQUc7d0NBQzdCeUIsU0FBUzs0Q0FBRTFCLFNBQVM7NENBQUdDLEdBQUc7d0NBQUU7d0NBQzVCTCxXQUFVO2tEQUNYOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVduQjtHQXBPTTdCO0tBQUFBO0FBc09OLGlFQUFlQSxTQUFTQSxFQUFBIiwic291cmNlcyI6WyJDOlxcUHJvamVjdHNcXE5ld0JsaW5rRmluZEFJXFxmcm9udGVuZC1tYXN0ZXJcXHNyY1xcY29tcG9uZW50c1xcZmVhdHVyZXNcXENvbnRhY3RVcy5qc3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5pbXBvcnQgeyB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0J1xuaW1wb3J0IHsgbW90aW9uIH0gZnJvbSAnZnJhbWVyLW1vdGlvbidcbmltcG9ydCB7IE1haWwsIFBob25lLCBNYXBQaW4gfSBmcm9tICdsdWNpZGUtcmVhY3QnXG5cbmNvbnN0IENvbnRhY3RVcyA9ICgpID0+IHtcbiAgY29uc3QgW2Zvcm1EYXRhLCBzZXRGb3JtRGF0YV0gPSB1c2VTdGF0ZSh7XG4gICAgbmFtZTogJycsXG4gICAgZW1haWw6ICcnLFxuICAgIG1lc3NhZ2U6ICcnXG4gIH0pXG4gIGNvbnN0IFtpc1N1Ym1pdHRpbmcsIHNldElzU3VibWl0dGluZ10gPSB1c2VTdGF0ZShmYWxzZSlcbiAgY29uc3QgW3N1Ym1pdFN0YXR1cywgc2V0U3VibWl0U3RhdHVzXSA9IHVzZVN0YXRlKG51bGwpIC8vICdzdWNjZXNzJywgJ2Vycm9yJywgbnVsbFxuXG4gIGNvbnN0IGhhbmRsZUNoYW5nZSA9IChlKSA9PiB7XG4gICAgc2V0Rm9ybURhdGEoe1xuICAgICAgLi4uZm9ybURhdGEsXG4gICAgICBbZS50YXJnZXQubmFtZV06IGUudGFyZ2V0LnZhbHVlXG4gICAgfSlcbiAgICAvLyBDbGVhciBzdGF0dXMgd2hlbiB1c2VyIHN0YXJ0cyB0eXBpbmdcbiAgICBpZiAoc3VibWl0U3RhdHVzKSB7XG4gICAgICBzZXRTdWJtaXRTdGF0dXMobnVsbClcbiAgICB9XG4gIH1cblxuICBjb25zdCBoYW5kbGVTdWJtaXQgPSBhc3luYyAoZSkgPT4ge1xuICAgIGUucHJldmVudERlZmF1bHQoKVxuICAgIHNldElzU3VibWl0dGluZyh0cnVlKVxuICAgIHNldFN1Ym1pdFN0YXR1cyhudWxsKVxuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGkvY29udGFjdCcsIHtcbiAgICAgICAgbWV0aG9kOiAnUE9TVCcsXG4gICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9LFxuICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeShmb3JtRGF0YSksXG4gICAgICB9KVxuXG4gICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCByZXNwb25zZS5qc29uKClcblxuICAgICAgaWYgKHJlc3BvbnNlLm9rKSB7XG4gICAgICAgIHNldFN1Ym1pdFN0YXR1cygnc3VjY2VzcycpXG4gICAgICAgIC8vIFJlc2V0IGZvcm1cbiAgICAgICAgc2V0Rm9ybURhdGEoe1xuICAgICAgICAgIG5hbWU6ICcnLFxuICAgICAgICAgIGVtYWlsOiAnJyxcbiAgICAgICAgICBtZXNzYWdlOiAnJ1xuICAgICAgICB9KVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgc2V0U3VibWl0U3RhdHVzKCdlcnJvcicpXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0NvbnRhY3QgZm9ybSBlcnJvcjonLCByZXN1bHQuZXJyb3IpXG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0NvbnRhY3QgZm9ybSBzdWJtaXNzaW9uIGZhaWxlZDonLCBlcnJvcilcbiAgICAgIHNldFN1Ym1pdFN0YXR1cygnZXJyb3InKVxuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRJc1N1Ym1pdHRpbmcoZmFsc2UpXG4gICAgfVxuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8c2VjdGlvbiBjbGFzc05hbWU9XCJweS0yMCBiZy1ibGFja1wiIGlkPVwiY29udGFjdFwiPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb250YWluZXIgbXgtYXV0byBweC02XCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBsZzpncmlkLWNvbHMtMiBnYXAtMTJcIj5cbiAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgPG1vdGlvbi5kaXYgXG4gICAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeTogMjAgfX1cbiAgICAgICAgICAgICAgd2hpbGVJblZpZXc9e3sgb3BhY2l0eTogMSwgeTogMCB9fVxuICAgICAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjUgfX1cbiAgICAgICAgICAgICAgdmlld3BvcnQ9e3sgb25jZTogdHJ1ZSB9fVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJtYi00XCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LXByaW1hcnlcIj5DT05UQUNUIFVTPC9zcGFuPlxuICAgICAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgICAgICAgXG4gICAgICAgICAgICA8bW90aW9uLmgyXG4gICAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeTogMjAgfX1cbiAgICAgICAgICAgICAgd2hpbGVJblZpZXc9e3sgb3BhY2l0eTogMSwgeTogMCB9fVxuICAgICAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjUsIGRlbGF5OiAwLjEgfX1cbiAgICAgICAgICAgICAgdmlld3BvcnQ9e3sgb25jZTogdHJ1ZSB9fVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LTN4bCBtZDp0ZXh0LTR4bCBmb250LWJvbGQgdGV4dC13aGl0ZSBtYi02XCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgTGV0J3MgYnVpbGQgc29tZXRoaW5nIGFtYXppbmcgdG9nZXRoZXJcbiAgICAgICAgICAgIDwvbW90aW9uLmgyPlxuICAgICAgICAgICAgXG4gICAgICAgICAgICA8bW90aW9uLnBcbiAgICAgICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB5OiAyMCB9fVxuICAgICAgICAgICAgICB3aGlsZUluVmlldz17eyBvcGFjaXR5OiAxLCB5OiAwIH19XG4gICAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDAuNSwgZGVsYXk6IDAuMiB9fVxuICAgICAgICAgICAgICB2aWV3cG9ydD17eyBvbmNlOiB0cnVlIH19XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDAgbWItOFwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIEhhdmUgYSBwcm9qZWN0IGluIG1pbmQgb3Igd2FudCB0byBsZWFybiBtb3JlIGFib3V0IG91ciBBSSBzb2x1dGlvbnM/IFJlYWNoIG91dCB0byBvdXIgdGVhbS5cbiAgICAgICAgICAgIDwvbW90aW9uLnA+XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICAgIDxtb3Rpb24uZGl2IFxuICAgICAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeTogMjAgfX1cbiAgICAgICAgICAgICAgICB3aGlsZUluVmlldz17eyBvcGFjaXR5OiAxLCB5OiAwIH19XG4gICAgICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMC41LCBkZWxheTogMC4zIH19XG4gICAgICAgICAgICAgICAgdmlld3BvcnQ9e3sgb25jZTogdHJ1ZSB9fVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtc3RhcnQgZ2FwLTRcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPE1haWwgY2xhc3NOYW1lPVwiaC01IHctNSB0ZXh0LXByaW1hcnkgbXQtMVwiIC8+XG4gICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIGZvbnQtbWVkaXVtXCI+RW1haWw8L2g0PlxuICAgICAgICAgICAgICAgICAgPGEgaHJlZj1cIm1haWx0bzpibGlua2ZpbmRvZmZpY2lhbEBnbWFpbC5jb21cIiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwIGhvdmVyOnRleHQtcHJpbWFyeSB0cmFuc2l0aW9uLWNvbG9yc1wiPlxuICAgICAgICAgICAgICAgICAgICBibGlua2ZpbmRvZmZpY2lhbEBnbWFpbC5jb21cbiAgICAgICAgICAgICAgICAgIDwvYT5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgPG1vdGlvbi5kaXYgXG4gICAgICAgICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB5OiAyMCB9fVxuICAgICAgICAgICAgICAgIHdoaWxlSW5WaWV3PXt7IG9wYWNpdHk6IDEsIHk6IDAgfX1cbiAgICAgICAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjUsIGRlbGF5OiAwLjQgfX1cbiAgICAgICAgICAgICAgICB2aWV3cG9ydD17eyBvbmNlOiB0cnVlIH19XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1zdGFydCBnYXAtNFwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8UGhvbmUgY2xhc3NOYW1lPVwiaC01IHctNSB0ZXh0LXByaW1hcnkgbXQtMVwiIC8+XG4gICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIGZvbnQtbWVkaXVtXCI+UGhvbmU8L2g0PlxuICAgICAgICAgICAgICAgICAgPGEgaHJlZj1cInRlbDorOTE2MzkwMTY4NjQ1XCIgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMCBob3Zlcjp0ZXh0LXByaW1hcnkgdHJhbnNpdGlvbi1jb2xvcnNcIj5cbiAgICAgICAgICAgICAgICAgICAgKzkxIDYzOTAxNjg2NDVcbiAgICAgICAgICAgICAgICAgIDwvYT5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgPG1vdGlvbi5kaXYgXG4gICAgICAgICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB5OiAyMCB9fVxuICAgICAgICAgICAgICAgIHdoaWxlSW5WaWV3PXt7IG9wYWNpdHk6IDEsIHk6IDAgfX1cbiAgICAgICAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjUsIGRlbGF5OiAwLjUgfX1cbiAgICAgICAgICAgICAgICB2aWV3cG9ydD17eyBvbmNlOiB0cnVlIH19XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1zdGFydCBnYXAtNFwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8TWFwUGluIGNsYXNzTmFtZT1cImgtNSB3LTUgdGV4dC1wcmltYXJ5IG10LTFcIiAvPlxuICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwidGV4dC13aGl0ZSBmb250LW1lZGl1bVwiPkxvY2F0aW9uPC9oND5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDBcIj5JbmRpYTwvcD5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgXG4gICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeTogMjAgfX1cbiAgICAgICAgICAgIHdoaWxlSW5WaWV3PXt7IG9wYWNpdHk6IDEsIHk6IDAgfX1cbiAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDAuNSB9fVxuICAgICAgICAgICAgdmlld3BvcnQ9e3sgb25jZTogdHJ1ZSB9fVxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxmb3JtIG9uU3VibWl0PXtoYW5kbGVTdWJtaXR9IGNsYXNzTmFtZT1cImdsYXNzLWVmZmVjdCBwLTggcm91bmRlZC14bFwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNlwiPlxuICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICA8bGFiZWwgaHRtbEZvcj1cIm5hbWVcIiBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS0zMDAgbWItMlwiPlxuICAgICAgICAgICAgICAgICAgICBZb3VyIE5hbWVcbiAgICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICAgICAgICBpZD1cIm5hbWVcIlxuICAgICAgICAgICAgICAgICAgICBuYW1lPVwibmFtZVwiXG4gICAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5uYW1lfVxuICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlQ2hhbmdlfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgYmctZ3JheS05MDAvNTAgYm9yZGVyIGJvcmRlci13aGl0ZS8xMCByb3VuZGVkLWxnIHB4LTQgcHktMyB0ZXh0LXdoaXRlIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLXByaW1hcnkgZm9jdXM6Ym9yZGVyLXRyYW5zcGFyZW50XCJcbiAgICAgICAgICAgICAgICAgICAgcmVxdWlyZWRcbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgIDxsYWJlbCBodG1sRm9yPVwiZW1haWxcIiBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS0zMDAgbWItMlwiPlxuICAgICAgICAgICAgICAgICAgICBFbWFpbCBBZGRyZXNzXG4gICAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgIHR5cGU9XCJlbWFpbFwiXG4gICAgICAgICAgICAgICAgICAgIGlkPVwiZW1haWxcIlxuICAgICAgICAgICAgICAgICAgICBuYW1lPVwiZW1haWxcIlxuICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEuZW1haWx9XG4gICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVDaGFuZ2V9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBiZy1ncmF5LTkwMC81MCBib3JkZXIgYm9yZGVyLXdoaXRlLzEwIHJvdW5kZWQtbGcgcHgtNCBweS0zIHRleHQtd2hpdGUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctcHJpbWFyeSBmb2N1czpib3JkZXItdHJhbnNwYXJlbnRcIlxuICAgICAgICAgICAgICAgICAgICByZXF1aXJlZFxuICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgPGxhYmVsIGh0bWxGb3I9XCJtZXNzYWdlXCIgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktMzAwIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgWW91ciBNZXNzYWdlXG4gICAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgPHRleHRhcmVhXG4gICAgICAgICAgICAgICAgICAgIGlkPVwibWVzc2FnZVwiXG4gICAgICAgICAgICAgICAgICAgIG5hbWU9XCJtZXNzYWdlXCJcbiAgICAgICAgICAgICAgICAgICAgcm93cz1cIjVcIlxuICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEubWVzc2FnZX1cbiAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9e2hhbmRsZUNoYW5nZX1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGJnLWdyYXktOTAwLzUwIGJvcmRlciBib3JkZXItd2hpdGUvMTAgcm91bmRlZC1sZyBweC00IHB5LTMgdGV4dC13aGl0ZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1wcmltYXJ5IGZvY3VzOmJvcmRlci10cmFuc3BhcmVudFwiXG4gICAgICAgICAgICAgICAgICAgIHJlcXVpcmVkXG4gICAgICAgICAgICAgICAgICA+PC90ZXh0YXJlYT5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICB0eXBlPVwic3VibWl0XCJcbiAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXtpc1N1Ym1pdHRpbmd9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgYmctZ3JhZGllbnQtdG8tciBmcm9tLXByaW1hcnkgdG8tcHVycGxlLTYwMCB0ZXh0LXdoaXRlIHB5LTMgcHgtNiByb3VuZGVkLWxnIGZvbnQtbWVkaXVtIGhvdmVyOm9wYWNpdHktOTAgdHJhbnNpdGlvbi1vcGFjaXR5IGRpc2FibGVkOm9wYWNpdHktNTAgZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICB7aXNTdWJtaXR0aW5nID8gJ1NlbmRpbmcuLi4nIDogJ1NlbmQgTWVzc2FnZSd9XG4gICAgICAgICAgICAgICAgPC9idXR0b24+XG5cbiAgICAgICAgICAgICAgICB7LyogU3RhdHVzIE1lc3NhZ2VzICovfVxuICAgICAgICAgICAgICAgIHtzdWJtaXRTdGF0dXMgPT09ICdzdWNjZXNzJyAmJiAoXG4gICAgICAgICAgICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgICAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHk6IDEwIH19XG4gICAgICAgICAgICAgICAgICAgIGFuaW1hdGU9e3sgb3BhY2l0eTogMSwgeTogMCB9fVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwLTQgYmctZ3JlZW4tNTAwLzIwIGJvcmRlciBib3JkZXItZ3JlZW4tNTAwLzMwIHJvdW5kZWQtbGcgdGV4dC1ncmVlbi00MDAgdGV4dC1zbVwiXG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIOKchSBUaGFuayB5b3UgZm9yIHlvdXIgbWVzc2FnZSEgV2UnbGwgZ2V0IGJhY2sgdG8geW91IHNvb24uXG4gICAgICAgICAgICAgICAgICA8L21vdGlvbi5kaXY+XG4gICAgICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAgICAgIHtzdWJtaXRTdGF0dXMgPT09ICdlcnJvcicgJiYgKFxuICAgICAgICAgICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB5OiAxMCB9fVxuICAgICAgICAgICAgICAgICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEsIHk6IDAgfX1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicC00IGJnLXJlZC01MDAvMjAgYm9yZGVyIGJvcmRlci1yZWQtNTAwLzMwIHJvdW5kZWQtbGcgdGV4dC1yZWQtNDAwIHRleHQtc21cIlxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICDinYwgRmFpbGVkIHRvIHNlbmQgbWVzc2FnZS4gUGxlYXNlIHRyeSBhZ2FpbiBvciBjb250YWN0IHVzIGRpcmVjdGx5LlxuICAgICAgICAgICAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9mb3JtPlxuICAgICAgICAgIDwvbW90aW9uLmRpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICA8L3NlY3Rpb24+XG4gIClcbn1cblxuZXhwb3J0IGRlZmF1bHQgQ29udGFjdFVzXG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJtb3Rpb24iLCJNYWlsIiwiUGhvbmUiLCJNYXBQaW4iLCJDb250YWN0VXMiLCJmb3JtRGF0YSIsInNldEZvcm1EYXRhIiwibmFtZSIsImVtYWlsIiwibWVzc2FnZSIsImlzU3VibWl0dGluZyIsInNldElzU3VibWl0dGluZyIsInN1Ym1pdFN0YXR1cyIsInNldFN1Ym1pdFN0YXR1cyIsImhhbmRsZUNoYW5nZSIsImUiLCJ0YXJnZXQiLCJ2YWx1ZSIsImhhbmRsZVN1Ym1pdCIsInByZXZlbnREZWZhdWx0IiwicmVzcG9uc2UiLCJmZXRjaCIsIm1ldGhvZCIsImhlYWRlcnMiLCJib2R5IiwiSlNPTiIsInN0cmluZ2lmeSIsInJlc3VsdCIsImpzb24iLCJvayIsImNvbnNvbGUiLCJlcnJvciIsInNlY3Rpb24iLCJjbGFzc05hbWUiLCJpZCIsImRpdiIsImluaXRpYWwiLCJvcGFjaXR5IiwieSIsIndoaWxlSW5WaWV3IiwidHJhbnNpdGlvbiIsImR1cmF0aW9uIiwidmlld3BvcnQiLCJvbmNlIiwic3BhbiIsImgyIiwiZGVsYXkiLCJwIiwiaDQiLCJhIiwiaHJlZiIsImZvcm0iLCJvblN1Ym1pdCIsImxhYmVsIiwiaHRtbEZvciIsImlucHV0IiwidHlwZSIsIm9uQ2hhbmdlIiwicmVxdWlyZWQiLCJ0ZXh0YXJlYSIsInJvd3MiLCJidXR0b24iLCJkaXNhYmxlZCIsImFuaW1hdGUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/features/ContactUs.jsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/features/PlatformStats.jsx":
/*!***************************************************!*\
  !*** ./src/components/features/PlatformStats.jsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* harmony import */ var _firebase_config__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/firebase/config */ \"(app-pages-browser)/./src/firebase/config.js\");\n/* harmony import */ var _barrel_optimize_names_MessageSquare_Star_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=MessageSquare,Star,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_MessageSquare_Star_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=MessageSquare,Star,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_MessageSquare_Star_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=MessageSquare,Star,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_MessageSquare_Star_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=MessageSquare,Star,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-in-view.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n// Simple animated number hook\nconst useAnimatedNumber = function(endValue) {\n    let duration = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 2, shouldAnimate = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;\n    _s();\n    const [displayValue, setDisplayValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"useAnimatedNumber.useEffect\": ()=>{\n            if (!shouldAnimate) return;\n            let startTime;\n            let animationFrame;\n            const animate = {\n                \"useAnimatedNumber.useEffect.animate\": (timestamp)=>{\n                    if (!startTime) startTime = timestamp;\n                    const progress = Math.min((timestamp - startTime) / (duration * 1000), 1);\n                    const easeOutQuart = 1 - Math.pow(1 - progress, 4);\n                    const currentValue = Math.floor(easeOutQuart * endValue);\n                    setDisplayValue(currentValue);\n                    if (progress < 1) {\n                        animationFrame = requestAnimationFrame(animate);\n                    } else {\n                        setDisplayValue(endValue);\n                    }\n                }\n            }[\"useAnimatedNumber.useEffect.animate\"];\n            animationFrame = requestAnimationFrame(animate);\n            return ({\n                \"useAnimatedNumber.useEffect\": ()=>{\n                    if (animationFrame) {\n                        cancelAnimationFrame(animationFrame);\n                    }\n                }\n            })[\"useAnimatedNumber.useEffect\"];\n        }\n    }[\"useAnimatedNumber.useEffect\"], [\n        endValue,\n        duration,\n        shouldAnimate\n    ]);\n    return displayValue;\n};\n_s(useAnimatedNumber, \"4hUZowD5MfzMUTSh4rovgv/1zfU=\");\nconst PlatformStatsSimple = ()=>{\n    _s1();\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalUsers: 0,\n        totalReviews: 0,\n        averageRating: 0,\n        successRate: 98\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const isInView = (0,framer_motion__WEBPACK_IMPORTED_MODULE_4__.useInView)(ref, {\n        once: true,\n        margin: \"-100px\"\n    });\n    const animatedUsers = useAnimatedNumber(stats.totalUsers, 2.5, isInView && !loading);\n    const animatedReviews = useAnimatedNumber(stats.totalReviews, 2, isInView && !loading);\n    const animatedRating = useAnimatedNumber(stats.averageRating * 10, 2, isInView && !loading) / 10;\n    const animatedSuccess = useAnimatedNumber(stats.successRate, 2, isInView && !loading);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PlatformStatsSimple.useEffect\": ()=>{\n            // Try to connect to Firebase\n            const setupFirebase = {\n                \"PlatformStatsSimple.useEffect.setupFirebase\": async ()=>{\n                    try {\n                        if (!_firebase_config__WEBPACK_IMPORTED_MODULE_3__.firestore || !_firebase_config__WEBPACK_IMPORTED_MODULE_3__.firestore.app) {\n                            console.log('🔥 Firestore not available, using demo data');\n                            setStats({\n                                totalUsers: 5847,\n                                totalReviews: 247,\n                                averageRating: 4.8,\n                                successRate: 98\n                            });\n                            setLoading(false);\n                            setIsConnected(false);\n                            return;\n                        }\n                        console.log('🔥 Setting up Firebase listeners...');\n                        // Set up real-time listener for users\n                        const unsubscribeUsers = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.onSnapshot)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_firebase_config__WEBPACK_IMPORTED_MODULE_3__.firestore, 'users'), {\n                            \"PlatformStatsSimple.useEffect.setupFirebase.unsubscribeUsers\": (snapshot)=>{\n                                const userCount = snapshot.size;\n                                console.log('👥 Users updated:', userCount);\n                                setStats({\n                                    \"PlatformStatsSimple.useEffect.setupFirebase.unsubscribeUsers\": (prev)=>({\n                                            ...prev,\n                                            totalUsers: Math.max(userCount, 5847)\n                                        })\n                                }[\"PlatformStatsSimple.useEffect.setupFirebase.unsubscribeUsers\"]);\n                                setIsConnected(true);\n                            }\n                        }[\"PlatformStatsSimple.useEffect.setupFirebase.unsubscribeUsers\"], {\n                            \"PlatformStatsSimple.useEffect.setupFirebase.unsubscribeUsers\": (error)=>{\n                                console.error('❌ Users listener error:', error);\n                                setIsConnected(false);\n                            }\n                        }[\"PlatformStatsSimple.useEffect.setupFirebase.unsubscribeUsers\"]);\n                        // Set up real-time listener for reviews\n                        const unsubscribeReviews = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.onSnapshot)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_firebase_config__WEBPACK_IMPORTED_MODULE_3__.firestore, 'reviews'), {\n                            \"PlatformStatsSimple.useEffect.setupFirebase.unsubscribeReviews\": (snapshot)=>{\n                                let totalRating = 0;\n                                let reviewCount = 0;\n                                snapshot.forEach({\n                                    \"PlatformStatsSimple.useEffect.setupFirebase.unsubscribeReviews\": (doc)=>{\n                                        const data = doc.data();\n                                        if (data.rating && typeof data.rating === 'number') {\n                                            totalRating += data.rating;\n                                            reviewCount++;\n                                        }\n                                    }\n                                }[\"PlatformStatsSimple.useEffect.setupFirebase.unsubscribeReviews\"]);\n                                const averageRating = reviewCount > 0 ? totalRating / reviewCount : 4.8;\n                                console.log('⭐ Reviews updated:', {\n                                    reviewCount,\n                                    averageRating\n                                });\n                                setStats({\n                                    \"PlatformStatsSimple.useEffect.setupFirebase.unsubscribeReviews\": (prev)=>({\n                                            ...prev,\n                                            totalReviews: reviewCount,\n                                            averageRating: parseFloat(averageRating.toFixed(1))\n                                        })\n                                }[\"PlatformStatsSimple.useEffect.setupFirebase.unsubscribeReviews\"]);\n                                setIsConnected(true);\n                            }\n                        }[\"PlatformStatsSimple.useEffect.setupFirebase.unsubscribeReviews\"], {\n                            \"PlatformStatsSimple.useEffect.setupFirebase.unsubscribeReviews\": (error)=>{\n                                console.error('❌ Reviews listener error:', error);\n                                setIsConnected(false);\n                            }\n                        }[\"PlatformStatsSimple.useEffect.setupFirebase.unsubscribeReviews\"]);\n                        setLoading(false);\n                        return ({\n                            \"PlatformStatsSimple.useEffect.setupFirebase\": ()=>{\n                                if (unsubscribeUsers) unsubscribeUsers();\n                                if (unsubscribeReviews) unsubscribeReviews();\n                                console.log('🧹 Firebase listeners cleaned up');\n                            }\n                        })[\"PlatformStatsSimple.useEffect.setupFirebase\"];\n                    } catch (error) {\n                        console.error('❌ Firebase setup error:', error);\n                        setStats({\n                            totalUsers: 5847,\n                            totalReviews: 247,\n                            averageRating: 4.8,\n                            successRate: 98\n                        });\n                        setLoading(false);\n                        setIsConnected(false);\n                    }\n                }\n            }[\"PlatformStatsSimple.useEffect.setupFirebase\"];\n            setupFirebase();\n        }\n    }[\"PlatformStatsSimple.useEffect\"], []);\n    const statItems = [\n        {\n            icon: _barrel_optimize_names_MessageSquare_Star_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            label: 'Active Users',\n            value: loading ? '...' : \"\".concat(animatedUsers.toLocaleString(), \"+\"),\n            gradient: 'from-blue-500 via-cyan-500 to-teal-500',\n            iconBg: 'bg-blue-500/20',\n            isLive: true\n        },\n        {\n            icon: _barrel_optimize_names_MessageSquare_Star_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            label: 'User Reviews',\n            value: loading ? '...' : animatedReviews.toLocaleString(),\n            gradient: 'from-purple-500 via-pink-500 to-rose-500',\n            iconBg: 'bg-purple-500/20',\n            isLive: true\n        },\n        {\n            icon: _barrel_optimize_names_MessageSquare_Star_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            label: 'Average Rating',\n            value: loading ? '...' : \"\".concat(animatedRating.toFixed(1), \"/5\"),\n            gradient: 'from-yellow-500 via-orange-500 to-red-500',\n            iconBg: 'bg-yellow-500/20',\n            isLive: false\n        },\n        {\n            icon: _barrel_optimize_names_MessageSquare_Star_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            label: 'Success Rate',\n            value: loading ? '...' : \"\".concat(animatedSuccess, \"%\"),\n            gradient: 'from-green-500 via-emerald-500 to-teal-500',\n            iconBg: 'bg-green-500/20',\n            isLive: false\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n        ref: ref,\n        className: \"mt-8 w-full\",\n        initial: {\n            opacity: 0,\n            y: 20\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        transition: {\n            duration: 0.6,\n            delay: 0.5\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-wrap items-center justify-center gap-6 lg:gap-8\",\n            children: statItems.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                    className: \"flex items-center gap-3 group\",\n                    initial: {\n                        opacity: 0,\n                        scale: 0.9\n                    },\n                    animate: {\n                        opacity: 1,\n                        scale: 1\n                    },\n                    transition: {\n                        duration: 0.5,\n                        delay: 0.6 + index * 0.1\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"\".concat(stat.iconBg, \" p-2 rounded-lg backdrop-blur-sm border border-white/10 relative\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(stat.icon, {\n                                    className: \"h-4 w-4 text-white\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\PlatformStats.jsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 15\n                                }, undefined),\n                                stat.isLive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute -top-1 -right-1 w-2 h-2 rounded-full animate-pulse \".concat(isConnected ? 'bg-green-400' : 'bg-yellow-400')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\PlatformStats.jsx\",\n                                    lineNumber: 212,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\PlatformStats.jsx\",\n                            lineNumber: 209,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-left\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-lg font-bold bg-gradient-to-r \".concat(stat.gradient, \" bg-clip-text text-transparent\"),\n                                    children: stat.value\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\PlatformStats.jsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-400 leading-tight\",\n                                    children: stat.label\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\PlatformStats.jsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\PlatformStats.jsx\",\n                            lineNumber: 217,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, stat.label, true, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\PlatformStats.jsx\",\n                    lineNumber: 199,\n                    columnNumber: 11\n                }, undefined))\n        }, void 0, false, {\n            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\PlatformStats.jsx\",\n            lineNumber: 197,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\PlatformStats.jsx\",\n        lineNumber: 190,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(PlatformStatsSimple, \"ZdQRbc1CgLN1iL5hLffLYUrRsN8=\", false, function() {\n    return [\n        framer_motion__WEBPACK_IMPORTED_MODULE_4__.useInView,\n        useAnimatedNumber,\n        useAnimatedNumber,\n        useAnimatedNumber,\n        useAnimatedNumber\n    ];\n});\n_c = PlatformStatsSimple;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PlatformStatsSimple);\nvar _c;\n$RefreshReg$(_c, \"PlatformStatsSimple\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/features/PlatformStats.jsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/features/ReviewsSection.jsx":
/*!****************************************************!*\
  !*** ./src/components/features/ReviewsSection.jsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.jsx\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* harmony import */ var _firebase_config__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/firebase/config */ \"(app-pages-browser)/./src/firebase/config.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\n\n\nconst StarRating = (param)=>{\n    let { rating, onRatingChange, readonly = false } = param;\n    _s();\n    const [hover, setHover] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center\",\n        children: [\n            ...Array(5)\n        ].map((_, index)=>{\n            const ratingValue = index + 1;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                type: \"button\",\n                className: \"text-2xl \".concat(ratingValue <= (hover || rating) ? 'text-yellow-400' : 'text-gray-600', \" \").concat(readonly ? 'cursor-default' : 'cursor-pointer hover:text-yellow-300', \" transition-colors\"),\n                onClick: ()=>!readonly && onRatingChange && onRatingChange(ratingValue),\n                onMouseEnter: ()=>!readonly && setHover(ratingValue),\n                onMouseLeave: ()=>!readonly && setHover(0),\n                disabled: readonly,\n                children: \"★\"\n            }, index, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                lineNumber: 25,\n                columnNumber: 11\n            }, undefined);\n        })\n    }, void 0, false, {\n        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, undefined);\n};\n_s(StarRating, \"CigvHnV0FUShgPKup4xazDU6StM=\");\n_c = StarRating;\nconst ReviewCard = (param)=>{\n    let { review } = param;\n    const formatDate = (timestamp)=>{\n        if (!timestamp) return '';\n        const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);\n        return date.toLocaleDateString('en-US', {\n            year: 'numeric',\n            month: 'short',\n            day: 'numeric'\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gray-900/20 backdrop-blur-md p-6 rounded-2xl border border-white/10 mb-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-start justify-between mb-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-10 h-10 bg-gradient-to-r from-neural-purple to-neural-pink rounded-full flex items-center justify-center text-white font-semibold mr-3\",\n                                children: review.userEmail ? review.userEmail.charAt(0).toUpperCase() : 'A'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                                lineNumber: 61,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white font-medium\",\n                                        children: review.userEmail ? review.userEmail.split('@')[0] : 'Anonymous'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                                        lineNumber: 65,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: formatDate(review.createdAt)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                                lineNumber: 64,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                        lineNumber: 60,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StarRating, {\n                        rating: review.rating,\n                        readonly: true\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                        lineNumber: 71,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-gray-300 leading-relaxed\",\n                children: review.comment\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                lineNumber: 73,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n        lineNumber: 58,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = ReviewCard;\nconst ReviewForm = (param)=>{\n    let { onReviewSubmitted } = param;\n    _s1();\n    const [rating, setRating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [comment, setComment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const { currentUser } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!currentUser) {\n            setError('You must be logged in to submit a review.');\n            return;\n        }\n        if (rating === 0) {\n            setError('Please select a rating.');\n            return;\n        }\n        if (comment.trim().length < 10) {\n            setError('Please write at least 10 characters in your review.');\n            return;\n        }\n        setLoading(true);\n        setError('');\n        try {\n            const reviewData = {\n                userId: currentUser.uid,\n                userEmail: currentUser.email,\n                rating: rating,\n                comment: comment.trim(),\n                createdAt: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.serverTimestamp)(),\n                approved: false,\n                status: 'pending' // Track approval status\n            };\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.addDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.collection)(_firebase_config__WEBPACK_IMPORTED_MODULE_4__.firestore, 'reviews'), reviewData);\n            // Reset form\n            setRating(0);\n            setComment('');\n            // Show success message\n            alert('Thank you for your review! It will be displayed after admin approval.');\n            // Notify parent component\n            if (onReviewSubmitted) {\n                onReviewSubmitted();\n            }\n        } catch (err) {\n            console.error('Error submitting review:', err);\n            setError('Failed to submit review. Please try again.');\n        } finally{\n            setLoading(false);\n        }\n    };\n    if (!currentUser) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-gray-900/20 backdrop-blur-md p-6 rounded-2xl border border-white/10 text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-300 mb-4\",\n                    children: \"Please log in to submit a review.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                    lineNumber: 141,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                    href: \"/login\",\n                    className: \"bg-gradient-to-r from-neural-purple to-neural-pink text-white px-6 py-2 rounded-lg hover:opacity-90 transition-opacity\",\n                    children: \"Log In\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                    lineNumber: 142,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n            lineNumber: 140,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gray-900/20 backdrop-blur-md p-6 rounded-2xl border border-white/10\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-xl font-bold text-white mb-4\",\n                children: \"Share Your Experience\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                lineNumber: 154,\n                columnNumber: 7\n            }, undefined),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4 p-3 bg-red-500/20 border border-red-500/30 rounded-lg text-red-300 text-sm\",\n                children: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                lineNumber: 157,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit,\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                children: \"Your Rating\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                                lineNumber: 164,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StarRating, {\n                                rating: rating,\n                                onRatingChange: setRating\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                                lineNumber: 167,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                        lineNumber: 163,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"comment\",\n                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                children: \"Your Review\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                                lineNumber: 171,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                id: \"comment\",\n                                value: comment,\n                                onChange: (e)=>setComment(e.target.value),\n                                rows: 4,\n                                className: \"w-full px-4 py-3 bg-gray-900/50 border border-white/10 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-neural-purple/50 focus:border-neural-purple text-white placeholder-gray-400 transition-all duration-300 ease-in-out backdrop-blur-sm resize-none\",\n                                placeholder: \"Tell us about your experience with BlinkFind...\",\n                                required: true\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                                lineNumber: 174,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500 text-xs mt-1\",\n                                children: [\n                                    comment.length,\n                                    \"/500 characters\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                                lineNumber: 183,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                        lineNumber: 170,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"submit\",\n                        disabled: loading || rating === 0 || comment.trim().length < 10,\n                        className: \"w-full bg-gradient-to-r from-neural-purple to-neural-pink text-white font-semibold py-3 px-4 rounded-lg hover:opacity-90 focus:outline-none focus:ring-2 focus:ring-neural-purple/50 disabled:opacity-50 transition-all duration-300 ease-in-out\",\n                        children: loading ? 'Submitting...' : 'Submit Review'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                        lineNumber: 186,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                lineNumber: 162,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n        lineNumber: 153,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(ReviewForm, \"BxyhwffAJ/DxyA1B5PFC4fcAhI4=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c2 = ReviewForm;\nconst ReviewsSection = ()=>{\n    _s2();\n    const [reviews, setReviews] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalReviews: 0,\n        averageRating: 0\n    });\n    const fetchReviews = async ()=>{\n        try {\n            const reviewsQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.collection)(_firebase_config__WEBPACK_IMPORTED_MODULE_4__.firestore, 'reviews'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.orderBy)('createdAt', 'desc'));\n            const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.getDocs)(reviewsQuery);\n            const allReviewsData = querySnapshot.docs.map((doc)=>({\n                    id: doc.id,\n                    ...doc.data()\n                }));\n            // Filter to only show approved reviews\n            const approvedReviews = allReviewsData.filter((review)=>review.approved === true);\n            setReviews(approvedReviews);\n            // Calculate stats based on approved reviews only\n            const totalReviews = approvedReviews.length;\n            const averageRating = totalReviews > 0 ? approvedReviews.reduce((sum, review)=>sum + review.rating, 0) / totalReviews : 0;\n            setStats({\n                totalReviews,\n                averageRating\n            });\n        } catch (error) {\n            console.error('Error fetching reviews:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ReviewsSection.useEffect\": ()=>{\n            fetchReviews();\n        }\n    }[\"ReviewsSection.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative overflow-hidden bg-gradient-to-b from-black to-[#0A0A0A] min-h-screen py-12\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 opacity-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 bg-[url('/grid.svg')] [mask-image:radial-gradient(ellipse_at_center,white,transparent)]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                    lineNumber: 242,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                lineNumber: 241,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 overflow-hidden\",\n                children: [\n                    ...Array(6)\n                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute rounded-full bg-neural-purple opacity-10 blur-xl animate-pulse\",\n                        style: {\n                            left: \"\".concat(Math.random() * 100, \"%\"),\n                            top: \"\".concat(Math.random() * 100, \"%\"),\n                            width: \"\".concat(Math.random() * 200 + 100, \"px\"),\n                            height: \"\".concat(Math.random() * 200 + 100, \"px\"),\n                            animationDelay: \"\".concat(Math.random() * 5, \"s\"),\n                            animationDuration: \"\".concat(Math.random() * 10 + 10, \"s\")\n                        }\n                    }, i, false, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                        lineNumber: 248,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                lineNumber: 246,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 max-w-4xl mx-auto px-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl font-bold bg-gradient-to-r from-neural-purple to-neural-pink bg-clip-text text-transparent mb-4\",\n                                children: \"User Reviews\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                                lineNumber: 266,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-300 text-lg mb-6\",\n                                children: \"See what our users are saying about BlinkFind\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                                lineNumber: 269,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center items-center space-x-8 mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-3xl font-bold text-white\",\n                                                children: stats.totalReviews\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400\",\n                                                children: \"Total Reviews\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                                                lineNumber: 277,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                                        lineNumber: 275,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center mb-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-3xl font-bold text-white mr-2\",\n                                                        children: stats.averageRating.toFixed(1)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                                                        lineNumber: 281,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StarRating, {\n                                                        rating: Math.round(stats.averageRating),\n                                                        readonly: true\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                                                        lineNumber: 284,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400\",\n                                                children: \"Average Rating\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                                                lineNumber: 286,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                                        lineNumber: 279,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                                lineNumber: 274,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                        lineNumber: 265,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-12\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ReviewForm, {\n                            onReviewSubmitted: fetchReviews\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                            lineNumber: 293,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                        lineNumber: 292,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-white mb-6\",\n                                children: \"Recent Reviews\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                                lineNumber: 298,\n                                columnNumber: 11\n                            }, undefined),\n                            loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-neural-purple\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                                        lineNumber: 302,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-300 mt-2\",\n                                        children: \"Loading reviews...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                                        lineNumber: 303,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                                lineNumber: 301,\n                                columnNumber: 13\n                            }, undefined) : reviews.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400\",\n                                    children: \"No reviews yet. Be the first to share your experience!\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                                    lineNumber: 307,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                                lineNumber: 306,\n                                columnNumber: 13\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: reviews.map((review)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ReviewCard, {\n                                        review: review\n                                    }, review.id, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                                        lineNumber: 312,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                                lineNumber: 310,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                        lineNumber: 297,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                lineNumber: 263,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n        lineNumber: 239,\n        columnNumber: 5\n    }, undefined);\n};\n_s2(ReviewsSection, \"pVI5GpUtmMsEI+MJajBQeHHnI6Y=\");\n_c3 = ReviewsSection;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ReviewsSection);\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"StarRating\");\n$RefreshReg$(_c1, \"ReviewCard\");\n$RefreshReg$(_c2, \"ReviewForm\");\n$RefreshReg$(_c3, \"ReviewsSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/features/ReviewsSection.jsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/features/ShowcaseProduct.jsx":
/*!*****************************************************!*\
  !*** ./src/components/features/ShowcaseProduct.jsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_FileText_Heart_Play_Search_Smartphone_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=FileText,Heart,Play,Search,Smartphone,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_FileText_Heart_Play_Search_Smartphone_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=FileText,Heart,Play,Search,Smartphone,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/smartphone.js\");\n/* harmony import */ var _barrel_optimize_names_FileText_Heart_Play_Search_Smartphone_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=FileText,Heart,Play,Search,Smartphone,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_FileText_Heart_Play_Search_Smartphone_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=FileText,Heart,Play,Search,Smartphone,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_FileText_Heart_Play_Search_Smartphone_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=FileText,Heart,Play,Search,Smartphone,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_FileText_Heart_Play_Search_Smartphone_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=FileText,Heart,Play,Search,Smartphone,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_FileText_Heart_Play_Search_Smartphone_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=FileText,Heart,Play,Search,Smartphone,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_SparklesIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=SparklesIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/SparklesIcon.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst ProductShowcase = ()=>{\n    _s();\n    const [isQuickFindPlaying, setIsQuickFindPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isResumeBuilderPlaying, setIsResumeBuilderPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const quickFindFeatures = [\n        {\n            icon: _barrel_optimize_names_FileText_Heart_Play_Search_Smartphone_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n            title: \"Easy Reporting\",\n            description: \"Found something valuable? Upload it and help someone reconnect.\"\n        },\n        {\n            icon: _barrel_optimize_names_FileText_Heart_Play_Search_Smartphone_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            title: \"Simple Search\",\n            description: \"Effortlessly find your lost items through easy search filters.\"\n        },\n        {\n            icon: _barrel_optimize_names_FileText_Heart_Play_Search_Smartphone_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            title: \"Emotional Value\",\n            description: \"Restoring memories and connections that can never be replaced.\"\n        }\n    ];\n    const resumeBuilderFeatures = [\n        {\n            icon: _barrel_optimize_names_FileText_Heart_Play_Search_Smartphone_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            title: \"AI-Powered Writing\",\n            description: \"Generate professional content tailored to your experience and industry.\"\n        },\n        {\n            icon: _barrel_optimize_names_FileText_Heart_Play_Search_Smartphone_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            title: \"Instant Creation\",\n            description: \"Build a complete resume in minutes with our intelligent templates.\"\n        },\n        {\n            icon: _barrel_optimize_names_FileText_Heart_Play_Search_Smartphone_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            title: \"ATS Optimized\",\n            description: \"Ensure your resume passes through applicant tracking systems.\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-20 bg-gradient-to-b from-black to-[#0A0A0A]\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.5\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"flex items-center justify-center gap-2 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_SparklesIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-5 w-5 text-primary\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-medium text-primary\",\n                                    children: \"OUR PRODUCTS\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                    lineNumber: 59,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                            lineNumber: 51,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.h2, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.5,\n                                delay: 0.1\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"text-3xl md:text-4xl font-bold text-white\",\n                            children: \"Innovative AI Solutions for Everyday Challenges\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                            lineNumber: 62,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                    lineNumber: 50,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.5,\n                                        delay: 0.2\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    className: \"relative aspect-[9/16] max-w-[300px] mx-auto lg:mx-0 order-2 lg:order-1\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"glass-effect rounded-2xl overflow-hidden border border-white/10 h-full\",\n                                        children: isResumeBuilderPlaying ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                            className: \"absolute inset-0 w-full h-full object-cover\",\n                                            src: \"/Videos/resume_builder_demo.mp4\",\n                                            title: \"AI Resume Builder Demo\",\n                                            autoPlay: true,\n                                            controls: true,\n                                            muted: true,\n                                            loop: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                            lineNumber: 85,\n                                            columnNumber: 19\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 flex items-center justify-center bg-gradient-to-br from-neural-purple/20 via-neural-pink/20 to-neural-blue/20 backdrop-blur-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-gradient-to-br from-purple-600/30 to-pink-600/30\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                                    lineNumber: 97,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-[url('/ai.jpg')] bg-cover bg-center opacity-40\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                                    lineNumber: 98,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-black/30\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                                    lineNumber: 99,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative z-10 text-center p-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mb-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileText_Heart_Play_Search_Smartphone_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                    className: \"h-12 w-12 text-white mx-auto mb-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                                                    lineNumber: 104,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"text-white font-bold text-lg\",\n                                                                    children: \"AI Resume Builder\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                                                    lineNumber: 105,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-200 text-sm\",\n                                                                    children: \"Professional resumes in minutes\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                                                    lineNumber: 106,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                                            lineNumber: 103,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setIsResumeBuilderPlaying(true),\n                                                            className: \"bg-gradient-to-r from-primary to-purple-600 text-white rounded-full p-3 transition-transform transform hover:scale-110 shadow-lg\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileText_Heart_Play_Search_Smartphone_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: \"h-6 w-6\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                                                lineNumber: 112,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                                            lineNumber: 108,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                                    lineNumber: 102,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                            lineNumber: 95,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"order-1 lg:order-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.h3, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 20\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.5,\n                                                delay: 0.2\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            className: \"text-2xl font-bold text-white mb-6\",\n                                            children: \"AI Resume Builder: Professional Resumes in Minutes\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: resumeBuilderFeatures.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                                    initial: {\n                                                        opacity: 0,\n                                                        y: 20\n                                                    },\n                                                    whileInView: {\n                                                        opacity: 1,\n                                                        y: 0\n                                                    },\n                                                    transition: {\n                                                        duration: 0.5,\n                                                        delay: 0.3 + index * 0.1\n                                                    },\n                                                    viewport: {\n                                                        once: true\n                                                    },\n                                                    className: \"flex items-start gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-primary/10 p-2 rounded-lg\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                                className: \"h-5 w-5 text-primary\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                                                lineNumber: 142,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                                            lineNumber: 141,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"text-lg font-semibold text-white\",\n                                                                    children: feature.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                                                    lineNumber: 145,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-400\",\n                                                                    children: feature.description\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                                                    lineNumber: 146,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                                            lineNumber: 144,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                                    lineNumber: 133,\n                                                    columnNumber: 19\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                            lineNumber: 131,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 20\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.5,\n                                                delay: 0.6\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            className: \"mt-8\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"/resume-builder\",\n                                                className: \"inline-flex items-center justify-center px-6 py-3 bg-gradient-to-r from-primary to-purple-600 text-white rounded-lg font-medium hover:opacity-90 transition-opacity\",\n                                                children: \"Try AI Resume Builder\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                                lineNumber: 159,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                            lineNumber: 75,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.h3, {\n                                                initial: {\n                                                    opacity: 0,\n                                                    y: 20\n                                                },\n                                                whileInView: {\n                                                    opacity: 1,\n                                                    y: 0\n                                                },\n                                                transition: {\n                                                    duration: 0.5,\n                                                    delay: 0.2\n                                                },\n                                                viewport: {\n                                                    once: true\n                                                },\n                                                className: \"text-2xl font-bold text-white mb-6\",\n                                                children: \"QuickFind: AI-Powered Lost & Found Platform\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-6\",\n                                                children: quickFindFeatures.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                                        initial: {\n                                                            opacity: 0,\n                                                            y: 20\n                                                        },\n                                                        whileInView: {\n                                                            opacity: 1,\n                                                            y: 0\n                                                        },\n                                                        transition: {\n                                                            duration: 0.5,\n                                                            delay: 0.3 + index * 0.1\n                                                        },\n                                                        viewport: {\n                                                            once: true\n                                                        },\n                                                        className: \"flex items-start gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-primary/10 p-2 rounded-lg\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                                    className: \"h-5 w-5 text-primary\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                                                    lineNumber: 193,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                                                lineNumber: 192,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"text-lg font-semibold text-white\",\n                                                                        children: feature.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                                                        lineNumber: 196,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-400\",\n                                                                        children: feature.description\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                                                        lineNumber: 197,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                                                lineNumber: 195,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                                        lineNumber: 184,\n                                                        columnNumber: 19\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                                lineNumber: 182,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                                initial: {\n                                                    opacity: 0,\n                                                    y: 20\n                                                },\n                                                whileInView: {\n                                                    opacity: 1,\n                                                    y: 0\n                                                },\n                                                transition: {\n                                                    duration: 0.5,\n                                                    delay: 0.6\n                                                },\n                                                viewport: {\n                                                    once: true\n                                                },\n                                                className: \"mt-8\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/quickfind\",\n                                                    className: \"inline-flex items-center justify-center px-6 py-3 bg-gradient-to-r from-primary to-purple-600 text-white rounded-lg font-medium hover:opacity-90 transition-opacity\",\n                                                    children: \"Learn More About QuickFind\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                                    lineNumber: 210,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            duration: 0.5,\n                                            delay: 0.2\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        className: \"relative aspect-[9/16] max-w-[300px] mx-auto lg:mx-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"glass-effect rounded-2xl overflow-hidden border border-white/10 h-full\",\n                                            children: isQuickFindPlaying ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                                className: \"absolute inset-0 w-full h-full object-cover\",\n                                                src: \"/Videos/quickFind_video.mp4\",\n                                                title: \"QuickFind App Demo\",\n                                                autoPlay: true,\n                                                controls: true,\n                                                muted: true,\n                                                loop: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 19\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 flex items-center justify-center bg-gradient-to-br from-neural-blue/20 via-neural-purple/20 to-neural-pink/20 backdrop-blur-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 bg-gradient-to-br from-blue-600/30 to-cyan-600/30\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                                        lineNumber: 240,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 bg-[url('/lostFound/lf1.jpg')] bg-cover bg-center opacity-40\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                                        lineNumber: 241,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-black/30\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                                        lineNumber: 242,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative z-10 text-center p-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mb-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileText_Heart_Play_Search_Smartphone_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                        className: \"h-12 w-12 text-white mx-auto mb-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                                                        lineNumber: 247,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"text-white font-bold text-lg\",\n                                                                        children: \"QuickFind\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                                                        lineNumber: 248,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-200 text-sm\",\n                                                                        children: \"AI-powered lost & found\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                                                        lineNumber: 249,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                                                lineNumber: 246,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>setIsQuickFindPlaying(true),\n                                                                className: \"bg-gradient-to-r from-primary to-purple-600 text-white rounded-full p-3 transition-transform transform hover:scale-110 shadow-lg\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileText_Heart_Play_Search_Smartphone_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    className: \"h-6 w-6\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                                                    lineNumber: 255,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                                                lineNumber: 251,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                                        lineNumber: 245,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                lineNumber: 170,\n                                columnNumber: 11\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                            lineNumber: 169,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                    lineNumber: 74,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n            lineNumber: 49,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n        lineNumber: 48,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ProductShowcase, \"JXLT6ol3RZbnHJ+S3feWfJx0r4Q=\");\n_c = ProductShowcase;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ProductShowcase);\nvar _c;\n$RefreshReg$(_c, \"ProductShowcase\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/features/ShowcaseProduct.jsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/layout/Hero.jsx":
/*!****************************************!*\
  !*** ./src/components/layout/Hero.jsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_SparklesIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=SparklesIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/SparklesIcon.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Bot_FileText_Globe_Search_Shield_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Bot,FileText,Globe,Search,Shield,Smartphone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Bot_FileText_Globe_Search_Shield_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Bot,FileText,Globe,Search,Shield,Smartphone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Bot_FileText_Globe_Search_Shield_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Bot,FileText,Globe,Search,Shield,Smartphone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Bot_FileText_Globe_Search_Shield_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Bot,FileText,Globe,Search,Shield,Smartphone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _components_features_PlatformStats__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/features/PlatformStats */ \"(app-pages-browser)/./src/components/features/PlatformStats.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst Hero = ()=>{\n    _s();\n    const [currentSlide, setCurrentSlide] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // AI Products data  // AI Products data (all marked as upcoming)\n    const aiProducts = [\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Bot_FileText_Globe_Search_Shield_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"h-8 w-8\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                lineNumber: 20,\n                columnNumber: 13\n            }, undefined),\n            title: \"AI Resume Builder\",\n            description: \"Create ATS-optimized resumes in minutes with AI-powered suggestions\",\n            features: [\n                \"Create Resume in 5 minutes\",\n                \"ATS Scoring\",\n                \"Job-Specific Content\"\n            ],\n            color: \"from-neural-purple to-neural-pink\",\n            link: \"/resume-builder\",\n            upcoming: true\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Bot_FileText_Globe_Search_Shield_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                className: \"h-8 w-8\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                lineNumber: 29,\n                columnNumber: 13\n            }, undefined),\n            title: \"QuickFind\",\n            description: \"Lost & Found platform with AI-powered matching and recovery\",\n            features: [\n                \"Instant Search\",\n                \"AI Matching\",\n                \"Secure Recovery\"\n            ],\n            color: \"from-neural-blue to-neural-purple\",\n            link: \"/quickfind\",\n            upcoming: true\n        }\n    ];\n    // Auto-rotate carousel\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Hero.useEffect\": ()=>{\n            const interval = setInterval({\n                \"Hero.useEffect.interval\": ()=>{\n                    setCurrentSlide({\n                        \"Hero.useEffect.interval\": (prev)=>(prev + 1) % aiProducts.length\n                    }[\"Hero.useEffect.interval\"]);\n                }\n            }[\"Hero.useEffect.interval\"], 4000);\n            return ({\n                \"Hero.useEffect\": ()=>clearInterval(interval)\n            })[\"Hero.useEffect\"];\n        }\n    }[\"Hero.useEffect\"], [\n        aiProducts.length\n    ]);\n    const nextSlide = ()=>{\n        setCurrentSlide((prev)=>(prev + 1) % aiProducts.length);\n    };\n    const prevSlide = ()=>{\n        setCurrentSlide((prev)=>(prev - 1 + aiProducts.length) % aiProducts.length);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative overflow-hidden bg-gradient-to-b from-black to-[#0A0A0A] min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 opacity-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 bg-[url('/grid.svg')] [mask-image:radial-gradient(ellipse_at_center,white,transparent)]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                    lineNumber: 57,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                lineNumber: 56,\n                columnNumber: 7\n            }, undefined),\n            \"      \",\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 overflow-hidden\",\n                children: [\n                    [\n                        ...Array(6)\n                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                            className: \"hidden sm:block absolute rounded-full bg-neural-purple opacity-15 lg:opacity-20 blur-xl\",\n                            initial: {\n                                x: Math.random() * 100 - 50,\n                                y: Math.random() * 100 - 50,\n                                width: Math.random() * 120 + 60,\n                                height: Math.random() * 120 + 60\n                            },\n                            animate: {\n                                x: Math.random() * 100 - 50,\n                                y: Math.random() * 100 - 50,\n                                transition: {\n                                    duration: Math.random() * 15 + 15,\n                                    repeat: Infinity,\n                                    repeatType: 'reverse'\n                                }\n                            }\n                        }, i, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                            lineNumber: 62,\n                            columnNumber: 11\n                        }, undefined)),\n                    [\n                        ...Array(3)\n                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                            className: \"block sm:hidden absolute rounded-full bg-neural-purple opacity-8 blur-lg\",\n                            initial: {\n                                x: Math.random() * 100 - 50,\n                                y: Math.random() * 100 - 50,\n                                width: Math.random() * 40 + 20,\n                                height: Math.random() * 40 + 20\n                            },\n                            animate: {\n                                x: Math.random() * 100 - 50,\n                                y: Math.random() * 100 - 50,\n                                transition: {\n                                    duration: Math.random() * 20 + 20,\n                                    repeat: Infinity,\n                                    repeatType: 'reverse'\n                                }\n                            }\n                        }, \"mobile-\".concat(i), false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                            lineNumber: 84,\n                            columnNumber: 11\n                        }, undefined))\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 pt-12 pb-12 sm:pt-16 sm:pb-16 lg:pt-20 lg:pb-20 xl:pt-24 xl:pb-24 min-h-screen flex items-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full lg:flex lg:items-center lg:gap-x-12\",\n                    children: [\n                        \"          \",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto max-w-2xl lg:mx-0 lg:flex-auto lg:w-[65%] text-center lg:text-left\",\n                            children: [\n                                \"            \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-x-2 mb-4 hidden sm:flex justify-center lg:justify-start\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_SparklesIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-5 w-5 text-neural-pink\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-semibold leading-6 text-neural-blue\",\n                                            children: \"Your Everyday AI for Smarter Living & Working\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 115\n                                }, undefined),\n                                \"          \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.h1, {\n                                    className: \"text-2xl font-bold tracking-tight text-white sm:text-3xl lg:text-4xl xl:text-5xl leading-tight relative\",\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.5\n                                    },\n                                    children: [\n                                        \"Turn \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"bg-gradient-to-r from-neural-purple to-neural-pink bg-clip-text text-transparent relative\",\n                                            children: [\n                                                \"Hours of Work\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    \"aria-hidden\": \"true\",\n                                                    viewBox: \"0 0 418 42\",\n                                                    className: \"absolute left-0 top-2/3 h-[0.5em] w-full fill-neural-purple/40 opacity-70\",\n                                                    preserveAspectRatio: \"none\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M203.371.916c-26.013-2.078-76.686 1.963-124.73 9.946L67.3 12.749C35.421 18.062 18.2 21.766 6.004 25.934 1.244 27.561.828 27.778.874 28.61c.07 1.214.828 1.121 9.595-1.176 9.072-2.377 17.15-3.92 39.246-7.496C123.565 7.986 157.869 4.492 195.942 5.046c7.461.108 19.25 1.696 19.17 2.582-.107 1.183-7.874 4.31-25.75 10.366-21.992 7.45-35.43 12.534-36.701 13.884-2.173 2.308-.202 4.407 4.442 4.734 2.654.187 3.263.157 15.593-.78 35.401-2.686 57.944-3.488 88.365-3.143 46.327.526 75.721 2.23 130.788 7.584 19.787 1.924 20.814 1.98 24.557 1.332l.066-.011c1.201-.203 1.53-1.825.399-2.335-2.911-1.31-4.893-1.604-22.048-3.261-57.509-5.556-87.871-7.36-132.059-7.842-23.239-.254-33.617-.116-50.627.674-11.629.54-42.371 2.494-46.696 2.967-2.359.259 8.133-3.625 26.504-9.81 23.239-7.825 27.934-10.149 28.304-14.005.417-4.348-3.529-6-16.878-7.066Z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                                        lineNumber: 125,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                                    lineNumber: 119,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                            lineNumber: 117,\n                                            columnNumber: 18\n                                        }, undefined),\n                                        \" Into \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"bg-gradient-to-r from-neural-blue to-neural-purple bg-clip-text text-transparent relative\",\n                                            children: [\n                                                \"Minutes\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    \"aria-hidden\": \"true\",\n                                                    viewBox: \"0 0 418 42\",\n                                                    className: \"absolute left-0 top-2/3 h-[0.4em] w-full fill-neural-blue/30 opacity-60\",\n                                                    preserveAspectRatio: \"none\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M203.371.916c-26.013-2.078-76.686 1.963-124.73 9.946L67.3 12.749C35.421 18.062 18.2 21.766 6.004 25.934 1.244 27.561.828 27.778.874 28.61c.07 1.214.828 1.121 9.595-1.176 9.072-2.377 17.15-3.92 39.246-7.496C123.565 7.986 157.869 4.492 195.942 5.046c7.461.108 19.25 1.696 19.17 2.582-.107 1.183-7.874 4.31-25.75 10.366-21.992 7.45-35.43 12.534-36.701 13.884-2.173 2.308-.202 4.407 4.442 4.734 2.654.187 3.263.157 15.593-.78 35.401-2.686 57.944-3.488 88.365-3.143 46.327.526 75.721 2.23 130.788 7.584 19.787 1.924 20.814 1.98 24.557 1.332l.066-.011c1.201-.203 1.53-1.825.399-2.335-2.911-1.31-4.893-1.604-22.048-3.261-57.509-5.556-87.871-7.36-132.059-7.842-23.239-.254-33.617-.116-50.627.674-11.629.54-42.371 2.494-46.696 2.967-2.359.259 8.133-3.625 26.504-9.81 23.239-7.825 27.934-10.149 28.304-14.005.417-4.348-3.529-6-16.878-7.066Z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                                        lineNumber: 135,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                                    lineNumber: 129,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 26\n                                        }, undefined),\n                                        \" — With \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"bg-gradient-to-r from-neural-pink to-neural-blue bg-clip-text text-transparent\",\n                                            children: \"AI\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 28\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 27\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.p, {\n                                    className: \"mt-4 text-base leading-6 text-gray-300 max-w-xl mx-auto lg:mx-0\",\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.5,\n                                        delay: 0.2\n                                    },\n                                    children: \"Apply for jobs, plan your day, automate your business — all without switching between tools.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.p, {\n                                    className: \"mt-3 text-xs text-gray-400 max-w-xl mx-auto lg:mx-0\",\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.5,\n                                        delay: 0.3\n                                    },\n                                    children: \"Built for busy people who want real results.          \"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 11\n                                }, undefined),\n                                \"          \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                    className: \"mt-6 flex flex-row items-center gap-3 justify-center lg:justify-start\",\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.5,\n                                        delay: 0.4\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/resume-builder\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.button, {\n                                            whileHover: {\n                                                scale: 1.05\n                                            },\n                                            whileTap: {\n                                                scale: 0.95\n                                            },\n                                            className: \"rounded-lg bg-gradient-to-r from-neural-purple to-neural-pink px-4 sm:px-6 py-2 sm:py-2.5 text-xs sm:text-sm font-semibold text-white shadow-lg hover:opacity-90 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-neural-purple transition-all duration-300\",\n                                            children: \"Start Free\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 12\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_features_PlatformStats__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                            lineNumber: 106,\n                            columnNumber: 11\n                        }, undefined),\n                        \"        \",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-8 sm:mt-12 lg:mt-0 lg:w-[30%] hidden sm:block\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                className: \"relative\",\n                                initial: {\n                                    opacity: 0,\n                                    x: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    x: 0\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: 0.3\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 rounded-xl bg-gradient-to-tr from-neural-purple to-neural-blue opacity-30 blur-xl\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative rounded-xl bg-gray-900/10 p-2 ring-1 ring-white/10 backdrop-blur-md\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-[300px] sm:h-[320px] lg:h-[340px] w-full rounded-lg bg-gradient-to-br from-black/50 to-gray-900/50 p-4 overflow-hidden\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2 text-gray-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Bot_FileText_Globe_Search_Shield_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-neural-blue\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                                                    lineNumber: 189,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs font-medium\",\n                                                                    children: \"AI Products\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                                                    lineNumber: 190,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                                            lineNumber: 188,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: prevSlide,\n                                                                    className: \"p-1 rounded-full bg-gray-800/50 hover:bg-gray-700/50 transition-colors\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Bot_FileText_Globe_Search_Shield_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                        className: \"h-3 w-3 text-gray-400 rotate-180\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                                                        lineNumber: 197,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                                                    lineNumber: 193,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: nextSlide,\n                                                                    className: \"p-1 rounded-full bg-gray-800/50 hover:bg-gray-700/50 transition-colors\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Bot_FileText_Globe_Search_Shield_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                        className: \"h-3 w-3 text-gray-400\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                                                        lineNumber: 203,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                                                    lineNumber: 199,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                                            lineNumber: 192,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                                    lineNumber: 187,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative h-full\",\n                                                    children: aiProducts.map((product, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                                            className: \"absolute inset-0 \".concat(index === currentSlide ? 'opacity-100' : 'opacity-0', \" transition-opacity duration-500\"),\n                                                            children: [\n                                                                \"                      \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-gray-900/60 backdrop-blur-sm rounded-lg p-4 h-full border border-white/10\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"inline-flex p-2 rounded-lg bg-gradient-to-r \".concat(product.color, \" mb-3\"),\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Bot_FileText_Globe_Search_Shield_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                className: \"h-6 w-6\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                                                                lineNumber: 216,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                                                            lineNumber: 215,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"text-lg font-bold text-white mb-2\",\n                                                                            children: product.title\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                                                            lineNumber: 219,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-300 text-xs mb-4 leading-relaxed\",\n                                                                            children: product.description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                                                            lineNumber: 223,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-2 mb-4\",\n                                                                            children: product.features.map((feature, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center gap-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"h-1 w-1 rounded-full bg-neural-purple\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                                                                            lineNumber: 230,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"text-xs text-gray-400\",\n                                                                                            children: feature\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                                                                            lineNumber: 231,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, idx, true, {\n                                                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                                                                    lineNumber: 229,\n                                                                                    columnNumber: 29\n                                                                                }, undefined))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                                                            lineNumber: 227,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                            href: product.link,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.button, {\n                                                                                whileHover: {\n                                                                                    scale: 1.02\n                                                                                },\n                                                                                whileTap: {\n                                                                                    scale: 0.98\n                                                                                },\n                                                                                className: \"w-full py-2.5 px-3 rounded-lg bg-gradient-to-r \".concat(product.color, \" text-white font-medium text-xs hover:opacity-90 transition-opacity\"),\n                                                                                children: [\n                                                                                    \"Explore \",\n                                                                                    product.title\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                                                                lineNumber: 237,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                                                            lineNumber: 236,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                                                    lineNumber: 214,\n                                                                    columnNumber: 44\n                                                                }, undefined)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                                            lineNumber: 211,\n                                                            columnNumber: 21\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                                    lineNumber: 209,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                \"                \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute bottom-3 left-1/2 transform -translate-x-1/2 flex gap-1.5\",\n                                                    children: aiProducts.map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setCurrentSlide(index),\n                                                            className: \"h-1.5 w-1.5 rounded-full transition-colors \".concat(index === currentSlide ? 'bg-neural-purple' : 'bg-gray-600')\n                                                        }, index, false, {\n                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                                            lineNumber: 251,\n                                                            columnNumber: 21\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                                    lineNumber: 249,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                lineNumber: 177,\n                                columnNumber: 11\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                            lineNumber: 176,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                    lineNumber: 105,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                lineNumber: 104,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n        lineNumber: 54,\n        columnNumber: 15\n    }, undefined);\n};\n_s(Hero, \"/jm+XmndjAYlDCFyCnfFEXJOloU=\");\n_c = Hero;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Hero);\nvar _c;\n$RefreshReg$(_c, \"Hero\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/layout/Hero.jsx\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["vendors","default-_app-pages-browser_src_contexts_AuthContext_jsx","main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CNewBlinkFindAI%5C%5Cfrontend-master%5C%5Csrc%5C%5Capp%5C%5Cpage.jsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);
'use client';

// Multi-Industry Template Definitions
export const INDUSTRY_TEMPLATES = {
  // Business & Corporate
  business_executive: {
    id: 'business_executive',
    name: 'Executive Professional',
    industry: 'Business & Corporate',
    description: 'Clean, authoritative design for senior business professionals',
    color: '#1e40af',
    features: ['Leadership Focus', 'Achievement Metrics', 'Board Experience'],
    sections: ['summary', 'experience', 'education', 'skills', 'achievements']
  },
  
  business_analyst: {
    id: 'business_analyst',
    name: 'Business Analyst',
    industry: 'Business & Corporate',
    description: 'Data-driven layout emphasizing analytical skills',
    color: '#059669',
    features: ['Data Analysis', 'Process Improvement', 'Stakeholder Management'],
    sections: ['summary', 'experience', 'education', 'skills', 'certifications']
  },

  // Healthcare
  healthcare_professional: {
    id: 'healthcare_professional',
    name: 'Healthcare Professional',
    industry: 'Healthcare',
    description: 'Professional medical resume with certification emphasis',
    color: '#dc2626',
    features: ['Medical Credentials', 'Patient Care', 'Clinical Experience'],
    sections: ['summary', 'experience', 'education', 'licenses', 'certifications']
  },

  healthcare_nurse: {
    id: 'healthcare_nurse',
    name: 'Nursing Professional',
    industry: 'Healthcare',
    description: 'Specialized for nursing roles with care focus',
    color: '#7c3aed',
    features: ['Patient Care', 'Clinical Skills', 'Specializations'],
    sections: ['summary', 'experience', 'education', 'certifications', 'skills']
  },

  // Education
  education_teacher: {
    id: 'education_teacher',
    name: 'Teaching Professional',
    industry: 'Education',
    description: 'Education-focused with curriculum and student outcomes',
    color: '#ea580c',
    features: ['Curriculum Development', 'Student Outcomes', 'Teaching Methods'],
    sections: ['summary', 'experience', 'education', 'certifications', 'achievements']
  },

  education_administrator: {
    id: 'education_administrator',
    name: 'Education Administrator',
    industry: 'Education',
    description: 'Leadership-focused for educational administration',
    color: '#0891b2',
    features: ['Educational Leadership', 'Program Management', 'Policy Development'],
    sections: ['summary', 'experience', 'education', 'achievements', 'skills']
  },

  // Creative & Design
  creative_designer: {
    id: 'creative_designer',
    name: 'Creative Professional',
    industry: 'Creative & Design',
    description: 'Visually appealing design for creative professionals',
    color: '#db2777',
    features: ['Portfolio Integration', 'Creative Skills', 'Visual Appeal'],
    sections: ['summary', 'experience', 'education', 'portfolio', 'skills']
  },

  creative_marketing: {
    id: 'creative_marketing',
    name: 'Marketing Creative',
    industry: 'Creative & Design',
    description: 'Marketing-focused creative with campaign highlights',
    color: '#7c2d12',
    features: ['Campaign Results', 'Brand Development', 'Creative Strategy'],
    sections: ['summary', 'experience', 'education', 'achievements', 'skills']
  },

  // Technology (Simplified)
  tech_professional: {
    id: 'tech_professional',
    name: 'Technology Professional',
    industry: 'Technology',
    description: 'Clean technical resume for IT professionals',
    color: '#374151',
    features: ['Technical Skills', 'Project Experience', 'Problem Solving'],
    sections: ['summary', 'experience', 'education', 'skills', 'projects']
  },

  // Sales & Customer Service
  sales_professional: {
    id: 'sales_professional',
    name: 'Sales Professional',
    industry: 'Sales & Customer Service',
    description: 'Results-driven design highlighting sales achievements',
    color: '#16a34a',
    features: ['Sales Metrics', 'Client Relations', 'Revenue Growth'],
    sections: ['summary', 'experience', 'education', 'achievements', 'skills']
  },

  customer_service: {
    id: 'customer_service',
    name: 'Customer Service',
    industry: 'Sales & Customer Service',
    description: 'Service-oriented with customer satisfaction focus',
    color: '#2563eb',
    features: ['Customer Satisfaction', 'Problem Resolution', 'Communication'],
    sections: ['summary', 'experience', 'education', 'skills', 'achievements']
  },

  // Hospitality & Retail
  hospitality_manager: {
    id: 'hospitality_manager',
    name: 'Hospitality Manager',
    industry: 'Hospitality & Retail',
    description: 'Service excellence with operational management focus',
    color: '#dc2626',
    features: ['Guest Experience', 'Operations Management', 'Team Leadership'],
    sections: ['summary', 'experience', 'education', 'achievements', 'skills']
  },

  retail_professional: {
    id: 'retail_professional',
    name: 'Retail Professional',
    industry: 'Hospitality & Retail',
    description: 'Customer-focused with sales and service emphasis',
    color: '#059669',
    features: ['Customer Service', 'Sales Performance', 'Inventory Management'],
    sections: ['summary', 'experience', 'education', 'skills', 'achievements']
  },

  // Finance & Accounting
  finance_professional: {
    id: 'finance_professional',
    name: 'Finance Professional',
    industry: 'Finance & Accounting',
    description: 'Professional financial resume with analytical focus',
    color: '#1e40af',
    features: ['Financial Analysis', 'Regulatory Compliance', 'Risk Management'],
    sections: ['summary', 'experience', 'education', 'certifications', 'skills']
  },

  // Manufacturing & Operations
  operations_manager: {
    id: 'operations_manager',
    name: 'Operations Manager',
    industry: 'Manufacturing & Operations',
    description: 'Efficiency-focused with process improvement emphasis',
    color: '#374151',
    features: ['Process Optimization', 'Quality Control', 'Team Management'],
    sections: ['summary', 'experience', 'education', 'achievements', 'skills']
  },

  // Non-Profit & Social Services
  nonprofit_professional: {
    id: 'nonprofit_professional',
    name: 'Non-Profit Professional',
    industry: 'Non-Profit & Social Services',
    description: 'Mission-driven design for social impact roles',
    color: '#7c3aed',
    features: ['Social Impact', 'Community Engagement', 'Program Development'],
    sections: ['summary', 'experience', 'education', 'volunteer', 'skills']
  }
};

// Industry Categories for Organization
export const INDUSTRY_CATEGORIES = {
  'Business & Corporate': [
    'business_executive',
    'business_analyst'
  ],
  'Healthcare': [
    'healthcare_professional',
    'healthcare_nurse'
  ],
  'Education': [
    'education_teacher',
    'education_administrator'
  ],
  'Creative & Design': [
    'creative_designer',
    'creative_marketing'
  ],
  'Technology': [
    'tech_professional'
  ],
  'Sales & Customer Service': [
    'sales_professional',
    'customer_service'
  ],
  'Hospitality & Retail': [
    'hospitality_manager',
    'retail_professional'
  ],
  'Finance & Accounting': [
    'finance_professional'
  ],
  'Manufacturing & Operations': [
    'operations_manager'
  ],
  'Non-Profit & Social Services': [
    'nonprofit_professional'
  ]
};

// Template Renderer Function
export const renderMultiIndustryTemplate = (templateId, formData) => {
  const template = INDUSTRY_TEMPLATES[templateId];
  if (!template) return null;

  const renderSection = (sectionType) => {
    switch (sectionType) {
      case 'summary':
        return formData.personal?.summary && (
          <div className="mb-6">
            <h2 className="text-lg font-bold text-gray-900 mb-3 border-b-2 border-gray-300 pb-1">
              PROFESSIONAL SUMMARY
            </h2>
            <p className="text-sm text-gray-700 leading-relaxed">
              {formData.personal.summary}
            </p>
          </div>
        );

      case 'experience':
        return formData.experience?.length > 0 && (
          <div className="mb-6">
            <h2 className="text-lg font-bold text-gray-900 mb-3 border-b-2 border-gray-300 pb-1">
              PROFESSIONAL EXPERIENCE
            </h2>
            <div className="space-y-4">
              {formData.experience.map((exp, index) => (
                <div key={index}>
                  <div className="flex justify-between items-start mb-1">
                    <h3 className="text-base font-semibold text-gray-900">{exp.title}</h3>
                    <span className="text-sm text-gray-600">
                      {exp.startDate} - {exp.current ? 'Present' : exp.endDate}
                    </span>
                  </div>
                  <p className="text-sm text-gray-700 font-medium mb-2">
                    {exp.company} {exp.location && `• ${exp.location}`}
                  </p>
                  {exp.description && (
                    <div className="text-sm text-gray-600">
                      {exp.description.split('\n').map((line, i) => (
                        <div key={i} className="mb-1">
                          {line.trim().startsWith('•') ? line : `• ${line.trim()}`}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        );

      case 'education':
        return formData.education?.length > 0 && (
          <div className="mb-6">
            <h2 className="text-lg font-bold text-gray-900 mb-3 border-b-2 border-gray-300 pb-1">
              EDUCATION
            </h2>
            <div className="space-y-3">
              {formData.education.map((edu, index) => (
                <div key={index}>
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="text-base font-semibold text-gray-900">
                        {edu.degree} {edu.field && `in ${edu.field}`}
                      </h3>
                      <p className="text-sm text-gray-700">{edu.institution}</p>
                      {edu.location && <p className="text-sm text-gray-600">{edu.location}</p>}
                    </div>
                    <span className="text-sm text-gray-600">{edu.graduationDate}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        );

      case 'skills':
        return formData.skills && (
          <div className="mb-6">
            <h2 className="text-lg font-bold text-gray-900 mb-3 border-b-2 border-gray-300 pb-1">
              CORE COMPETENCIES
            </h2>
            <div className="space-y-2">
              {formData.skills.technical?.length > 0 && (
                <div>
                  <span className="text-sm font-semibold text-gray-900">Technical Skills: </span>
                  <span className="text-sm text-gray-700">
                    {formData.skills.technical.join(' • ')}
                  </span>
                </div>
              )}
              {formData.skills.languages?.length > 0 && (
                <div>
                  <span className="text-sm font-semibold text-gray-900">Languages: </span>
                  <span className="text-sm text-gray-700">
                    {formData.skills.languages.join(' • ')}
                  </span>
                </div>
              )}
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="bg-white p-8 max-w-4xl mx-auto" style={{ fontFamily: 'Arial, sans-serif' }}>
      {/* Header */}
      <div className="text-center mb-6 border-b-4 pb-4" style={{ borderColor: template.color }}>
        <h1 className="text-2xl font-bold text-gray-900 mb-2">
          {formData.personal?.firstName} {formData.personal?.lastName}
        </h1>
        <div className="flex justify-center space-x-4 text-sm text-gray-600">
          {formData.personal?.email && <span>{formData.personal.email}</span>}
          {formData.personal?.phone && <span>{formData.personal.phone}</span>}
          {formData.personal?.location && <span>{formData.personal.location}</span>}
        </div>
      </div>

      {/* Render sections based on template configuration */}
      {template.sections.map((section, index) => (
        <div key={index}>
          {renderSection(section)}
        </div>
      ))}
    </div>
  );
};

"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/resume-builder/page",{

/***/ "(app-pages-browser)/./src/components/resume/RedesignedResumeBuilder.jsx":
/*!***********************************************************!*\
  !*** ./src/components/resume/RedesignedResumeBuilder.jsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _SimplifiedNavigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./SimplifiedNavigation */ \"(app-pages-browser)/./src/components/resume/SimplifiedNavigation.jsx\");\n/* harmony import */ var _StreamlinedPreview__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./StreamlinedPreview */ \"(app-pages-browser)/./src/components/resume/StreamlinedPreview.jsx\");\n/* harmony import */ var _FullscreenPreviewModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./FullscreenPreviewModal */ \"(app-pages-browser)/./src/components/resume/FullscreenPreviewModal.jsx\");\n/* harmony import */ var _UniversalTemplateSelector__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./UniversalTemplateSelector */ \"(app-pages-browser)/./src/components/resume/UniversalTemplateSelector.jsx\");\n/* harmony import */ var _forms_UniversalFormFields__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./forms/UniversalFormFields */ \"(app-pages-browser)/./src/components/resume/forms/UniversalFormFields.jsx\");\n/* harmony import */ var _templates_MultiIndustryTemplates__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./templates/MultiIndustryTemplates */ \"(app-pages-browser)/./src/components/resume/templates/MultiIndustryTemplates.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n// Import new components\n\n\n\n\n\n// Import template system\n\nconst RedesignedResumeBuilder = (param)=>{\n    let { hideHeader = false } = param;\n    _s();\n    // Core state\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [selectedTemplate, setSelectedTemplate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('business_executive');\n    const [showTemplateSelector, setShowTemplateSelector] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showFullscreenPreview, setShowFullscreenPreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Form data with universal structure\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        personal: {\n            firstName: \"\",\n            lastName: \"\",\n            email: \"\",\n            phone: \"\",\n            location: \"\",\n            summary: \"\"\n        },\n        experience: [\n            {\n                id: 1,\n                title: \"\",\n                company: \"\",\n                location: \"\",\n                startDate: \"\",\n                endDate: \"\",\n                current: false,\n                description: \"\"\n            }\n        ],\n        education: [\n            {\n                id: 1,\n                degree: \"\",\n                field: \"\",\n                institution: \"\",\n                location: \"\",\n                graduationDate: \"\",\n                gpa: \"\"\n            }\n        ],\n        skills: {\n            technical: [],\n            languages: [],\n            certifications: []\n        }\n    });\n    // UI state\n    const [validationErrors, setValidationErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isSaving, setIsSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [completedSteps, setCompletedSteps] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Step configuration - simplified and universal\n    const steps = [\n        {\n            id: 0,\n            title: \"Personal Info\",\n            description: \"Your contact information and professional summary\"\n        },\n        {\n            id: 1,\n            title: \"Experience\",\n            description: \"Your work history and achievements\"\n        },\n        {\n            id: 2,\n            title: \"Education\",\n            description: \"Your educational background and qualifications\"\n        },\n        {\n            id: 3,\n            title: \"Skills\",\n            description: \"Your core competencies and abilities\"\n        },\n        {\n            id: 4,\n            title: \"Review\",\n            description: \"Review and download your resume\"\n        }\n    ];\n    // Auto-save functionality\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RedesignedResumeBuilder.useEffect\": ()=>{\n            const timeoutId = setTimeout({\n                \"RedesignedResumeBuilder.useEffect.timeoutId\": ()=>{\n                    localStorage.setItem('universalResumeData', JSON.stringify(formData));\n                    localStorage.setItem('selectedTemplate', selectedTemplate);\n                }\n            }[\"RedesignedResumeBuilder.useEffect.timeoutId\"], 2000);\n            return ({\n                \"RedesignedResumeBuilder.useEffect\": ()=>clearTimeout(timeoutId)\n            })[\"RedesignedResumeBuilder.useEffect\"];\n        }\n    }[\"RedesignedResumeBuilder.useEffect\"], [\n        formData,\n        selectedTemplate\n    ]);\n    // Load saved data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RedesignedResumeBuilder.useEffect\": ()=>{\n            const savedData = localStorage.getItem('universalResumeData');\n            const savedTemplate = localStorage.getItem('selectedTemplate');\n            if (savedData) {\n                try {\n                    setFormData(JSON.parse(savedData));\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success('Previous work restored!');\n                } catch (error) {\n                    console.error('Failed to load saved data:', error);\n                }\n            }\n            if (savedTemplate && _templates_MultiIndustryTemplates__WEBPACK_IMPORTED_MODULE_8__.INDUSTRY_TEMPLATES[savedTemplate]) {\n                setSelectedTemplate(savedTemplate);\n            }\n        }\n    }[\"RedesignedResumeBuilder.useEffect\"], []);\n    // Validation logic\n    const validateStep = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"RedesignedResumeBuilder.useCallback[validateStep]\": (stepIndex)=>{\n            const errors = {};\n            switch(stepIndex){\n                case 0:\n                    var _formData_personal_firstName, _formData_personal_lastName, _formData_personal_email;\n                    if (!((_formData_personal_firstName = formData.personal.firstName) === null || _formData_personal_firstName === void 0 ? void 0 : _formData_personal_firstName.trim())) errors.firstName = 'First name is required';\n                    if (!((_formData_personal_lastName = formData.personal.lastName) === null || _formData_personal_lastName === void 0 ? void 0 : _formData_personal_lastName.trim())) errors.lastName = 'Last name is required';\n                    if (!((_formData_personal_email = formData.personal.email) === null || _formData_personal_email === void 0 ? void 0 : _formData_personal_email.trim())) errors.email = 'Email is required';\n                    else if (!/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(formData.personal.email)) {\n                        errors.email = 'Please enter a valid email address';\n                    }\n                    break;\n                case 1:\n                    var _formData_experience;\n                    const validExperience = (_formData_experience = formData.experience) === null || _formData_experience === void 0 ? void 0 : _formData_experience.filter({\n                        \"RedesignedResumeBuilder.useCallback[validateStep]\": (exp)=>{\n                            var _exp_title, _exp_company;\n                            return ((_exp_title = exp.title) === null || _exp_title === void 0 ? void 0 : _exp_title.trim()) && ((_exp_company = exp.company) === null || _exp_company === void 0 ? void 0 : _exp_company.trim());\n                        }\n                    }[\"RedesignedResumeBuilder.useCallback[validateStep]\"]);\n                    if (!validExperience || validExperience.length === 0) {\n                        errors.experience = 'At least one work experience entry is required';\n                    }\n                    break;\n                case 2:\n                    var _formData_education;\n                    const validEducation = (_formData_education = formData.education) === null || _formData_education === void 0 ? void 0 : _formData_education.filter({\n                        \"RedesignedResumeBuilder.useCallback[validateStep]\": (edu)=>{\n                            var _edu_degree, _edu_institution;\n                            return ((_edu_degree = edu.degree) === null || _edu_degree === void 0 ? void 0 : _edu_degree.trim()) && ((_edu_institution = edu.institution) === null || _edu_institution === void 0 ? void 0 : _edu_institution.trim());\n                        }\n                    }[\"RedesignedResumeBuilder.useCallback[validateStep]\"]);\n                    if (!validEducation || validEducation.length === 0) {\n                        errors.education = 'At least one education entry is required';\n                    }\n                    break;\n                case 3:\n                    break;\n                default:\n                    break;\n            }\n            return errors;\n        }\n    }[\"RedesignedResumeBuilder.useCallback[validateStep]\"], [\n        formData\n    ]);\n    // Navigation logic\n    const canProceedToNextStep = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"RedesignedResumeBuilder.useCallback[canProceedToNextStep]\": (stepIndex)=>{\n            const errors = validateStep(stepIndex);\n            return Object.keys(errors).length === 0;\n        }\n    }[\"RedesignedResumeBuilder.useCallback[canProceedToNextStep]\"], [\n        validateStep\n    ]);\n    const nextStep = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"RedesignedResumeBuilder.useCallback[nextStep]\": ()=>{\n            const errors = validateStep(currentStep);\n            if (Object.keys(errors).length > 0) {\n                setValidationErrors(errors);\n                const errorMessages = Object.values(errors);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(errorMessages[0]);\n                return;\n            }\n            setCompletedSteps({\n                \"RedesignedResumeBuilder.useCallback[nextStep]\": (prev)=>[\n                        ...new Set([\n                            ...prev,\n                            currentStep\n                        ])\n                    ]\n            }[\"RedesignedResumeBuilder.useCallback[nextStep]\"]);\n            setValidationErrors({});\n            if (currentStep < steps.length - 1) {\n                setCurrentStep(currentStep + 1);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"\".concat(steps[currentStep].title, \" completed!\"));\n            }\n        }\n    }[\"RedesignedResumeBuilder.useCallback[nextStep]\"], [\n        currentStep,\n        validateStep,\n        steps\n    ]);\n    const prevStep = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"RedesignedResumeBuilder.useCallback[prevStep]\": ()=>{\n            setValidationErrors({});\n            if (currentStep > 0) setCurrentStep(currentStep - 1);\n        }\n    }[\"RedesignedResumeBuilder.useCallback[prevStep]\"], [\n        currentStep\n    ]);\n    // Form data management\n    const updateFormData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"RedesignedResumeBuilder.useCallback[updateFormData]\": function(section, field, value) {\n            let index = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : null;\n            setFormData({\n                \"RedesignedResumeBuilder.useCallback[updateFormData]\": (prev)=>{\n                    let newData = {\n                        ...prev\n                    };\n                    if (index !== null && Array.isArray(prev[section])) {\n                        const newArray = [\n                            ...prev[section]\n                        ];\n                        newArray[index] = {\n                            ...newArray[index],\n                            [field]: value\n                        };\n                        newData[section] = newArray;\n                    } else if (typeof prev[section] === 'object' && !Array.isArray(prev[section])) {\n                        newData[section] = {\n                            ...prev[section],\n                            [field]: value\n                        };\n                    } else {\n                        newData[field] = value;\n                    }\n                    return newData;\n                }\n            }[\"RedesignedResumeBuilder.useCallback[updateFormData]\"]);\n            // Clear validation errors for this field\n            if (validationErrors[field]) {\n                setValidationErrors({\n                    \"RedesignedResumeBuilder.useCallback[updateFormData]\": (prev)=>{\n                        const newErrors = {\n                            ...prev\n                        };\n                        delete newErrors[field];\n                        return newErrors;\n                    }\n                }[\"RedesignedResumeBuilder.useCallback[updateFormData]\"]);\n            }\n        }\n    }[\"RedesignedResumeBuilder.useCallback[updateFormData]\"], [\n        validationErrors\n    ]);\n    const addArrayItem = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"RedesignedResumeBuilder.useCallback[addArrayItem]\": (section, template)=>{\n            setFormData({\n                \"RedesignedResumeBuilder.useCallback[addArrayItem]\": (prev)=>({\n                        ...prev,\n                        [section]: [\n                            ...prev[section],\n                            {\n                                ...template,\n                                id: Math.random().toString(36).substring(2, 11)\n                            }\n                        ]\n                    })\n            }[\"RedesignedResumeBuilder.useCallback[addArrayItem]\"]);\n        }\n    }[\"RedesignedResumeBuilder.useCallback[addArrayItem]\"], []);\n    const removeArrayItem = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"RedesignedResumeBuilder.useCallback[removeArrayItem]\": (section, id)=>{\n            setFormData({\n                \"RedesignedResumeBuilder.useCallback[removeArrayItem]\": (prev)=>({\n                        ...prev,\n                        [section]: prev[section].filter({\n                            \"RedesignedResumeBuilder.useCallback[removeArrayItem]\": (item)=>item.id !== id\n                        }[\"RedesignedResumeBuilder.useCallback[removeArrayItem]\"])\n                    })\n            }[\"RedesignedResumeBuilder.useCallback[removeArrayItem]\"]);\n        }\n    }[\"RedesignedResumeBuilder.useCallback[removeArrayItem]\"], []);\n    // Save functionality\n    const handleSave = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"RedesignedResumeBuilder.useCallback[handleSave]\": ()=>{\n            setIsSaving(true);\n            localStorage.setItem('universalResumeData', JSON.stringify(formData));\n            localStorage.setItem('selectedTemplate', selectedTemplate);\n            setTimeout({\n                \"RedesignedResumeBuilder.useCallback[handleSave]\": ()=>{\n                    setIsSaving(false);\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success('Progress saved!');\n                }\n            }[\"RedesignedResumeBuilder.useCallback[handleSave]\"], 1000);\n        }\n    }[\"RedesignedResumeBuilder.useCallback[handleSave]\"], [\n        formData,\n        selectedTemplate\n    ]);\n    // Download functionality\n    const handleDownload = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"RedesignedResumeBuilder.useCallback[handleDownload]\": ()=>{\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success('Download feature coming soon!');\n        }\n    }[\"RedesignedResumeBuilder.useCallback[handleDownload]\"], []);\n    // Calculate completion percentage\n    const getCompletionPercentage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"RedesignedResumeBuilder.useCallback[getCompletionPercentage]\": ()=>{\n            var _formData_experience, _formData_education, _formData_skills_technical, _formData_skills;\n            let totalFields = 0;\n            let completedFields = 0;\n            // Personal info (4 required fields)\n            totalFields += 4;\n            if (formData.personal.firstName) completedFields++;\n            if (formData.personal.lastName) completedFields++;\n            if (formData.personal.email) completedFields++;\n            if (formData.personal.summary) completedFields++;\n            // Experience (at least 1 entry with title and company)\n            totalFields += 2;\n            const validExp = (_formData_experience = formData.experience) === null || _formData_experience === void 0 ? void 0 : _formData_experience.filter({\n                \"RedesignedResumeBuilder.useCallback[getCompletionPercentage]\": (exp)=>exp.title && exp.company\n            }[\"RedesignedResumeBuilder.useCallback[getCompletionPercentage]\"]);\n            if ((validExp === null || validExp === void 0 ? void 0 : validExp.length) > 0) completedFields += 2;\n            // Education (at least 1 entry with degree and institution)\n            totalFields += 2;\n            const validEdu = (_formData_education = formData.education) === null || _formData_education === void 0 ? void 0 : _formData_education.filter({\n                \"RedesignedResumeBuilder.useCallback[getCompletionPercentage]\": (edu)=>edu.degree && edu.institution\n            }[\"RedesignedResumeBuilder.useCallback[getCompletionPercentage]\"]);\n            if ((validEdu === null || validEdu === void 0 ? void 0 : validEdu.length) > 0) completedFields += 2;\n            // Skills (optional but counts if present)\n            if (((_formData_skills = formData.skills) === null || _formData_skills === void 0 ? void 0 : (_formData_skills_technical = _formData_skills.technical) === null || _formData_skills_technical === void 0 ? void 0 : _formData_skills_technical.length) > 0) {\n                totalFields += 1;\n                completedFields += 1;\n            }\n            return Math.round(completedFields / totalFields * 100);\n        }\n    }[\"RedesignedResumeBuilder.useCallback[getCompletionPercentage]\"], [\n        formData\n    ]);\n    // Render step content\n    const renderStepContent = ()=>{\n        const commonProps = {\n            formData,\n            updateFormData,\n            addArrayItem,\n            removeArrayItem,\n            validationErrors\n        };\n        switch(currentStep){\n            case 0:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_UniversalFormFields__WEBPACK_IMPORTED_MODULE_7__.UniversalPersonalForm, {\n                    ...commonProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                    lineNumber: 306,\n                    columnNumber: 16\n                }, undefined);\n            case 1:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_UniversalFormFields__WEBPACK_IMPORTED_MODULE_7__.UniversalExperienceForm, {\n                    ...commonProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                    lineNumber: 308,\n                    columnNumber: 16\n                }, undefined);\n            case 2:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_UniversalFormFields__WEBPACK_IMPORTED_MODULE_7__.UniversalEducationForm, {\n                    ...commonProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                    lineNumber: 310,\n                    columnNumber: 16\n                }, undefined);\n            case 3:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_UniversalFormFields__WEBPACK_IMPORTED_MODULE_7__.UniversalSkillsForm, {\n                    ...commonProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                    lineNumber: 312,\n                    columnNumber: 16\n                }, undefined);\n            case 4:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg border border-gray-200 p-6 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-semibold text-gray-900 mb-4\",\n                                children: \"Your Resume is Ready!\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                lineNumber: 317,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-6\",\n                                children: \"Review your resume in the preview panel and download when ready.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                lineNumber: 320,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowTemplateSelector(true),\n                                        className: \"px-6 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors\",\n                                        children: \"Change Template\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                        lineNumber: 325,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowFullscreenPreview(true),\n                                        className: \"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                                        children: \"Preview Fullscreen\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                        lineNumber: 331,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleDownload,\n                                        className: \"px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\",\n                                        children: \"Download Resume\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                        lineNumber: 337,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                lineNumber: 324,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                        lineNumber: 316,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                    lineNumber: 315,\n                    columnNumber: 11\n                }, undefined);\n            default:\n                return null;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-b from-black to-[#0A0A0A] text-white relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 opacity-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 bg-[url('/grid.svg')] [mask-image:radial-gradient(ellipse_at_center,white,transparent)]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                    lineNumber: 356,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                lineNumber: 355,\n                columnNumber: 7\n            }, undefined),\n            !hideHeader && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                        className: \"text-center\",\n                        initial: {\n                            opacity: 0,\n                            y: -20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center gap-3 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                                className: \"h-10 w-10 text-neural-pink\",\n                                                animate: {\n                                                    rotate: 360\n                                                },\n                                                transition: {\n                                                    duration: 20,\n                                                    repeat: Infinity,\n                                                    ease: \"linear\"\n                                                },\n                                                children: \"✨\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                                lineNumber: 370,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-neural-pink opacity-20 rounded-full blur-md\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                                lineNumber: 377,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                        lineNumber: 369,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl lg:text-4xl font-bold text-white\",\n                                        children: \"Universal Resume Builder\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                        lineNumber: 379,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                lineNumber: 368,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-300 text-lg max-w-2xl mx-auto\",\n                                children: \"Create professional resumes for any industry with our intelligent builder\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                lineNumber: 383,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                        lineNumber: 363,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                    lineNumber: 362,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                lineNumber: 361,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 pb-32\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col xl:flex-row gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 max-w-4xl\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.AnimatePresence, {\n                                    mode: \"wait\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            x: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        exit: {\n                                            opacity: 0,\n                                            x: -20\n                                        },\n                                        transition: {\n                                            duration: 0.3\n                                        },\n                                        children: renderStepContent()\n                                    }, currentStep, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                        lineNumber: 398,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                    lineNumber: 397,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                lineNumber: 396,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden xl:block xl:w-80 xl:flex-shrink-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"sticky top-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StreamlinedPreview__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        formData: formData,\n                                        selectedTemplate: selectedTemplate,\n                                        onOpenFullscreen: ()=>setShowFullscreenPreview(true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                        lineNumber: 413,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                    lineNumber: 412,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                lineNumber: 411,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                        lineNumber: 394,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                    lineNumber: 393,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                lineNumber: 392,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SimplifiedNavigation__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                currentStep: currentStep,\n                totalSteps: steps.length,\n                onPrevious: prevStep,\n                onNext: nextStep,\n                onSave: handleSave,\n                onDownload: handleDownload,\n                onHome: ()=>window.location.href = '/',\n                canProceed: canProceedToNextStep(currentStep),\n                isSaving: isSaving,\n                completionPercentage: getCompletionPercentage(),\n                stepTitles: steps.map((step)=>step.title)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                lineNumber: 425,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.AnimatePresence, {\n                children: showTemplateSelector && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_UniversalTemplateSelector__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    selectedTemplate: selectedTemplate,\n                    onTemplateSelect: setSelectedTemplate,\n                    onClose: ()=>setShowTemplateSelector(false),\n                    formData: formData\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                    lineNumber: 442,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                lineNumber: 440,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.AnimatePresence, {\n                children: showFullscreenPreview && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FullscreenPreviewModal__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    isOpen: showFullscreenPreview,\n                    onClose: ()=>setShowFullscreenPreview(false),\n                    formData: formData,\n                    selectedTemplate: selectedTemplate,\n                    onTemplateChange: setSelectedTemplate\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                    lineNumber: 453,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                lineNumber: 451,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n        lineNumber: 353,\n        columnNumber: 5\n    }, undefined);\n};\n_s(RedesignedResumeBuilder, \"DLu6Gwuw9dLx2aADXayQf6m5jMU=\");\n_c = RedesignedResumeBuilder;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (RedesignedResumeBuilder);\nvar _c;\n$RefreshReg$(_c, \"RedesignedResumeBuilder\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/resume/RedesignedResumeBuilder.jsx\n"));

/***/ })

});
// Production Configuration
export const PRODUCTION_CONFIG = {
  // API Configuration
  api: {
    gemini: {
      enabled: !!process.env.NEXT_PUBLIC_GEMINI_API_KEY,
      rateLimit: {
        calls: 10,
        window: 60000 // 1 minute
      }
    },
    firebase: {
      enabled: !!process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
      config: {
        apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
        authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
        projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
        storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
        messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
        appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
        databaseURL: process.env.NEXT_PUBLIC_FIREBASE_DATABASE_URL,
        measurementId: process.env.NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID
      }
    }
  },

  // Feature Flags
  features: {
    aiEnhancement: true,
    atsScoring: true,
    realTimePreview: true,
    templateSelector: true,
    mobilePreview: true,
    autoSave: true,
    errorReporting: process.env.NODE_ENV === 'production'
  },

  // Performance Settings
  performance: {
    debounceDelay: 2000, // 2 seconds for AI analysis
    autoSaveInterval: 30000, // 30 seconds
    previewUpdateDelay: 500, // 0.5 seconds
    maxFileSize: 5 * 1024 * 1024, // 5MB
    maxImageSize: 2 * 1024 * 1024 // 2MB
  },

  // UI Configuration
  ui: {
    theme: 'dark',
    animations: true,
    reducedMotion: false,
    mobileBreakpoint: 768,
    desktopBreakpoint: 1024
  },

  // Analytics & Monitoring
  monitoring: {
    errorReporting: process.env.NODE_ENV === 'production',
    performanceTracking: process.env.NODE_ENV === 'production',
    userAnalytics: false // Set to true when analytics are implemented
  },

  // Security
  security: {
    maxRequestsPerMinute: 60,
    sessionTimeout: 24 * 60 * 60 * 1000, // 24 hours
    csrfProtection: true,
    sanitizeInput: true
  },

  // Resume Builder Specific
  resumeBuilder: {
    maxSections: {
      experience: 10,
      education: 5,
      skills: 50,
      projects: 8,
      certifications: 10
    },
    templates: {
      default: 'business_executive',
      available: [
        'business_executive',
        'business_analyst',
        'healthcare_professional',
        'healthcare_nurse',
        'education_teacher',
        'education_administrator',
        'technology_software',
        'technology_data',
        'creative_designer',
        'creative_marketing',
        'sales_professional',
        'sales_manager',
        'finance_analyst',
        'finance_manager',
        'legal_attorney',
        'legal_paralegal',
        'engineering_mechanical',
        'engineering_civil',
        'hospitality_manager',
        'hospitality_service',
        'retail_manager',
        'retail_associate',
        'nonprofit_professional'
      ]
    },
    ai: {
      maxEnhancementRequests: 5, // per session
      enhancementTypes: ['summary', 'experience', 'skills'],
      fallbackEnabled: true
    }
  }
};

// Environment-specific overrides
if (process.env.NODE_ENV === 'development') {
  PRODUCTION_CONFIG.performance.debounceDelay = 1000; // Faster in dev
  PRODUCTION_CONFIG.monitoring.errorReporting = false;
  PRODUCTION_CONFIG.security.maxRequestsPerMinute = 120; // More lenient in dev
}

// Validation function
export const validateConfig = () => {
  const errors = [];
  
  if (!PRODUCTION_CONFIG.api.firebase.enabled) {
    errors.push('Firebase configuration is missing');
  }
  
  if (!PRODUCTION_CONFIG.api.gemini.enabled) {
    console.warn('Gemini API not configured - AI features will use fallbacks');
  }
  
  if (errors.length > 0) {
    console.error('Configuration errors:', errors);
    return false;
  }
  
  return true;
};

// Export individual configs for easier imports
export const API_CONFIG = PRODUCTION_CONFIG.api;
export const FEATURE_FLAGS = PRODUCTION_CONFIG.features;
export const PERFORMANCE_CONFIG = PRODUCTION_CONFIG.performance;
export const UI_CONFIG = PRODUCTION_CONFIG.ui;
export const SECURITY_CONFIG = PRODUCTION_CONFIG.security;
export const RESUME_CONFIG = PRODUCTION_CONFIG.resumeBuilder;

export default PRODUCTION_CONFIG;

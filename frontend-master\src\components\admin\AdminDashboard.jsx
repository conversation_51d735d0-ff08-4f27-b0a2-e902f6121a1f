'use client';

import { useState, useEffect } from 'react';
import { useAdmin } from '@/contexts/AdminContext';
import FirebaseStatus from '@/components/common/FirebaseStatus';
import { initializeFirebaseData } from '@/firebase/initialize';
import { testFirebaseConnection } from '@/firebase/test';
import visitorService from '@/services/VisitorService';
import { firestore, database } from '@/firebase/config';
import { collection, getDocs, doc, getDoc, deleteDoc } from 'firebase/firestore';
import { ref, get } from 'firebase/database';
import { motion } from 'framer-motion';
import AdminSystemTest from './AdminSystemTest';
import WebsiteAnalytics from './WebsiteAnalytics';
import ReviewManagement from './ReviewManagement';
import { 
  Database, 
  Play, 
  CheckCircle, 
  AlertCircle, 
  Settings, 
  Users, 
  MessageSquare, 
  BarChart3, 
  Trash2, 
  Eye,
  LogOut,
  Shield,
  Activity,
  Zap
} from 'lucide-react';

const AdminDashboard = () => {
  const { user, adminLogout } = useAdmin();
  const [activeTab, setActiveTab] = useState('dashboard');
  const [initStatus, setInitStatus] = useState(null);
  const [isInitializing, setIsInitializing] = useState(false);
  const [testResults, setTestResults] = useState(null);
  const [isTesting, setIsTesting] = useState(false);
  const [stats, setStats] = useState({ users: 0, reviews: 0, applications: 0, totalVisitors: 0 });
  const [recentUsers, setRecentUsers] = useState([]);
  const [recentReviews, setRecentReviews] = useState([]);
  const [systemHealth, setSystemHealth] = useState({ status: 'checking', lastChecked: null });

  useEffect(() => {
    fetchDashboardData();
    checkSystemHealth();
    
    // Auto-refresh every 30 seconds
    const interval = setInterval(() => {
      fetchDashboardData();
      checkSystemHealth();
    }, 30000);
    
    return () => clearInterval(interval);
  }, []);

  const fetchDashboardData = async () => {
    try {
      // Fetch user count
      const usersSnapshot = await getDocs(collection(firestore, 'users'));
      const userCount = usersSnapshot.size;
      
      // Fetch reviews count
      const reviewsSnapshot = await getDocs(collection(firestore, 'reviews'));
      const reviewCount = reviewsSnapshot.size;
      
      // Fetch recent users (last 5)
      const recentUsersData = [];
      usersSnapshot.forEach((doc) => {
        recentUsersData.push({ id: doc.id, ...doc.data() });
      });
      recentUsersData.sort((a, b) => new Date(b.joinedAt) - new Date(a.joinedAt));
      setRecentUsers(recentUsersData.slice(0, 5));
      
      // Fetch recent reviews (last 5)
      const recentReviewsData = [];
      reviewsSnapshot.forEach((doc) => {
        recentReviewsData.push({ id: doc.id, ...doc.data() });
      });
      recentReviewsData.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
      setRecentReviews(recentReviewsData.slice(0, 5));
        // Get applications count from realtime database
      const appsRef = ref(database, 'stats/totalApplications');
      const appsSnapshot = await get(appsRef);
      const appCount = appsSnapshot.val() || 0;
      
      // Get total visitors count
      const totalVisitors = await visitorService.getTotalVisitors();
      
      setStats({ 
        users: userCount, 
        reviews: reviewCount, 
        applications: appCount,
        totalVisitors: totalVisitors
      });
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
    }
  };

  const checkSystemHealth = async () => {
    try {
      const results = await testFirebaseConnection();
      const isHealthy = results.firestore && results.realtimeDatabase;
      setSystemHealth({
        status: isHealthy ? 'healthy' : 'warning',
        lastChecked: new Date().toLocaleTimeString(),
        details: results
      });
    } catch (error) {
      setSystemHealth({
        status: 'error',
        lastChecked: new Date().toLocaleTimeString(),
        error: error.message
      });
    }
  };

  const handleInitializeData = async () => {
    setIsInitializing(true);
    setInitStatus(null);
    
    try {
      const result = await initializeFirebaseData();
      setInitStatus(result);
      if (result.success) {
        // Refresh dashboard data after initialization
        setTimeout(fetchDashboardData, 2000);
      }
    } catch (error) {
      setInitStatus({ success: false, error: error.message });
    } finally {
      setIsInitializing(false);
    }
  };

  const handleTestConnection = async () => {
    setIsTesting(true);
    setTestResults(null);
    
    try {
      const results = await testFirebaseConnection();
      setTestResults(results);
    } catch (error) {
      setTestResults({ success: false, error: error.message });
    } finally {
      setIsTesting(false);
    }
  };

  const handleDeleteUser = async (userId) => {
    if (window.confirm('Are you sure you want to delete this user?')) {
      try {
        await deleteDoc(doc(firestore, 'users', userId));
        fetchDashboardData(); // Refresh data
      } catch (error) {
        console.error('Error deleting user:', error);
      }
    }
  };

  const handleDeleteReview = async (reviewId) => {
    if (window.confirm('Are you sure you want to delete this review?')) {
      try {
        await deleteDoc(doc(firestore, 'reviews', reviewId));
        fetchDashboardData(); // Refresh data
      } catch (error) {
        console.error('Error deleting review:', error);
      }
    }
  };

  const getHealthStatusColor = (status) => {
    switch (status) {
      case 'healthy': return 'text-green-400';
      case 'warning': return 'text-yellow-400';
      case 'error': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  const getHealthStatusIcon = (status) => {
    switch (status) {
      case 'healthy': return <CheckCircle className="w-5 h-5" />;
      case 'warning': return <AlertCircle className="w-5 h-5" />;
      case 'error': return <AlertCircle className="w-5 h-5" />;
      default: return <Activity className="w-5 h-5 animate-spin" />;
    }
  };  return (
    <div className="min-h-screen bg-gradient-to-br from-black via-gray-900/30 to-black relative overflow-hidden">
      {/* Neural Network Background */}
      <div className="absolute inset-0">{/* Animated Neural Nodes */}
        {[...Array(8)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-2 h-2 bg-gradient-to-r from-neural-purple to-neural-pink rounded-full"
            style={{
              left: `${15 + (i * 12)}%`,
              top: `${20 + (i * 8)}%`,
            }}            animate={{
              scale: [0.5, 1.2, 0.5],
              opacity: [0.05, 0.2, 0.05],
            }}
            transition={{
              duration: 3 + (i * 0.5),
              repeat: Infinity,
              ease: "easeInOut",
            }}
          />
        ))}
        
        {/* Neural Connections */}        <svg className="absolute inset-0 w-full h-full">
          <defs>            <linearGradient id="neural-line" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor="#8B5CF6" stopOpacity="0.08" />
              <stop offset="50%" stopColor="#EC4899" stopOpacity="0.12" />
              <stop offset="100%" stopColor="#3B82F6" stopOpacity="0.08" />
            </linearGradient>
          </defs>
          {[...Array(6)].map((_, i) => (
            <motion.line
              key={i}
              x1={`${10 + (i * 15)}%`}
              y1={`${15 + (i * 12)}%`}
              x2={`${30 + (i * 10)}%`}
              y2={`${40 + (i * 8)}%`}
              stroke="url(#neural-line)"
              strokeWidth="1"
              initial={{ pathLength: 0, opacity: 0 }}
              animate={{ pathLength: 1, opacity: 1 }}
              transition={{
                duration: 2,
                delay: i * 0.3,
                repeat: Infinity,
                repeatType: "reverse",
                repeatDelay: 1,
              }}
            />
          ))}
        </svg>        {/* Floating Orbs */}        <motion.div
          className="absolute top-20 left-20 w-32 h-32 bg-gradient-to-r from-neural-purple/5 to-neural-pink/5 rounded-full blur-xl"
          animate={{
            scale: [1, 1.3, 1],
            opacity: [0.1, 0.2, 0.1],
            x: [0, 30, 0],
            y: [0, -20, 0],
          }}
          transition={{
            duration: 12,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        />        <motion.div
          className="absolute bottom-32 right-16 w-24 h-24 bg-gradient-to-r from-neural-blue/5 to-neural-purple/5 rounded-full blur-xl"
          animate={{
            scale: [1.2, 0.8, 1.2],
            opacity: [0.08, 0.15, 0.08],
            x: [0, -25, 0],
            y: [0, 15, 0],
          }}
          transition={{
            duration: 10,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        />
      </div>      <div className="relative z-10 pt-20 p-4 md:pt-24 md:p-8">
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <motion.div 
            className="flex flex-col md:flex-row md:items-center justify-between mb-8"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <div className="flex items-center gap-4 mb-4 md:mb-0">
              <motion.div
                className="relative"
                animate={{
                  rotate: [0, 360],
                }}
                transition={{
                  duration: 20,
                  repeat: Infinity,
                  ease: "linear",
                }}
              >
                <Shield className="w-10 h-10 text-neural-purple" />
                <motion.div
                  className="absolute inset-0 w-10 h-10 border-2 border-neural-pink/30 rounded-full"
                  animate={{
                    scale: [1, 1.2, 1],
                    opacity: [0.5, 0.8, 0.5],
                  }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    ease: "easeInOut",
                  }}
                />
              </motion.div>              <div>
                <h1 className="text-3xl font-bold bg-gradient-to-r from-neural-purple to-neural-pink bg-clip-text text-transparent">
                  BlinkFind Dashboard
                </h1>
                <p className="text-gray-300">Welcome back, {user?.email}</p>
              </div>
            </div>
              <div className="flex items-center gap-4">
              {/* System Health */}
              <motion.div 
                className={`flex items-center gap-2 px-4 py-2 rounded-full bg-black/20 backdrop-blur-sm border border-white/20 ${getHealthStatusColor(systemHealth.status)}`}
                whileHover={{ scale: 1.05 }}
                transition={{ duration: 0.2 }}
              >
                {getHealthStatusIcon(systemHealth.status)}
                <span className="text-sm capitalize font-medium ">{systemHealth.status}</span>
              </motion.div>
              
              {/* Logout Button */}
              <motion.button
                onClick={adminLogout}
                className="flex items-center gap-2 bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white px-4 py-2 rounded-xl transition-all duration-300 font-medium shadow-lg shadow-red-500/30"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <LogOut className="w-4 h-4" />
                Logout
              </motion.button>
            </div>
          </motion.div>

          {/* Tab Navigation */}
          <motion.div
            className="flex gap-2 mb-8"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
          >
            {[
              { id: 'dashboard', label: 'Dashboard', icon: BarChart3 },
              { id: 'reviews', label: 'Review Management', icon: MessageSquare },
              { id: 'analytics', label: 'Analytics', icon: Activity },
              { id: 'system', label: 'System', icon: Settings }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center gap-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 ${
                  activeTab === tab.id
                    ? 'bg-neural-purple text-white shadow-lg shadow-neural-purple/30'
                    : 'bg-black/20 text-gray-300 hover:bg-black/30 hover:text-white border border-white/10'
                }`}
              >
                <tab.icon className="h-4 w-4" />
                {tab.label}
              </button>
            ))}
          </motion.div>

          {/* Tab Content */}
          {activeTab === 'dashboard' && (
            <>
              {/* Stats Cards */}
          <motion.div 
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          ><motion.div 
              className="bg-black/20 backdrop-blur-xl border border-white/20 rounded-2xl p-6 shadow-lg shadow-neural-blue/10 hover:bg-black/30 transition-all duration-300"
              whileHover={{ scale: 1.02, y: -5 }}
              transition={{ duration: 0.3 }}
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-300 text-sm font-medium">Total Users</p>
                  <motion.p 
                    className="text-3xl font-bold bg-gradient-to-r from-neural-blue to-neural-purple bg-clip-text text-transparent"
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ duration: 0.5, delay: 0.3 }}
                  >
                    {stats.users}
                  </motion.p>
                </div>
                <motion.div 
                  className="p-3 bg-gradient-to-r from-neural-blue/20 to-neural-purple/20 rounded-xl"
                  whileHover={{ rotate: 5 }}
                  transition={{ duration: 0.2 }}
                >
                  <Users className="w-8 h-8 text-neural-blue" />
                </motion.div>
              </div>            </motion.div>
            <motion.div 
              className="bg-black/20 backdrop-blur-xl border border-white/20 rounded-2xl p-6 shadow-lg shadow-green-500/10 hover:bg-black/30 transition-all duration-300"
              whileHover={{ scale: 1.02, y: -5 }}
              transition={{ duration: 0.3 }}
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-300 text-sm font-medium">Total Reviews</p>
                  <motion.p 
                    className="text-3xl font-bold bg-gradient-to-r from-green-400 to-emerald-400 bg-clip-text text-transparent"
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ duration: 0.5, delay: 0.4 }}
                  >
                    {stats.reviews}
                  </motion.p>
                </div>
                <motion.div 
                  className="p-3 bg-gradient-to-r from-green-500/20 to-emerald-500/20 rounded-xl"
                  whileHover={{ rotate: 5 }}
                  transition={{ duration: 0.2 }}
                >
                  <MessageSquare className="w-8 h-8 text-green-400" />
                </motion.div>
              </div>
            </motion.div>            <motion.div 
              className="bg-black/20 backdrop-blur-xl border border-white/20 rounded-2xl p-6 shadow-lg shadow-neural-pink/10 hover:bg-black/30 transition-all duration-300"
              whileHover={{ scale: 1.02, y: -5 }}
              transition={{ duration: 0.3 }}
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-300 text-sm font-medium">Applications</p>
                  <motion.p 
                    className="text-3xl font-bold bg-gradient-to-r from-neural-pink to-neural-purple bg-clip-text text-transparent"
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ duration: 0.5, delay: 0.5 }}
                  >
                    {stats.applications}
                  </motion.p>
                </div>
                <motion.div 
                  className="p-3 bg-gradient-to-r from-neural-pink/20 to-neural-purple/20 rounded-xl"
                  whileHover={{ rotate: 5 }}
                  transition={{ duration: 0.2 }}
                >
                  <BarChart3 className="w-8 h-8 text-neural-pink" />
                </motion.div>
              </div>
            </motion.div>

            <motion.div 
              className="bg-black/20 backdrop-blur-xl border border-white/20 rounded-2xl p-6 shadow-lg shadow-amber-500/10 hover:bg-black/30 transition-all duration-300"
              whileHover={{ scale: 1.02, y: -5 }}
              transition={{ duration: 0.3 }}
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-300 text-sm font-medium">Website Visitors</p>
                  <motion.p 
                    className="text-3xl font-bold bg-gradient-to-r from-amber-400 to-orange-400 bg-clip-text text-transparent"
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ duration: 0.5, delay: 0.6 }}
                  >
                    {stats.totalVisitors}
                  </motion.p>
                </div>
                <motion.div 
                  className="p-3 bg-gradient-to-r from-amber-500/20 to-orange-500/20 rounded-xl"
                  whileHover={{ rotate: 5 }}
                  transition={{ duration: 0.2 }}
                >
                  <Eye className="w-8 h-8 text-amber-400" />
                </motion.div>
              </div>
            </motion.div>
          </motion.div>{/* Firebase Status */}
        <motion.div 
          className="mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
        >
          <h2 className="text-xl font-semibold text-white mb-4 flex items-center gap-2">
            <motion.div
              animate={{ rotate: [0, 360] }}
              transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
            >
              <Database className="w-5 h-5 text-neural-blue" />
            </motion.div>
            Firebase Connection Status
          </h2>
          <FirebaseStatus showDetails={true} />
        </motion.div>

        {/* Website Analytics */}
        <motion.div 
          className="mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.65 }}
        >
          <h2 className="text-xl font-semibold text-white mb-4 flex items-center gap-2">
            <motion.div
              animate={{ scale: [1, 1.1, 1] }}
              transition={{ duration: 2, repeat: Infinity }}
            >
              <BarChart3 className="w-5 h-5 text-neural-pink" />
            </motion.div>
            Website Analytics
          </h2>
          <WebsiteAnalytics />
        </motion.div>

        {/* Action Cards */}
        <motion.div 
          className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.7 }}
        >
          {/* System Diagnostics */}
          <div className="lg:col-span-1">
            <AdminSystemTest />
          </div>

          {/* Test Connection Card */}
          <motion.div 
            className="bg-black/20 backdrop-blur-xl border border-white/20 rounded-2xl p-6 hover:bg-black/30 transition-all duration-300"
            whileHover={{ scale: 1.02, y: -5 }}
            transition={{ duration: 0.3 }}
          >
            <h3 className="text-lg font-semibold text-white mb-3 flex items-center gap-2">
              <motion.div
                animate={{ scale: [1, 1.2, 1] }}
                transition={{ duration: 2, repeat: Infinity }}
              >
                <Zap className="w-5 h-5 text-green-400" />
              </motion.div>
              System Health Check
            </h3>
            <p className="text-gray-300 text-sm mb-4">
              Run comprehensive tests on all Firebase services and system components.
            </p>
            <motion.button
              onClick={handleTestConnection}
              disabled={isTesting}
              className="w-full bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 disabled:from-green-500/50 disabled:to-emerald-500/50 text-white px-4 py-2 rounded-xl transition-all duration-300 flex items-center justify-center gap-2 shadow-lg shadow-green-500/20"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              {isTesting ? (
                <>
                  <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                  Testing...
                </>
              ) : (
                <>
                  <Play className="w-4 h-4" />
                  Run Health Check
                </>
              )}
            </motion.button>            {testResults && (
              <motion.div 
                className="mt-4 p-3 bg-black/30 backdrop-blur-sm rounded-xl border border-white/10"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
              >
                <div className="text-sm space-y-1">
                  <div className={`flex items-center gap-2 ${testResults.firestore ? 'text-green-400' : 'text-red-400'}`}>
                    {testResults.firestore ? <CheckCircle className="w-4 h-4" /> : <AlertCircle className="w-4 h-4" />}
                    Firestore: {testResults.firestore ? 'Operational' : 'Failed'}
                  </div>
                  <div className={`flex items-center gap-2 ${testResults.realtimeDatabase ? 'text-green-400' : 'text-red-400'}`}>
                    {testResults.realtimeDatabase ? <CheckCircle className="w-4 h-4" /> : <AlertCircle className="w-4 h-4" />}
                    Realtime DB: {testResults.realtimeDatabase ? 'Operational' : 'Failed'}
                  </div>
                </div>
              </motion.div>
            )}          </motion.div>

          {/* Initialize Data Card */}
          <motion.div 
            className="bg-black/20 backdrop-blur-xl border border-white/20 rounded-2xl p-6 hover:bg-black/30 transition-all duration-300"
            whileHover={{ scale: 1.02, y: -5 }}
            transition={{ duration: 0.3 }}
          >
            <h3 className="text-lg font-semibold text-white mb-3 flex items-center gap-2">
              <motion.div
                animate={{ rotate: [0, 360] }}
                transition={{ duration: 6, repeat: Infinity, ease: "linear" }}
              >
                <Database className="w-5 h-5 text-neural-blue" />
              </motion.div>
              Database Management
            </h3>
            <p className="text-gray-300 text-sm mb-4">
              Initialize database with sample data or reset collections for testing purposes.
            </p>
            <motion.button
              onClick={handleInitializeData}
              disabled={isInitializing}
              className="w-full bg-gradient-to-r from-neural-blue to-neural-purple hover:from-blue-600 hover:to-purple-600 disabled:from-neural-blue/50 disabled:to-neural-purple/50 text-white px-4 py-2 rounded-xl transition-all duration-300 flex items-center justify-center gap-2 shadow-lg shadow-neural-blue/20"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              {isInitializing ? (
                <>
                  <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                  Initializing...
                </>
              ) : (
                <>
                  <Database className="w-4 h-4" />
                  Initialize Sample Data
                </>
              )}
            </motion.button>

            {initStatus && (
              <motion.div 
                className={`mt-4 p-3 rounded-xl border ${initStatus.success ? 'bg-green-500/10 border-green-500/20' : 'bg-red-500/10 border-red-500/20'}`}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
              >
                <div className={`flex items-center gap-2 text-sm ${initStatus.success ? 'text-green-400' : 'text-red-400'}`}>
                  {initStatus.success ? <CheckCircle className="w-4 h-4" /> : <AlertCircle className="w-4 h-4" />}
                  {initStatus.success ? 'Database initialized successfully!' : `Failed: ${initStatus.error}`}
                </div>
              </motion.div>
            )}
          </motion.div>
        </motion.div>        {/* Data Management */}
        <motion.div 
          className="grid lg:grid-cols-2 gap-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
        >
          {/* Recent Users */}
          <motion.div 
            className="bg-black/20 backdrop-blur-xl border border-white/20 rounded-2xl p-6 hover:bg-black/30 transition-all duration-300"
            whileHover={{ scale: 1.01, y: -2 }}
            transition={{ duration: 0.3 }}
          >
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
              <motion.div
                animate={{ scale: [1, 1.1, 1] }}
                transition={{ duration: 2, repeat: Infinity }}
              >
                <Users className="w-5 h-5 text-neural-blue" />
              </motion.div>
              Recent Users
            </h3>
            <div className="space-y-3">
              {recentUsers.length > 0 ? (
                recentUsers.map((user, index) => (
                  <motion.div 
                    key={user.id} 
                    className="flex items-center justify-between p-3 bg-black/30 backdrop-blur-sm rounded-xl border border-white/10 hover:bg-black/40 transition-all duration-300"
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                    whileHover={{ scale: 1.02 }}
                  >
                    <div className="flex items-center gap-3">
                      <motion.div 
                        className="w-8 h-8 bg-gradient-to-r from-neural-purple to-neural-pink rounded-full flex items-center justify-center text-white text-sm font-medium"
                        whileHover={{ rotate: 180 }}
                        transition={{ duration: 0.3 }}
                      >
                        {user.name?.charAt(0) || 'U'}
                      </motion.div>
                      <div>
                        <div className="text-white text-sm font-medium">{user.name || 'Unknown'}</div>
                        <div className="text-gray-400 text-xs">{user.email}</div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <motion.button
                        onClick={() => handleDeleteUser(user.id)}
                        className="text-red-400 hover:text-red-300 transition-colors p-1 rounded-lg hover:bg-red-500/10"
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                      >
                        <Trash2 className="w-4 h-4" />
                      </motion.button>
                    </div>
                  </motion.div>
                ))              ) : (
                <motion.div 
                  className="text-gray-400 text-center py-8 bg-black/10 rounded-xl border border-white/5"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.5 }}
                >
                  <Users className="w-8 h-8 mx-auto mb-2 opacity-50" />
                  No users found
                </motion.div>
              )}
            </div>
          </motion.div>

          {/* Recent Reviews */}
          <motion.div 
            className="bg-black/20 backdrop-blur-xl border border-white/20 rounded-2xl p-6 hover:bg-black/30 transition-all duration-300"
            whileHover={{ scale: 1.01, y: -2 }}
            transition={{ duration: 0.3 }}
          >
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
              <motion.div
                animate={{ rotate: [0, 15, -15, 0] }}
                transition={{ duration: 3, repeat: Infinity }}
              >
                <MessageSquare className="w-5 h-5 text-green-400" />
              </motion.div>
              Recent Reviews
            </h3>
            <div className="space-y-3">
              {recentReviews.length > 0 ? (                recentReviews.map((review, index) => (
                  <motion.div 
                    key={review.id} 
                    className="p-3 bg-black/30 backdrop-blur-sm rounded-xl border border-white/10 hover:bg-black/40 transition-all duration-300"
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                    whileHover={{ scale: 1.02 }}
                  >
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex items-center gap-2">
                        <div className="text-white text-sm font-medium">{review.userName || 'Anonymous'}</div>
                        <div className="flex items-center">
                          {[...Array(5)].map((_, i) => (
                            <motion.span 
                              key={i} 
                              className={`text-xs ${i < review.rating ? 'text-yellow-400' : 'text-gray-600'}`}
                              whileHover={{ scale: 1.2 }}
                              transition={{ duration: 0.1 }}
                            >
                              ★
                            </motion.span>
                          ))}
                        </div>
                      </div>
                      <motion.button
                        onClick={() => handleDeleteReview(review.id)}
                        className="text-red-400 hover:text-red-300 transition-colors p-1 rounded-lg hover:bg-red-500/10"
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                      >
                        <Trash2 className="w-4 h-4" />
                      </motion.button>
                    </div>
                    <div className="text-gray-300 text-sm">{review.comment}</div>
                  </motion.div>                ))
              ) : (
                <motion.div 
                  className="text-gray-400 text-center py-8 bg-black/10 rounded-xl border border-white/5"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.5 }}
                >
                  <MessageSquare className="w-8 h-8 mx-auto mb-2 opacity-50" />
                  No reviews found
                </motion.div>
              )}
            </div>
          </motion.div>
        </motion.div>        {/* System Information */}
        <motion.div 
          className="mt-8 bg-black/20 backdrop-blur-xl border border-white/20 rounded-2xl p-6 hover:bg-black/30 transition-all duration-300"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.9 }}
          whileHover={{ scale: 1.01 }}
        >
          <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
            <motion.div
              animate={{ rotate: [0, 360] }}
              transition={{ duration: 10, repeat: Infinity, ease: "linear" }}
            >
              <Settings className="w-5 h-5 text-neural-purple" />
            </motion.div>
            System Information
          </h3>
          <div className="grid md:grid-cols-3 gap-4 text-sm">
            <motion.div
              whileHover={{ scale: 1.02 }}
              transition={{ duration: 0.2 }}
            >
              <div className="text-gray-400 mb-2">Project ID</div>
              <div className="text-white font-mono bg-black/30 px-3 py-2 rounded-xl border border-white/10">
                {process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID || 'Not configured'}
              </div>
            </motion.div>
            <motion.div
              whileHover={{ scale: 1.02 }}
              transition={{ duration: 0.2 }}
            >
              <div className="text-gray-400 mb-2">Environment</div>
              <div className="text-white font-mono bg-black/30 px-3 py-2 rounded-xl border border-white/10">
                {process.env.NODE_ENV || 'development'}
              </div>
            </motion.div>
            <motion.div
              whileHover={{ scale: 1.02 }}
              transition={{ duration: 0.2 }}
            >
              <div className="text-gray-400 mb-2">Last Health Check</div>
              <div className="text-white font-mono bg-black/30 px-3 py-2 rounded-xl border border-white/10">
                {systemHealth.lastChecked || 'Never'}
              </div>            </motion.div>
          </div>
        </motion.div>
            </>
          )}

          {/* Review Management Tab */}
          {activeTab === 'reviews' && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <ReviewManagement />
            </motion.div>
          )}

          {/* Analytics Tab */}
          {activeTab === 'analytics' && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <WebsiteAnalytics />
            </motion.div>
          )}

          {/* System Tab */}
          {activeTab === 'system' && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <AdminSystemTest />
            </motion.div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard;

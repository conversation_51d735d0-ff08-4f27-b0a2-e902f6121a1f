import { NextResponse } from 'next/server';
import { collection, addDoc, serverTimestamp } from 'firebase/firestore';
import { firestore } from '@/firebase/config';

export async function POST(request) {
  try {
    const { name, email, message } = await request.json();

    // Validate required fields
    if (!name || !email || !message) {
      return NextResponse.json(
        { error: 'All fields are required' },
        { status: 400 }
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: 'Please enter a valid email address' },
        { status: 400 }
      );
    }

    // Validate message length
    if (message.trim().length < 10) {
      return NextResponse.json(
        { error: 'Message must be at least 10 characters long' },
        { status: 400 }
      );
    }

    // Create contact message data
    const contactData = {
      name: name.trim(),
      email: email.trim().toLowerCase(),
      message: message.trim(),
      createdAt: serverTimestamp(),
      status: 'new', // new, read, replied
      ipAddress: request.headers.get('x-forwarded-for') || 
                 request.headers.get('x-real-ip') || 
                 'unknown',
      userAgent: request.headers.get('user-agent') || 'unknown'
    };

    // Save to Firebase Firestore
    const docRef = await addDoc(collection(firestore, 'contacts'), contactData);

    console.log('Contact form submission saved:', docRef.id);

    // Return success response
    return NextResponse.json({
      success: true,
      message: 'Thank you for your message! We will get back to you soon.',
      id: docRef.id
    });

  } catch (error) {
    console.error('Contact form submission error:', error);
    
    // Return error response
    return NextResponse.json(
      { 
        error: 'Failed to send message. Please try again later.',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    );
  }
}

// GET method to retrieve contact messages (admin only)
export async function GET(request) {
  try {
    // This would typically require admin authentication
    // For now, we'll return a simple response
    return NextResponse.json({
      message: 'Contact API is operational',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Contact API GET error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

import { motion } from 'framer-motion';
import { useState } from 'react';
import { Check, Eye, Download, Palette, Star, Zap, Crown, Award, Globe, MapPin, Building, Users, Briefcase, GraduationCap, Heart, Leaf, Camera, Code, Pen } from 'lucide-react';

const ResumeTemplateSelector = ({
  selectedTemplate = 'ats_optimized',
  onTemplateChange,
  formData,
  resumeData,
  onPreview,
  onDownload
}) => {
  const [previewTemplate, setPreviewTemplate] = useState(null);
  const [activeCategory, setActiveCategory] = useState('all');

  const templates = [
    // Universal Templates
    {
      id: 'ats_optimized',
      name: 'ATS Champion',
      description: 'Maximum ATS compatibility with clean, scannable format',
      icon: <Award className="h-5 w-5" />,
      color: 'from-green-500 to-green-600',
      popular: true,
      atsScore: 100,
      category: 'universal',
      features: ['100% ATS Compatible', 'Single Column', 'Standard Fonts']
    },
    {
      id: 'modern',
      name: 'Professional Standard',
      description: 'Clean, contemporary design optimized for ATS',
      icon: <Zap className="h-5 w-5" />,
      color: 'from-blue-500 to-blue-600',
      popular: true,
      atsScore: 98,
      category: 'universal',
      features: ['ATS-Friendly', 'Clean Layout', 'Professional']
    },
    {
      id: 'executive',
      name: 'Executive Professional',
      description: 'Sophisticated design for senior positions',
      icon: <Crown className="h-5 w-5" />,
      color: 'from-purple-500 to-purple-600',
      atsScore: 96,
      category: 'universal',
      features: ['Executive Style', 'ATS-Optimized', 'Leadership Focus']
    },
    {
      id: 'tech',
      name: 'Tech Professional',
      description: 'Perfect for developers and tech roles',
      icon: <Code className="h-5 w-5" />,
      color: 'from-green-500 to-teal-500',
      atsScore: 97,
      category: 'industry',
      features: ['Tech-Focused', 'Skills Highlight', 'ATS-Safe']
    },
    {
      id: 'minimal',
      name: 'Clean Minimalist',
      description: 'Simple, elegant design focusing on content',
      icon: <Check className="h-5 w-5" />,
      color: 'from-gray-500 to-gray-600',
      atsScore: 99,
      category: 'universal',
      features: ['Ultra Clean', 'Content Focus', 'ATS-Perfect']
    },
    {
      id: 'classic',
      name: 'Traditional Professional',
      description: 'Classic format preferred by recruiters',
      icon: <Palette className="h-5 w-5" />,
      color: 'from-indigo-500 to-blue-600',
      atsScore: 95,
      category: 'universal',
      features: ['Traditional', 'Recruiter Favorite', 'Time-Tested']
    },

    // Regional Templates
    {
      id: 'german_cv',
      name: 'German CV (Lebenslauf)',
      description: 'Traditional German CV format with photo placement',
      icon: <Globe className="h-5 w-5" />,
      color: 'from-red-500 to-yellow-500',
      atsScore: 92,
      category: 'regional',
      region: 'Germany',
      features: ['Photo Section', 'Personal Details', 'German Standards']
    },
    {
      id: 'european_cv',
      name: 'European CV (Europass)',
      description: 'Standard European CV format following Europass guidelines',
      icon: <Star className="h-5 w-5" />,
      color: 'from-blue-600 to-purple-600',
      atsScore: 94,
      category: 'regional',
      region: 'Europe',
      features: ['Europass Format', 'Language Skills', 'EU Standard']
    },
    {
      id: 'uk_cv',
      name: 'UK CV Format',
      description: 'British CV format with personal statement focus',
      icon: <Crown className="h-5 w-5" />,
      color: 'from-blue-700 to-red-600',
      atsScore: 96,
      category: 'regional',
      region: 'United Kingdom',
      features: ['Personal Statement', 'UK Standards', 'A4 Format']
    },
    {
      id: 'usa_resume',
      name: 'USA Resume',
      description: 'American resume format optimized for US job market',
      icon: <Star className="h-5 w-5" />,
      color: 'from-red-600 to-blue-600',
      atsScore: 98,
      category: 'regional',
      region: 'United States',
      features: ['No Photo', 'Skills Focus', 'US Standards']
    },
    {
      id: 'dubai_cv',
      name: 'Dubai/UAE CV',
      description: 'Middle East CV format with photo and personal details',
      icon: <Building className="h-5 w-5" />,
      color: 'from-yellow-500 to-orange-600',
      atsScore: 93,
      category: 'regional',
      region: 'UAE/Middle East',
      features: ['Photo Required', 'Personal Info', 'UAE Format']
    },
    {
      id: 'indian_cv',
      name: 'Indian CV Format',
      description: 'Indian resume format with comprehensive personal details',
      icon: <Heart className="h-5 w-5" />,
      color: 'from-orange-500 to-green-600',
      atsScore: 94,
      category: 'regional',
      region: 'India',
      features: ['Personal Details', 'Photo Optional', 'Indian Standards']
    },
    {
      id: 'canadian_resume',
      name: 'Canadian Resume',
      description: 'Canadian resume format similar to US but with local preferences',
      icon: <Leaf className="h-5 w-5" />,
      color: 'from-red-500 to-red-600',
      atsScore: 97,
      category: 'regional',
      region: 'Canada',
      features: ['Bilingual Ready', 'Canadian Format', 'ATS-Friendly']
    },
    {
      id: 'australian_cv',
      name: 'Australian CV',
      description: 'Australian CV format with local market preferences',
      icon: <Globe className="h-5 w-5" />,
      color: 'from-blue-500 to-green-500',
      atsScore: 95,
      category: 'regional',
      region: 'Australia',
      features: ['Australian Format', 'Skills Focus', 'Local Standards']
    },

    // Industry-Specific Templates
    {
      id: 'creative',
      name: 'Creative Professional',
      description: 'Eye-catching design for creative industries',
      icon: <Palette className="h-5 w-5" />,
      color: 'from-pink-500 to-purple-600',
      atsScore: 88,
      category: 'industry',
      features: ['Creative Design', 'Portfolio Focus', 'Visual Appeal']
    },
    {
      id: 'academic',
      name: 'Academic CV',
      description: 'Comprehensive format for academic and research positions',
      icon: <GraduationCap className="h-5 w-5" />,
      color: 'from-blue-600 to-indigo-700',
      atsScore: 91,
      category: 'industry',
      features: ['Publications', 'Research Focus', 'Academic Format']
    },
    {
      id: 'healthcare',
      name: 'Healthcare Professional',
      description: 'Medical and healthcare industry optimized format',
      icon: <Heart className="h-5 w-5" />,
      color: 'from-red-500 to-pink-600',
      atsScore: 96,
      category: 'industry',
      features: ['Certifications', 'Medical Focus', 'Compliance Ready']
    },
    {
      id: 'finance',
      name: 'Finance & Banking',
      description: 'Conservative format for financial sector',
      icon: <Building className="h-5 w-5" />,
      color: 'from-green-600 to-blue-700',
      atsScore: 97,
      category: 'industry',
      features: ['Conservative', 'Numbers Focus', 'Financial Sector']
    },
    {
      id: 'sales',
      name: 'Sales Professional',
      description: 'Results-focused format highlighting achievements',
      icon: <Users className="h-5 w-5" />,
      color: 'from-orange-500 to-red-600',
      atsScore: 95,
      category: 'industry',
      features: ['Results Focus', 'Achievement Driven', 'Sales Metrics']
    },
    {
      id: 'rural_simple',
      name: 'Rural/Entry Level',
      description: 'Simple, accessible format for rural areas and entry-level positions',
      icon: <Leaf className="h-5 w-5" />,
      color: 'from-green-400 to-green-600',
      atsScore: 98,
      category: 'special',
      features: ['Simple Format', 'Entry-Level Friendly', 'Rural Focused']
    },
    {
      id: 'freelancer',
      name: 'Freelancer/Consultant',
      description: 'Project-focused format for independent professionals',
      icon: <Briefcase className="h-5 w-5" />,
      color: 'from-purple-500 to-pink-600',
      atsScore: 93,
      category: 'special',
      features: ['Project Focus', 'Client Work', 'Freelance Ready']
    }
  ];

  const categories = [
    { id: 'all', label: 'All Templates', icon: <Palette className="h-4 w-4" /> },
    { id: 'universal', label: 'Universal', icon: <Globe className="h-4 w-4" /> },
    { id: 'regional', label: 'Regional', icon: <MapPin className="h-4 w-4" /> },
    { id: 'industry', label: 'Industry-Specific', icon: <Briefcase className="h-4 w-4" /> },
    { id: 'special', label: 'Special Purpose', icon: <Star className="h-4 w-4" /> }
  ];

  const filteredTemplates = activeCategory === 'all'
    ? templates
    : templates.filter(template => template.category === activeCategory);

  const formatResumeWithTemplate = (template, formData, resumeData) => {
    const { personal } = formData;
    const enhanced = resumeData?.enhancedContent || {};

    // ATS-Optimized Template Styles - Based on 2024/2025 Best Practices
    const templateStyles = {
      ats_optimized: {
        headerBg: '#ffffff',
        accentColor: '#000000',
        fontFamily: "'Arial', 'Helvetica', sans-serif",
        sectionBorder: 'none',
        sectionUnderline: '1px solid #000000',
        headerTextColor: '#000000',
        bodyTextColor: '#000000'
      },
      modern: {
        headerBg: '#ffffff',
        accentColor: '#2563eb',
        fontFamily: "'Arial', 'Helvetica', sans-serif",
        sectionBorder: 'none',
        sectionUnderline: '2px solid #2563eb',
        headerTextColor: '#1f2937',
        bodyTextColor: '#374151'
      },
      executive: {
        headerBg: '#ffffff',
        accentColor: '#1e40af',
        fontFamily: "'Calibri', 'Arial', sans-serif",
        sectionBorder: 'none',
        sectionUnderline: '2px solid #1e40af',
        headerTextColor: '#111827',
        bodyTextColor: '#374151'
      },
      minimal: {
        headerBg: '#ffffff',
        accentColor: '#374151',
        fontFamily: "'Arial', 'Helvetica', sans-serif",
        sectionBorder: 'none',
        sectionUnderline: '1px solid #374151',
        headerTextColor: '#111827',
        bodyTextColor: '#374151'
      },
      tech: {
        headerBg: '#ffffff',
        accentColor: '#059669',
        fontFamily: "'Arial', 'Helvetica', sans-serif",
        sectionBorder: 'none',
        sectionUnderline: '2px solid #059669',
        headerTextColor: '#065f46',
        bodyTextColor: '#374151'
      },
      classic: {
        headerBg: '#ffffff',
        accentColor: '#1f2937',
        fontFamily: "'Times New Roman', serif",
        sectionBorder: 'none',
        sectionUnderline: '1px solid #1f2937',
        headerTextColor: '#111827',
        bodyTextColor: '#374151'
      }
    };

    const style = templateStyles[template] || templateStyles.ats_optimized;

    return `
      <div style="font-family: ${style.fontFamily}; color: ${style.bodyTextColor}; line-height: 1.4; max-width: 8.5in; margin: 0 auto; background: white; padding: 0.5in; font-size: 11pt;">
        
        <!-- Header Section - ATS Optimized -->
        <div style="text-align: center; margin-bottom: 20px; border-bottom: 1px solid #e5e7eb; padding-bottom: 15px;">
          <h1 style="margin: 0 0 8px 0; font-size: 18pt; font-weight: bold; color: ${style.headerTextColor}; text-transform: uppercase;">
            ${personal.firstName || 'FIRST'} ${personal.lastName || 'LAST'}
          </h1>
          <div style="margin: 8px 0; color: ${style.bodyTextColor}; font-size: 10pt;">
            ${personal.email ? `${personal.email}` : ''}${personal.phone ? ` | ${personal.phone}` : ''}${personal.location ? ` | ${personal.location}` : ''}
          </div>
          ${personal.linkedin ? `<div style="margin: 4px 0; color: ${style.accentColor}; font-size: 10pt;">${personal.linkedin}</div>` : ''}
        </div>

        <!-- Professional Summary -->
        ${enhanced.professionalSummary ? `
          <div style="margin-bottom: 18px;">
            <h2 style="font-size: 12pt; font-weight: bold; color: ${style.headerTextColor}; margin: 0 0 8px 0; text-transform: uppercase; border-bottom: ${style.sectionUnderline}; padding-bottom: 2px;">
              PROFESSIONAL SUMMARY
            </h2>
            <p style="margin: 0; font-size: 11pt; line-height: 1.4; text-align: justify;">
              ${enhanced.professionalSummary}
            </p>
          </div>
        ` : ''}

        <!-- Core Competencies / Skills -->
        ${enhanced.skills && enhanced.skills.technical?.length > 0 ? `
          <div style="margin-bottom: 18px;">
            <h2 style="font-size: 12pt; font-weight: bold; color: ${style.headerTextColor}; margin: 0 0 8px 0; text-transform: uppercase; border-bottom: ${style.sectionUnderline}; padding-bottom: 2px;">
              CORE COMPETENCIES
            </h2>
            <p style="margin: 0; font-size: 11pt; line-height: 1.4;">
              ${enhanced.skills.technical.join(' • ')}
            </p>
          </div>
        ` : ''}

        <!-- Professional Experience -->
        ${enhanced.experience && enhanced.experience.length > 0 ? `
          <div style="margin-bottom: 18px;">
            <h2 style="font-size: 12pt; font-weight: bold; color: ${style.headerTextColor}; margin: 0 0 8px 0; text-transform: uppercase; border-bottom: ${style.sectionUnderline}; padding-bottom: 2px;">
              PROFESSIONAL EXPERIENCE
            </h2>
            ${enhanced.experience.map(exp => `
              <div style="margin-bottom: 14px;">
                <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 4px;">
                  <div style="flex: 1;">
                    <h3 style="margin: 0; font-size: 11pt; font-weight: bold; color: ${style.headerTextColor};">${exp.title}</h3>
                    <p style="margin: 2px 0; font-size: 11pt; color: ${style.accentColor}; font-weight: 600;">${exp.company}</p>
                    ${exp.location ? `<p style="margin: 1px 0; font-size: 10pt; color: ${style.bodyTextColor};">${exp.location}</p>` : ''}
                  </div>
                  <div style="text-align: right; font-size: 10pt; color: ${style.bodyTextColor}; font-weight: 500;">
                    ${exp.startDate} - ${exp.endDate}
                  </div>
                </div>
                ${exp.achievements && exp.achievements.length > 0 ? `
                  <ul style="margin: 6px 0 0 16px; padding: 0; list-style-type: disc;">
                    ${exp.achievements.map(achievement => `
                      <li style="margin-bottom: 3px; font-size: 10pt; line-height: 1.3;">${achievement}</li>
                    `).join('')}
                  </ul>
                ` : ''}
              </div>
            `).join('')}
          </div>
        ` : ''}

        <!-- Education -->
        ${enhanced.education && enhanced.education.length > 0 ? `
          <div style="margin-bottom: 18px;">
            <h2 style="font-size: 12pt; font-weight: bold; color: ${style.headerTextColor}; margin: 0 0 8px 0; text-transform: uppercase; border-bottom: ${style.sectionUnderline}; padding-bottom: 2px;">
              EDUCATION
            </h2>
            ${enhanced.education.map(edu => `
              <div style="margin-bottom: 8px;">
                <div style="display: flex; justify-content: space-between; align-items: flex-start;">
                  <div style="flex: 1;">
                    <h3 style="margin: 0; font-size: 11pt; font-weight: bold; color: ${style.headerTextColor};">${edu.degree}</h3>
                    <p style="margin: 2px 0; font-size: 10pt; color: ${style.accentColor};">${edu.institution}</p>
                    ${edu.location ? `<p style="margin: 1px 0; font-size: 10pt; color: ${style.bodyTextColor};">${edu.location}</p>` : ''}
                  </div>
                  <div style="text-align: right; font-size: 10pt; color: ${style.bodyTextColor}; font-weight: 500;">
                    ${edu.startDate} - ${edu.endDate}
                  </div>
                </div>
                ${edu.gpa ? `<p style="margin: 3px 0; font-size: 10pt; color: ${style.bodyTextColor};">GPA: ${edu.gpa}</p>` : ''}
              </div>
            `).join('')}
          </div>
        ` : ''}

        <!-- Additional Skills/Certifications -->
        ${enhanced.skills && (enhanced.skills.languages?.length > 0 || enhanced.skills.certifications?.length > 0) ? `
          <div style="margin-bottom: 18px;">
            ${enhanced.skills.languages?.length > 0 ? `
              <div style="margin-bottom: 8px;">
                <h3 style="font-size: 11pt; font-weight: bold; color: ${style.headerTextColor}; margin: 0 0 4px 0;">LANGUAGES:</h3>
                <p style="margin: 0; font-size: 10pt;">${enhanced.skills.languages.join(' • ')}</p>
              </div>
            ` : ''}
            ${enhanced.skills.certifications?.length > 0 ? `
              <div style="margin-bottom: 8px;">
                <h3 style="font-size: 11pt; font-weight: bold; color: ${style.headerTextColor}; margin: 0 0 4px 0;">CERTIFICATIONS:</h3>
                <p style="margin: 0; font-size: 10pt;">${enhanced.skills.certifications.join(' • ')}</p>
              </div>
            ` : ''}
          </div>
        ` : ''}

        <!-- Key Projects (if space allows) -->
        ${enhanced.projects && enhanced.projects.length > 0 && enhanced.projects.length <= 2 ? `
          <div style="margin-bottom: 12px;">
            <h2 style="font-size: 12pt; font-weight: bold; color: ${style.headerTextColor}; margin: 0 0 8px 0; text-transform: uppercase; border-bottom: ${style.sectionUnderline}; padding-bottom: 2px;">
              KEY PROJECTS
            </h2>
            ${enhanced.projects.slice(0, 2).map(project => `
              <div style="margin-bottom: 6px;">
                <h3 style="margin: 0 0 2px 0; font-size: 11pt; font-weight: bold; color: ${style.headerTextColor};">${project.name}</h3>
                <p style="margin: 0; font-size: 10pt; line-height: 1.3;">${project.description}</p>
                ${project.technologies ? `
                  <p style="margin: 2px 0 0 0; font-size: 9pt; color: ${style.bodyTextColor}; font-style: italic;">Technologies: ${project.technologies}</p>
                ` : ''}
              </div>
            `).join('')}
          </div>
        ` : ''}

        <!-- Footer Note -->
        <div style="margin-top: 20px; text-align: center; font-size: 8pt; color: #94a3b8; border-top: 1px solid #e5e7eb; padding-top: 8px;">
          <p style="margin: 0;">Generated with AI-Enhanced Resume Builder • ATS-Optimized Format</p>
        </div>
      </div>
    `;
  };

  const handlePreview = (templateId) => {
    const template = templates.find(t => t.id === templateId);
    const formattedResume = formatResumeWithTemplate(templateId, formData, resumeData);
    
    setPreviewTemplate({
      id: templateId,
      name: template.name,
      content: formattedResume
    });
  };

  const handleDownloadWithTemplate = async (templateId) => {
    try {
      // Create a temporary div with the resume content
      const tempDiv = document.createElement('div');
      tempDiv.style.position = 'absolute';
      tempDiv.style.left = '-9999px';
      tempDiv.style.width = '210mm'; // A4 width
      tempDiv.style.backgroundColor = 'white';
      tempDiv.style.color = 'black';
      tempDiv.style.padding = '20mm';
      tempDiv.style.fontSize = '12px';
      tempDiv.style.lineHeight = '1.5';

      // Format the resume content with selected template
      const resumeContent = formatResumeWithTemplate(templateId, formData, resumeData);
      tempDiv.innerHTML = resumeContent;

      document.body.appendChild(tempDiv);

      // Import required libraries
      const html2canvas = (await import('html2canvas')).default;
      const jsPDF = (await import('jspdf')).default;

      // Generate PDF using html2canvas and jsPDF
      const canvas = await html2canvas(tempDiv, {
        scale: 2,
        useCORS: true,
        backgroundColor: '#ffffff'
      });

      const imgData = canvas.toDataURL('image/png');
      const pdf = new jsPDF('p', 'mm', 'a4');

      const imgWidth = 210; // A4 width in mm
      const pageHeight = 295; // A4 height in mm
      const imgHeight = (canvas.height * imgWidth) / canvas.width;

      // Ensure single page format
      if (imgHeight > pageHeight) {
        const scaledHeight = pageHeight;
        const scaledWidth = (canvas.width * pageHeight) / canvas.height;
        pdf.addImage(imgData, 'PNG', (210 - scaledWidth) / 2, 0, scaledWidth, scaledHeight);
      } else {
        pdf.addImage(imgData, 'PNG', 0, 0, imgWidth, imgHeight);
      }

      // Download the PDF
      const template = templates.find(t => t.id === templateId);
      const fileName = `${formData.personal.firstName}_${formData.personal.lastName}_Resume_${template.name.replace(/\s+/g, '_')}.pdf`;
      pdf.save(fileName);

      // Clean up
      document.body.removeChild(tempDiv);

    } catch (error) {
      console.error('Error generating PDF:', error);
      alert('Error generating PDF. Please try again.');
    }
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-neural-purple to-neural-pink mb-4">
          ATS-Optimized Resume Templates
        </h2>
        <p className="text-gray-300 max-w-2xl mx-auto">
          Select from our professionally designed, ATS-optimized templates. All templates are designed for maximum ATS compatibility and one-page format.
        </p>
        <div className="mt-4 p-4 bg-green-500/10 border border-green-500/20 rounded-lg max-w-xl mx-auto">
          <p className="text-green-400 text-sm">
            ✅ All templates updated with 2024/2025 ATS best practices for top ranking
          </p>
        </div>
      </div>

      {/* Category Tabs */}
      <div className="flex flex-wrap gap-2 mb-8 p-1 bg-gray-900/40 rounded-xl border border-white/10">
        {categories.map((category) => (
          <button
            key={category.id}
            onClick={() => setActiveCategory(category.id)}
            className={`flex items-center gap-2 px-4 py-3 rounded-lg text-sm font-medium transition-all duration-300 ${
              activeCategory === category.id
                ? 'bg-neural-purple text-white shadow-lg'
                : 'text-gray-300 hover:bg-white/5 hover:text-white'
            }`}
          >
            {category.icon}
            {category.label}
            <span className="ml-1 bg-white/20 text-xs px-2 py-1 rounded-full">
              {category.id === 'all' ? templates.length : templates.filter(t => t.category === category.id).length}
            </span>
          </button>
        ))}
      </div>

      {/* Template Count */}
      <div className="mb-6 text-center">
        <p className="text-gray-400 text-sm">
          Showing {filteredTemplates.length} template{filteredTemplates.length !== 1 ? 's' : ''}
          {activeCategory !== 'all' && ` in ${categories.find(c => c.id === activeCategory)?.label}`}
        </p>
      </div>

      {/* Template Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredTemplates.map((template) => (
          <motion.div
            key={template.id}
            className={`relative bg-gray-900/50 backdrop-blur-md rounded-xl p-6 border transition-all cursor-pointer ${
              selectedTemplate === template.id 
                ? 'border-neural-purple shadow-lg shadow-neural-purple/20' 
                : 'border-white/10 hover:border-neural-purple/30'
            }`}
            whileHover={{ y: -5 }}
            onClick={() => onTemplateChange?.(template.id)}
          >
            {/* Template Header */}
            <div className="flex items-start justify-between mb-4">
              <div className={`p-3 rounded-lg bg-gradient-to-r ${template.color}`}>
                {template.icon}
              </div>
              <div className="flex gap-2">
                {template.popular && (
                  <span className="px-2 py-1 bg-green-500/20 text-green-400 text-xs rounded-full border border-green-500/30">
                    Most Popular
                  </span>
                )}
                {template.atsScore === 100 && (
                  <span className="px-2 py-1 bg-yellow-500/20 text-yellow-400 text-xs rounded-full border border-yellow-500/30">
                    Perfect Score
                  </span>
                )}
              </div>
            </div>

            {/* Template Info */}
            <div className="mb-4">
              <div className="flex items-center gap-2 mb-2">
                <h3 className="text-lg font-semibold text-white">{template.name}</h3>
                {template.region && (
                  <span className="px-2 py-1 bg-blue-500/20 text-blue-400 text-xs rounded-full border border-blue-500/30">
                    {template.region}
                  </span>
                )}
              </div>
              <p className="text-gray-400 text-sm mb-3">{template.description}</p>

              {/* Category Badge */}
              <div className="flex items-center gap-2 mb-3">
                <span className={`px-2 py-1 text-xs rounded-full border ${
                  template.category === 'universal' ? 'bg-green-500/20 text-green-400 border-green-500/30' :
                  template.category === 'regional' ? 'bg-blue-500/20 text-blue-400 border-blue-500/30' :
                  template.category === 'industry' ? 'bg-purple-500/20 text-purple-400 border-purple-500/30' :
                  'bg-orange-500/20 text-orange-400 border-orange-500/30'
                }`}>
                  {template.category === 'universal' ? 'Universal' :
                   template.category === 'regional' ? 'Regional' :
                   template.category === 'industry' ? 'Industry' : 'Special'}
                </span>
              </div>
              
              {/* ATS Score */}
              <div className="flex items-center gap-2 mb-3">
                <div className="flex items-center gap-1">
                  <Star className="h-4 w-4 text-yellow-400" />
                  <span className="text-sm font-semibold text-white">{template.atsScore}%</span>
                </div>
                <span className="text-xs text-gray-400">ATS Score</span>
                {template.atsScore >= 98 && (
                  <span className="text-xs text-green-400 font-semibold">Excellent</span>
                )}
              </div>

              {/* Features */}
              <div className="flex flex-wrap gap-1">
                {template.features.map((feature, index) => (
                  <span key={index} className="px-2 py-1 bg-gray-800 text-gray-300 text-xs rounded">
                    {feature}
                  </span>
                ))}
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex gap-2">
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  handlePreview(template.id);
                }}
                className="flex-1 flex items-center justify-center gap-2 px-3 py-2 bg-gray-800 hover:bg-gray-700 text-white text-sm rounded-lg transition-colors"
              >
                <Eye className="h-4 w-4" />
                Preview
              </button>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  handleDownloadWithTemplate(template.id);
                }}
                className="flex-1 flex items-center justify-center gap-2 px-3 py-2 bg-gradient-to-r from-neural-purple to-neural-pink text-white text-sm rounded-lg hover:opacity-90 transition-opacity"
              >
                <Download className="h-4 w-4" />
                Download
              </button>
            </div>

            {/* Selected Indicator */}
            {selectedTemplate === template.id && (
              <motion.div
                className="absolute -top-2 -right-2 bg-neural-purple rounded-full p-1"
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
              >
                <Check className="h-4 w-4 text-white" />
              </motion.div>
            )}
          </motion.div>
        ))}
      </div>

      {/* ATS Tips Section */}
      <div className="bg-gray-900/30 backdrop-blur-md rounded-xl p-6 border border-white/10">
        <h3 className="text-xl font-semibold text-white mb-4 flex items-center gap-2">
          <Award className="h-5 w-5 text-green-400" />
          ATS Optimization Features
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-3">
            <div className="flex items-start gap-3">
              <div className="w-2 h-2 bg-green-400 rounded-full mt-2"></div>
              <div>
                <h4 className="text-sm font-semibold text-white">Standard Fonts</h4>
                <p className="text-xs text-gray-400">Arial, Calibri, and Times New Roman for maximum compatibility</p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <div className="w-2 h-2 bg-green-400 rounded-full mt-2"></div>
              <div>
                <h4 className="text-sm font-semibold text-white">Single Column Layout</h4>
                <p className="text-xs text-gray-400">Clean, linear format that ATS systems can easily parse</p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <div className="w-2 h-2 bg-green-400 rounded-full mt-2"></div>
              <div>
                <h4 className="text-sm font-semibold text-white">Standard Section Headers</h4>
                <p className="text-xs text-gray-400">Professional Summary, Experience, Education, Skills</p>
              </div>
            </div>
          </div>
          <div className="space-y-3">
            <div className="flex items-start gap-3">
              <div className="w-2 h-2 bg-green-400 rounded-full mt-2"></div>
              <div>
                <h4 className="text-sm font-semibold text-white">One Page Format</h4>
                <p className="text-xs text-gray-400">Optimized for single page to meet modern recruiting standards</p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <div className="w-2 h-2 bg-green-400 rounded-full mt-2"></div>
              <div>
                <h4 className="text-sm font-semibold text-white">Keyword Optimization</h4>
                <p className="text-xs text-gray-400">Built-in keyword placement for better ATS ranking</p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <div className="w-2 h-2 bg-green-400 rounded-full mt-2"></div>
              <div>
                <h4 className="text-sm font-semibold text-white">No Graphics/Tables</h4>
                <p className="text-xs text-gray-400">Pure text format that won't confuse ATS parsers</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Template Preview Modal */}
      {previewTemplate && (
        <motion.div
          className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          onClick={() => setPreviewTemplate(null)}
        >
          <motion.div
            className="bg-white rounded-lg max-w-4xl max-h-[90vh] overflow-hidden"
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            onClick={(e) => e.stopPropagation()}
          >
            {/* Preview Header */}
            <div className="bg-gray-900 text-white p-4 flex items-center justify-between">
              <h3 className="text-lg font-semibold">{previewTemplate.name} Template Preview</h3>
              <button
                onClick={() => setPreviewTemplate(null)}
                className="text-gray-400 hover:text-white transition-colors"
              >
                ✕
              </button>
            </div>
            
            {/* Preview Content */}
            <div className="p-8 overflow-y-auto max-h-[calc(90vh-80px)]">
              <div dangerouslySetInnerHTML={{ __html: previewTemplate.content }} />
            </div>
          </motion.div>
        </motion.div>
      )}
    </div>
  );
};

export default ResumeTemplateSelector;

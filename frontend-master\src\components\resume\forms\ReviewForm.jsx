'use client';
import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  FileText, CheckCircle, AlertCircle, Eye, Target, Sparkles, Award, User, GraduationCap, Briefcase, Code, Folder, Globe, Calendar, MapPin, Building, Mail, Phone, Link, Wand2, Save
} from 'lucide-react';
import SmartForm<PERSON>ield from './SmartFormField';
import TemplateSelector from '../TemplateSelector';

export const EnhancedReviewForm = ({
  formData,
  updateFormData,
  atsAnalysis,
  onSave,
  onAISuggest,
  selectedTemplate,
  onTemplateSelect
}) => {
  const [showTemplateSelector, setShowTemplateSelector] = useState(false);
  const [jobDescription, setJobDescription] = useState(formData.jobDescription || '');

  // Update job description in parent state
  const handleJobDescriptionChange = (e) => {
    setJobDescription(e.target.value);
    updateFormData('jobDescription', '', e.target.value);
  };

  return (
    <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} exit={{ opacity: 0, y: -20 }} className="space-y-8">
      {/* Review Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-neural-purple/20 rounded-xl flex items-center justify-center">
            <FileText className="h-5 w-5 text-neural-purple" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-white">Review & Generate</h2>
            <p className="text-gray-400 text-sm">Review your information and generate your professional resume</p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <button onClick={onSave} className="flex items-center gap-2 px-3 py-2 bg-gray-800 hover:bg-gray-700 border border-gray-600 rounded-lg transition-colors text-sm">
            <Save className="h-4 w-4" /> Save Draft
          </button>
        </div>
      </div>

      {/* Template Selector */}
      <div className="mb-4">
        <button onClick={() => setShowTemplateSelector(true)} className="px-4 py-2 bg-gradient-to-r from-neural-purple to-neural-pink text-white rounded-lg font-medium transition-all duration-300 shadow-lg hover:shadow-xl">
          Choose Template
        </button>
        <span className="ml-4 text-sm text-gray-400">Selected: <span className="text-white font-medium">{selectedTemplate}</span></span>
      </div>
      {showTemplateSelector && (
        <TemplateSelector
          selectedTemplate={selectedTemplate}
          onTemplateSelect={(templateId) => {
            onTemplateSelect(templateId);
            setShowTemplateSelector(false);
          }}
          onClose={() => setShowTemplateSelector(false)}
        />
      )}

      {/* Job Description Input */}
      <div className="bg-gradient-to-r from-neural-purple/10 to-neural-pink/10 border border-neural-purple/30 rounded-xl p-6">
        <h3 className="text-lg font-semibold mb-2 flex items-center gap-2">
          <Target className="h-5 w-5 text-neural-purple" />
          Target Job Description (Optional)
        </h3>
        <p className="text-gray-300 text-sm mb-3">Paste a job description to optimize your resume for a specific role. The AI will tailor your summary, skills, and experience accordingly.</p>
        <textarea
          value={jobDescription}
          onChange={handleJobDescriptionChange}
          rows={6}
          className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400 resize-none mb-2"
          placeholder="Paste the job description here for AI-powered resume optimization..."
        />
        <div className="text-xs text-gray-400">💡 Adding a job description helps our AI tailor your resume to match specific requirements and keywords.</div>
      </div>

      {/* User Data Summary */}
      <div className="bg-gray-900/40 backdrop-blur-sm rounded-2xl p-6 border border-white/10">
        <h3 className="text-xl font-semibold text-white mb-4">Your Resume Information</h3>
        <div className="space-y-4">
          <div>
            <h4 className="font-semibold text-neural-purple mb-2 flex items-center gap-2"><User className="h-4 w-4" /> Personal Info</h4>
            <div className="text-sm text-gray-300">{formData.personal.firstName} {formData.personal.lastName} | {formData.personal.email} | {formData.personal.phone}</div>
            <div className="text-sm text-gray-400">{formData.personal.location} | {formData.personal.linkedin} | {formData.personal.portfolio}</div>
            <div className="mt-2 text-gray-200 text-sm">{formData.personal.summary}</div>
          </div>
          <div>
            <h4 className="font-semibold text-neural-purple mb-2 flex items-center gap-2"><GraduationCap className="h-4 w-4" /> Education</h4>
            {formData.education.map((edu, i) => (
              <div key={edu.id} className="mb-1 text-sm text-gray-300">
                {edu.degree} - {edu.institution} ({edu.startDate} - {edu.endDate}) | {edu.location}
                {edu.gpa && <span className="ml-2 text-xs text-gray-400">GPA: {edu.gpa}</span>}
                {edu.relevant && <div className="text-xs text-gray-400">{edu.relevant}</div>}
              </div>
            ))}
          </div>
          <div>
            <h4 className="font-semibold text-neural-purple mb-2 flex items-center gap-2"><Briefcase className="h-4 w-4" /> Experience</h4>
            {formData.experience.map((exp, i) => (
              <div key={exp.id} className="mb-1 text-sm text-gray-300">
                {exp.title} at {exp.company} ({exp.startDate} - {exp.current ? 'Present' : exp.endDate}) | {exp.location}
                <div className="text-xs text-gray-400 whitespace-pre-line">{exp.description}</div>
              </div>
            ))}
          </div>
          <div>
            <h4 className="font-semibold text-neural-purple mb-2 flex items-center gap-2"><Award className="h-4 w-4" /> Skills & Projects</h4>
            <div className="text-xs text-gray-400 mb-1">Technical: {formData.skills.technical.join(', ')}</div>
            <div className="text-xs text-gray-400 mb-1">Languages: {formData.skills.languages.join(', ')}</div>
            <div className="text-xs text-gray-400 mb-1">Certifications: {formData.skills.certifications.join(', ')}</div>
            <div className="mt-2">
              {formData.projects.map((proj, i) => (
                <div key={proj.id} className="mb-1 text-sm text-gray-300">
                  {proj.name} ({proj.technologies}) <span className="text-xs text-gray-400">{proj.link}</span>
                  <div className="text-xs text-gray-400 whitespace-pre-line">{proj.description}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

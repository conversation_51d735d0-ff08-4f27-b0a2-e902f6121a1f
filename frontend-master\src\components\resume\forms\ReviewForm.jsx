'use client';
import { useState, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  FileText, CheckCircle, AlertCircle, Eye, Target, Sparkles, Award, User, GraduationCap,
  Briefcase, Code, Folder, Globe, Calendar, MapPin, Building, Mail, Phone, Link, Wand2,
  Save, AlertTriangle, ArrowRight, Info, Download, Edit3, Zap, TrendingUp, Shield,
  Clock, Star, Crown, Palette
} from 'lucide-react';
import EnhancedFormField from './EnhancedFormField';
import EnhancedTemplateSelector from '../EnhancedTemplateSelector';

export const EnhancedReviewForm = ({
  formData,
  updateFormData,
  atsAnalysis,
  onSave,
  onAISuggest,
  selectedTemplate,
  onTemplateSelect
}) => {
  const [showTemplateSelector, setShowTemplateSelector] = useState(false);
  const [jobDescription, setJobDescription] = useState(formData.jobDescription || '');
  const [activeTab, setActiveTab] = useState('overview'); // 'overview', 'content', 'optimization'
  const [isGenerating, setIsGenerating] = useState(false);
  const reviewRef = useRef(null);

  // Update job description in parent state
  const handleJobDescriptionChange = (e) => {
    setJobDescription(e.target.value);
    updateFormData('jobDescription', '', e.target.value);
  };

  // Calculate completion score
  const getCompletionScore = () => {
    let score = 0;
    let maxScore = 0;

    // Personal info (25 points)
    maxScore += 25;
    if (formData.personal.firstName && formData.personal.lastName) score += 10;
    if (formData.personal.email && formData.personal.phone) score += 10;
    if (formData.personal.summary && formData.personal.summary.length >= 100) score += 5;

    // Education (20 points)
    maxScore += 20;
    if (formData.education.some(edu => edu.degree && edu.institution)) score += 20;

    // Experience (35 points)
    maxScore += 35;
    if (formData.experience.length > 0) score += 15;
    if (formData.experience.some(exp => exp.description && exp.description.length >= 50)) score += 20;

    // Skills (20 points)
    maxScore += 20;
    if (formData.skills.technical && formData.skills.technical.length > 0) score += 10;
    if (formData.skills.languages && formData.skills.languages.length > 0) score += 5;
    if (formData.skills.certifications && formData.skills.certifications.length > 0) score += 5;

    return Math.round((score / maxScore) * 100);
  };

  const completionScore = getCompletionScore();

  // Render Overview Tab
  const renderOverviewTab = () => (
    <div className="space-y-6">
      {/* Resume Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-gray-800/50 backdrop-blur-sm rounded-xl p-4 border border-gray-700">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-blue-500/20 rounded-lg flex items-center justify-center">
              <User className="h-5 w-5 text-blue-400" />
            </div>
            <div>
              <div className="text-2xl font-bold text-white">
                {formData.experience.length}
              </div>
              <div className="text-gray-400 text-sm">Work Experiences</div>
            </div>
          </div>
        </div>

        <div className="bg-gray-800/50 backdrop-blur-sm rounded-xl p-4 border border-gray-700">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-green-500/20 rounded-lg flex items-center justify-center">
              <GraduationCap className="h-5 w-5 text-green-400" />
            </div>
            <div>
              <div className="text-2xl font-bold text-white">
                {formData.education.length}
              </div>
              <div className="text-gray-400 text-sm">Education Entries</div>
            </div>
          </div>
        </div>

        <div className="bg-gray-800/50 backdrop-blur-sm rounded-xl p-4 border border-gray-700">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-purple-500/20 rounded-lg flex items-center justify-center">
              <Award className="h-5 w-5 text-purple-400" />
            </div>
            <div>
              <div className="text-2xl font-bold text-white">
                {(formData.skills.technical?.length || 0) + (formData.skills.languages?.length || 0)}
              </div>
              <div className="text-gray-400 text-sm">Skills Listed</div>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Summary */}
      <div className="bg-gray-800/50 backdrop-blur-sm rounded-xl p-6 border border-gray-700">
        <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
          <Eye className="h-5 w-5 text-neural-purple" />
          Resume Summary
        </h3>

        <div className="space-y-4">
          <div className="flex items-start gap-3">
            <User className="h-5 w-5 text-gray-400 mt-0.5" />
            <div>
              <div className="font-medium text-white">
                {formData.personal.firstName} {formData.personal.lastName}
              </div>
              <div className="text-gray-400 text-sm">
                {formData.personal.email} • {formData.personal.phone}
              </div>
              {formData.personal.location && (
                <div className="text-gray-400 text-sm">{formData.personal.location}</div>
              )}
            </div>
          </div>

          {formData.personal.summary && (
            <div className="bg-gray-700/30 rounded-lg p-4">
              <div className="text-gray-300 text-sm leading-relaxed">
                {formData.personal.summary}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Generate Resume Section */}
      <div className="bg-gradient-to-r from-neural-purple/10 to-neural-pink/10 border border-neural-purple/30 rounded-xl p-6">
        <div className="flex items-center gap-3 mb-4">
          <div className="w-12 h-12 bg-gradient-to-r from-neural-purple to-neural-pink rounded-xl flex items-center justify-center">
            <Sparkles className="h-6 w-6 text-white" />
          </div>
          <div>
            <h3 className="text-xl font-semibold text-white">Ready to Generate?</h3>
            <p className="text-gray-300 text-sm">Your resume is {completionScore}% complete</p>
          </div>
        </div>

        <div className="flex flex-col sm:flex-row gap-4">
          <button
            onClick={() => {
              setIsGenerating(true);
              // Simulate generation process
              setTimeout(() => {
                setIsGenerating(false);
                if (typeof window !== 'undefined') {
                  const event = new CustomEvent('generateResume');
                  window.dispatchEvent(event);
                }
              }, 2000);
            }}
            disabled={completionScore < 70 || isGenerating}
            className="flex-1 flex items-center justify-center gap-2 px-6 py-3 bg-gradient-to-r from-neural-purple to-neural-pink text-white font-semibold rounded-lg hover:opacity-90 transition-opacity shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isGenerating ? (
              <>
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                >
                  <Sparkles className="h-5 w-5" />
                </motion.div>
                Generating...
              </>
            ) : (
              <>
                <Download className="h-5 w-5" />
                Generate Resume
                <ArrowRight className="h-4 w-4" />
              </>
            )}
          </button>
        </div>

        {completionScore < 70 && (
          <div className="mt-4 p-3 bg-yellow-500/10 border border-yellow-500/20 rounded-lg">
            <p className="text-yellow-300 text-sm flex items-start gap-2">
              <AlertTriangle className="h-4 w-4 mt-0.5 flex-shrink-0" />
              <span>
                Complete at least 70% of your resume to generate. Add more details to your experience and education sections.
              </span>
            </p>
          </div>
        )}
      </div>
    </div>
  );

  // Render Content Review Tab
  const renderContentTab = () => (
    <div className="space-y-6">
      {/* Content Requirements Check */}
      <div className="bg-gray-800/50 backdrop-blur-sm rounded-xl p-6 border border-gray-700">
        <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
          <CheckCircle className="h-5 w-5 text-green-400" />
          Content Requirements
        </h3>

        <div className="space-y-3">
          {[
            {
              label: 'Personal Information',
              check: formData.personal.firstName && formData.personal.lastName && formData.personal.email,
              details: 'Name and contact information'
            },
            {
              label: 'Professional Summary',
              check: formData.personal.summary && formData.personal.summary.length >= 100,
              details: `${formData.personal.summary?.length || 0}/100 characters minimum`
            },
            {
              label: 'Work Experience',
              check: formData.experience.some(exp => exp.title && exp.company && exp.description?.length >= 50),
              details: `${formData.experience.length} entries, descriptions 50+ chars each`
            },
            {
              label: 'Education',
              check: formData.education.some(edu => edu.degree && edu.institution),
              details: `${formData.education.length} education entries`
            },
            {
              label: 'Skills',
              check: (formData.skills.technical?.length || 0) >= 3,
              details: `${formData.skills.technical?.length || 0} technical skills listed`
            }
          ].map((item, index) => (
            <div key={index} className="flex items-center justify-between p-3 bg-gray-700/30 rounded-lg">
              <div className="flex items-center gap-3">
                {item.check ? (
                  <CheckCircle className="h-5 w-5 text-green-400" />
                ) : (
                  <AlertTriangle className="h-5 w-5 text-yellow-400" />
                )}
                <div>
                  <div className={`font-medium ${item.check ? 'text-green-400' : 'text-yellow-400'}`}>
                    {item.label}
                  </div>
                  <div className="text-gray-400 text-sm">{item.details}</div>
                </div>
              </div>
              <div className={`px-2 py-1 rounded text-xs font-medium ${
                item.check ? 'bg-green-500/20 text-green-400' : 'bg-yellow-500/20 text-yellow-400'
              }`}>
                {item.check ? 'Complete' : 'Needs Work'}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Detailed Content Review */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Personal Information */}
        <div className="bg-gray-800/50 backdrop-blur-sm rounded-xl p-6 border border-gray-700">
          <h4 className="font-semibold text-neural-purple mb-3 flex items-center gap-2">
            <User className="h-4 w-4" />
            Personal Information
          </h4>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-400">Name:</span>
              <span className="text-white">{formData.personal.firstName} {formData.personal.lastName}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Email:</span>
              <span className="text-white">{formData.personal.email}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Phone:</span>
              <span className="text-white">{formData.personal.phone}</span>
            </div>
            {formData.personal.location && (
              <div className="flex justify-between">
                <span className="text-gray-400">Location:</span>
                <span className="text-white">{formData.personal.location}</span>
              </div>
            )}
          </div>
        </div>

        {/* Experience Summary */}
        <div className="bg-gray-800/50 backdrop-blur-sm rounded-xl p-6 border border-gray-700">
          <h4 className="font-semibold text-neural-purple mb-3 flex items-center gap-2">
            <Briefcase className="h-4 w-4" />
            Experience Summary
          </h4>
          <div className="space-y-3">
            {formData.experience.slice(0, 3).map((exp, index) => (
              <div key={index} className="text-sm">
                <div className="font-medium text-white">{exp.title}</div>
                <div className="text-gray-400">{exp.company}</div>
                <div className="text-xs text-gray-500">
                  {exp.description?.length || 0} characters
                </div>
              </div>
            ))}
            {formData.experience.length > 3 && (
              <div className="text-xs text-gray-400">
                +{formData.experience.length - 3} more experiences
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Word Count Analysis */}
      <div className="bg-gray-800/50 backdrop-blur-sm rounded-xl p-6 border border-gray-700">
        <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
          <FileText className="h-5 w-5 text-neural-purple" />
          Content Analysis
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center p-4 bg-gray-700/30 rounded-lg">
            <div className="text-2xl font-bold text-white mb-1">
              {formData.personal.summary?.length || 0}
            </div>
            <div className="text-gray-400 text-sm">Summary Characters</div>
            <div className={`text-xs mt-1 ${
              (formData.personal.summary?.length || 0) >= 100 ? 'text-green-400' : 'text-yellow-400'
            }`}>
              {(formData.personal.summary?.length || 0) >= 100 ? 'Optimal' : 'Needs more'}
            </div>
          </div>

          <div className="text-center p-4 bg-gray-700/30 rounded-lg">
            <div className="text-2xl font-bold text-white mb-1">
              {formData.experience.reduce((total, exp) => total + (exp.description?.length || 0), 0)}
            </div>
            <div className="text-gray-400 text-sm">Experience Characters</div>
            <div className={`text-xs mt-1 ${
              formData.experience.reduce((total, exp) => total + (exp.description?.length || 0), 0) >= 300 ? 'text-green-400' : 'text-yellow-400'
            }`}>
              {formData.experience.reduce((total, exp) => total + (exp.description?.length || 0), 0) >= 300 ? 'Good detail' : 'Add more detail'}
            </div>
          </div>

          <div className="text-center p-4 bg-gray-700/30 rounded-lg">
            <div className="text-2xl font-bold text-white mb-1">
              {(formData.skills.technical?.length || 0) + (formData.skills.languages?.length || 0)}
            </div>
            <div className="text-gray-400 text-sm">Total Skills</div>
            <div className={`text-xs mt-1 ${
              ((formData.skills.technical?.length || 0) + (formData.skills.languages?.length || 0)) >= 5 ? 'text-green-400' : 'text-yellow-400'
            }`}>
              {((formData.skills.technical?.length || 0) + (formData.skills.languages?.length || 0)) >= 5 ? 'Well-rounded' : 'Add more skills'}
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  // Render Optimization Tab
  const renderOptimizationTab = () => (
    <div className="space-y-6">
      {/* Job Description Input */}
      <div className="bg-gradient-to-r from-neural-purple/10 to-neural-pink/10 border border-neural-purple/30 rounded-xl p-6">
        <h3 className="text-lg font-semibold text-white mb-3 flex items-center gap-2">
          <Target className="h-5 w-5 text-neural-purple" />
          Target Job Optimization
        </h3>
        <p className="text-gray-300 text-sm mb-4">
          Paste a job description to optimize your resume for specific requirements and keywords.
        </p>

        <EnhancedFormField
          label="Job Description"
          type="textarea"
          value={jobDescription}
          onChange={handleJobDescriptionChange}
          placeholder="Paste the job description here for AI-powered resume optimization..."
          rows={6}
          variant="modern"
          helpText="Adding a job description helps our AI tailor your resume to match specific requirements and improve ATS compatibility."
          hint="💡 This helps optimize keywords and highlight relevant experience"
        />
      </div>

      {/* ATS Optimization Score */}
      <div className="bg-gray-800/50 backdrop-blur-sm rounded-xl p-6 border border-gray-700">
        <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
          <Shield className="h-5 w-5 text-green-400" />
          ATS Compatibility Score
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div className="text-center p-4 bg-green-500/10 border border-green-500/20 rounded-lg">
            <div className="text-3xl font-bold text-green-400 mb-1">95%</div>
            <div className="text-green-300 text-sm font-medium">ATS Score</div>
            <div className="text-green-400/70 text-xs mt-1">Excellent</div>
          </div>

          <div className="text-center p-4 bg-blue-500/10 border border-blue-500/20 rounded-lg">
            <div className="text-3xl font-bold text-blue-400 mb-1">12</div>
            <div className="text-blue-300 text-sm font-medium">Keywords Matched</div>
            <div className="text-blue-400/70 text-xs mt-1">Good coverage</div>
          </div>

          <div className="text-center p-4 bg-purple-500/10 border border-purple-500/20 rounded-lg">
            <div className="text-3xl font-bold text-purple-400 mb-1">A+</div>
            <div className="text-purple-300 text-sm font-medium">Format Grade</div>
            <div className="text-purple-400/70 text-xs mt-1">Perfect structure</div>
          </div>
        </div>

        <div className="space-y-3">
          <div className="flex items-center justify-between p-3 bg-green-500/10 border border-green-500/20 rounded-lg">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-400" />
              <span className="text-green-300 text-sm font-medium">Single-column layout</span>
            </div>
            <span className="text-green-400 text-xs">Optimal</span>
          </div>

          <div className="flex items-center justify-between p-3 bg-green-500/10 border border-green-500/20 rounded-lg">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-400" />
              <span className="text-green-300 text-sm font-medium">Standard fonts used</span>
            </div>
            <span className="text-green-400 text-xs">Perfect</span>
          </div>

          <div className="flex items-center justify-between p-3 bg-green-500/10 border border-green-500/20 rounded-lg">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-400" />
              <span className="text-green-300 text-sm font-medium">Clear section headers</span>
            </div>
            <span className="text-green-400 text-xs">Excellent</span>
          </div>

          <div className="flex items-center justify-between p-3 bg-yellow-500/10 border border-yellow-500/20 rounded-lg">
            <div className="flex items-center gap-2">
              <AlertTriangle className="h-4 w-4 text-yellow-400" />
              <span className="text-yellow-300 text-sm font-medium">Add more industry keywords</span>
            </div>
            <span className="text-yellow-400 text-xs">Improve</span>
          </div>
        </div>
      </div>

      {/* AI Suggestions */}
      <div className="bg-gray-800/50 backdrop-blur-sm rounded-xl p-6 border border-gray-700">
        <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
          <Sparkles className="h-5 w-5 text-neural-purple" />
          AI Enhancement Suggestions
        </h3>

        <div className="space-y-4">
          <div className="p-4 bg-neural-purple/10 border border-neural-purple/20 rounded-lg">
            <div className="flex items-start gap-3">
              <div className="w-8 h-8 bg-neural-purple/20 rounded-lg flex items-center justify-center flex-shrink-0">
                <Edit3 className="h-4 w-4 text-neural-purple" />
              </div>
              <div>
                <div className="font-medium text-white mb-1">Enhance Professional Summary</div>
                <div className="text-gray-300 text-sm mb-2">
                  Add more quantifiable achievements and industry-specific keywords to your summary.
                </div>
                <button className="text-neural-purple text-sm hover:text-neural-purple/80 transition-colors">
                  Apply Suggestion →
                </button>
              </div>
            </div>
          </div>

          <div className="p-4 bg-blue-500/10 border border-blue-500/20 rounded-lg">
            <div className="flex items-start gap-3">
              <div className="w-8 h-8 bg-blue-500/20 rounded-lg flex items-center justify-center flex-shrink-0">
                <TrendingUp className="h-4 w-4 text-blue-400" />
              </div>
              <div>
                <div className="font-medium text-white mb-1">Optimize Experience Descriptions</div>
                <div className="text-gray-300 text-sm mb-2">
                  Use more action verbs and quantify your achievements with specific metrics.
                </div>
                <button className="text-blue-400 text-sm hover:text-blue-400/80 transition-colors">
                  Apply Suggestion →
                </button>
              </div>
            </div>
          </div>

          <div className="p-4 bg-green-500/10 border border-green-500/20 rounded-lg">
            <div className="flex items-start gap-3">
              <div className="w-8 h-8 bg-green-500/20 rounded-lg flex items-center justify-center flex-shrink-0">
                <Award className="h-4 w-4 text-green-400" />
              </div>
              <div>
                <div className="font-medium text-white mb-1">Add Relevant Skills</div>
                <div className="text-gray-300 text-sm mb-2">
                  Include these trending skills in your field: React, TypeScript, AWS, Docker.
                </div>
                <button className="text-green-400 text-sm hover:text-green-400/80 transition-colors">
                  Apply Suggestion →
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Template Recommendations */}
      <div className="bg-gray-800/50 backdrop-blur-sm rounded-xl p-6 border border-gray-700">
        <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
          <Palette className="h-5 w-5 text-neural-purple" />
          Template Recommendations
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="p-4 bg-neural-purple/10 border border-neural-purple/20 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Crown className="h-4 w-4 text-neural-purple" />
              <span className="font-medium text-white">Current: Classic ATS</span>
            </div>
            <div className="text-gray-300 text-sm mb-3">
              Perfect for maximum ATS compatibility with traditional formatting.
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-green-400 rounded-full"></div>
              <span className="text-green-400 text-sm">100% ATS Score</span>
            </div>
          </div>

          <div className="p-4 bg-gray-700/30 border border-gray-600 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Star className="h-4 w-4 text-yellow-400" />
              <span className="font-medium text-white">Recommended: Modern Minimal</span>
            </div>
            <div className="text-gray-300 text-sm mb-3">
              Clean contemporary design with subtle styling for your industry.
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                <span className="text-green-400 text-sm">98% ATS Score</span>
              </div>
              <button className="text-neural-purple text-sm hover:text-neural-purple/80 transition-colors">
                Switch Template
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Sticky Review Header */}
      <div className="sticky top-4 z-30 bg-gray-900/95 backdrop-blur-sm rounded-2xl border border-gray-700/50 p-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-4"
        >
          {/* Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-gradient-to-r from-neural-purple to-neural-pink rounded-xl flex items-center justify-center">
                <FileText className="h-6 w-6 text-white" />
              </div>
              <div>
                <h2 className="text-2xl font-bold text-white">Review & Generate</h2>
                <p className="text-gray-400 text-sm">Finalize your professional resume</p>
              </div>
            </div>

            {/* Completion Score */}
            <div className="text-right">
              <div className="flex items-center gap-2 mb-1">
                <div className={`w-3 h-3 rounded-full ${
                  completionScore >= 90 ? 'bg-green-400' :
                  completionScore >= 70 ? 'bg-yellow-400' : 'bg-red-400'
                }`} />
                <span className="text-white font-semibold">{completionScore}% Complete</span>
              </div>
              <div className="w-24 bg-gray-700 rounded-full h-2">
                <motion.div
                  className={`h-2 rounded-full ${
                    completionScore >= 90 ? 'bg-green-400' :
                    completionScore >= 70 ? 'bg-yellow-400' : 'bg-red-400'
                  }`}
                  initial={{ width: 0 }}
                  animate={{ width: `${completionScore}%` }}
                  transition={{ duration: 0.5 }}
                />
              </div>
            </div>
          </div>

          {/* Tab Navigation */}
          <div className="flex items-center gap-1 bg-gray-800/50 rounded-lg p-1">
            {[
              { id: 'overview', label: 'Overview', icon: Eye },
              { id: 'content', label: 'Content Review', icon: FileText },
              { id: 'optimization', label: 'Optimization', icon: TrendingUp }
            ].map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-all duration-200 ${
                    activeTab === tab.id
                      ? 'bg-neural-purple text-white shadow-lg'
                      : 'text-gray-400 hover:text-white hover:bg-gray-700/50'
                  }`}
                >
                  <Icon className="h-4 w-4" />
                  <span className="text-sm font-medium">{tab.label}</span>
                </button>
              );
            })}
          </div>

          {/* Quick Actions */}
          <div className="flex items-center gap-3">
            <button
              onClick={onSave}
              className="flex items-center gap-2 px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors"
            >
              <Save className="h-4 w-4" />
              <span>Save Draft</span>
            </button>

            <button
              onClick={() => setShowTemplateSelector(true)}
              className="flex items-center gap-2 px-4 py-2 bg-neural-purple/20 hover:bg-neural-purple/30 text-neural-purple border border-neural-purple/30 rounded-lg transition-colors"
            >
              <Palette className="h-4 w-4" />
              <span>Change Template</span>
            </button>

            <div className="flex items-center gap-2 text-sm text-gray-400">
              <span>Template:</span>
              <span className="text-white font-medium capitalize">
                {selectedTemplate?.replace('_', ' ') || 'Classic ATS'}
              </span>
            </div>
          </div>
        </motion.div>
      </div>

      {/* Tab Content */}
      <AnimatePresence mode="wait">
        <motion.div
          key={activeTab}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.2 }}
          className="space-y-6"
        >
          {activeTab === 'overview' && renderOverviewTab()}
          {activeTab === 'content' && renderContentTab()}
          {activeTab === 'optimization' && renderOptimizationTab()}
        </motion.div>
      </AnimatePresence>

      {/* Enhanced Template Selector Modal */}
      <AnimatePresence>
        {showTemplateSelector && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.95 }}
              className="bg-gray-900 rounded-2xl border border-gray-700 w-full max-w-6xl max-h-[90vh] overflow-hidden"
            >
              <EnhancedTemplateSelector
                selectedTemplate={selectedTemplate}
                onTemplateSelect={(templateId) => {
                  onTemplateSelect(templateId);
                  setShowTemplateSelector(false);
                }}
                formData={formData}
                showPreview={true}
              />
              <div className="p-4 border-t border-gray-700 flex justify-end">
                <button
                  onClick={() => setShowTemplateSelector(false)}
                  className="px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors"
                >
                  Close
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

    </div>
  );
};

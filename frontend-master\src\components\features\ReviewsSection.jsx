'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { 
  collection, 
  addDoc, 
  getDocs, 
  query, 
  orderBy, 
  serverTimestamp,
  doc,
  getDoc 
} from 'firebase/firestore';
import { firestore } from '@/firebase/config';

const StarRating = ({ rating, onRatingChange, readonly = false }) => {
  const [hover, setHover] = useState(0);

  return (
    <div className="flex items-center">
      {[...Array(5)].map((_, index) => {
        const ratingValue = index + 1;
        return (
          <button
            key={index}
            type="button"
            className={`text-2xl ${
              ratingValue <= (hover || rating) 
                ? 'text-yellow-400' 
                : 'text-gray-600'
            } ${readonly ? 'cursor-default' : 'cursor-pointer hover:text-yellow-300'} transition-colors`}
            onClick={() => !readonly && onRatingChange && onRatingChange(ratingValue)}
            onMouseEnter={() => !readonly && setHover(ratingValue)}
            onMouseLeave={() => !readonly && setHover(0)}
            disabled={readonly}
          >
            ★
          </button>
        );
      })}
    </div>
  );
};

const ReviewCard = ({ review }) => {
  const formatDate = (timestamp) => {
    if (!timestamp) return '';
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <div className="bg-gray-900/20 backdrop-blur-md p-6 rounded-2xl border border-white/10 mb-4">
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center">
          <div className="w-10 h-10 bg-gradient-to-r from-neural-purple to-neural-pink rounded-full flex items-center justify-center text-white font-semibold mr-3">
            {review.userEmail ? review.userEmail.charAt(0).toUpperCase() : 'A'}
          </div>
          <div>
            <p className="text-white font-medium">
              {review.userEmail ? review.userEmail.split('@')[0] : 'Anonymous'}
            </p>
            <p className="text-gray-400 text-sm">{formatDate(review.createdAt)}</p>
          </div>
        </div>
        <StarRating rating={review.rating} readonly={true} />
      </div>
      <p className="text-gray-300 leading-relaxed">{review.comment}</p>
    </div>
  );
};

const ReviewForm = ({ onReviewSubmitted }) => {
  const [rating, setRating] = useState(0);
  const [comment, setComment] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const { currentUser } = useAuth();

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!currentUser) {
      setError('You must be logged in to submit a review.');
      return;
    }

    if (rating === 0) {
      setError('Please select a rating.');
      return;
    }

    if (comment.trim().length < 10) {
      setError('Please write at least 10 characters in your review.');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const reviewData = {
        userId: currentUser.uid,
        userEmail: currentUser.email,
        rating: rating,
        comment: comment.trim(),
        createdAt: serverTimestamp()
      };

      await addDoc(collection(firestore, 'reviews'), reviewData);
      
      // Reset form
      setRating(0);
      setComment('');
      
      // Notify parent component
      if (onReviewSubmitted) {
        onReviewSubmitted();
      }
    } catch (err) {
      console.error('Error submitting review:', err);
      setError('Failed to submit review. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  if (!currentUser) {
    return (
      <div className="bg-gray-900/20 backdrop-blur-md p-6 rounded-2xl border border-white/10 text-center">
        <p className="text-gray-300 mb-4">Please log in to submit a review.</p>
        <a 
          href="/login" 
          className="bg-gradient-to-r from-neural-purple to-neural-pink text-white px-6 py-2 rounded-lg hover:opacity-90 transition-opacity"
        >
          Log In
        </a>
      </div>
    );
  }

  return (
    <div className="bg-gray-900/20 backdrop-blur-md p-6 rounded-2xl border border-white/10">
      <h3 className="text-xl font-bold text-white mb-4">Share Your Experience</h3>
      
      {error && (
        <div className="mb-4 p-3 bg-red-500/20 border border-red-500/30 rounded-lg text-red-300 text-sm">
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Your Rating
          </label>
          <StarRating rating={rating} onRatingChange={setRating} />
        </div>

        <div>
          <label htmlFor="comment" className="block text-sm font-medium text-gray-300 mb-2">
            Your Review
          </label>
          <textarea
            id="comment"
            value={comment}
            onChange={(e) => setComment(e.target.value)}
            rows={4}
            className="w-full px-4 py-3 bg-gray-900/50 border border-white/10 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-neural-purple/50 focus:border-neural-purple text-white placeholder-gray-400 transition-all duration-300 ease-in-out backdrop-blur-sm resize-none"
            placeholder="Tell us about your experience with BlinkFind..."
            required
          />
          <p className="text-gray-500 text-xs mt-1">{comment.length}/500 characters</p>
        </div>

        <button
          type="submit"
          disabled={loading || rating === 0 || comment.trim().length < 10}
          className="w-full bg-gradient-to-r from-neural-purple to-neural-pink text-white font-semibold py-3 px-4 rounded-lg hover:opacity-90 focus:outline-none focus:ring-2 focus:ring-neural-purple/50 disabled:opacity-50 transition-all duration-300 ease-in-out"
        >
          {loading ? 'Submitting...' : 'Submit Review'}
        </button>
      </form>
    </div>
  );
};

const ReviewsSection = () => {
  const [reviews, setReviews] = useState([]);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({ totalReviews: 0, averageRating: 0 });

  const fetchReviews = async () => {
    try {
      const reviewsQuery = query(
        collection(firestore, 'reviews'),
        orderBy('createdAt', 'desc')
      );
      
      const querySnapshot = await getDocs(reviewsQuery);
      const reviewsData = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      setReviews(reviewsData);

      // Calculate stats
      const totalReviews = reviewsData.length;
      const averageRating = totalReviews > 0 
        ? reviewsData.reduce((sum, review) => sum + review.rating, 0) / totalReviews
        : 0;

      setStats({ totalReviews, averageRating });
    } catch (error) {
      console.error('Error fetching reviews:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchReviews();
  }, []);

  return (
    <div className="relative overflow-hidden bg-gradient-to-b from-black to-[#0A0A0A] min-h-screen py-12">
      {/* Animated background elements */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute inset-0 bg-[url('/grid.svg')] [mask-image:radial-gradient(ellipse_at_center,white,transparent)]" />
      </div>
      
      {/* Floating AI nodes */}
      <div className="absolute inset-0 overflow-hidden">
        {[...Array(6)].map((_, i) => (
          <div
            key={i}
            className="absolute rounded-full bg-neural-purple opacity-10 blur-xl animate-pulse"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              width: `${Math.random() * 200 + 100}px`,
              height: `${Math.random() * 200 + 100}px`,
              animationDelay: `${Math.random() * 5}s`,
              animationDuration: `${Math.random() * 10 + 10}s`,
            }}
          />
        ))}
      </div>

      <div className="relative z-10 max-w-4xl mx-auto px-6">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold bg-gradient-to-r from-neural-purple to-neural-pink bg-clip-text text-transparent mb-4">
            User Reviews
          </h1>
          <p className="text-gray-300 text-lg mb-6">
            See what our users are saying about BlinkFind
          </p>
          
          {/* Stats */}
          <div className="flex justify-center items-center space-x-8 mb-8">
            <div className="text-center">
              <p className="text-3xl font-bold text-white">{stats.totalReviews}</p>
              <p className="text-gray-400">Total Reviews</p>
            </div>
            <div className="text-center">
              <div className="flex items-center justify-center mb-1">
                <span className="text-3xl font-bold text-white mr-2">
                  {stats.averageRating.toFixed(1)}
                </span>
                <StarRating rating={Math.round(stats.averageRating)} readonly={true} />
              </div>
              <p className="text-gray-400">Average Rating</p>
            </div>
          </div>
        </div>

        {/* Review Form */}
        <div className="mb-12">
          <ReviewForm onReviewSubmitted={fetchReviews} />
        </div>

        {/* Reviews List */}
        <div>
          <h2 className="text-2xl font-bold text-white mb-6">Recent Reviews</h2>
          
          {loading ? (
            <div className="text-center py-8">
              <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-neural-purple"></div>
              <p className="text-gray-300 mt-2">Loading reviews...</p>
            </div>
          ) : reviews.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-gray-400">No reviews yet. Be the first to share your experience!</p>
            </div>
          ) : (
            <div>
              {reviews.map((review) => (
                <ReviewCard key={review.id} review={review} />
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ReviewsSection;

"use client";
import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  <PERSON><PERSON><PERSON>, 
  <PERSON>bulb, 
  Co<PERSON>, 
  Check, 
  RefreshCw,
  Wand2,
  Target,
  TrendingUp
} from 'lucide-react';
import { toast } from 'react-hot-toast';

const AIContentSuggestions = ({ 
  fieldType, 
  currentValue, 
  onSuggestionApply, 
  context = {} 
}) => {
  const [suggestions, setSuggestions] = useState([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [copiedIndex, setCopiedIndex] = useState(null);

  const generateSuggestions = async () => {
    setIsGenerating(true);
    try {
      const response = await fetch('/api/generate-suggestions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          fieldType,
          currentValue,
          context
        }),
      });

      const data = await response.json();
      if (data.success) {
        setSuggestions(data.suggestions);
      } else {
        // Fallback suggestions
        setSuggestions(getFallbackSuggestions(fieldType, context));
      }
    } catch (error) {
      console.error('Error generating suggestions:', error);
      setSuggestions(getFallbackSuggestions(fieldType, context));
    } finally {
      setIsGenerating(false);
    }
  };

  const copyToClipboard = async (text, index) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedIndex(index);
      toast.success('Copied to clipboard!');
      setTimeout(() => setCopiedIndex(null), 2000);
    } catch (error) {
      toast.error('Failed to copy');
    }
  };

  const applySuggestion = (suggestion) => {
    onSuggestionApply(suggestion);
    toast.success('Suggestion applied!');
  };

  const getFallbackSuggestions = (type, ctx) => {
    switch (type) {
      case 'summary':
        return [
          "Experienced professional with strong background in technology and proven track record of delivering results.",
          "Dedicated specialist with expertise in modern technologies and commitment to continuous learning and innovation.",
          "Results-driven professional with comprehensive experience in software development and team collaboration."
        ];
      case 'experience':
        return [
          "• Developed and maintained web applications using modern frameworks and technologies",
          "• Collaborated with cross-functional teams to deliver high-quality software solutions",
          "• Implemented best practices for code quality, testing, and deployment processes"
        ];
      case 'skills':
        return [
          "JavaScript, React, Node.js, Python, Java",
          "HTML5, CSS3, TypeScript, MongoDB, PostgreSQL",
          "Git, Docker, AWS, Agile, Scrum"
        ];
      case 'projects':
        return [
          "Developed a full-stack web application with user authentication and real-time features",
          "Built a responsive e-commerce platform with payment integration and inventory management",
          "Created a data visualization dashboard with interactive charts and analytics"
        ];
      default:
        return ["AI suggestions will appear here"];
    }
  };

  const getFieldIcon = (type) => {
    switch (type) {
      case 'summary': return Target;
      case 'experience': return TrendingUp;
      case 'skills': return Sparkles;
      case 'projects': return Lightbulb;
      default: return Wand2;
    }
  };

  const FieldIcon = getFieldIcon(fieldType);

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-gray-800/30 rounded-lg p-4 border border-gray-700"
    >
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <FieldIcon className="h-4 w-4 text-neural-purple" />
          <h4 className="text-sm font-semibold text-white">AI Suggestions</h4>
        </div>
        <motion.button
          onClick={generateSuggestions}
          disabled={isGenerating}
          className="flex items-center gap-1 px-3 py-1 bg-neural-purple/20 hover:bg-neural-purple/30 border border-neural-purple/50 rounded-md text-xs text-neural-purple hover:text-white transition-all duration-200 disabled:opacity-50"
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          {isGenerating ? (
            <>
              <RefreshCw className="h-3 w-3 animate-spin" />
              Generating...
            </>
          ) : (
            <>
              <Sparkles className="h-3 w-3" />
              Generate
            </>
          )}
        </motion.button>
      </div>

      {suggestions.length > 0 && (
        <div className="space-y-2">
          {suggestions.map((suggestion, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, x: -10 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
              className="bg-gray-900/50 rounded-md p-3 border border-gray-600 hover:border-neural-purple/50 transition-all duration-200"
            >
              <p className="text-sm text-gray-300 mb-2 leading-relaxed">
                {suggestion}
              </p>
              <div className="flex gap-2">
                <button
                  onClick={() => copyToClipboard(suggestion, index)}
                  className="flex items-center gap-1 px-2 py-1 bg-gray-700 hover:bg-gray-600 rounded text-xs text-gray-300 hover:text-white transition-colors"
                >
                  {copiedIndex === index ? (
                    <>
                      <Check className="h-3 w-3" />
                      Copied
                    </>
                  ) : (
                    <>
                      <Copy className="h-3 w-3" />
                      Copy
                    </>
                  )}
                </button>
                <button
                  onClick={() => applySuggestion(suggestion)}
                  className="flex items-center gap-1 px-2 py-1 bg-neural-purple/20 hover:bg-neural-purple/30 border border-neural-purple/50 rounded text-xs text-neural-purple hover:text-white transition-colors"
                >
                  <Wand2 className="h-3 w-3" />
                  Apply
                </button>
              </div>
            </motion.div>
          ))}
        </div>
      )}

      {suggestions.length === 0 && !isGenerating && (
        <div className="text-center py-4">
          <Lightbulb className="h-8 w-8 text-gray-500 mx-auto mb-2" />
          <p className="text-sm text-gray-500">
            Click "Generate" to get AI-powered content suggestions
          </p>
        </div>
      )}
    </motion.div>
  );
};

export default AIContentSuggestions;

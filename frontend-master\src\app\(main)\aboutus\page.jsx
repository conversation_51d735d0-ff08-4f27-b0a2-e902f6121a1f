'use client';

import React from "react";
import Image from "next/image";
import { motion } from "framer-motion";
import { 
  Target, 
  Lightbulb, 
  Users, 
  Globe, 
  TrendingUp, 
  Zap,
  Building,
  GraduationCap,
  Briefcase,
  Star,
  Award,  CheckCircle,
  ArrowRight,
  Shield
} from "lucide-react";

const teamMembers = [
  {
    name: "<PERSON>",
    role: "Founder & CEO (AI & Android Lead)",
    bio: "Passionate about transforming productivity through AI technology to help people achieve more with less effort.",
    image: "/OurTeamImages/avatar/abdullah.jpg",
    linkedin: "https://www.linkedin.com/in/abdullahkhanspn/",
    github: "https://github.com/Abdullahkhanspn",
  },
  {
    name: "<PERSON><PERSON><PERSON> Fatima",
    role: "Co-Founder (AI/ML Specialist)",
    bio: "Specializes in developing intelligent algorithms that understand user needs and provide personalized AI solutions.",
    image: "/OurTeamImages/avatar/zainab.jpg",
    linkedin: "https://www.linkedin.com/in/zainaboptique?",
    instagram: "https://www.instagram.com/zainaboptique?",
  },
];

const problemPoints = [
  "People are intense with tasks, stress and digital distractions",
  "Most tools are too scattered or complicated to actually help",
  "Businesses, especially small ones, can't afford big automation teams or software",
  "Students, job seekers, freelancers all want help but don't know where to begin"
];

const solutionPoints = [
  "A simple tool to build your own assistant, no technical knowledge required",
  "A place where businesses can use digital workers to handle their routine tasks",
  "Personal tools for students and job seekers, like resume help, reminders, and content creation",
  "A safe space to talk, whether for support, advice, or just some company",
  "All of this, in one clean dashboard"
];

const useCases = [
  {
    icon: GraduationCap,
    title: "Student",
    description: "Logs in and builds a bot that reminds her of study hours and suggests job openings"
  },
  {
    icon: Building,
    title: "Shop Owner",
    description: "Uses it to reply to customer messages and send invoices automatically"
  },
  {
    icon: Briefcase,
    title: "Young Graduate",
    description: "Uses it to write his first resume and prepare for interviews"
  },
  {
    icon: Star,
    title: "Creator",
    description: "Uses it to draft social media posts and edit scripts"
  }
];

const AboutUs = () => {
  return (
    <div className="min-h-screen py-16 pt-24 px-4 sm:px-6 lg:px-8 bg-gradient-to-b from-black to-[#0A0A0A]">
      <div className="max-w-7xl mx-auto">
        
        {/* Hero Section */}
        <motion.div 
          className="text-center mb-20"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <h1 className="text-5xl md:text-7xl font-bold text-white mb-6">
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-neural-purple to-neural-pink">
              BlinkFind
            </span>{" "}
            Pvt. Ltd.
          </h1>
          <p className="text-xl md:text-2xl text-gray-300 max-w-4xl mx-auto leading-relaxed">
            One platform for AI-powered productivity, business automation and daily life solutions.
          </p>
          
          {/* Certification Badges */}
          <div className="flex flex-wrap justify-center gap-4 mt-8">
            <div className="bg-gradient-to-r from-neural-purple/20 to-neural-pink/20 backdrop-blur-md border border-neural-purple/30 rounded-lg px-6 py-3">
              <div className="flex items-center gap-2">
                <Shield className="h-5 w-5 text-neural-purple" />
                <span className="text-sm text-white font-medium">Government Recognized Startup, Startup India Certified</span>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Problem Section */}
        <motion.section 
          className="mb-20"
          initial={{ opacity: 0, y: 40 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <div className="flex items-center gap-3 mb-6">
                <Target className="h-8 w-8 text-red-400" />
                <h2 className="text-3xl font-bold text-white">The Problem</h2>
              </div>
              <div className="space-y-4">
                {problemPoints.map((point, index) => (
                  <div key={index} className="flex items-start gap-3">
                    <div className="w-2 h-2 rounded-full bg-red-400 mt-2 flex-shrink-0"></div>
                    <p className="text-gray-300 text-lg">{point}</p>
                  </div>
                ))}
              </div>            </div>
            <div className="relative h-[300px] md:h-[400px] rounded-xl overflow-hidden border border-white/10">
              <Image
                src="/web1.jpeg"
                alt="Digital workplace challenges and complexity"
                layout="fill"
                objectFit="cover"
                className="opacity-90"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
            </div>
          </div>
        </motion.section>        {/* Solution Section */}
        <motion.section 
          className="mb-20"
          initial={{ opacity: 0, y: 40 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="order-2 lg:order-1">
              <div className="relative h-[400px] rounded-2xl overflow-hidden border border-neural-purple/20">
                <Image
                  src="/ai.jpg"
                  alt="AI Solutions and Technology"
                  layout="fill"
                  objectFit="cover"
                  className="opacity-80"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent" />
                <div className="absolute bottom-8 left-8 right-8">
                  <div className="flex items-center gap-3 mb-3">
                    <Lightbulb className="h-8 w-8 text-neural-purple" />
                    <h3 className="text-2xl font-bold text-white">Smart Solutions</h3>
                  </div>
                  <p className="text-gray-200">AI-powered tools that work together seamlessly</p>
                </div>
              </div>
            </div>
            <div className="order-1 lg:order-2">
              <div className="flex items-center gap-3 mb-6">
                <Lightbulb className="h-8 w-8 text-neural-purple" />
                <h2 className="text-3xl font-bold text-white">Our Solution</h2>
              </div>
              <div className="space-y-4">
                {solutionPoints.map((point, index) => (
                  <div key={index} className="flex items-start gap-3">
                    <CheckCircle className="w-6 h-6 text-neural-purple mt-1 flex-shrink-0" />
                    <p className="text-gray-300 text-lg">{point}</p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </motion.section>        {/* Why BlinkFind Matters Section */}
        <motion.section 
          className="mb-20"
          initial={{ opacity: 0, y: 40 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <div className="text-center mb-12">
            <div className="flex items-center justify-center gap-3 mb-6">
              <Globe className="h-8 w-8 text-neural-purple" />
              <h2 className="text-3xl font-bold text-white">Why BlinkFind Matters</h2>
            </div>
          </div>
          
          {/* AI Technology Showcase */}
          <div className="relative mb-12 h-[300px] rounded-2xl overflow-hidden border border-neural-purple/20">
            <Image
              src="/ai1.jpg"
              alt="AI Technology and Innovation"
              layout="fill"
              objectFit="cover"
              className="opacity-70"
            />
            <div className="absolute inset-0 bg-gradient-to-r from-black/80 via-black/50 to-black/80" />
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="text-center max-w-4xl px-8">
                <h3 className="text-3xl md:text-4xl font-bold text-white mb-4">
                  Transforming Work with <span className="text-transparent bg-clip-text bg-gradient-to-r from-neural-purple to-neural-pink">Artificial Intelligence</span>
                </h3>
                <p className="text-xl text-gray-200">
                  Making advanced AI technology accessible to everyone, everywhere
                </p>
              </div>
            </div>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <div className="bg-gradient-to-br from-green-500/10 to-emerald-500/10 backdrop-blur-md border border-green-500/20 rounded-xl p-6">
              <Zap className="h-12 w-12 text-green-400 mb-4" />
              <h3 className="text-xl font-bold text-white mb-3">Accessible AI</h3>
              <p className="text-gray-300">Making AI tools available to everyone, not just tech companies or those who can afford expensive solutions.</p>
            </div>
            <div className="bg-gradient-to-br from-blue-500/10 to-cyan-500/10 backdrop-blur-md border border-blue-500/20 rounded-xl p-6">
              <Users className="h-12 w-12 text-blue-400 mb-4" />
              <h3 className="text-xl font-bold text-white mb-3">Real Impact</h3>
              <p className="text-gray-300">Helping small businesses compete with larger ones and giving individuals the tools to succeed.</p>
            </div>
            <div className="bg-gradient-to-br from-purple-500/10 to-pink-500/10 backdrop-blur-md border border-purple-500/20 rounded-xl p-6">
              <TrendingUp className="h-12 w-12 text-purple-400 mb-4" />
              <h3 className="text-xl font-bold text-white mb-3">Future Ready</h3>
              <p className="text-gray-300">Preparing people for an AI-driven world by making these technologies easy to understand and use.</p>
            </div>
          </div>
        </motion.section>

        {/* Use Cases Section */}
        <motion.section 
          className="mb-20"
          initial={{ opacity: 0, y: 40 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-white mb-4">Real-World Use Cases</h2>
            <p className="text-gray-300 text-lg max-w-3xl mx-auto">
              See how BlinkFind transforms daily workflows for different users
            </p>
          </div>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {useCases.map((useCase, index) => (
              <motion.div 
                key={index}
                className="bg-gray-900/50 backdrop-blur-md border border-white/10 rounded-xl p-6 hover:border-neural-purple/30 transition-all duration-300"              whileHover={{ y: -5 }}
              >
                <div className="text-neural-purple mb-4">
                  <useCase.icon className="h-6 w-6" />
                </div>
                <h3 className="text-lg font-bold text-white mb-2">{useCase.title}</h3>
                <p className="text-gray-400 text-sm">{useCase.description}</p>
              </motion.div>
            ))}
          </div>        </motion.section>        {/* Meet Our Founders Section */}
        <motion.section 
          className="mb-20"
          initial={{ opacity: 0, y: 40 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">Meet Our Founders</h2>
            <p className="text-gray-300 text-lg max-w-2xl mx-auto">
              The visionaries behind BlinkFind's mission to democratize AI technology
            </p>
          </div>

          <div className="max-w-5xl mx-auto space-y-12">
            {/* Abdullah Khan */}
            <motion.div 
              className="bg-gray-900/30 border border-gray-700/30 rounded-2xl p-8 md:p-10"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
            >
              <div className="grid md:grid-cols-4 gap-8 items-center">
                <div className="text-center md:text-left">
                  <Image
                    src="/OurTeamImages/avatar/abdullah.jpg"
                    alt="Abdullah Khan"
                    width={120}
                    height={120}
                    className="rounded-full mx-auto md:mx-0 mb-4"
                  />
                  <h3 className="text-xl font-bold text-white mb-2">Abdullah Khan</h3>
                  <p className="text-neural-purple font-medium mb-1">Founder & CEO</p>
                  <p className="text-gray-400 text-sm mb-4">AI & Android Lead</p>
                  <div className="flex gap-3 justify-center md:justify-start">
                    <a
                      href="https://www.linkedin.com/in/abdullahkhanspn/"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-gray-400 hover:text-white transition-colors"
                    >
                      <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z"/>
                      </svg>
                    </a>
                    <a
                      href="https://github.com/Abdullahkhanspn"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-gray-400 hover:text-white transition-colors"
                    >
                      <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                      </svg>
                    </a>
                  </div>
                </div>
                <div className="md:col-span-3">
                  <blockquote className="text-gray-300 text-lg leading-relaxed border-l-3 border-gray-600 pl-6">
                    "When I started BlinkFind, I had a simple vision: make AI technology so intuitive that anyone can harness its power. Growing up, I saw brilliant minds held back by complex tools. Today, we're breaking those barriers. Every line of code we write, every feature we build, is driven by one question – how can we make this simpler for the user?"
                  </blockquote>
                </div>
              </div>
            </motion.div>

            {/* Zainab Fatima */}
            <motion.div 
              className="bg-gray-900/30 border border-gray-700/30 rounded-2xl p-8 md:p-10"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              viewport={{ once: true }}
            >
              <div className="grid md:grid-cols-4 gap-8 items-center">
                <div className="text-center md:text-left md:order-2">
                  <Image
                    src="/OurTeamImages/avatar/zainab.jpg"
                    alt="Zainab Fatima"
                    width={120}
                    height={120}
                    className="rounded-full mx-auto md:mx-0 mb-4"
                  />
                  <h3 className="text-xl font-bold text-white mb-2">Zainab Fatima</h3>
                  <p className="text-neural-pink font-medium mb-1">Co-Founder</p>
                  <p className="text-gray-400 text-sm mb-4">AI/ML Specialist</p>
                  <div className="flex gap-3 justify-center md:justify-start">
                    <a
                      href="https://www.linkedin.com/in/zainaboptique?"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-gray-400 hover:text-white transition-colors"
                    >
                      <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z"/>
                      </svg>
                    </a>
                    <a
                      href="https://www.instagram.com/zainaboptique?"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-gray-400 hover:text-white transition-colors"
                    >
                      <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                      </svg>
                    </a>
                  </div>
                </div>
                <div className="md:col-span-3 md:order-1">
                  <blockquote className="text-gray-300 text-lg leading-relaxed border-l-3 border-gray-600 pl-6">
                    "AI is not about replacing human intelligence; it's about amplifying it. My passion lies in creating algorithms that understand not just what users do, but why they do it. At BlinkFind, we're building AI that feels less like a tool and more like a thoughtful assistant who truly gets you."
                  </blockquote>
                </div>
              </div>
            </motion.div>
          </div>
        </motion.section>        {/* Mission & Vision Section */}
        <motion.section 
          className="mb-20"
          initial={{ opacity: 0, y: 40 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">Our Mission & Vision</h2>
            <p className="text-gray-300 text-lg max-w-3xl mx-auto">
              Driving the future of AI-powered productivity through innovation and accessibility
            </p>
          </div>

          <div className="max-w-6xl mx-auto grid lg:grid-cols-2 gap-8">
            {/* Mission Card */}
            <motion.div
              className="bg-gray-900/50 border border-white/10 rounded-xl p-8 hover:border-neural-purple/30 transition-all duration-300"
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
            >
              <div className="flex items-center mb-6">
                <div className="bg-neural-purple/20 p-3 rounded-lg mr-4">
                  <Lightbulb className="h-6 w-6 text-neural-purple" />
                </div>
                <h3 className="text-2xl font-bold text-white">Mission</h3>
              </div>
              <p className="text-gray-300 text-lg leading-relaxed mb-6">
                To democratize AI technology and make powerful automation tools accessible to everyone, 
                helping individuals and businesses achieve more with intelligent solutions.
              </p>
              <div className="space-y-3">
                {[
                  "Democratize AI Technology",
                  "Empower Every Individual",
                  "Transform Business Operations"
                ].map((highlight, index) => (
                  <div key={index} className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-neural-purple rounded-full"></div>
                    <span className="text-gray-400 text-sm">{highlight}</span>
                  </div>
                ))}
              </div>
            </motion.div>

            {/* Vision Card */}
            <motion.div
              className="bg-gray-900/50 border border-white/10 rounded-xl p-8 hover:border-neural-pink/30 transition-all duration-300"
              initial={{ opacity: 0, x: 20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              viewport={{ once: true }}
            >
              <div className="flex items-center mb-6">
                <div className="bg-neural-pink/20 p-3 rounded-lg mr-4">
                  <Globe className="h-6 w-6 text-neural-pink" />
                </div>
                <h3 className="text-2xl font-bold text-white">Vision</h3>
              </div>
              <p className="text-gray-300 text-lg leading-relaxed mb-6">
                To become the leading platform for AI-powered productivity tools in emerging markets, 
                bridging the gap between complex technology and everyday users.
              </p>
              <div className="space-y-3">
                {[
                  "Global AI Leadership",
                  "Bridge Technology Gaps",
                  "Emerging Market Focus"
                ].map((highlight, index) => (
                  <div key={index} className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-neural-pink rounded-full"></div>
                    <span className="text-gray-400 text-sm">{highlight}</span>
                  </div>
                ))}
              </div>
            </motion.div>
          </div>
        </motion.section>
      </div>
    </div>
  );
};

export default AboutUs;
'use client';

import { useState } from 'react';
import { useAdmin } from '@/contexts/AdminContext';
import { Eye, EyeOff, Lock, Mail, Shield, AlertCircle, Sparkles, Zap, Users, BarChart3, Settings, Monitor } from 'lucide-react';
import { motion } from 'framer-motion';

const AdminLogin = () => {
  const [credentials, setCredentials] = useState({ email: '', password: '' });
  const [showPassword, setShowPassword] = useState(false);
  const [isLogging, setIsLogging] = useState(false);
  const { adminLogin, error } = useAdmin();

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLogging(true);
    
    try {
      await adminLogin(credentials.email, credentials.password);
    } catch (error) {
      console.error('Admin login failed:', error);
    } finally {
      setIsLogging(false);
    }
  };

  const handleChange = (e) => {
    setCredentials(prev => ({
      ...prev,
      [e.target.name]: e.target.value
    }));
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-black to-[#0A0A0A] flex">
      {/* Left Side - Content & Imagery */}
      <div className="hidden lg:flex lg:w-1/2 relative overflow-hidden">
        {/* Neural Network Background */}
        <div className="absolute inset-0">
          {/* Animated Grid Background */}
          <div className="absolute inset-0 opacity-20">
            <div className="absolute inset-0 bg-[url('/grid.svg')] [mask-image:radial-gradient(ellipse_at_center,white,transparent)]" />
          </div>
            {/* Floating Neural Network Nodes */}
          {[...Array(5)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute rounded-full bg-neural-purple opacity-10 blur-xl"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                width: `${Math.random() * 150 + 80}px`,
                height: `${Math.random() * 150 + 80}px`,
              }}
              animate={{
                scale: [1, 1.2, 1],
                opacity: [0.1, 0.3, 0.1],
              }}
              transition={{
                duration: Math.random() * 3 + 3,
                repeat: Infinity,
                ease: "easeInOut",
                delay: Math.random() * 2,
              }}
            />
          ))}
          
          {/* Pink accent nodes */}
          {[...Array(3)].map((_, i) => (
            <motion.div
              key={`pink-${i}`}
              className="absolute rounded-full bg-neural-pink opacity-5 blur-2xl"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                width: `${Math.random() * 120 + 60}px`,
                height: `${Math.random() * 120 + 60}px`,
              }}
              animate={{
                y: [0, -20, 0],
                opacity: [0.05, 0.15, 0.05],
              }}
              transition={{
                duration: Math.random() * 4 + 4,
                repeat: Infinity,
                ease: "easeInOut",
                delay: Math.random() * 3,
              }}
            />
          ))}

          {/* Neural Network Connecting Lines */}
          <svg className="absolute inset-0 w-full h-full pointer-events-none">
            <defs>
              <linearGradient id="neural-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#8B5CF6" />
                <stop offset="50%" stopColor="#EC4899" />
                <stop offset="100%" stopColor="#06B6D4" />
              </linearGradient>
            </defs>
            {[...Array(4)].map((_, i) => (
              <motion.line
                key={i}
                x1={`${Math.random() * 100}%`}
                y1={`${Math.random() * 100}%`}
                x2={`${Math.random() * 100}%`}
                y2={`${Math.random() * 100}%`}
                stroke="url(#neural-gradient)"
                strokeWidth="1"
                opacity="0.1"
                animate={{
                  opacity: [0.05, 0.2, 0.05],
                }}
                transition={{
                  duration: Math.random() * 3 + 2,
                  repeat: Infinity,
                  ease: "easeInOut",
                  delay: Math.random() * 2,
                }}
              />
            ))}
          </svg>
        </div>        {/* Content Overlay */}
        <div className="relative z-10 flex flex-col justify-center p-12 text-white">
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
          >
            {/* Logo and Title */}
            <div className="mb-8">
              <motion.div
                className="flex items-center gap-3 mb-6"
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
              >
                <motion.div
                  className="relative"
                  animate={{
                    rotate: [0, 360],
                  }}
                  transition={{
                    duration: 20,
                    repeat: Infinity,
                    ease: "linear",
                  }}
                >
                  <Shield className="w-10 h-10 text-neural-purple" />
                  <motion.div
                    className="absolute inset-0 w-10 h-10 border-2 border-neural-pink/30 rounded-full"
                    animate={{
                      scale: [1, 1.2, 1],
                      opacity: [0.5, 0.8, 0.5],
                    }}
                    transition={{
                      duration: 3,
                      repeat: Infinity,
                      ease: "easeInOut",
                    }}
                  />
                </motion.div>
                <div>
                  <h1 className="text-3xl font-bold bg-gradient-to-r from-neural-purple to-neural-pink bg-clip-text text-transparent">
                    BlinkFind
                  </h1>
                  <div className="text-neural-purple/80 font-semibold tracking-wider text-sm">ADMIN PORTAL</div>
                </div>
              </motion.div>

              <motion.h2 
                className="text-2xl font-bold text-white mb-4"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.4 }}
              >
                Administrative Control
                <span className="block text-transparent bg-gradient-to-r from-neural-purple to-neural-pink bg-clip-text">
                  Made Simple
                </span>
              </motion.h2>

              <motion.p 
                className="text-gray-300 leading-relaxed mb-6"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.6 }}
              >
                Secure access to BlinkFind's administration tools and system management.
              </motion.p>
            </div>

            {/* Feature Grid */}
            <motion.div 
              className="grid grid-cols-2 gap-4"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.8 }}
            >
              <div className="flex items-start gap-2">
                <div className="bg-neural-purple/20 p-2 rounded-lg">
                  <BarChart3 className="w-4 h-4 text-neural-purple" />
                </div>
                <div>
                  <h3 className="font-semibold text-white text-sm">Analytics</h3>
                  <p className="text-gray-400 text-xs">Real-time insights</p>
                </div>
              </div>
              
              <div className="flex items-start gap-2">
                <div className="bg-neural-pink/20 p-2 rounded-lg">
                  <Users className="w-4 h-4 text-neural-pink" />
                </div>
                <div>
                  <h3 className="font-semibold text-white text-sm">Users</h3>
                  <p className="text-gray-400 text-xs">User management</p>
                </div>
              </div>
              
              <div className="flex items-start gap-2">
                <div className="bg-neural-blue/20 p-2 rounded-lg">
                  <Monitor className="w-4 h-4 text-neural-blue" />
                </div>
                <div>
                  <h3 className="font-semibold text-white text-sm">Monitor</h3>
                  <p className="text-gray-400 text-xs">System health</p>
                </div>
              </div>
              
              <div className="flex items-start gap-2">
                <div className="bg-green-500/20 p-2 rounded-lg">
                  <Settings className="w-4 h-4 text-green-400" />
                </div>
                <div>
                  <h3 className="font-semibold text-white text-sm">Settings</h3>
                  <p className="text-gray-400 text-xs">Configuration</p>
                </div>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </div>      {/* Right Side - Login Form */}
      <div className="w-full lg:w-1/2 flex items-center justify-center p-6 lg:p-12 relative">
        {/* Mobile Background for smaller screens */}
        <div className="absolute inset-0 lg:hidden">
          <div className="absolute inset-0 opacity-10">
            <div className="absolute inset-0 bg-[url('/grid.svg')] [mask-image:radial-gradient(ellipse_at_center,white,transparent)]" />
          </div>
          {[...Array(3)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute rounded-full bg-neural-purple opacity-5 blur-xl"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                width: `${Math.random() * 150 + 80}px`,
                height: `${Math.random() * 150 + 80}px`,
              }}
              animate={{
                scale: [1, 1.2, 1],
                opacity: [0.05, 0.15, 0.05],
              }}
              transition={{
                duration: Math.random() * 3 + 3,
                repeat: Infinity,
                ease: "easeInOut",
                delay: Math.random() * 2,
              }}
            />
          ))}
        </div>

        <motion.div 
          className="w-full max-w-md relative z-10"
          initial={{ opacity: 0, x: 50 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.8 }}
        >
          {/* Header for mobile */}
          <motion.div 
            className="text-center mb-6 lg:hidden"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            <div className="flex items-center justify-center gap-3 mb-4">
              <motion.div
                className="relative"
                animate={{
                  rotate: [0, 360],
                }}
                transition={{
                  duration: 20,
                  repeat: Infinity,
                  ease: "linear",
                }}              >
                <Shield className="w-8 h-8 text-neural-purple" />
                <motion.div
                  className="absolute inset-0 w-8 h-8 border-2 border-neural-pink/30 rounded-full"
                  animate={{
                    scale: [1, 1.2, 1],
                    opacity: [0.5, 0.8, 0.5],
                  }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    ease: "easeInOut",
                  }}
                />
              </motion.div>
              <div>
                <h1 className="text-2xl font-bold bg-gradient-to-r from-neural-purple to-neural-pink bg-clip-text text-transparent">
                  BlinkFind
                </h1>
                <div className="text-sm text-neural-purple/80 font-semibold tracking-wider">ADMIN PORTAL</div>
              </div>
            </div>
            <motion.p 
              className="text-gray-300"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.4 }}
            >
              Secure administrative access
            </motion.p>
          </motion.div>          {/* Login Form */}
          <motion.div 
            className="bg-gray-900/25 backdrop-blur-md border border-white/10 rounded-2xl p-6 shadow-2xl shadow-neural-purple/10"
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8, delay: 0.3 }}
          >
            <form onSubmit={handleSubmit} className="space-y-5">
              {/* Email Input */}
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.5 }}
              >                <label className="block text-sm font-semibold text-gray-300 mb-2">
                  Administrator Email
                </label>
                <div className="relative group">
                  <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-neural-purple/60 group-focus-within:text-neural-purple transition-colors" />
                  <input
                    type="email"
                    name="email"
                    value={credentials.email}
                    onChange={handleChange}
                    required
                    className="w-full pl-10 pr-4 py-3 bg-gray-900/50 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:border-neural-purple focus:ring-4 focus:ring-neural-purple/20 focus:outline-none transition-all duration-300 font-medium hover:border-neural-purple/30"
                    placeholder="<EMAIL>"
                  />
                </div>
              </motion.div>

              {/* Password Input */}
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.6 }}
              >                <label className="block text-sm font-semibold text-gray-300 mb-2">
                  Security Password
                </label>
                <div className="relative group">
                  <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-neural-purple/60 group-focus-within:text-neural-purple transition-colors" />
                  <input
                    type={showPassword ? 'text' : 'password'}
                    name="password"
                    value={credentials.password}
                    onChange={handleChange}
                    required
                    className="w-full pl-10 pr-12 py-3 bg-gray-900/50 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:border-neural-purple focus:ring-4 focus:ring-neural-purple/20 focus:outline-none transition-all duration-300 font-medium hover:border-neural-purple/30"
                    placeholder="Enter secure password"
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-neural-purple/60 hover:text-neural-purple transition-colors p-1"
                  >
                    {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                  </button>
                </div>
              </motion.div>

              {/* Error Message */}
              {error && (                <motion.div 
                  className="flex items-start gap-3 p-3 bg-red-500/20 border border-red-500/30 rounded-xl"
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3 }}
                >
                  <AlertCircle className="w-5 h-5 text-red-300 mt-0.5 flex-shrink-0" />
                  <div className="text-red-300 text-sm font-medium">{error}</div>
                </motion.div>
              )}

              {/* Login Button */}
              <motion.button
                type="submit"
                disabled={isLogging || !credentials.email || !credentials.password}
                className="w-full bg-gradient-to-r from-neural-purple to-neural-pink hover:from-neural-purple/90 hover:to-neural-pink/90 disabled:from-gray-400 disabled:to-gray-500 disabled:cursor-not-allowed text-white font-semibold py-3 px-6 rounded-xl transition-all duration-300 flex items-center justify-center gap-3 shadow-lg shadow-neural-purple/30 hover:shadow-xl hover:shadow-neural-purple/40 transform hover:scale-[1.02] disabled:transform-none"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.7 }}
                whileTap={{ scale: 0.98 }}
              >
                {isLogging ? (
                  <>
                    <motion.div 
                      className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full"
                      animate={{ rotate: 360 }}
                      transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                    />
                    Authenticating...
                  </>
                ) : (
                  <>
                    <Zap className="w-5 h-5" />
                    Access Admin Dashboard
                    <Sparkles className="w-4 h-4" />
                  </>
                )}
              </motion.button>            </form>          </motion.div>
        </motion.div>
      </div>
    </div>
  );
};

export default AdminLogin;

import { GoogleGenerativeAI } from '@google/generative-ai';

// Initialize Gemini AI with error handling
let genAI = null;
let model = null;

try {
  if (process.env.NEXT_PUBLIC_GEMINI_API_KEY) {
    genAI = new GoogleGenerativeAI(process.env.NEXT_PUBLIC_GEMINI_API_KEY);
    model = genAI.getGenerativeModel({ model: 'gemini-pro' });
  } else {
    console.warn('Gemini API key not found. AI features will use fallback responses.');
  }
} catch (error) {
  console.error('Failed to initialize Gemini AI:', error);
}

// Rate limiting
const API_CALLS = new Map();
const RATE_LIMIT = 10; // calls per minute
const RATE_WINDOW = 60000; // 1 minute

const checkRateLimit = (userId = 'anonymous') => {
  const now = Date.now();
  const userCalls = API_CALLS.get(userId) || [];

  // Remove calls older than rate window
  const recentCalls = userCalls.filter(time => now - time < RATE_WINDOW);

  if (recentCalls.length >= RATE_LIMIT) {
    throw new Error('Rate limit exceeded. Please wait a moment before trying again.');
  }

  recentCalls.push(now);
  API_CALLS.set(userId, recentCalls);
};

// ATS Scoring Service
export const analyzeResumeATS = async (resumeData, userId = 'anonymous') => {
  try {
    // Check if AI is available
    if (!model) {
      return getFallbackATSAnalysis();
    }

    // Rate limiting
    checkRateLimit(userId);

    const prompt = `
    Analyze this resume data for ATS (Applicant Tracking System) compatibility and provide a detailed score:

    Resume Data:
    ${JSON.stringify(resumeData, null, 2)}

    Please provide a JSON response with the following structure:
    {
      "overallScore": number (0-100),
      "scores": {
        "formatting": number (0-100),
        "keywords": number (0-100),
        "sections": number (0-100),
        "content": number (0-100)
      },
      "recommendations": [
        {
          "category": "string",
          "issue": "string",
          "suggestion": "string",
          "priority": "high|medium|low"
        }
      ],
      "strengths": ["string"],
      "keywordDensity": {
        "total": number,
        "relevant": number,
        "missing": ["string"]
      }
    }

    Focus on:
    1. ATS-friendly formatting
    2. Keyword optimization
    3. Section completeness
    4. Content quality and relevance
    5. Industry-specific requirements
    `;

    const result = await model.generateContent(prompt);
    const response = await result.response;
    const text = response.text();
    
    // Parse JSON response
    try {
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }
    } catch (parseError) {
      console.error('Error parsing ATS analysis:', parseError);
    }

    // Fallback response if parsing fails
    return {
      overallScore: 75,
      scores: {
        formatting: 80,
        keywords: 70,
        sections: 75,
        content: 75
      },
      recommendations: [
        {
          category: "keywords",
          issue: "Could use more industry-specific keywords",
          suggestion: "Add relevant technical skills and industry terms",
          priority: "medium"
        }
      ],
      strengths: ["Clear structure", "Professional formatting"],
      keywordDensity: {
        total: 0,
        relevant: 0,
        missing: ["industry-specific terms"]
      }
    };
  } catch (error) {
    console.error('Error analyzing resume with ATS:', error);
    throw new Error('Failed to analyze resume. Please try again.');
  }
};

// Content Enhancement Service
export const enhanceContent = async (content, type, context = {}) => {
  try {
    let prompt = '';
    
    switch (type) {
      case 'summary':
        prompt = `
        Enhance this professional summary for maximum ATS impact:
        
        Original: "${content}"
        
        Context:
        - Industry: ${context.industry || 'General'}
        - Experience Level: ${context.experienceLevel || 'Mid-level'}
        - Target Role: ${context.targetRole || 'Professional'}
        
        Provide 3 enhanced versions that are:
        1. ATS-optimized with relevant keywords
        2. Compelling and professional
        3. 2-3 sentences each
        4. Tailored to the industry and role
        
        Return as JSON:
        {
          "variations": [
            {
              "title": "Professional & Direct",
              "content": "enhanced summary text"
            },
            {
              "title": "Achievement-Focused",
              "content": "enhanced summary text"
            },
            {
              "title": "Skills-Emphasized",
              "content": "enhanced summary text"
            }
          ]
        }
        `;
        break;
        
      case 'experience':
        prompt = `
        Enhance this job description for ATS optimization:
        
        Original: "${content}"
        
        Context:
        - Job Title: ${context.title || 'Professional'}
        - Company: ${context.company || 'Company'}
        - Industry: ${context.industry || 'General'}
        
        Provide 3 enhanced versions with:
        1. Strong action verbs
        2. Quantified achievements where possible
        3. Industry-relevant keywords
        4. ATS-friendly formatting
        
        Return as JSON:
        {
          "variations": [
            {
              "title": "Achievement-Focused",
              "content": "enhanced bullet points"
            },
            {
              "title": "Skills-Emphasized",
              "content": "enhanced bullet points"
            },
            {
              "title": "Results-Driven",
              "content": "enhanced bullet points"
            }
          ]
        }
        `;
        break;
        
      case 'skills':
        prompt = `
        Suggest relevant skills for this professional profile:
        
        Current Skills: ${Array.isArray(content) ? content.join(', ') : content}
        
        Context:
        - Industry: ${context.industry || 'General'}
        - Role: ${context.role || 'Professional'}
        - Experience Level: ${context.experienceLevel || 'Mid-level'}
        
        Provide skill suggestions in categories:
        
        Return as JSON:
        {
          "suggestions": {
            "technical": ["skill1", "skill2"],
            "soft": ["skill1", "skill2"],
            "industry": ["skill1", "skill2"],
            "trending": ["skill1", "skill2"]
          }
        }
        `;
        break;
        
      default:
        throw new Error('Invalid content type');
    }

    const result = await model.generateContent(prompt);
    const response = await result.response;
    const text = response.text();
    
    // Parse JSON response
    try {
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }
    } catch (parseError) {
      console.error('Error parsing content enhancement:', parseError);
    }

    // Fallback response
    return {
      variations: [
        {
          title: "Enhanced Version",
          content: content
        }
      ]
    };
  } catch (error) {
    console.error('Error enhancing content:', error);
    throw new Error('Failed to enhance content. Please try again.');
  }
};

// Job Matching Service
export const getJobMatchScore = async (resumeData, jobDescription) => {
  try {
    const prompt = `
    Analyze how well this resume matches the job description:
    
    Resume Data:
    ${JSON.stringify(resumeData, null, 2)}
    
    Job Description:
    "${jobDescription}"
    
    Provide a JSON response:
    {
      "matchScore": number (0-100),
      "matchedSkills": ["skill1", "skill2"],
      "missingSkills": ["skill1", "skill2"],
      "recommendations": [
        {
          "action": "string",
          "description": "string",
          "impact": "high|medium|low"
        }
      ],
      "keywordAlignment": number (0-100)
    }
    `;

    const result = await model.generateContent(prompt);
    const response = await result.response;
    const text = response.text();
    
    try {
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }
    } catch (parseError) {
      console.error('Error parsing job match analysis:', parseError);
    }

    return {
      matchScore: 75,
      matchedSkills: [],
      missingSkills: [],
      recommendations: [],
      keywordAlignment: 75
    };
  } catch (error) {
    console.error('Error analyzing job match:', error);
    throw new Error('Failed to analyze job match. Please try again.');
  }
};

// Fallback Functions for when AI is unavailable
const getFallbackATSAnalysis = () => ({
  overallScore: 75,
  scores: {
    formatting: 80,
    keywords: 70,
    sections: 75,
    content: 75
  },
  recommendations: [
    {
      category: "keywords",
      issue: "AI analysis temporarily unavailable",
      suggestion: "Continue building your resume. AI features will be restored shortly.",
      priority: "low"
    }
  ],
  strengths: ["Professional structure", "Clear formatting"],
  keywordDensity: {
    total: 0,
    relevant: 0,
    missing: ["AI analysis pending"]
  }
});

const getFallbackContentEnhancement = (content, type) => ({
  variations: [
    {
      title: "Original Content",
      content: content || "Please add your content here."
    },
    {
      title: "Enhanced Version",
      content: content ? `${content} (AI enhancement will be available shortly)` : "AI enhancement temporarily unavailable."
    }
  ]
});

// Export fallback functions for testing
export { getFallbackATSAnalysis, getFallbackContentEnhancement };

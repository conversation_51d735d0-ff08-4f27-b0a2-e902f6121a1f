import "./globals.css";
import { Toaster } from "react-hot-toast";
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import VisitorTracker from "@/components/common/VisitorTracker";
import { AuthProvider } from "@/contexts/AuthContext";
import { AdminProvider } from "@/contexts/AdminContext";
import { GoogleAnalytics } from '@next/third-parties/google';

export const metadata = {
  title: "BlinkFind AI",
  description: "BlinkFind is transforming into a cutting-edge startup dedicated to identifying and solving realworld problems through innovative solutions. Our focus is on addressing challenges faced by users, businesses, and communities by developing faster, more secure, and optimized applications, websites, AI-driven solutions, and more.",
  icons: {
    icon: '/logo.svg',
    shortcut: '/logo.svg',
    apple: '/logo.svg',
  },
};

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <head>
        <link
          href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&family=Poppins:wght@400;600&display=swap"
          rel="stylesheet"
        />
      </head>
      <body className={`font-sans min-h-screen flex flex-col`} suppressHydrationWarning={true}>
        <AuthProvider>
          <AdminProvider>
            <VisitorTracker />
            <Navbar />
            <main className="flex-grow">
              <Toaster position="top-right"/>
              <GoogleAnalytics gaId="G-L435R9BL3T" />
              {children}
            </main>
            <Footer />
          </AdminProvider>
        </AuthProvider>
      </body>
    </html>
  );
}

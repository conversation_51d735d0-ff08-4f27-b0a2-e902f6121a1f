'use client'
import { motion } from 'framer-motion'
import { Play, Smartphone, Search, Heart, FileText, Zap, Target } from 'lucide-react'
import { SparklesIcon } from '@heroicons/react/24/solid'
import { useState } from 'react'

const ProductShowcase = () => {
  const [isQuickFindPlaying, setIsQuickFindPlaying] = useState(false)
  const [isResumeBuilderPlaying, setIsResumeBuilderPlaying] = useState(false)

  const quickFindFeatures = [
    {
      icon: Search,
      title: "Easy Reporting",
      description: "Found something valuable? Upload it and help someone reconnect."
    },
    {
      icon: Smartphone,
      title: "Simple Search",
      description: "Effortlessly find your lost items through easy search filters."
    },
    {
      icon: Heart,
      title: "Emotional Value",
      description: "Restoring memories and connections that can never be replaced."
    }
  ]

  const resumeBuilderFeatures = [
    {
      icon: FileText,
      title: "AI-Powered Writing",
      description: "Generate professional content tailored to your experience and industry."
    },
    {
      icon: Zap,
      title: "Instant Creation",
      description: "Build a complete resume in minutes with our intelligent templates."
    },
    {
      icon: Target,
      title: "ATS Optimized",
      description: "Ensure your resume passes through applicant tracking systems."
    }
  ]

  return (
    <section className="py-20 bg-gradient-to-b from-black to-[#0A0A0A]">
      <div className="container mx-auto px-6">
        <div className="text-center mb-16">
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="flex items-center justify-center gap-2 mb-4"
          >
            <SparklesIcon className="h-5 w-5 text-primary" />
            <span className="text-sm font-medium text-primary">OUR PRODUCTS</span>
          </motion.div>
          
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            viewport={{ once: true }}
            className="text-3xl md:text-4xl font-bold text-white"
          >
            Innovative AI Solutions for Everyday Challenges
          </motion.h2>
        </div>       

        {/* AI Resume Builder Product Section */}
        <div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              viewport={{ once: true }}
              className="relative aspect-[9/16] max-w-[300px] mx-auto lg:mx-0 order-2 lg:order-1"
            >
              <div className="glass-effect rounded-2xl overflow-hidden border border-white/10 h-full">
                {isResumeBuilderPlaying ? (
                  <video
                    className="absolute inset-0 w-full h-full object-cover"
                    src="/Videos/resume_builder_demo.mp4"
                    title="AI Resume Builder Demo"
                    autoPlay
                    controls
                    muted
                    loop
                  />
                ) : (
                  <div className="absolute inset-0 flex items-center justify-center bg-black/50">
                    <button
                      onClick={() => setIsResumeBuilderPlaying(true)}
                      className="bg-gradient-to-r from-primary to-purple-600 text-white rounded-full p-4 transition-transform transform hover:scale-110"
                    >
                      <Play className="h-8 w-8" />
                    </button>
                  </div>
                )}
              </div>
            </motion.div>

            <div className="order-1 lg:order-2">
              <motion.h3
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
                viewport={{ once: true }}
                className="text-2xl font-bold text-white mb-6"
              >
                AI Resume Builder: Professional Resumes in Minutes
              </motion.h3>

              <div className="space-y-6">
                {resumeBuilderFeatures.map((feature, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.3 + index * 0.1 }}
                    viewport={{ once: true }}
                    className="flex items-start gap-4"
                  >
                    <div className="bg-primary/10 p-2 rounded-lg">
                      <feature.icon className="h-5 w-5 text-primary" />
                    </div>
                    <div>
                      <h4 className="text-lg font-semibold text-white">{feature.title}</h4>
                      <p className="text-gray-400">{feature.description}</p>
                    </div>
                  </motion.div>
                ))}
              </div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.6 }}
                viewport={{ once: true }}
                className="mt-8"
              >
                <a
                  href="/resume-builder"
                  className="inline-flex items-center justify-center px-6 py-3 bg-gradient-to-r from-primary to-purple-600 text-white rounded-lg font-medium hover:opacity-90 transition-opacity"
                >
                  Try AI Resume Builder
                </a>
              </motion.div>
            </div>
          </div>
           {/* QuickFind Product Section */}
        <div className="mb-24">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <motion.h3
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
                viewport={{ once: true }}
                className="text-2xl font-bold text-white mb-6"
              >
                QuickFind: AI-Powered Lost & Found Platform
              </motion.h3>

              <div className="space-y-6">
                {quickFindFeatures.map((feature, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.3 + index * 0.1 }}
                    viewport={{ once: true }}
                    className="flex items-start gap-4"
                  >
                    <div className="bg-primary/10 p-2 rounded-lg">
                      <feature.icon className="h-5 w-5 text-primary" />
                    </div>
                    <div>
                      <h4 className="text-lg font-semibold text-white">{feature.title}</h4>
                      <p className="text-gray-400">{feature.description}</p>
                    </div>
                  </motion.div>
                ))}
              </div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.6 }}
                viewport={{ once: true }}
                className="mt-8"
              >
                <a
                  href="/quickfind"
                  className="inline-flex items-center justify-center px-6 py-3 bg-gradient-to-r from-primary to-purple-600 text-white rounded-lg font-medium hover:opacity-90 transition-opacity"
                >
                  Learn More About QuickFind
                </a>
              </motion.div>
            </div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              viewport={{ once: true }}
              className="relative aspect-[9/16] max-w-[300px] mx-auto lg:mx-0"
            >
              <div className="glass-effect rounded-2xl overflow-hidden border border-white/10 h-full">
                {isQuickFindPlaying ? (
                  <video
                    className="absolute inset-0 w-full h-full object-cover"
                    src="/Videos/quickFind_video.mp4"
                    title="QuickFind App Demo"
                    autoPlay
                    controls
                    muted
                    loop
                  />
                ) : (
                  <div className="absolute inset-0 flex items-center justify-center bg-black/50">
                    <button
                      onClick={() => setIsQuickFindPlaying(true)}
                      className="bg-gradient-to-r from-primary to-purple-600 text-white rounded-full p-4 transition-transform transform hover:scale-110"
                    >
                      <Play className="h-8 w-8" />
                    </button>
                  </div>
                )}
              </div>
            </motion.div>
          </div>
        </div>
        </div>
      </div>
    </section>
  )
}

export default ProductShowcase
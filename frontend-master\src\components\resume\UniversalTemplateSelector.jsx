'use client';
import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Search, Filter, Check, Star, Eye, 
  Briefcase, Heart, GraduationCap, Palette, 
  Code, TrendingUp, Users, Wrench, Building
} from 'lucide-react';
import { INDUSTRY_TEMPLATES, INDUSTRY_CATEGORIES } from './templates/MultiIndustryTemplates';

const UniversalTemplateSelector = ({ 
  selectedTemplate, 
  onTemplateSelect, 
  onClose,
  formData 
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedIndustry, setSelectedIndustry] = useState('All');
  const [viewMode, setViewMode] = useState('grid'); // grid or list

  // Industry icons mapping
  const industryIcons = {
    'Business & Corporate': Briefcase,
    'Healthcare': Heart,
    'Education': GraduationCap,
    'Creative & Design': Palette,
    'Technology': Code,
    'Sales & Customer Service': TrendingUp,
    'Hospitality & Retail': Users,
    'Finance & Accounting': Building,
    'Manufacturing & Operations': Wrench,
    'Non-Profit & Social Services': Heart
  };

  // Filter templates based on search and industry
  const filteredTemplates = Object.values(INDUSTRY_TEMPLATES).filter(template => {
    const matchesSearch = template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         template.industry.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         template.description.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesIndustry = selectedIndustry === 'All' || template.industry === selectedIndustry;
    
    return matchesSearch && matchesIndustry;
  });

  // Get unique industries
  const industries = ['All', ...Object.keys(INDUSTRY_CATEGORIES)];

  const handleTemplateSelect = (templateId) => {
    onTemplateSelect(templateId);
    onClose();
  };

  const TemplateCard = ({ template }) => {
    const isSelected = selectedTemplate === template.id;
    
    return (
      <motion.div
        layout
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
        whileHover={{ y: -4 }}
        className={`
          relative bg-white rounded-xl border-2 cursor-pointer transition-all duration-200 overflow-hidden
          ${isSelected 
            ? 'border-blue-500 shadow-lg ring-2 ring-blue-200' 
            : 'border-gray-200 hover:border-gray-300 hover:shadow-md'
          }
        `}
        onClick={() => handleTemplateSelect(template.id)}
      >
        {/* Template Preview */}
        <div className="aspect-[3/4] bg-gray-50 relative overflow-hidden">
          <div 
            className="absolute inset-0 opacity-10"
            style={{ backgroundColor: template.color }}
          />
          
          {/* Mock Resume Content */}
          <div className="p-3 text-xs space-y-2">
            <div className="text-center border-b pb-2" style={{ borderColor: template.color }}>
              <div className="h-3 bg-gray-800 rounded mb-1"></div>
              <div className="h-2 bg-gray-600 rounded w-3/4 mx-auto"></div>
            </div>
            
            <div className="space-y-1">
              <div className="h-2 bg-gray-700 rounded w-1/3"></div>
              <div className="h-1 bg-gray-500 rounded"></div>
              <div className="h-1 bg-gray-500 rounded w-4/5"></div>
            </div>
            
            <div className="space-y-1">
              <div className="h-2 bg-gray-700 rounded w-1/2"></div>
              <div className="h-1 bg-gray-500 rounded w-3/4"></div>
              <div className="h-1 bg-gray-500 rounded w-2/3"></div>
            </div>
          </div>

          {/* Selection Indicator */}
          {isSelected && (
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              className="absolute top-2 right-2 w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center"
            >
              <Check className="h-4 w-4 text-white" />
            </motion.div>
          )}
        </div>

        {/* Template Info */}
        <div className="p-4">
          <div className="flex items-start justify-between mb-2">
            <h3 className="font-semibold text-gray-900 text-sm">{template.name}</h3>
            <div className="flex items-center gap-1">
              <Star className="h-3 w-3 text-yellow-500 fill-current" />
              <span className="text-xs text-gray-500">4.8</span>
            </div>
          </div>
          
          <p className="text-xs text-gray-600 mb-3 line-clamp-2">
            {template.description}
          </p>
          
          <div className="flex items-center justify-between">
            <span 
              className="text-xs px-2 py-1 rounded-full text-white font-medium"
              style={{ backgroundColor: template.color }}
            >
              {template.industry}
            </span>
            
            <div className="flex items-center gap-1">
              {template.features.slice(0, 2).map((feature, index) => (
                <span 
                  key={index}
                  className="text-xs px-2 py-0.5 bg-gray-100 text-gray-600 rounded"
                >
                  {feature}
                </span>
              ))}
            </div>
          </div>
        </div>
      </motion.div>
    );
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        className="bg-white rounded-2xl shadow-2xl w-full max-w-6xl max-h-[90vh] flex flex-col"
      >
        {/* Header */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h2 className="text-2xl font-bold text-gray-900">Choose Your Template</h2>
              <p className="text-gray-600">Select a template that matches your industry and style</p>
            </div>
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
            >
              ×
            </button>
          </div>

          {/* Search and Filters */}
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search templates..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            
            <select
              value={selectedIndustry}
              onChange={(e) => setSelectedIndustry(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              {industries.map(industry => (
                <option key={industry} value={industry}>
                  {industry === 'All' ? 'All Industries' : industry}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Industry Quick Filters */}
        <div className="px-6 py-4 border-b border-gray-100 bg-gray-50">
          <div className="flex flex-wrap gap-2">
            {industries.slice(0, 6).map(industry => {
              const Icon = industryIcons[industry] || Briefcase;
              const isSelected = selectedIndustry === industry;
              
              return (
                <button
                  key={industry}
                  onClick={() => setSelectedIndustry(industry)}
                  className={`
                    flex items-center gap-2 px-3 py-1.5 rounded-full text-sm font-medium transition-colors
                    ${isSelected 
                      ? 'bg-blue-600 text-white' 
                      : 'bg-white text-gray-600 hover:bg-gray-100 border border-gray-200'
                    }
                  `}
                >
                  {industry !== 'All' && <Icon className="h-4 w-4" />}
                  {industry === 'All' ? 'All' : industry.split(' ')[0]}
                </button>
              );
            })}
          </div>
        </div>

        {/* Templates Grid */}
        <div className="flex-1 overflow-auto p-6">
          <div className="mb-4 flex items-center justify-between">
            <p className="text-sm text-gray-600">
              {filteredTemplates.length} template{filteredTemplates.length !== 1 ? 's' : ''} found
            </p>
          </div>

          <motion.div 
            layout
            className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
          >
            <AnimatePresence>
              {filteredTemplates.map(template => (
                <TemplateCard key={template.id} template={template} />
              ))}
            </AnimatePresence>
          </motion.div>

          {filteredTemplates.length === 0 && (
            <div className="text-center py-12">
              <div className="text-gray-400 mb-4">
                <Search className="h-12 w-12 mx-auto" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No templates found</h3>
              <p className="text-gray-600">Try adjusting your search or filter criteria</p>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-gray-200 bg-gray-50">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-600">
              All templates are ATS-friendly and professionally designed
            </div>
            <div className="flex gap-3">
              <button
                onClick={onClose}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={() => handleTemplateSelect(selectedTemplate)}
                className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Use Selected Template
              </button>
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default UniversalTemplateSelector;

'use client';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  User, 
  GraduationCap, 
  Briefcase, 
  Award, 
  FileText, 
  Check, 
  AlertCircle,
  ChevronRight,
  Target
} from 'lucide-react';

const EnhancedStepNavigation = ({ 
  currentStep, 
  totalSteps, 
  steps, 
  onStepClick, 
  allowClickNavigation = true,
  completedSteps = [],
  stepValidation = {},
  estimatedTime = {}
}) => {
  const getStepStatus = (stepIndex) => {
    if (completedSteps.includes(stepIndex)) return 'completed';
    if (stepIndex === currentStep) return 'current';
    if (stepIndex < currentStep) return 'completed';
    if (stepValidation[stepIndex] === false) return 'error';
    return 'upcoming';
  };

  const getStepIcon = (step, status) => {
    const IconComponent = step.icon;
    
    if (status === 'completed') {
      return <Check className="h-5 w-5" />;
    }
    
    if (status === 'error') {
      return <AlertCircle className="h-5 w-5" />;
    }
    
    return <IconComponent className="h-5 w-5" />;
  };

  const getStepColors = (status) => {
    switch (status) {
      case 'completed':
        return {
          bg: 'bg-green-500',
          border: 'border-green-500',
          text: 'text-white',
          icon: 'text-white'
        };
      case 'current':
        return {
          bg: 'bg-neural-purple',
          border: 'border-neural-purple',
          text: 'text-white',
          icon: 'text-white'
        };
      case 'error':
        return {
          bg: 'bg-red-500/20',
          border: 'border-red-500',
          text: 'text-red-400',
          icon: 'text-red-400'
        };
      default:
        return {
          bg: 'bg-gray-800',
          border: 'border-gray-600',
          text: 'text-gray-400',
          icon: 'text-gray-400'
        };
    }
  };

  return (
    <div className="w-full max-w-4xl mx-auto">
      {/* Mobile Step Navigation */}
      <div className="md:hidden mb-6">
        <div className="bg-gray-900/60 backdrop-blur-md rounded-xl p-4 border border-white/10">
          <div className="flex items-center justify-between mb-3">
            <span className="text-sm text-gray-400">Step {currentStep + 1} of {totalSteps}</span>
            <span className="text-sm text-neural-purple font-medium">
              {Math.round(((currentStep + 1) / totalSteps) * 100)}% Complete
            </span>
          </div>
          
          {/* Progress Bar */}
          <div className="w-full bg-gray-700 rounded-full h-2 mb-4">
            <motion.div
              className="bg-gradient-to-r from-neural-purple to-neural-pink h-2 rounded-full"
              initial={{ width: 0 }}
              animate={{ width: `${((currentStep + 1) / totalSteps) * 100}%` }}
              transition={{ duration: 0.5, ease: "easeOut" }}
            />
          </div>

          {/* Current Step Info */}
          <div className="flex items-center gap-3">
            <div className={`w-10 h-10 rounded-full flex items-center justify-center ${getStepColors(getStepStatus(currentStep)).bg}`}>
              {getStepIcon(steps[currentStep], getStepStatus(currentStep))}
            </div>
            <div>
              <h3 className="font-semibold text-white">{steps[currentStep].title}</h3>
              <p className="text-sm text-gray-400">{steps[currentStep].description}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Desktop Step Navigation */}
      <div className="hidden md:block">
        <div className="bg-gray-900/60 backdrop-blur-md rounded-2xl p-6 border border-white/10 mb-8">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-bold text-white">Resume Builder Progress</h2>
            <div className="flex items-center gap-2 text-sm text-gray-400">
              <Target className="h-4 w-4" />
              <span>{completedSteps.length} of {totalSteps} completed</span>
            </div>
          </div>

          <div className="relative">
            {/* Progress Line */}
            <div className="absolute top-6 left-6 right-6 h-0.5 bg-gray-700">
              <motion.div
                className="h-full bg-gradient-to-r from-neural-purple to-neural-pink"
                initial={{ width: 0 }}
                animate={{ width: `${(currentStep / (totalSteps - 1)) * 100}%` }}
                transition={{ duration: 0.8, ease: "easeOut" }}
              />
            </div>

            {/* Steps */}
            <div className="relative flex justify-between">
              {steps.map((step, index) => {
                const status = getStepStatus(index);
                const colors = getStepColors(status);
                const isClickable = allowClickNavigation && (index <= currentStep || completedSteps.includes(index));

                return (
                  <motion.div
                    key={step.id}
                    className="flex flex-col items-center"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                  >
                    {/* Step Circle */}
                    <motion.button
                      onClick={() => isClickable && onStepClick && onStepClick(index)}
                      disabled={!isClickable}
                      className={`
                        relative w-12 h-12 rounded-full flex items-center justify-center
                        border-2 transition-all duration-300 z-10
                        ${colors.bg} ${colors.border}
                        ${isClickable ? 'cursor-pointer hover:scale-110 hover:shadow-lg' : 'cursor-not-allowed'}
                        ${status === 'current' ? 'shadow-lg shadow-neural-purple/50' : ''}
                      `}
                      whileHover={isClickable ? { scale: 1.1 } : {}}
                      whileTap={isClickable ? { scale: 0.95 } : {}}
                    >
                      <span className={colors.icon}>
                        {getStepIcon(step, status)}
                      </span>
                      
                      {/* Pulse animation for current step */}
                      {status === 'current' && (
                        <motion.div
                          className="absolute inset-0 rounded-full bg-neural-purple"
                          animate={{ scale: [1, 1.2, 1], opacity: [0.5, 0, 0.5] }}
                          transition={{ duration: 2, repeat: Infinity }}
                        />
                      )}
                    </motion.button>

                    {/* Step Info */}
                    <div className="mt-3 text-center max-w-[120px]">
                      <h3 className={`text-sm font-medium ${status === 'current' ? 'text-white' : status === 'completed' ? 'text-green-400' : status === 'error' ? 'text-red-400' : 'text-gray-400'}`}>
                        {step.title}
                      </h3>
                      <p className="text-xs text-gray-500 mt-1 leading-tight">
                        {step.description}
                      </p>
                      
                      {/* Status Indicator */}
                      <AnimatePresence>
                        {status === 'completed' && (
                          <motion.div
                            initial={{ opacity: 0, scale: 0 }}
                            animate={{ opacity: 1, scale: 1 }}
                            exit={{ opacity: 0, scale: 0 }}
                            className="flex items-center justify-center gap-1 mt-1"
                          >
                            <Check className="h-3 w-3 text-green-400" />
                            <span className="text-xs text-green-400">Complete</span>
                          </motion.div>
                        )}
                        
                        {status === 'error' && (
                          <motion.div
                            initial={{ opacity: 0, scale: 0 }}
                            animate={{ opacity: 1, scale: 1 }}
                            className="flex items-center justify-center gap-1 mt-1"
                          >
                            <AlertCircle className="h-3 w-3 text-red-400" />
                            <span className="text-xs text-red-400">Issues</span>
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </div>

                    {/* Next Step Arrow */}
                    {index < steps.length - 1 && (
                      <ChevronRight className="absolute top-6 -right-4 h-4 w-4 text-gray-600 z-0" />
                    )}
                  </motion.div>
                );
              })}
            </div>
          </div>

          {/* Quick Stats */}
          <div className="mt-6 grid grid-cols-2 gap-4">
            <div className="text-center p-3 bg-gray-800/50 rounded-lg">
              <div className="text-lg font-bold text-green-400">{completedSteps.length}</div>
              <div className="text-xs text-gray-400">Completed</div>
            </div>
            <div className="text-center p-3 bg-gray-800/50 rounded-lg">
              <div className="text-lg font-bold text-neural-purple">{totalSteps - completedSteps.length}</div>
              <div className="text-xs text-gray-400">Remaining</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EnhancedStepNavigation;
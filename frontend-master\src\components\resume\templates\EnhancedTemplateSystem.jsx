'use client';
import { motion } from 'framer-motion';

// Enhanced Template System with Modern ATS-Optimized Designs
export const ENHANCED_TEMPLATES = {
  // Classic ATS-Optimized Templates
  classic_ats: {
    id: 'classic_ats',
    name: 'Classic ATS',
    category: 'ats-optimized',
    atsScore: 100,
    description: 'Maximum ATS compatibility with traditional formatting',
    features: ['Single column', 'Standard fonts', 'Clear sections', 'No graphics'],
    preview: '/templates/classic-ats.png',
    isPremium: false
  },

  modern_minimal: {
    id: 'modern_minimal',
    name: 'Modern Minimal',
    category: 'modern',
    atsScore: 98,
    description: 'Clean, contemporary design with subtle styling',
    features: ['Minimal design', 'Modern typography', 'Subtle accents', 'ATS-friendly'],
    preview: '/templates/modern-minimal.png',
    isPremium: false
  },

  professional_executive: {
    id: 'professional_executive',
    name: 'Professional Executive',
    category: 'executive',
    atsScore: 96,
    description: 'Sophisticated layout for senior-level positions',
    features: ['Executive styling', 'Leadership focus', 'Premium look', 'ATS-compatible'],
    preview: '/templates/professional-executive.png',
    isPremium: true
  },

  // Industry-Specific Templates
  tech_developer: {
    id: 'tech_developer',
    name: 'Tech Developer',
    category: 'tech',
    atsScore: 97,
    description: 'Optimized for software development roles',
    features: ['Skills-focused', 'Project highlights', 'Tech-friendly', 'GitHub integration'],
    preview: '/templates/tech-developer.png',
    isPremium: false
  },

  creative_professional: {
    id: 'creative_professional',
    name: 'Creative Professional',
    category: 'creative',
    atsScore: 90,
    description: 'Balanced creativity with ATS compatibility',
    features: ['Creative elements', 'Portfolio focus', 'Visual appeal', 'ATS-safe'],
    preview: '/templates/creative-professional.png',
    isPremium: true
  },

  healthcare_medical: {
    id: 'healthcare_medical',
    name: 'Healthcare Medical',
    category: 'healthcare',
    atsScore: 95,
    description: 'Tailored for medical and healthcare professionals',
    features: ['Certification focus', 'Medical formatting', 'Clean layout', 'ATS-optimized'],
    preview: '/templates/healthcare-medical.png',
    isPremium: true
  },

  // Regional Templates
  european_cv: {
    id: 'european_cv',
    name: 'European CV',
    category: 'regional',
    atsScore: 94,
    description: 'European CV format with photo placement',
    features: ['Photo section', 'EU standards', 'Language skills', 'Europass compatible'],
    preview: '/templates/european-cv.png',
    isPremium: false
  },

  uk_professional: {
    id: 'uk_professional',
    name: 'UK Professional',
    category: 'regional',
    atsScore: 96,
    description: 'British CV format with personal statement',
    features: ['Personal statement', 'UK standards', 'A4 optimized', 'Traditional'],
    preview: '/templates/uk-professional.png',
    isPremium: false
  },

  // Modern Variants
  contemporary_edge: {
    id: 'contemporary_edge',
    name: 'Contemporary Edge',
    category: 'modern',
    atsScore: 92,
    description: 'Modern design with subtle visual elements',
    features: ['Contemporary style', 'Visual hierarchy', 'Color accents', 'ATS-friendly'],
    preview: '/templates/contemporary-edge.png',
    isPremium: true
  },

  minimalist_pro: {
    id: 'minimalist_pro',
    name: 'Minimalist Pro',
    category: 'minimal',
    atsScore: 99,
    description: 'Ultra-clean design focusing on content',
    features: ['Minimal styling', 'Content focus', 'Perfect spacing', 'Maximum ATS'],
    preview: '/templates/minimalist-pro.png',
    isPremium: false
  }
};

// Template rendering function with enhanced formatting
export const renderEnhancedTemplate = (templateId, formData) => {
  const template = ENHANCED_TEMPLATES[templateId];
  if (!template) return null;

  // Common styles for all templates
  const commonStyles = {
    fontFamily: "'Inter', 'Helvetica Neue', Arial, sans-serif",
    fontSize: '11pt',
    lineHeight: '1.4',
    color: '#000000',
    margin: 0,
    padding: '0.5in',
    backgroundColor: '#ffffff'
  };

  // Template-specific rendering
  switch (templateId) {
    case 'classic_ats':
      return renderClassicATS(formData, commonStyles);
    case 'modern_minimal':
      return renderModernMinimal(formData, commonStyles);
    case 'professional_executive':
      return renderProfessionalExecutive(formData, commonStyles);
    case 'tech_developer':
      return renderTechDeveloper(formData, commonStyles);
    case 'creative_professional':
      return renderCreativeProfessional(formData, commonStyles);
    case 'healthcare_medical':
      return renderHealthcareMedical(formData, commonStyles);
    case 'european_cv':
      return renderEuropeanCV(formData, commonStyles);
    case 'uk_professional':
      return renderUKProfessional(formData, commonStyles);
    case 'contemporary_edge':
      return renderContemporaryEdge(formData, commonStyles);
    case 'minimalist_pro':
      return renderMinimalistPro(formData, commonStyles);
    default:
      return renderClassicATS(formData, commonStyles);
  }
};

// Classic ATS Template - Maximum compatibility
const renderClassicATS = (formData, baseStyles) => {
  return (
    <div style={{
      ...baseStyles,
      maxWidth: '8.5in',
      minHeight: '11in',
      margin: '0 auto',
      padding: '0.75in'
    }}>
      {/* Header */}
      <div style={{ textAlign: 'center', marginBottom: '20pt' }}>
        <h1 style={{
          fontSize: '18pt',
          fontWeight: 'bold',
          margin: '0 0 8pt 0',
          textTransform: 'uppercase',
          letterSpacing: '1pt'
        }}>
          {formData.personal.firstName} {formData.personal.lastName}
        </h1>
        
        <div style={{
          fontSize: '10pt',
          color: '#333333',
          lineHeight: '1.3'
        }}>
          {formData.personal.email && <span>{formData.personal.email}</span>}
          {formData.personal.phone && <span> • {formData.personal.phone}</span>}
          {formData.personal.location && <span> • {formData.personal.location}</span>}
          {formData.personal.linkedin && <span> • {formData.personal.linkedin}</span>}
        </div>
      </div>

      {/* Professional Summary */}
      {formData.personal.summary && (
        <div style={{ marginBottom: '16pt' }}>
          <h2 style={{
            fontSize: '12pt',
            fontWeight: 'bold',
            textTransform: 'uppercase',
            borderBottom: '1pt solid #000000',
            paddingBottom: '2pt',
            marginBottom: '8pt'
          }}>
            Professional Summary
          </h2>
          <p style={{
            fontSize: '11pt',
            lineHeight: '1.4',
            textAlign: 'justify',
            margin: 0
          }}>
            {formData.personal.summary}
          </p>
        </div>
      )}

      {/* Experience */}
      {formData.experience && formData.experience.length > 0 && (
        <div style={{ marginBottom: '16pt' }}>
          <h2 style={{
            fontSize: '12pt',
            fontWeight: 'bold',
            textTransform: 'uppercase',
            borderBottom: '1pt solid #000000',
            paddingBottom: '2pt',
            marginBottom: '8pt'
          }}>
            Professional Experience
          </h2>
          {formData.experience.map((exp, index) => (
            <div key={index} style={{ marginBottom: '12pt' }}>
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'baseline',
                marginBottom: '2pt'
              }}>
                <h3 style={{
                  fontSize: '11pt',
                  fontWeight: 'bold',
                  margin: 0
                }}>
                  {exp.title}
                </h3>
                <span style={{
                  fontSize: '10pt',
                  color: '#333333'
                }}>
                  {exp.startDate} - {exp.current ? 'Present' : exp.endDate}
                </span>
              </div>
              <div style={{
                fontSize: '11pt',
                fontWeight: '600',
                color: '#333333',
                marginBottom: '4pt'
              }}>
                {exp.company} {exp.location && `• ${exp.location}`}
              </div>
              {exp.description && (
                <div style={{
                  fontSize: '11pt',
                  lineHeight: '1.4',
                  textAlign: 'justify'
                }}>
                  {exp.description.split('\n').map((line, lineIndex) => (
                    <div key={lineIndex} style={{ marginBottom: '2pt' }}>
                      {line.trim().startsWith('•') ? line : `• ${line}`}
                    </div>
                  ))}
                </div>
              )}
            </div>
          ))}
        </div>
      )}

      {/* Education */}
      {formData.education && formData.education.length > 0 && (
        <div style={{ marginBottom: '16pt' }}>
          <h2 style={{
            fontSize: '12pt',
            fontWeight: 'bold',
            textTransform: 'uppercase',
            borderBottom: '1pt solid #000000',
            paddingBottom: '2pt',
            marginBottom: '8pt'
          }}>
            Education
          </h2>
          {formData.education.map((edu, index) => (
            <div key={index} style={{ marginBottom: '8pt' }}>
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'baseline'
              }}>
                <div>
                  <span style={{
                    fontSize: '11pt',
                    fontWeight: 'bold'
                  }}>
                    {edu.degree}
                  </span>
                  <span style={{
                    fontSize: '11pt',
                    color: '#333333'
                  }}>
                    {edu.field && ` in ${edu.field}`}
                  </span>
                </div>
                <span style={{
                  fontSize: '10pt',
                  color: '#333333'
                }}>
                  {edu.graduationDate}
                </span>
              </div>
              <div style={{
                fontSize: '11pt',
                color: '#333333'
              }}>
                {edu.institution} {edu.location && `• ${edu.location}`}
              </div>
              {edu.gpa && (
                <div style={{
                  fontSize: '10pt',
                  color: '#333333'
                }}>
                  GPA: {edu.gpa}
                </div>
              )}
            </div>
          ))}
        </div>
      )}

      {/* Skills */}
      {formData.skills && (
        <div style={{ marginBottom: '16pt' }}>
          <h2 style={{
            fontSize: '12pt',
            fontWeight: 'bold',
            textTransform: 'uppercase',
            borderBottom: '1pt solid #000000',
            paddingBottom: '2pt',
            marginBottom: '8pt'
          }}>
            Skills
          </h2>
          
          {formData.skills.technical && formData.skills.technical.length > 0 && (
            <div style={{ marginBottom: '6pt' }}>
              <span style={{ fontWeight: 'bold', fontSize: '11pt' }}>Technical: </span>
              <span style={{ fontSize: '11pt' }}>
                {formData.skills.technical.join(', ')}
              </span>
            </div>
          )}
          
          {formData.skills.languages && formData.skills.languages.length > 0 && (
            <div style={{ marginBottom: '6pt' }}>
              <span style={{ fontWeight: 'bold', fontSize: '11pt' }}>Languages: </span>
              <span style={{ fontSize: '11pt' }}>
                {formData.skills.languages.join(', ')}
              </span>
            </div>
          )}
          
          {formData.skills.certifications && formData.skills.certifications.length > 0 && (
            <div style={{ marginBottom: '6pt' }}>
              <span style={{ fontWeight: 'bold', fontSize: '11pt' }}>Certifications: </span>
              <span style={{ fontSize: '11pt' }}>
                {formData.skills.certifications.join(', ')}
              </span>
            </div>
          )}
        </div>
      )}

      {/* Projects */}
      {formData.projects && formData.projects.length > 0 && (
        <div style={{ marginBottom: '16pt' }}>
          <h2 style={{
            fontSize: '12pt',
            fontWeight: 'bold',
            textTransform: 'uppercase',
            borderBottom: '1pt solid #000000',
            paddingBottom: '2pt',
            marginBottom: '8pt'
          }}>
            Projects
          </h2>
          {formData.projects.map((project, index) => (
            <div key={index} style={{ marginBottom: '8pt' }}>
              <h3 style={{
                fontSize: '11pt',
                fontWeight: 'bold',
                margin: '0 0 2pt 0'
              }}>
                {project.name}
                {project.technologies && (
                  <span style={{
                    fontSize: '10pt',
                    fontWeight: 'normal',
                    color: '#333333'
                  }}>
                    {' '}({project.technologies})
                  </span>
                )}
              </h3>
              {project.description && (
                <p style={{
                  fontSize: '11pt',
                  lineHeight: '1.4',
                  margin: '0 0 4pt 0',
                  textAlign: 'justify'
                }}>
                  {project.description}
                </p>
              )}
              {(project.link || project.github) && (
                <div style={{
                  fontSize: '10pt',
                  color: '#333333'
                }}>
                  {project.link && <span>Demo: {project.link}</span>}
                  {project.link && project.github && <span> • </span>}
                  {project.github && <span>Code: {project.github}</span>}
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

// Modern Minimal Template - Clean contemporary design
const renderModernMinimal = (formData, baseStyles) => {
  return (
    <div style={{
      ...baseStyles,
      maxWidth: '8.5in',
      minHeight: '11in',
      margin: '0 auto',
      padding: '0.75in',
      fontFamily: "'Inter', 'Segoe UI', sans-serif"
    }}>
      {/* Header with subtle accent */}
      <div style={{
        textAlign: 'center',
        marginBottom: '24pt',
        borderBottom: '2pt solid #f0f0f0',
        paddingBottom: '16pt'
      }}>
        <h1 style={{
          fontSize: '24pt',
          fontWeight: '300',
          margin: '0 0 8pt 0',
          color: '#1a1a1a',
          letterSpacing: '0.5pt'
        }}>
          {formData.personal.firstName} {formData.personal.lastName}
        </h1>

        <div style={{
          fontSize: '11pt',
          color: '#666666',
          lineHeight: '1.5',
          fontWeight: '400'
        }}>
          {formData.personal.email && <span>{formData.personal.email}</span>}
          {formData.personal.phone && <span> • {formData.personal.phone}</span>}
          {formData.personal.location && <span> • {formData.personal.location}</span>}
          {formData.personal.linkedin && <span> • {formData.personal.linkedin}</span>}
        </div>
      </div>

      {/* Professional Summary with modern styling */}
      {formData.personal.summary && (
        <div style={{ marginBottom: '20pt' }}>
          <h2 style={{
            fontSize: '14pt',
            fontWeight: '500',
            color: '#2563eb',
            marginBottom: '10pt',
            letterSpacing: '0.3pt'
          }}>
            Professional Summary
          </h2>
          <p style={{
            fontSize: '11pt',
            lineHeight: '1.6',
            color: '#333333',
            margin: 0,
            textAlign: 'justify'
          }}>
            {formData.personal.summary}
          </p>
        </div>
      )}

      {/* Experience with modern layout */}
      {formData.experience && formData.experience.length > 0 && (
        <div style={{ marginBottom: '20pt' }}>
          <h2 style={{
            fontSize: '14pt',
            fontWeight: '500',
            color: '#2563eb',
            marginBottom: '12pt',
            letterSpacing: '0.3pt'
          }}>
            Experience
          </h2>
          {formData.experience.map((exp, index) => (
            <div key={index} style={{
              marginBottom: '16pt',
              paddingLeft: '12pt',
              borderLeft: '3pt solid #f0f0f0'
            }}>
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'baseline',
                marginBottom: '4pt'
              }}>
                <h3 style={{
                  fontSize: '12pt',
                  fontWeight: '600',
                  margin: 0,
                  color: '#1a1a1a'
                }}>
                  {exp.title}
                </h3>
                <span style={{
                  fontSize: '10pt',
                  color: '#666666',
                  fontWeight: '500'
                }}>
                  {exp.startDate} - {exp.current ? 'Present' : exp.endDate}
                </span>
              </div>
              <div style={{
                fontSize: '11pt',
                fontWeight: '500',
                color: '#2563eb',
                marginBottom: '6pt'
              }}>
                {exp.company} {exp.location && `• ${exp.location}`}
              </div>
              {exp.description && (
                <div style={{
                  fontSize: '11pt',
                  lineHeight: '1.5',
                  color: '#333333'
                }}>
                  {exp.description.split('\n').map((line, lineIndex) => (
                    <div key={lineIndex} style={{ marginBottom: '3pt' }}>
                      {line.trim().startsWith('•') ? line : `• ${line}`}
                    </div>
                  ))}
                </div>
              )}
            </div>
          ))}
        </div>
      )}

      {/* Education with clean styling */}
      {formData.education && formData.education.length > 0 && (
        <div style={{ marginBottom: '20pt' }}>
          <h2 style={{
            fontSize: '14pt',
            fontWeight: '500',
            color: '#2563eb',
            marginBottom: '12pt',
            letterSpacing: '0.3pt'
          }}>
            Education
          </h2>
          {formData.education.map((edu, index) => (
            <div key={index} style={{
              marginBottom: '12pt',
              paddingLeft: '12pt',
              borderLeft: '3pt solid #f0f0f0'
            }}>
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'baseline',
                marginBottom: '2pt'
              }}>
                <div>
                  <span style={{
                    fontSize: '12pt',
                    fontWeight: '600',
                    color: '#1a1a1a'
                  }}>
                    {edu.degree}
                  </span>
                  <span style={{
                    fontSize: '11pt',
                    color: '#666666'
                  }}>
                    {edu.field && ` in ${edu.field}`}
                  </span>
                </div>
                <span style={{
                  fontSize: '10pt',
                  color: '#666666',
                  fontWeight: '500'
                }}>
                  {edu.graduationDate}
                </span>
              </div>
              <div style={{
                fontSize: '11pt',
                color: '#2563eb',
                fontWeight: '500'
              }}>
                {edu.institution} {edu.location && `• ${edu.location}`}
              </div>
              {edu.gpa && (
                <div style={{
                  fontSize: '10pt',
                  color: '#666666',
                  marginTop: '2pt'
                }}>
                  GPA: {edu.gpa}
                </div>
              )}
            </div>
          ))}
        </div>
      )}

      {/* Skills with modern grid layout */}
      {formData.skills && (
        <div style={{ marginBottom: '20pt' }}>
          <h2 style={{
            fontSize: '14pt',
            fontWeight: '500',
            color: '#2563eb',
            marginBottom: '12pt',
            letterSpacing: '0.3pt'
          }}>
            Skills
          </h2>

          <div style={{ paddingLeft: '12pt', borderLeft: '3pt solid #f0f0f0' }}>
            {formData.skills.technical && formData.skills.technical.length > 0 && (
              <div style={{ marginBottom: '8pt' }}>
                <span style={{
                  fontWeight: '600',
                  fontSize: '11pt',
                  color: '#1a1a1a'
                }}>
                  Technical:
                </span>
                <span style={{
                  fontSize: '11pt',
                  color: '#333333',
                  marginLeft: '6pt'
                }}>
                  {formData.skills.technical.join(' • ')}
                </span>
              </div>
            )}

            {formData.skills.languages && formData.skills.languages.length > 0 && (
              <div style={{ marginBottom: '8pt' }}>
                <span style={{
                  fontWeight: '600',
                  fontSize: '11pt',
                  color: '#1a1a1a'
                }}>
                  Languages:
                </span>
                <span style={{
                  fontSize: '11pt',
                  color: '#333333',
                  marginLeft: '6pt'
                }}>
                  {formData.skills.languages.join(' • ')}
                </span>
              </div>
            )}

            {formData.skills.certifications && formData.skills.certifications.length > 0 && (
              <div style={{ marginBottom: '8pt' }}>
                <span style={{
                  fontWeight: '600',
                  fontSize: '11pt',
                  color: '#1a1a1a'
                }}>
                  Certifications:
                </span>
                <span style={{
                  fontSize: '11pt',
                  color: '#333333',
                  marginLeft: '6pt'
                }}>
                  {formData.skills.certifications.join(' • ')}
                </span>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Projects with modern styling */}
      {formData.projects && formData.projects.length > 0 && (
        <div style={{ marginBottom: '20pt' }}>
          <h2 style={{
            fontSize: '14pt',
            fontWeight: '500',
            color: '#2563eb',
            marginBottom: '12pt',
            letterSpacing: '0.3pt'
          }}>
            Projects
          </h2>
          {formData.projects.map((project, index) => (
            <div key={index} style={{
              marginBottom: '12pt',
              paddingLeft: '12pt',
              borderLeft: '3pt solid #f0f0f0'
            }}>
              <h3 style={{
                fontSize: '12pt',
                fontWeight: '600',
                margin: '0 0 4pt 0',
                color: '#1a1a1a'
              }}>
                {project.name}
                {project.technologies && (
                  <span style={{
                    fontSize: '10pt',
                    fontWeight: '500',
                    color: '#666666',
                    marginLeft: '8pt'
                  }}>
                    ({project.technologies})
                  </span>
                )}
              </h3>
              {project.description && (
                <p style={{
                  fontSize: '11pt',
                  lineHeight: '1.5',
                  margin: '0 0 6pt 0',
                  color: '#333333',
                  textAlign: 'justify'
                }}>
                  {project.description}
                </p>
              )}
              {(project.link || project.github) && (
                <div style={{
                  fontSize: '10pt',
                  color: '#2563eb',
                  fontWeight: '500'
                }}>
                  {project.link && <span>Demo: {project.link}</span>}
                  {project.link && project.github && <span> • </span>}
                  {project.github && <span>Code: {project.github}</span>}
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

const renderProfessionalExecutive = (formData, baseStyles) => {
  // Implementation for professional executive template
  return renderClassicATS(formData, baseStyles); // Temporary fallback
};

const renderTechDeveloper = (formData, baseStyles) => {
  // Implementation for tech developer template
  return renderClassicATS(formData, baseStyles); // Temporary fallback
};

const renderCreativeProfessional = (formData, baseStyles) => {
  // Implementation for creative professional template
  return renderClassicATS(formData, baseStyles); // Temporary fallback
};

const renderHealthcareMedical = (formData, baseStyles) => {
  // Implementation for healthcare medical template
  return renderClassicATS(formData, baseStyles); // Temporary fallback
};

const renderEuropeanCV = (formData, baseStyles) => {
  // Implementation for European CV template
  return renderClassicATS(formData, baseStyles); // Temporary fallback
};

const renderUKProfessional = (formData, baseStyles) => {
  // Implementation for UK professional template
  return renderClassicATS(formData, baseStyles); // Temporary fallback
};

const renderContemporaryEdge = (formData, baseStyles) => {
  // Implementation for contemporary edge template
  return renderClassicATS(formData, baseStyles); // Temporary fallback
};

const renderMinimalistPro = (formData, baseStyles) => {
  // Implementation for minimalist pro template
  return renderClassicATS(formData, baseStyles); // Temporary fallback
};

const EnhancedTemplateSystem = { ENHANCED_TEMPLATES, renderEnhancedTemplate };
export default EnhancedTemplateSystem;

'use client';
import { useState } from 'react';
import { motion } from 'framer-motion';
import { Eye, EyeOff, Download, FileText } from 'lucide-react';
// import { renderEnhancedTemplate } from './templates/EnhancedTemplateSystem';

const SimpleResumePreview = ({ 
  formData, 
  selectedTemplate = 'classic_ats',
  showPreview = false,
  onTogglePreview
}) => {
  const [zoom] = useState(0.4); // Fixed zoom for sidebar

  // Render the resume content
  const renderResumeContent = () => {
    if (!formData) {
      return (
        <div className="flex items-center justify-center h-full text-gray-400">
          <div className="text-center">
            <FileText className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p className="text-xs">Fill out the form to see preview</p>
          </div>
        </div>
      );
    }

    return renderEnhancedTemplate(selectedTemplate, formData);
  };

  return (
    <div className="bg-gray-900/60 backdrop-blur-md rounded-2xl border border-white/10 h-full flex flex-col">
      {/* Preview Header */}
      <div className="flex items-center justify-between p-4 border-b border-white/10 flex-shrink-0">
        <h3 className="text-lg font-semibold flex items-center gap-2 text-white">
          <Eye className="h-5 w-5 text-neural-purple" />
          Live Preview
        </h3>
        <div className="flex items-center gap-2">
          <button
            onClick={onTogglePreview}
            className={`px-3 py-1.5 rounded-lg text-sm font-medium transition-colors ${
              showPreview ? 'bg-neural-purple text-white' : 'bg-gray-800 text-gray-300 hover:bg-gray-700'
            }`}
          >
            {showPreview ? 'Hide' : 'Show'}
          </button>
        </div>
      </div>

      {/* Preview Content */}
      <div className="flex-1 overflow-hidden">
        {showPreview ? (
          <div className="h-full overflow-y-auto p-2">
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.3 }}
              className="bg-white rounded-lg shadow-lg"
              style={{
                transform: `scale(${zoom})`,
                transformOrigin: 'top left',
                width: `${100 / zoom}%`,
                height: `${100 / zoom}%`
              }}
            >
              <div 
                className="w-full h-full"
                style={{
                  fontFamily: "'Inter', 'Helvetica Neue', Arial, sans-serif",
                  fontSize: '11pt',
                  lineHeight: '1.4',
                  color: '#000000',
                  padding: '0.75in',
                  backgroundColor: '#ffffff',
                  minHeight: '11in'
                }}
              >
                {renderResumeContent()}
              </div>
            </motion.div>
          </div>
        ) : (
          <div className="flex items-center justify-center h-full text-gray-400">
            <div className="text-center">
              <Eye className="h-12 w-12 mx-auto mb-3 opacity-30" />
              <p className="text-sm">Click "Show" to preview</p>
              <p className="text-xs text-gray-500 mt-1">Live updates as you type</p>
            </div>
          </div>
        )}
      </div>

      {/* Preview Footer */}
      {showPreview && (
        <div className="p-3 border-t border-white/10 bg-gray-800/30 flex-shrink-0">
          <div className="flex items-center justify-between text-xs text-gray-400">
            <span>Template: {selectedTemplate?.replace('_', ' ') || 'Classic ATS'}</span>
            <span>Auto-updating</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default SimpleResumePreview;

'use client';
import { useState } from 'react';
import { motion } from 'framer-motion';
import { Eye, EyeOff, Download, FileText } from 'lucide-react';
// import { renderEnhancedTemplate } from './templates/EnhancedTemplateSystem';

const SimpleResumePreview = ({ 
  formData, 
  selectedTemplate = 'classic_ats',
  showPreview = false,
  onTogglePreview
}) => {
  const [zoom] = useState(0.6); // Fixed zoom for sidebar

  // Render the resume content
  const renderResumeContent = () => {
    // Debug: Check what formData contains
    console.log('SimpleResumePreview formData:', formData);

    if (!formData || !formData.personal || !formData.personal.firstName) {
      return (
        <div className="flex items-center justify-center h-full text-gray-400">
          <div className="text-center">
            <FileText className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p className="text-xs">Fill out the form to see preview</p>
            <p className="text-xs mt-1">
              {!formData ? 'No form data' :
               !formData.personal ? 'No personal data' :
               'No first name'}
            </p>
          </div>
        </div>
      );
    }

    // Simple resume rendering for preview
    return (
      <div className="p-6 space-y-4">
        {/* Header */}
        <div className="text-center border-b border-gray-300 pb-4">
          <h1 className="text-xl font-bold text-gray-900 mb-2">
            {formData.personal.firstName} {formData.personal.lastName}
          </h1>
          <div className="text-sm text-gray-600 space-y-1">
            {formData.personal.email && <div>{formData.personal.email}</div>}
            {formData.personal.phone && <div>{formData.personal.phone}</div>}
            {formData.personal.location && <div>{formData.personal.location}</div>}
          </div>
        </div>

        {/* Professional Summary */}
        {formData.personal.summary && (
          <div>
            <h2 className="text-sm font-bold text-gray-900 mb-2 uppercase border-b border-gray-300">
              Professional Summary
            </h2>
            <p className="text-xs text-gray-800 leading-relaxed">
              {formData.personal.summary}
            </p>
          </div>
        )}

        {/* Experience */}
        {formData.experience && formData.experience.length > 0 && (
          <div>
            <h2 className="text-sm font-bold text-gray-900 mb-2 uppercase border-b border-gray-300">
              Experience
            </h2>
            <div className="space-y-3">
              {formData.experience.slice(0, 3).map((exp, index) => (
                <div key={index} className="text-xs">
                  <div className="flex justify-between items-start mb-1">
                    <h3 className="font-semibold text-gray-900">{exp.title}</h3>
                    <span className="text-gray-600 text-xs">
                      {exp.startDate} - {exp.current ? 'Present' : exp.endDate}
                    </span>
                  </div>
                  <div className="text-gray-700 font-medium mb-1">
                    {exp.company} {exp.location && `• ${exp.location}`}
                  </div>
                  {exp.description && (
                    <div className="text-gray-600 text-xs leading-relaxed">
                      {exp.description.split('\n').slice(0, 2).map((line, i) => (
                        <div key={i}>• {line.trim()}</div>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Education */}
        {formData.education && formData.education.length > 0 && (
          <div>
            <h2 className="text-sm font-bold text-gray-900 mb-2 uppercase border-b border-gray-300">
              Education
            </h2>
            <div className="space-y-2">
              {formData.education.slice(0, 2).map((edu, index) => (
                <div key={index} className="text-xs">
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="font-semibold text-gray-900">
                        {edu.degree} {edu.field && `in ${edu.field}`}
                      </h3>
                      <div className="text-gray-700">{edu.institution}</div>
                    </div>
                    <span className="text-gray-600 text-xs">{edu.graduationDate}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Skills */}
        {formData.skills && (
          <div>
            <h2 className="text-sm font-bold text-gray-900 mb-2 uppercase border-b border-gray-300">
              Skills
            </h2>
            <div className="text-xs space-y-1">
              {formData.skills.technical && formData.skills.technical.length > 0 && (
                <div>
                  <span className="font-semibold text-gray-900">Technical: </span>
                  <span className="text-gray-700">
                    {formData.skills.technical.slice(0, 8).join(', ')}
                  </span>
                </div>
              )}
              {formData.skills.languages && formData.skills.languages.length > 0 && (
                <div>
                  <span className="font-semibold text-gray-900">Languages: </span>
                  <span className="text-gray-700">
                    {formData.skills.languages.join(', ')}
                  </span>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="bg-gray-900/60 backdrop-blur-md rounded-2xl border border-white/10 h-full flex flex-col">
      {/* Preview Header */}
      <div className="flex items-center justify-between p-4 border-b border-white/10 flex-shrink-0">
        <h3 className="text-lg font-semibold flex items-center gap-2 text-white">
          <Eye className="h-5 w-5 text-neural-purple" />
          Live Preview
        </h3>
        <div className="flex items-center gap-2">
          <button
            onClick={onTogglePreview}
            className={`px-3 py-1.5 rounded-lg text-sm font-medium transition-colors ${
              showPreview ? 'bg-neural-purple text-white' : 'bg-gray-800 text-gray-300 hover:bg-gray-700'
            }`}
          >
            {showPreview ? 'Hide' : 'Show'}
          </button>
        </div>
      </div>

      {/* Preview Content */}
      <div className="flex-1 overflow-hidden">
        {showPreview ? (
          <div className="h-full overflow-y-auto p-2">
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.3 }}
              className="bg-white rounded-lg shadow-lg min-h-[600px]"
              style={{
                transform: `scale(${zoom})`,
                transformOrigin: 'top left',
                width: `${100 / zoom}%`
              }}
            >
              <div
                className="w-full"
                style={{
                  fontFamily: "'Inter', 'Helvetica Neue', Arial, sans-serif",
                  fontSize: '10pt',
                  lineHeight: '1.3',
                  color: '#000000',
                  backgroundColor: '#ffffff',
                  minHeight: '600px'
                }}
              >
                {renderResumeContent()}
              </div>
            </motion.div>
          </div>
        ) : (
          <div className="flex items-center justify-center h-full text-gray-400">
            <div className="text-center">
              <Eye className="h-12 w-12 mx-auto mb-3 opacity-30" />
              <p className="text-sm">Click "Show" to preview</p>
              <p className="text-xs text-gray-500 mt-1">Live updates as you type</p>
            </div>
          </div>
        )}
      </div>

      {/* Preview Footer */}
      {showPreview && (
        <div className="p-3 border-t border-white/10 bg-gray-800/30 flex-shrink-0">
          <div className="flex items-center justify-between text-xs text-gray-400">
            <span>Template: {selectedTemplate?.replace('_', ' ') || 'Classic ATS'}</span>
            <span>Auto-updating</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default SimpleResumePreview;

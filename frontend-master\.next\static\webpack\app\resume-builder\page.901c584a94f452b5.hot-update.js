"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/resume-builder/page",{

/***/ "(app-pages-browser)/./src/components/resume/RedesignedResumeBuilder.jsx":
/*!***********************************************************!*\
  !*** ./src/components/resume/RedesignedResumeBuilder.jsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _TopStepNavigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./TopStepNavigation */ \"(app-pages-browser)/./src/components/resume/TopStepNavigation.jsx\");\n/* harmony import */ var _MobileActionBar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./MobileActionBar */ \"(app-pages-browser)/./src/components/resume/MobileActionBar.jsx\");\n/* harmony import */ var _MobilePreviewModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./MobilePreviewModal */ \"(app-pages-browser)/./src/components/resume/MobilePreviewModal.jsx\");\n/* harmony import */ var _StreamlinedPreview__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./StreamlinedPreview */ \"(app-pages-browser)/./src/components/resume/StreamlinedPreview.jsx\");\n/* harmony import */ var _FullscreenPreviewModal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./FullscreenPreviewModal */ \"(app-pages-browser)/./src/components/resume/FullscreenPreviewModal.jsx\");\n/* harmony import */ var _UniversalTemplateSelector__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./UniversalTemplateSelector */ \"(app-pages-browser)/./src/components/resume/UniversalTemplateSelector.jsx\");\n/* harmony import */ var _forms_UniversalFormFields__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./forms/UniversalFormFields */ \"(app-pages-browser)/./src/components/resume/forms/UniversalFormFields.jsx\");\n/* harmony import */ var _templates_MultiIndustryTemplates__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./templates/MultiIndustryTemplates */ \"(app-pages-browser)/./src/components/resume/templates/MultiIndustryTemplates.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n// Import new components\n\n\n\n\n\n\n\n// Import template system\n\nconst RedesignedResumeBuilder = (param)=>{\n    let { hideHeader = false } = param;\n    _s();\n    // Core state\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [selectedTemplate, setSelectedTemplate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('business_executive');\n    const [showTemplateSelector, setShowTemplateSelector] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showFullscreenPreview, setShowFullscreenPreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showMobilePreview, setShowMobilePreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Form data with universal structure\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        personal: {\n            firstName: \"\",\n            lastName: \"\",\n            email: \"\",\n            phone: \"\",\n            location: \"\",\n            summary: \"\"\n        },\n        experience: [\n            {\n                id: 1,\n                title: \"\",\n                company: \"\",\n                location: \"\",\n                startDate: \"\",\n                endDate: \"\",\n                current: false,\n                description: \"\"\n            }\n        ],\n        education: [\n            {\n                id: 1,\n                degree: \"\",\n                field: \"\",\n                institution: \"\",\n                location: \"\",\n                graduationDate: \"\",\n                gpa: \"\"\n            }\n        ],\n        skills: {\n            technical: [],\n            languages: [],\n            certifications: []\n        }\n    });\n    // UI state\n    const [validationErrors, setValidationErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isSaving, setIsSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [completedSteps, setCompletedSteps] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Step configuration - simplified and universal\n    const steps = [\n        {\n            id: 0,\n            title: \"Personal Info\",\n            description: \"Your contact information and professional summary\"\n        },\n        {\n            id: 1,\n            title: \"Experience\",\n            description: \"Your work history and achievements\"\n        },\n        {\n            id: 2,\n            title: \"Education\",\n            description: \"Your educational background and qualifications\"\n        },\n        {\n            id: 3,\n            title: \"Skills\",\n            description: \"Your core competencies and abilities\"\n        },\n        {\n            id: 4,\n            title: \"Review\",\n            description: \"Review and download your resume\"\n        }\n    ];\n    // Auto-save functionality\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RedesignedResumeBuilder.useEffect\": ()=>{\n            const timeoutId = setTimeout({\n                \"RedesignedResumeBuilder.useEffect.timeoutId\": ()=>{\n                    localStorage.setItem('universalResumeData', JSON.stringify(formData));\n                    localStorage.setItem('selectedTemplate', selectedTemplate);\n                }\n            }[\"RedesignedResumeBuilder.useEffect.timeoutId\"], 2000);\n            return ({\n                \"RedesignedResumeBuilder.useEffect\": ()=>clearTimeout(timeoutId)\n            })[\"RedesignedResumeBuilder.useEffect\"];\n        }\n    }[\"RedesignedResumeBuilder.useEffect\"], [\n        formData,\n        selectedTemplate\n    ]);\n    // Load saved data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RedesignedResumeBuilder.useEffect\": ()=>{\n            const savedData = localStorage.getItem('universalResumeData');\n            const savedTemplate = localStorage.getItem('selectedTemplate');\n            if (savedData) {\n                try {\n                    setFormData(JSON.parse(savedData));\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success('Previous work restored!');\n                } catch (error) {\n                    console.error('Failed to load saved data:', error);\n                }\n            }\n            if (savedTemplate && _templates_MultiIndustryTemplates__WEBPACK_IMPORTED_MODULE_10__.INDUSTRY_TEMPLATES[savedTemplate]) {\n                setSelectedTemplate(savedTemplate);\n            }\n        }\n    }[\"RedesignedResumeBuilder.useEffect\"], []);\n    // Validation logic\n    const validateStep = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"RedesignedResumeBuilder.useCallback[validateStep]\": (stepIndex)=>{\n            const errors = {};\n            switch(stepIndex){\n                case 0:\n                    var _formData_personal_firstName, _formData_personal_lastName, _formData_personal_email;\n                    if (!((_formData_personal_firstName = formData.personal.firstName) === null || _formData_personal_firstName === void 0 ? void 0 : _formData_personal_firstName.trim())) errors.firstName = 'First name is required';\n                    if (!((_formData_personal_lastName = formData.personal.lastName) === null || _formData_personal_lastName === void 0 ? void 0 : _formData_personal_lastName.trim())) errors.lastName = 'Last name is required';\n                    if (!((_formData_personal_email = formData.personal.email) === null || _formData_personal_email === void 0 ? void 0 : _formData_personal_email.trim())) errors.email = 'Email is required';\n                    else if (!/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(formData.personal.email)) {\n                        errors.email = 'Please enter a valid email address';\n                    }\n                    break;\n                case 1:\n                    var _formData_experience;\n                    const validExperience = (_formData_experience = formData.experience) === null || _formData_experience === void 0 ? void 0 : _formData_experience.filter({\n                        \"RedesignedResumeBuilder.useCallback[validateStep]\": (exp)=>{\n                            var _exp_title, _exp_company;\n                            return ((_exp_title = exp.title) === null || _exp_title === void 0 ? void 0 : _exp_title.trim()) && ((_exp_company = exp.company) === null || _exp_company === void 0 ? void 0 : _exp_company.trim());\n                        }\n                    }[\"RedesignedResumeBuilder.useCallback[validateStep]\"]);\n                    if (!validExperience || validExperience.length === 0) {\n                        errors.experience = 'At least one work experience entry is required';\n                    }\n                    break;\n                case 2:\n                    var _formData_education;\n                    const validEducation = (_formData_education = formData.education) === null || _formData_education === void 0 ? void 0 : _formData_education.filter({\n                        \"RedesignedResumeBuilder.useCallback[validateStep]\": (edu)=>{\n                            var _edu_degree, _edu_institution;\n                            return ((_edu_degree = edu.degree) === null || _edu_degree === void 0 ? void 0 : _edu_degree.trim()) && ((_edu_institution = edu.institution) === null || _edu_institution === void 0 ? void 0 : _edu_institution.trim());\n                        }\n                    }[\"RedesignedResumeBuilder.useCallback[validateStep]\"]);\n                    if (!validEducation || validEducation.length === 0) {\n                        errors.education = 'At least one education entry is required';\n                    }\n                    break;\n                case 3:\n                    break;\n                default:\n                    break;\n            }\n            return errors;\n        }\n    }[\"RedesignedResumeBuilder.useCallback[validateStep]\"], [\n        formData\n    ]);\n    // Navigation logic\n    const canProceedToNextStep = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"RedesignedResumeBuilder.useCallback[canProceedToNextStep]\": (stepIndex)=>{\n            const errors = validateStep(stepIndex);\n            return Object.keys(errors).length === 0;\n        }\n    }[\"RedesignedResumeBuilder.useCallback[canProceedToNextStep]\"], [\n        validateStep\n    ]);\n    const nextStep = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"RedesignedResumeBuilder.useCallback[nextStep]\": ()=>{\n            const errors = validateStep(currentStep);\n            if (Object.keys(errors).length > 0) {\n                setValidationErrors(errors);\n                const errorMessages = Object.values(errors);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(errorMessages[0]);\n                return;\n            }\n            setCompletedSteps({\n                \"RedesignedResumeBuilder.useCallback[nextStep]\": (prev)=>[\n                        ...new Set([\n                            ...prev,\n                            currentStep\n                        ])\n                    ]\n            }[\"RedesignedResumeBuilder.useCallback[nextStep]\"]);\n            setValidationErrors({});\n            if (currentStep < steps.length - 1) {\n                setCurrentStep(currentStep + 1);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"\".concat(steps[currentStep].title, \" completed!\"));\n            }\n        }\n    }[\"RedesignedResumeBuilder.useCallback[nextStep]\"], [\n        currentStep,\n        validateStep,\n        steps\n    ]);\n    const prevStep = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"RedesignedResumeBuilder.useCallback[prevStep]\": ()=>{\n            setValidationErrors({});\n            if (currentStep > 0) setCurrentStep(currentStep - 1);\n        }\n    }[\"RedesignedResumeBuilder.useCallback[prevStep]\"], [\n        currentStep\n    ]);\n    // Form data management\n    const updateFormData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"RedesignedResumeBuilder.useCallback[updateFormData]\": function(section, field, value) {\n            let index = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : null;\n            setFormData({\n                \"RedesignedResumeBuilder.useCallback[updateFormData]\": (prev)=>{\n                    let newData = {\n                        ...prev\n                    };\n                    if (index !== null && Array.isArray(prev[section])) {\n                        const newArray = [\n                            ...prev[section]\n                        ];\n                        newArray[index] = {\n                            ...newArray[index],\n                            [field]: value\n                        };\n                        newData[section] = newArray;\n                    } else if (typeof prev[section] === 'object' && !Array.isArray(prev[section])) {\n                        newData[section] = {\n                            ...prev[section],\n                            [field]: value\n                        };\n                    } else {\n                        newData[field] = value;\n                    }\n                    return newData;\n                }\n            }[\"RedesignedResumeBuilder.useCallback[updateFormData]\"]);\n            // Clear validation errors for this field\n            if (validationErrors[field]) {\n                setValidationErrors({\n                    \"RedesignedResumeBuilder.useCallback[updateFormData]\": (prev)=>{\n                        const newErrors = {\n                            ...prev\n                        };\n                        delete newErrors[field];\n                        return newErrors;\n                    }\n                }[\"RedesignedResumeBuilder.useCallback[updateFormData]\"]);\n            }\n        }\n    }[\"RedesignedResumeBuilder.useCallback[updateFormData]\"], [\n        validationErrors\n    ]);\n    const addArrayItem = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"RedesignedResumeBuilder.useCallback[addArrayItem]\": (section, template)=>{\n            setFormData({\n                \"RedesignedResumeBuilder.useCallback[addArrayItem]\": (prev)=>({\n                        ...prev,\n                        [section]: [\n                            ...prev[section],\n                            {\n                                ...template,\n                                id: Math.random().toString(36).substring(2, 11)\n                            }\n                        ]\n                    })\n            }[\"RedesignedResumeBuilder.useCallback[addArrayItem]\"]);\n        }\n    }[\"RedesignedResumeBuilder.useCallback[addArrayItem]\"], []);\n    const removeArrayItem = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"RedesignedResumeBuilder.useCallback[removeArrayItem]\": (section, id)=>{\n            setFormData({\n                \"RedesignedResumeBuilder.useCallback[removeArrayItem]\": (prev)=>({\n                        ...prev,\n                        [section]: prev[section].filter({\n                            \"RedesignedResumeBuilder.useCallback[removeArrayItem]\": (item)=>item.id !== id\n                        }[\"RedesignedResumeBuilder.useCallback[removeArrayItem]\"])\n                    })\n            }[\"RedesignedResumeBuilder.useCallback[removeArrayItem]\"]);\n        }\n    }[\"RedesignedResumeBuilder.useCallback[removeArrayItem]\"], []);\n    // Save functionality\n    const handleSave = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"RedesignedResumeBuilder.useCallback[handleSave]\": ()=>{\n            setIsSaving(true);\n            localStorage.setItem('universalResumeData', JSON.stringify(formData));\n            localStorage.setItem('selectedTemplate', selectedTemplate);\n            setTimeout({\n                \"RedesignedResumeBuilder.useCallback[handleSave]\": ()=>{\n                    setIsSaving(false);\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success('Progress saved!');\n                }\n            }[\"RedesignedResumeBuilder.useCallback[handleSave]\"], 1000);\n        }\n    }[\"RedesignedResumeBuilder.useCallback[handleSave]\"], [\n        formData,\n        selectedTemplate\n    ]);\n    // Download functionality\n    const handleDownload = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"RedesignedResumeBuilder.useCallback[handleDownload]\": ()=>{\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success('Download feature coming soon!');\n        }\n    }[\"RedesignedResumeBuilder.useCallback[handleDownload]\"], []);\n    // Calculate completion percentage\n    const getCompletionPercentage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"RedesignedResumeBuilder.useCallback[getCompletionPercentage]\": ()=>{\n            var _formData_experience, _formData_education, _formData_skills_technical, _formData_skills;\n            let totalFields = 0;\n            let completedFields = 0;\n            // Personal info (4 required fields)\n            totalFields += 4;\n            if (formData.personal.firstName) completedFields++;\n            if (formData.personal.lastName) completedFields++;\n            if (formData.personal.email) completedFields++;\n            if (formData.personal.summary) completedFields++;\n            // Experience (at least 1 entry with title and company)\n            totalFields += 2;\n            const validExp = (_formData_experience = formData.experience) === null || _formData_experience === void 0 ? void 0 : _formData_experience.filter({\n                \"RedesignedResumeBuilder.useCallback[getCompletionPercentage]\": (exp)=>exp.title && exp.company\n            }[\"RedesignedResumeBuilder.useCallback[getCompletionPercentage]\"]);\n            if ((validExp === null || validExp === void 0 ? void 0 : validExp.length) > 0) completedFields += 2;\n            // Education (at least 1 entry with degree and institution)\n            totalFields += 2;\n            const validEdu = (_formData_education = formData.education) === null || _formData_education === void 0 ? void 0 : _formData_education.filter({\n                \"RedesignedResumeBuilder.useCallback[getCompletionPercentage]\": (edu)=>edu.degree && edu.institution\n            }[\"RedesignedResumeBuilder.useCallback[getCompletionPercentage]\"]);\n            if ((validEdu === null || validEdu === void 0 ? void 0 : validEdu.length) > 0) completedFields += 2;\n            // Skills (optional but counts if present)\n            if (((_formData_skills = formData.skills) === null || _formData_skills === void 0 ? void 0 : (_formData_skills_technical = _formData_skills.technical) === null || _formData_skills_technical === void 0 ? void 0 : _formData_skills_technical.length) > 0) {\n                totalFields += 1;\n                completedFields += 1;\n            }\n            return Math.round(completedFields / totalFields * 100);\n        }\n    }[\"RedesignedResumeBuilder.useCallback[getCompletionPercentage]\"], [\n        formData\n    ]);\n    // Render step content\n    const renderStepContent = ()=>{\n        const commonProps = {\n            formData,\n            updateFormData,\n            addArrayItem,\n            removeArrayItem,\n            validationErrors\n        };\n        switch(currentStep){\n            case 0:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_UniversalFormFields__WEBPACK_IMPORTED_MODULE_9__.UniversalPersonalForm, {\n                    ...commonProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                    lineNumber: 309,\n                    columnNumber: 16\n                }, undefined);\n            case 1:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_UniversalFormFields__WEBPACK_IMPORTED_MODULE_9__.UniversalExperienceForm, {\n                    ...commonProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                    lineNumber: 311,\n                    columnNumber: 16\n                }, undefined);\n            case 2:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_UniversalFormFields__WEBPACK_IMPORTED_MODULE_9__.UniversalEducationForm, {\n                    ...commonProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                    lineNumber: 313,\n                    columnNumber: 16\n                }, undefined);\n            case 3:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_UniversalFormFields__WEBPACK_IMPORTED_MODULE_9__.UniversalSkillsForm, {\n                    ...commonProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                    lineNumber: 315,\n                    columnNumber: 16\n                }, undefined);\n            case 4:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                        className: \"bg-gray-900/60 backdrop-blur-md rounded-2xl border border-gray-700/50 p-8 text-center\",\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center gap-3 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                                className: \"h-8 w-8 text-neural-pink\",\n                                                animate: {\n                                                    scale: [\n                                                        1,\n                                                        1.2,\n                                                        1\n                                                    ]\n                                                },\n                                                transition: {\n                                                    duration: 2,\n                                                    repeat: Infinity\n                                                },\n                                                children: \"⚡\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                                lineNumber: 326,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-neural-pink opacity-20 rounded-full blur-md\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                                lineNumber: 333,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                        lineNumber: 325,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-semibold text-white\",\n                                        children: \"Resume Created in Minutes!\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                        lineNumber: 335,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                lineNumber: 324,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-300 mb-4 max-w-md mx-auto\",\n                                children: \"You've turned hours of work into minutes with AI. Your professional resume is ready for job applications.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                lineNumber: 340,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center gap-4 text-sm text-gray-400 mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"flex items-center gap-1\",\n                                        children: \"✅ ATS-Optimized\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                        lineNumber: 345,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"flex items-center gap-1\",\n                                        children: \"✅ Job-Ready Format\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                        lineNumber: 348,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"flex items-center gap-1\",\n                                        children: \"✅ Professional Design\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                        lineNumber: 351,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                lineNumber: 344,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowTemplateSelector(true),\n                                        className: \"px-6 py-3 bg-gray-800 hover:bg-gray-700 text-white rounded-lg transition-colors border border-gray-600\",\n                                        children: \"Change Template\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                        lineNumber: 357,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowFullscreenPreview(true),\n                                        className: \"px-6 py-3 bg-gradient-to-r from-neural-purple to-neural-pink text-white rounded-lg hover:opacity-90 transition-opacity\",\n                                        children: \"Preview Fullscreen\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                        lineNumber: 363,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.info('Upgrade to Pro to download your resume!'),\n                                        className: \"px-6 py-3 bg-gradient-to-r from-yellow-500 to-orange-500 text-white rounded-lg hover:opacity-90 transition-opacity\",\n                                        children: \"Upgrade to Download\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                        lineNumber: 369,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                lineNumber: 356,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 p-4 bg-gradient-to-r from-neural-purple/10 to-neural-pink/10 border border-neural-purple/20 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-neural-blue text-sm font-medium\",\n                                        children: \"\\uD83D\\uDE80 Upgrade to Pro: Download instantly, access 50+ premium templates, and get AI-powered job matching\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                        lineNumber: 378,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400 text-xs mt-1\",\n                                        children: \"Join thousands who've landed their dream jobs faster with our AI tools\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                        lineNumber: 381,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                lineNumber: 377,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                        lineNumber: 319,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                    lineNumber: 318,\n                    columnNumber: 11\n                }, undefined);\n            default:\n                return null;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-b from-black to-[#0A0A0A] text-white relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 opacity-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 bg-[url('/grid.svg')] [mask-image:radial-gradient(ellipse_at_center,white,transparent)]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                    lineNumber: 397,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                lineNumber: 396,\n                columnNumber: 7\n            }, undefined),\n            !hideHeader && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 pt-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                        className: \"text-center\",\n                        initial: {\n                            opacity: 0,\n                            y: -20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center gap-2 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                                className: \"h-8 w-8 text-neural-pink\",\n                                                animate: {\n                                                    rotate: 360\n                                                },\n                                                transition: {\n                                                    duration: 20,\n                                                    repeat: Infinity,\n                                                    ease: \"linear\"\n                                                },\n                                                children: \"✨\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                                lineNumber: 411,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-neural-pink opacity-20 rounded-full blur-md\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                                lineNumber: 418,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                        lineNumber: 410,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-semibold text-neural-blue\",\n                                        children: \"Turn Hours Into Minutes — With AI\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                        lineNumber: 420,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                lineNumber: 409,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl lg:text-4xl font-bold text-white mb-4\",\n                                children: \"AI Resume Builder\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                lineNumber: 425,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-300 text-lg max-w-2xl mx-auto mb-2\",\n                                children: \"Create ATS-optimized resumes in minutes with AI-powered suggestions\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                lineNumber: 429,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap items-center justify-center gap-4 text-sm text-gray-400\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"flex items-center gap-1\",\n                                        children: \"⚡ Create Resume in 5 minutes\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                        lineNumber: 434,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"flex items-center gap-1\",\n                                        children: \"\\uD83C\\uDFAF ATS Scoring\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                        lineNumber: 437,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"flex items-center gap-1\",\n                                        children: \"\\uD83D\\uDE80 Job-Specific Content\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                        lineNumber: 440,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                lineNumber: 433,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                        lineNumber: 404,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                    lineNumber: 403,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                lineNumber: 402,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 pb-32 xl:pb-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col xl:flex-row gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 max-w-4xl\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TopStepNavigation__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        currentStep: currentStep,\n                                        totalSteps: steps.length,\n                                        onPrevious: prevStep,\n                                        onNext: nextStep,\n                                        onSave: handleSave,\n                                        onHome: ()=>window.location.href = '/',\n                                        canProceed: canProceedToNextStep(currentStep),\n                                        isSaving: isSaving,\n                                        completionPercentage: getCompletionPercentage(),\n                                        stepTitles: steps.map((step)=>step.title),\n                                        completedSteps: completedSteps\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                        lineNumber: 456,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.AnimatePresence, {\n                                        mode: \"wait\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                x: 20\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                x: 0\n                                            },\n                                            exit: {\n                                                opacity: 0,\n                                                x: -20\n                                            },\n                                            transition: {\n                                                duration: 0.3\n                                            },\n                                            children: renderStepContent()\n                                        }, currentStep, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                            lineNumber: 472,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                        lineNumber: 471,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                lineNumber: 454,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden xl:block xl:w-80 xl:flex-shrink-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"sticky top-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StreamlinedPreview__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        formData: formData,\n                                        selectedTemplate: selectedTemplate,\n                                        onOpenFullscreen: ()=>setShowFullscreenPreview(true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                        lineNumber: 487,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                    lineNumber: 486,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                lineNumber: 485,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                        lineNumber: 452,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                    lineNumber: 451,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                lineNumber: 450,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MobileActionBar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                currentStep: currentStep,\n                totalSteps: steps.length,\n                onPrevious: prevStep,\n                onNext: nextStep,\n                onSave: handleSave,\n                onTogglePreview: ()=>setShowMobilePreview(true),\n                canProceed: canProceedToNextStep(currentStep),\n                isSaving: isSaving,\n                showPreview: showMobilePreview\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                lineNumber: 499,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.AnimatePresence, {\n                children: showTemplateSelector && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_UniversalTemplateSelector__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    selectedTemplate: selectedTemplate,\n                    onTemplateSelect: setSelectedTemplate,\n                    onClose: ()=>setShowTemplateSelector(false),\n                    formData: formData\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                    lineNumber: 514,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                lineNumber: 512,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.AnimatePresence, {\n                children: showFullscreenPreview && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FullscreenPreviewModal__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    isOpen: showFullscreenPreview,\n                    onClose: ()=>setShowFullscreenPreview(false),\n                    formData: formData,\n                    selectedTemplate: selectedTemplate,\n                    onTemplateChange: setSelectedTemplate\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                    lineNumber: 525,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                lineNumber: 523,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n        lineNumber: 394,\n        columnNumber: 5\n    }, undefined);\n};\n_s(RedesignedResumeBuilder, \"8XIJ96370pph98Xr04gYA0Y/zTk=\");\n_c = RedesignedResumeBuilder;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (RedesignedResumeBuilder);\nvar _c;\n$RefreshReg$(_c, \"RedesignedResumeBuilder\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/resume/RedesignedResumeBuilder.jsx\n"));

/***/ })

});
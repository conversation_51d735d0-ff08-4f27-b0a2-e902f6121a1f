import { GoogleGenerativeAI } from '@google/generative-ai';
import { NextResponse } from 'next/server';

// Initialize Gemini AI with API key
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);

export async function POST(request) {
  let formData = {};
  try {
    console.log('🚀 Resume generation API called');
    const requestData = await request.json();
    console.log('📝 Request data received:', JSON.stringify(requestData, null, 2));

    // Handle nested formData structure
    formData = requestData.formData || requestData;
    console.log('📋 Processed form data:', JSON.stringify(formData, null, 2));

    // Validate required fields
    if (!formData.personal?.firstName || !formData.personal?.lastName || !formData.personal?.email) {
      console.error('❌ Missing required fields');
      console.log('🔍 Form data structure:', {
        hasPersonal: !!formData.personal,
        firstName: formData.personal?.firstName,
        lastName: formData.personal?.lastName,
        email: formData.personal?.email
      });
      return NextResponse.json(
        { error: 'Missing required personal information (First Name, Last Name, and Email are required)' },
        { status: 400 }
      );
    }

    // Check if API key is configured
    if (!process.env.GEMINI_API_KEY || process.env.GEMINI_API_KEY === 'your-gemini-api-key-here') {
      console.log('⚠️ Gemini API key not configured, using fallback');
      // Fallback: return a basic resume structure
      const fallbackResume = createFallbackResume(formData);
      return NextResponse.json({
        success: true,
        resumeData: fallbackResume,
        atsScore: fallbackResume.atsScore?.overall || 75,
        suggestions: fallbackResume.atsScore?.improvements || [],
        downloadUrl: '/api/download-resume/' + Date.now(),
        message: 'Resume generated successfully (using template)',
        fallback: true
      });
    }

    console.log('🤖 Initializing Gemini AI...');

    // Get the generative model - Using Gemini 2.0 Flash (latest and most efficient)
    const model = genAI.getGenerativeModel({
      model: 'gemini-2.0-flash-exp',
      generationConfig: {
        temperature: 0.3,
        topK: 40,
        topP: 0.95,
        maxOutputTokens: 8000,
      }
    });

    // Prepare prompt for Gemini API
    const prompt = createResumePrompt(formData);
    console.log('📋 Prompt created, length:', prompt.length);

    // Generate content using the official SDK
    console.log('🔄 Calling Gemini API...');
    const result = await model.generateContent(prompt);
    const response = await result.response;
    const generatedContent = response.text();
    console.log('✅ Gemini response received, length:', generatedContent?.length || 0);

    if (!generatedContent) {
      throw new Error('No content generated from Gemini API');
    }

    // Process the generated content and create resume
    console.log('🔧 Processing generated content...');
    const resumeData = processGeneratedContent(generatedContent, formData);
    console.log('📊 Resume data processed:', {
      hasEnhancedContent: !!resumeData.enhancedContent,
      atsScore: resumeData.atsScore?.overall,
      suggestionsCount: resumeData.atsScore?.improvements?.length || 0
    });

    // Return the processed data with enhanced structure
    const responseData = {
      success: true,
      resumeData,
      atsScore: resumeData.atsScore?.overall || 75,
      suggestions: resumeData.atsScore?.improvements || [],
      downloadUrl: '/api/download-resume/' + Date.now(),
      message: 'Resume generated successfully with AI optimization',
      generatedAt: new Date().toISOString()
    };

    console.log('✨ Sending successful response');
    return NextResponse.json(responseData);

  } catch (error) {
    console.error('💥 Resume generation error:', error);
    console.error('Error stack:', error.stack);

    // Enhanced error handling with fallback
    try {
      const fallbackResume = createFallbackResume(formData || {});
      const fallbackResponse = {
        success: true,
        resumeData: fallbackResume,
        atsScore: fallbackResume.atsScore?.overall || 75,
        suggestions: fallbackResume.atsScore?.improvements || [],
        downloadUrl: '/api/download-resume/' + Date.now(),
        message: 'Resume generated successfully (fallback mode)',
        fallback: true,
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      };

      console.log('🔄 Sending fallback response');
      return NextResponse.json(fallbackResponse);
    } catch (fallbackError) {
      console.error('💥 Fallback also failed:', fallbackError);
      return NextResponse.json(
        {
          error: 'Failed to generate resume',
          details: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
        },
        { status: 500 }
      );
    }
  }
}

function createResumePrompt(formData) {
  const { personal, education, experience, skills, projects } = formData;

  // Create a raw resume text from form data
  const rawResumeText = `
NAME: ${personal.firstName} ${personal.lastName}
EMAIL: ${personal.email}
PHONE: ${personal.phone || 'Not provided'}
LOCATION: ${personal.location || 'Not provided'}
LINKEDIN: ${personal.linkedin || 'Not provided'}
PORTFOLIO: ${personal.portfolio || 'Not provided'}

SUMMARY: ${personal.summary || 'Not provided'}

EDUCATION:
${education?.map(edu => `${edu.degree} | ${edu.institution} | ${edu.startDate} - ${edu.endDate}`).join('\n') || 'Not provided'}

EXPERIENCE:
${experience?.map(exp => `${exp.title} | ${exp.company} | ${exp.startDate} - ${exp.current ? 'Present' : exp.endDate}\n${exp.description || ''}`).join('\n\n') || 'Not provided'}

SKILLS:
Technical: ${skills?.technical?.join(', ') || 'Not provided'}
Languages: ${skills?.languages?.join(', ') || 'Not provided'}
Certifications: ${skills?.certifications?.join(', ') || 'Not provided'}

PROJECTS:
${projects?.map(proj => `${proj.name} | ${proj.technologies}\n${proj.description || ''}`).join('\n\n') || 'Not provided'}
  `;

  return `You are a professional ATS resume optimizer with expertise in realistic scoring based on actual ATS system requirements.

Your tasks:
✅ Enhance resume content with professional formatting and action verbs
✅ Add relevant keywords naturally (avoid keyword stuffing)
✅ Improve bullet points with quantified achievements where possible
✅ Maintain ATS-friendly structure and standard section headers
✅ Provide realistic ATS scoring based on actual content quality

Resume Format Reference:
---
NAME
📧 Email | 📱 Phone | 🔗 LinkedIn | 🌐 Portfolio

*PROFESSIONAL SUMMARY*
2–3 lines highlighting role focus, key skills, and relevant experience

*EDUCATION*
Degree | Institution | Year | Relevant details

*EXPERIENCE*
Role | Company | Date Range
• Achievement with action verb and context
• Technical contribution or responsibility
• Impact or result (with metrics if available)

*PROJECTS*
Project Title | Technologies Used
• Project description with technical approach
• Outcome or learning achieved

*SKILLS*
Technical Skills: Relevant technologies and tools
Languages: Spoken languages
Certifications: Professional certifications
---

ATS SCORING CRITERIA (Be realistic and accurate):
- Keywords (25%): Relevant industry/technical terms naturally integrated
- Formatting (25%): ATS-friendly structure, standard headers, clean layout  
- Content Quality (25%): Clear achievements, action verbs, professional language
- Completeness (25%): All sections filled, contact info, relevant experience

SCORING GUIDELINES:
- 90-100: Exceptional resume with quantified achievements, perfect formatting, comprehensive content
- 80-89: Strong resume with good content, clear structure, relevant keywords
- 70-79: Decent resume with adequate content, standard formatting, some improvements needed
- 60-69: Basic resume with minimal content, simple formatting, significant improvements needed
- 50-59: Poor resume with weak content, formatting issues, major improvements required
- Below 50: Inadequate resume missing critical elements

OUTPUT FORMAT: Return a JSON object with this exact structure:
{
  "enhancedContent": {
    "professionalSummary": "Enhanced 2-3 line summary",
    "experience": [
      {
        "title": "Job title",
        "company": "Company name",
        "location": "Location",
        "startDate": "MM/YYYY",
        "endDate": "MM/YYYY or Present",
        "achievements": [
          "• Action verb + responsibility/achievement",
          "• Technical contribution or project",
          "• Result or impact (quantified if possible)"
        ]
      }
    ],
    "education": [
      {
        "degree": "Degree name",
        "institution": "Institution name",
        "location": "Location",
        "startDate": "MM/YYYY",
        "endDate": "MM/YYYY",
        "gpa": "GPA if provided",
        "relevant": "Relevant details if any"
      }
    ],
    "skills": {
      "technical": ["Enhanced technical skills"],
      "languages": ["Languages with levels"],
      "certifications": ["Professional certifications"]
    },
    "projects": [
      {
        "name": "Project name",
        "description": "Enhanced description with technical details",
        "technologies": "Technologies used",
        "link": "Project link if available"
      }
    ]
  },
  "atsScore": {
    "overall": [realistic score 50-85 based on actual content quality],
    "breakdown": {
      "keywords": [50-85 based on relevant terms],
      "formatting": [60-90 based on structure],
      "content": [40-85 based on achievement quality],
      "completeness": [50-95 based on section coverage]
    },
    "improvements": [
      "Specific, actionable improvement suggestions",
      "Based on actual content gaps or weaknesses",
      "Realistic recommendations for score improvement"
    ]
  },
  "keywords": ["relevant technical and industry keywords found"]
}

Resume to optimize:
${rawResumeText}

Provide realistic scoring and meaningful enhancements. Do not inflate scores - be honest about content quality.`;
}

function processGeneratedContent(content, originalData) {
  try {
    // Extract JSON from the AI response (handle potential markdown formatting)
    let jsonContent = content;

    // Remove markdown code blocks if present
    if (content.includes('```json')) {
      const jsonMatch = content.match(/```json\s*([\s\S]*?)\s*```/);
      if (jsonMatch) {
        jsonContent = jsonMatch[1];
      }
    } else if (content.includes('```')) {
      const jsonMatch = content.match(/```\s*([\s\S]*?)\s*```/);
      if (jsonMatch) {
        jsonContent = jsonMatch[1];
      }
    }

    // Parse the enhanced content
    const enhancedData = JSON.parse(jsonContent);

    // Validate the structure
    if (!enhancedData.enhancedContent || !enhancedData.atsScore) {
      throw new Error('Invalid AI response structure');
    }

    return {
      enhancedContent: enhancedData.enhancedContent,
      atsScore: enhancedData.atsScore,
      keywords: enhancedData.keywords || [],
      originalData,
      timestamp: new Date().toISOString(),
      version: '2.0',
      type: 'ai-enhanced'
    };

  } catch (error) {
    console.error('Error parsing AI response:', error);

    // Fallback: create a basic enhanced structure
    return createEnhancedFallback(originalData, content);
  }
}

function createEnhancedFallback(originalData, rawContent) {
  const { personal, education, experience, skills, projects } = originalData;

  // Calculate realistic ATS score based on actual content
  const atsScore = calculateRealisticATSScore(originalData);

  return {
    enhancedContent: {
      professionalSummary: personal.summary || 'Dedicated professional with strong background and commitment to excellence.',
      experience: experience?.map(exp => ({
        title: exp.title,
        company: exp.company,
        location: exp.location,
        startDate: exp.startDate,
        endDate: exp.current ? 'Present' : exp.endDate,
        achievements: exp.description ? [exp.description] : ['Contributed to team success and organizational goals']
      })) || [],
      education: education || [],
      skills: skills || { technical: [], languages: [], certifications: [] },
      projects: projects || []
    },
    atsScore,
    keywords: extractKeywords(originalData),
    originalData,
    timestamp: new Date().toISOString(),
    version: '2.0',
    type: 'fallback-enhanced',
    rawContent
  };
}

function createFallbackResume(formData) {
  console.log('🔄 Creating fallback resume for:', formData);

  // Ensure formData has the expected structure
  const safeFormData = {
    personal: formData?.personal || {},
    education: formData?.education || [],
    experience: formData?.experience || [],
    skills: formData?.skills || { technical: [], languages: [], certifications: [] },
    projects: formData?.projects || []
  };

  const { personal, education, experience, skills, projects } = safeFormData;

  // Calculate realistic ATS score
  const atsScore = calculateRealisticATSScore(safeFormData);

  const fallbackData = {
    enhancedContent: {
      professionalSummary: personal.summary || 'Dedicated professional with strong educational background and commitment to excellence.',
      experience: experience.map(exp => ({
        title: exp.title || 'Position',
        company: exp.company || 'Company',
        location: exp.location || '',
        startDate: exp.startDate || '',
        endDate: exp.current ? 'Present' : (exp.endDate || ''),
        achievements: exp.description ? [exp.description] : ['Contributed to team objectives and organizational success']
      })),
      education: education,
      skills: {
        technical: skills.technical || [],
        languages: skills.languages || [],
        certifications: skills.certifications || []
      },
      projects: projects
    },
    atsScore,
    keywords: extractKeywords(safeFormData),
    originalData: safeFormData,
    timestamp: new Date().toISOString(),
    version: '2.0',
    type: 'fallback'
  };

  console.log('✅ Fallback resume created with ATS score:', atsScore.overall);
  return fallbackData;
}

// Realistic ATS scoring algorithm based on actual content quality
function calculateRealisticATSScore(formData) {
  const { personal, education, experience, skills, projects } = formData;

  // Keywords scoring (25% weight)
  let keywordScore = 20; // Base score
  const technicalSkills = skills?.technical || [];
  const certifications = skills?.certifications || [];
  
  if (technicalSkills.length > 0) keywordScore += Math.min(25, technicalSkills.length * 3);
  if (certifications.length > 0) keywordScore += Math.min(15, certifications.length * 5);
  
  // Experience descriptions keyword analysis
  const experienceText = experience?.map(exp => exp.description || '').join(' ').toLowerCase();
  const actionVerbs = ['led', 'developed', 'implemented', 'achieved', 'managed', 'created', 'improved', 'optimized', 'designed', 'built'];
  const actionVerbCount = actionVerbs.filter(verb => experienceText.includes(verb)).length;
  keywordScore += Math.min(10, actionVerbCount * 2);
  
  keywordScore = Math.min(85, keywordScore); // Cap at 85

  // Formatting scoring (25% weight)
  let formattingScore = 60; // Base for standard structure
  if (personal?.email && personal?.firstName && personal?.lastName) formattingScore += 10;
  if (personal?.phone) formattingScore += 5;
  if (personal?.linkedin) formattingScore += 5;
  if (education?.length > 0) formattingScore += 10;
  if (experience?.length > 0) formattingScore += 10;
  formattingScore = Math.min(90, formattingScore);

  // Content quality scoring (25% weight)
  let contentScore = 30; // Base score
  
  // Professional summary quality
  if (personal?.summary && personal.summary.length > 50) contentScore += 10;
  
  // Experience quality
  const experienceQuality = experience?.reduce((score, exp) => {
    let expScore = 0;
    if (exp.description && exp.description.length > 30) expScore += 5;
    if (exp.description && /\d+/.test(exp.description)) expScore += 3; // Contains numbers
    if (exp.company && exp.title) expScore += 2;
    return score + expScore;
  }, 0) || 0;
  contentScore += Math.min(25, experienceQuality);
  
  // Projects quality
  const projectQuality = projects?.reduce((score, proj) => {
    let projScore = 0;
    if (proj.description && proj.description.length > 30) projScore += 3;
    if (proj.technologies) projScore += 2;
    return score + projScore;
  }, 0) || 0;
  contentScore += Math.min(10, projectQuality);
  
  contentScore = Math.min(85, contentScore);

  // Completeness scoring (25% weight)
  let completenessScore = 40; // Base score
  if (personal?.summary) completenessScore += 10;
  if (education?.length > 0) completenessScore += 15;
  if (experience?.length > 0) completenessScore += 20;
  if (skills?.technical?.length > 0) completenessScore += 10;
  if (projects?.length > 0) completenessScore += 5;
  completenessScore = Math.min(95, completenessScore);

  // Calculate overall weighted score
  const overall = Math.round(
    (keywordScore * 0.25) + 
    (formattingScore * 0.25) + 
    (contentScore * 0.25) + 
    (completenessScore * 0.25)
  );

  // Generate realistic improvements
  const improvements = generateImprovements(keywordScore, formattingScore, contentScore, completenessScore, formData);

  return {
    overall,
    breakdown: {
      keywords: keywordScore,
      formatting: formattingScore,
      content: contentScore,
      completeness: completenessScore
    },
    improvements
  };
}

function generateImprovements(keywordScore, formattingScore, contentScore, completenessScore, formData) {
  const improvements = [];
  
  if (keywordScore < 70) {
    improvements.push('Add more technical skills and industry-relevant keywords');
  }
  if (keywordScore < 60) {
    improvements.push('Include action verbs in experience descriptions (Led, Developed, Implemented)');
  }
  
  if (formattingScore < 80) {
    improvements.push('Complete contact information (phone, LinkedIn profile)');
  }
  
  if (contentScore < 70) {
    improvements.push('Add quantified achievements with specific metrics and results');
  }
  if (contentScore < 60) {
    improvements.push('Expand experience descriptions with more detailed accomplishments');
  }
  
  if (completenessScore < 80) {
    improvements.push('Add professional summary highlighting key strengths and experience');
  }
  if (completenessScore < 70) {
    improvements.push('Include relevant projects to showcase technical abilities');
  }
  
  // Specific improvements based on missing content
  if (!formData.personal?.summary) {
    improvements.push('Write a compelling professional summary (2-3 sentences)');
  }
  if (!formData.skills?.certifications?.length) {
    improvements.push('Add relevant professional certifications to boost credibility');
  }
  if (!formData.personal?.linkedin) {
    improvements.push('Include LinkedIn profile URL for better networking opportunities');
  }

  return improvements.slice(0, 4); // Limit to 4 most important improvements
}

function extractKeywords(formData) {
  const keywords = [];
  
  // Extract from technical skills
  if (formData.skills?.technical) {
    keywords.push(...formData.skills.technical);
  }
  
  // Extract from experience titles and companies
  formData.experience?.forEach(exp => {
    if (exp.title) keywords.push(exp.title);
    if (exp.company) keywords.push(exp.company);
  });
  
  // Extract from education
  formData.education?.forEach(edu => {
    if (edu.degree) keywords.push(edu.degree);
    if (edu.institution) keywords.push(edu.institution);
  });
  
  // Extract from projects
  formData.projects?.forEach(proj => {
    if (proj.technologies) {
      keywords.push(...proj.technologies.split(',').map(t => t.trim()));
    }
  });
  
  return [...new Set(keywords)].slice(0, 15); // Unique keywords, limit to 15
}

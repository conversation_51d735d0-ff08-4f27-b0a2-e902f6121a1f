import { NextResponse } from 'next/server';
import { GoogleGenerativeAI } from '@google/generative-ai';

// Initialize Gemini AI
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);

export async function POST(request) {
  try {
    console.log('🎯 Targeted resume generation API called');
    
    const { extractedResumeData, jobDescription, jobTitle, company } = await request.json();
    
    if (!extractedResumeData || !jobDescription) {
      return NextResponse.json(
        { error: 'Missing required data: resume data and job description are required' },
        { status: 400 }
      );
    }

    console.log('📊 Processing targeted resume generation...');
    console.log('🎯 Job Title:', jobTitle);
    console.log('🏢 Company:', company);
    console.log('📝 Job Description Length:', jobDescription.length);

    // Check if API key is configured
    if (!process.env.GEMINI_API_KEY || process.env.GEMINI_API_KEY === 'your-gemini-api-key-here') {
      console.log('⚠️ Gemini API key not configured, using fallback enhancement');
      return createFallbackTargetedResume(extractedResumeData, jobDescription, jobTitle, company);
    }

    try {
      // Get the generative model - Using Gemini 2.0 Flash (latest and most efficient)
      const model = genAI.getGenerativeModel({
        model: 'gemini-2.0-flash-exp',
        generationConfig: {
          temperature: 0.3,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 8000,
        }
      });

      const prompt = createTargetedResumePrompt(extractedResumeData, jobDescription, jobTitle, company);

      console.log('🤖 Calling Gemini AI for targeted resume generation...');
      const result = await model.generateContent(prompt);
      const response = await result.response;
      const generatedContent = response.text();

      console.log('✅ Gemini targeted analysis completed, length:', generatedContent?.length || 0);

      // Parse the AI response
      const enhancedResume = parseTargetedResumeResponse(generatedContent);

      return NextResponse.json({
        success: true,
        enhancedResume,
        jobTargeting: {
          jobTitle,
          company,
          description: jobDescription.substring(0, 200) + '...'
        },
        processedAt: new Date().toISOString()
      });

    } catch (aiError) {
      console.error('AI enhancement error:', aiError);
      console.log('⚠️ AI enhancement failed, using fallback enhancement');
      return createFallbackTargetedResume(extractedResumeData, jobDescription, jobTitle, company);
    }

  } catch (error) {
    console.error('💥 Targeted resume generation error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to generate targeted resume', 
        details: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
      },
      { status: 500 }
    );
  }
}

function createTargetedResumePrompt(resumeData, jobDescription, jobTitle, company) {
  // Convert resume data to raw text format
  const rawResumeText = `
NAME: ${resumeData.personal?.firstName || ''} ${resumeData.personal?.lastName || ''}
EMAIL: ${resumeData.personal?.email || ''}
PHONE: ${resumeData.personal?.phone || ''}
LOCATION: ${resumeData.personal?.location || ''}
LINKEDIN: ${resumeData.personal?.linkedin || ''}
PORTFOLIO: ${resumeData.personal?.portfolio || ''}

SUMMARY: ${resumeData.personal?.summary || ''}

EDUCATION:
${resumeData.education?.map(edu => `${edu.degree} | ${edu.institution} | ${edu.startDate} - ${edu.endDate}`).join('\n') || 'Not provided'}

EXPERIENCE:
${resumeData.experience?.map(exp => `${exp.title} | ${exp.company} | ${exp.startDate} - ${exp.current ? 'Present' : exp.endDate}\n${exp.description || ''}`).join('\n\n') || 'Not provided'}

SKILLS:
Technical: ${resumeData.skills?.technical?.join(', ') || 'Not provided'}
Languages: ${resumeData.skills?.languages?.join(', ') || 'Not provided'}
Certifications: ${resumeData.skills?.certifications?.join(', ') || 'Not provided'}

PROJECTS:
${resumeData.projects?.map(proj => `${proj.name} | ${proj.technologies}\n${proj.description || ''}`).join('\n\n') || 'Not provided'}
  `;

  return `
You are a professional ATS resume optimizer designed to maximize job-matching score (target: 95+ ATS score) for the specific position: ${jobTitle || 'target role'}${company ? ` at ${company}` : ''}.

Your tasks:
✅ Reformat the resume using consistent, clean bullet points
✅ Match job-specific keywords and relevant tech terms from the job description
✅ Improve wording, sentence flow, and action verbs
✅ Maintain clear formatting: Summary, Education, Experience, Projects, Skills
✅ Prioritize skills and experiences most relevant to this specific job
✅ Add quantified achievements that align with job requirements
✅ Use terminology and keywords from the job description naturally

Resume Format Reference (mimic this style):
---
NAME
📧 Email | 📱 Phone | 🔗 LinkedIn | 🌐 Portfolio

*PROFESSIONAL SUMMARY*
2–4 lines specifically targeting this role, incorporating key job requirements and showcasing relevant value proposition

*EDUCATION*
Degree | Institution | Year | Relevant coursework that matches job requirements

*EXPERIENCE*
Role | Company | Date Range
• Achievements that directly align with job responsibilities
• Quantifiable impact using metrics relevant to the target role
• Technical skills and tools mentioned in job description

*PROJECTS*
Project Title | Timeline | Technologies from job requirements
• Goals and technical implementation relevant to target role
• Results that demonstrate skills needed for the job

*SKILLS*
Technical Skills: Prioritized based on job description requirements
Languages: Relevant to the position
Certifications: That align with job qualifications
---

CRITICAL JOB-TARGETING REQUIREMENTS:
1. Analyze the job description for key requirements and responsibilities
2. Prioritize resume content that matches job requirements
3. Use keywords from job description naturally throughout resume
4. Emphasize achievements that demonstrate required competencies
5. Align technical skills with job requirements
6. Create a professional summary that directly addresses the role

TARGET JOB INFORMATION:
Job Title: ${jobTitle || 'Not specified'}
Company: ${company || 'Not specified'}
Job Description: ${jobDescription}

Please respond in the following JSON format:
{
  "enhancedResume": {
    "personal": {
      "firstName": "Enhanced first name",
      "lastName": "Enhanced last name", 
      "email": "<EMAIL>",
      "phone": "+1234567890",
      "location": "City, State",
      "linkedin": "linkedin.com/in/profile",
      "portfolio": "portfolio.com",
      "summary": "Compelling 3-4 sentence professional summary specifically targeted to this job, incorporating key requirements and showcasing relevant value proposition"
    },
    "experience": [
      {
        "id": 1,
        "title": "Job Title",
        "company": "Company Name",
        "location": "City, State",
        "startDate": "YYYY-MM",
        "endDate": "YYYY-MM",
        "current": false,
        "description": "Enhanced description with quantified achievements",
        "achievements": [
          "• Quantified achievement that aligns with job requirements",
          "• Another achievement using keywords from job description",
          "• Third achievement showing relevant impact and skills"
        ]
      }
    ],
    "education": [
      {
        "id": 1,
        "degree": "Degree Name",
        "institution": "Institution Name",
        "location": "City, State",
        "startDate": "YYYY-MM",
        "endDate": "YYYY-MM",
        "gpa": "GPA if relevant",
        "relevant": "Relevant coursework that aligns with job requirements"
      }
    ],
    "skills": {
      "technical": ["Prioritized technical skills matching job requirements"],
      "languages": ["Languages if relevant to the job"],
      "certifications": ["Relevant certifications for this role"]
    },
    "projects": [
      {
        "id": 1,
        "name": "Project Name",
        "description": "Project description highlighting skills relevant to the job",
        "technologies": "Technologies that match job requirements",
        "link": "project-link.com"
      }
    ]
  },
  "atsScore": {
    "overall": 95,
    "breakdown": {
      "keywords": 95,
      "formatting": 90,
      "structure": 95,
      "achievements": 90,
      "skills": 95
    }
  },
  "targetingAnalysis": {
    "keywordMatches": ["List of keywords from job description incorporated"],
    "skillsAlignment": ["Skills that directly match job requirements"],
    "experienceRelevance": "Analysis of how experience aligns with job",
    "improvementsMade": [
      "Specific improvement made for job targeting",
      "Another enhancement for ATS optimization",
      "Additional targeting improvement"
    ]
  },
  "recommendations": [
    "Specific recommendation for this job application",
    "Another targeted suggestion",
    "Additional advice for this role"
  ]
}

Ensure the enhanced resume is specifically optimized for this job while maintaining authenticity and accuracy of the original information.
`;
}

function parseTargetedResumeResponse(generatedContent) {
  try {
    // Try to extract JSON from the response
    const jsonMatch = generatedContent.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      const parsedJson = JSON.parse(jsonMatch[0]);
      return parsedJson;
    }
  } catch (error) {
    console.error('Error parsing targeted resume response:', error);
  }

  // Fallback response
  return {
    enhancedResume: {
      personal: { firstName: '', lastName: '', email: '', phone: '', location: '', summary: 'Enhanced professional summary targeting the specific job requirements.' },
      experience: [],
      education: [],
      skills: { technical: [], languages: [], certifications: [] },
      projects: []
    },
    atsScore: { overall: 85, breakdown: { keywords: 85, formatting: 85, structure: 85, achievements: 85, skills: 85 } },
    targetingAnalysis: {
      keywordMatches: [],
      skillsAlignment: [],
      experienceRelevance: 'Resume has been optimized for the target job.',
      improvementsMade: ['Job-specific optimization applied', 'ATS compatibility enhanced']
    },
    recommendations: ['Review the enhanced resume for accuracy', 'Customize further based on specific job requirements']
  };
}

function createFallbackTargetedResume(resumeData, jobDescription, jobTitle, company) {
  return NextResponse.json({
    success: true,
    enhancedResume: {
      ...resumeData,
      personal: {
        ...resumeData.personal,
        summary: resumeData.personal.summary || `Experienced professional seeking ${jobTitle || 'the target position'} role${company ? ` at ${company}` : ''}. Ready to contribute relevant skills and experience to drive organizational success.`
      }
    },
    atsScore: {
      overall: 80,
      breakdown: {
        keywords: 75,
        formatting: 85,
        structure: 80,
        achievements: 75,
        skills: 85
      }
    },
    targetingAnalysis: {
      keywordMatches: ['Basic optimization applied'],
      skillsAlignment: ['Manual review recommended'],
      experienceRelevance: 'Resume structure optimized for ATS compatibility.',
      improvementsMade: ['Basic job targeting applied', 'ATS formatting enhanced']
    },
    recommendations: [
      'Review and manually optimize keywords from the job description',
      'Quantify achievements with specific metrics',
      'Highlight experience most relevant to the target role',
      'Consider adding skills mentioned in the job description'
    ],
    fallback: true
  });
}

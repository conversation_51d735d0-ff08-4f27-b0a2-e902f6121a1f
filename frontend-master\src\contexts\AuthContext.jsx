'use client';

import React, { useContext, useState, useEffect, createContext } from 'react';
import {
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  signOut,
  onAuthStateChanged,
  GoogleAuthProvider,
  signInWithPopup
} from 'firebase/auth';
import { auth } from '../firebase/config'; // Adjusted path to Firebase config file

const AuthContext = createContext();

export function useAuth() {
  return useContext(AuthContext);
}

export function AuthProvider({ children }) {
  const [currentUser, setCurrentUser] = useState(null);
  const [loading, setLoading] = useState(true);
  function signup(email, password) {
    if (!auth) {
      return Promise.reject(new Error('Authentication service not available'));
    }
    return createUserWithEmailAndPassword(auth, email, password);
  }

  function login(email, password) {
    if (!auth) {
      return Promise.reject(new Error('Authentication service not available'));
    }
    return signInWithEmailAndPassword(auth, email, password);
  }

  function logout() {
    if (!auth) {
      return Promise.reject(new Error('Authentication service not available'));
    }
    return signOut(auth);
  }

  function loginWithGoogle() {
    if (!auth) {
      return Promise.reject(new Error('Authentication service not available'));
    }
    const provider = new GoogleAuthProvider();
    return signInWithPopup(auth, provider);
  }
  useEffect(() => {
    if (!auth || !auth.onAuthStateChanged) {
      console.warn('Auth service not available, setting loading to false');
      setLoading(false);
      return;
    }

    const unsubscribe = onAuthStateChanged(auth, (user) => {
      setCurrentUser(user);
      setLoading(false);
    });

    return unsubscribe; // Cleanup subscription on unmount
  }, []);

  const value = {
    currentUser,
    signup,
    login,
    logout,
    loginWithGoogle,
    loading
  };

  return (
    <AuthContext.Provider value={value}>
      {!loading && children}
    </AuthContext.Provider>
  );
}
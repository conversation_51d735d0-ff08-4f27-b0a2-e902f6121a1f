'use client';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  User, GraduationCap, Briefcase, FileText, Award, CheckCircle, 
  Circle, Clock, AlertTriangle, Sparkles, ArrowRight 
} from 'lucide-react';

const EnhancedProgressIndicator = ({
  steps,
  currentStep,
  completedSteps = [],
  onStepClick,
  variant = 'modern', // 'modern', 'minimal', 'detailed'
  showLabels = true,
  showProgress = true,
  showEstimatedTime = false // Disabled by default as requested
}) => {
  const getStepIcon = (step, index) => {
    const iconMap = {
      0: User,
      1: GraduationCap,
      2: Briefcase,
      3: FileText,
      4: Award,
      5: CheckCircle
    };
    
    return iconMap[index] || Circle;
  };

  const getStepStatus = (stepIndex) => {
    if (completedSteps.includes(stepIndex)) return 'completed';
    if (stepIndex === currentStep) return 'current';
    if (stepIndex < currentStep) return 'completed';
    return 'upcoming';
  };

  const getStepColor = (status) => {
    switch (status) {
      case 'completed':
        return 'text-green-400 bg-green-400/20 border-green-400';
      case 'current':
        return 'text-neural-purple bg-neural-purple/20 border-neural-purple';
      case 'upcoming':
        return 'text-gray-400 bg-gray-700/50 border-gray-600';
      default:
        return 'text-gray-400 bg-gray-700/50 border-gray-600';
    }
  };

  const totalProgress = steps.length > 0 ? ((currentStep + 1) / steps.length) * 100 : 0;

  if (variant === 'minimal') {
    return (
      <div className="p-4">
        {/* Progress Header */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <h3 className="text-lg font-semibold text-white">Progress</h3>
            <span className="px-3 py-1 bg-neural-purple/20 text-neural-purple text-sm rounded-full border border-neural-purple/30">
              Step {currentStep + 1} of {steps.length}
            </span>
          </div>
          <div className="text-neural-purple font-semibold">
            {Math.round(totalProgress)}%
          </div>
        </div>

        {/* Progress Bar */}
        <div className="w-full bg-gray-700 rounded-full h-2 mb-4">
          <motion.div
            className="bg-gradient-to-r from-neural-purple to-neural-pink h-2 rounded-full"
            initial={{ width: 0 }}
            animate={{ width: `${totalProgress}%` }}
            transition={{ duration: 0.5, ease: "easeOut" }}
          />
        </div>

        {/* Step Indicators */}
        <div className="flex items-center justify-between">
          {steps.map((step, index) => {
            const status = getStepStatus(index);
            const Icon = getStepIcon(step, index);
            const isClickable = onStepClick && (status === 'completed' || status === 'current');

            return (
              <div key={index} className="flex flex-col items-center flex-1">
                <motion.button
                  onClick={() => isClickable && onStepClick(index)}
                  className={`w-8 h-8 rounded-full border-2 flex items-center justify-center mb-2 transition-all duration-300 ${getStepColor(status)} ${
                    isClickable ? 'hover:scale-110 cursor-pointer' : 'cursor-default'
                  }`}
                  whileHover={isClickable ? { scale: 1.1 } : {}}
                  whileTap={isClickable ? { scale: 0.95 } : {}}
                  disabled={!isClickable}
                >
                  {status === 'completed' ? (
                    <CheckCircle className="h-4 w-4" />
                  ) : (
                    <Icon className="h-4 w-4" />
                  )}
                </motion.button>

                {/* Step Label */}
                <div className="text-center">
                  <div className={`text-xs font-medium ${
                    status === 'current' ? 'text-neural-purple' :
                    status === 'completed' ? 'text-green-400' : 'text-gray-400'
                  }`}>
                    {step.title}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    );
  }

  if (variant === 'detailed') {
    return (
      <div className="bg-gray-800/50 backdrop-blur-sm rounded-2xl p-6 border border-gray-700 mb-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h3 className="text-lg font-semibold text-white">Resume Builder Progress</h3>
            <p className="text-gray-400 text-sm">Complete all sections to generate your resume</p>
          </div>
          {showProgress && (
            <div className="text-right">
              <div className="text-2xl font-bold text-neural-purple">{Math.round(totalProgress)}%</div>
              <div className="text-gray-400 text-sm">Complete</div>
            </div>
          )}
        </div>

        {/* Progress Bar */}
        {showProgress && (
          <div className="mb-6">
            <div className="w-full bg-gray-700 rounded-full h-2">
              <motion.div
                className="bg-gradient-to-r from-neural-purple to-neural-pink h-2 rounded-full"
                initial={{ width: 0 }}
                animate={{ width: `${totalProgress}%` }}
                transition={{ duration: 0.5, ease: "easeOut" }}
              />
            </div>
          </div>
        )}

        {/* Steps */}
        <div className="space-y-4">
          {steps.map((step, index) => {
            const status = getStepStatus(index);
            const Icon = getStepIcon(step, index);
            const isClickable = onStepClick && (status === 'completed' || status === 'current');
            
            return (
              <motion.div
                key={index}
                className={`flex items-center gap-4 p-3 rounded-xl transition-all duration-300 ${
                  isClickable ? 'cursor-pointer hover:bg-gray-700/30' : ''
                } ${status === 'current' ? 'bg-neural-purple/10 border border-neural-purple/30' : ''}`}
                onClick={() => isClickable && onStepClick(index)}
                whileHover={isClickable ? { scale: 1.02 } : {}}
                whileTap={isClickable ? { scale: 0.98 } : {}}
              >
                {/* Step Icon */}
                <div className={`w-10 h-10 rounded-xl border-2 flex items-center justify-center ${getStepColor(status)}`}>
                  {status === 'completed' ? (
                    <CheckCircle className="h-5 w-5" />
                  ) : status === 'current' ? (
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                    >
                      <Icon className="h-5 w-5" />
                    </motion.div>
                  ) : (
                    <Icon className="h-5 w-5" />
                  )}
                </div>

                {/* Step Info */}
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <h4 className={`font-medium ${
                      status === 'current' ? 'text-neural-purple' : 
                      status === 'completed' ? 'text-green-400' : 'text-gray-400'
                    }`}>
                      {step.title}
                    </h4>
                    {step.required && (
                      <span className="px-2 py-1 bg-red-500/20 text-red-400 text-xs rounded-full">
                        Required
                      </span>
                    )}
                  </div>
                  <p className="text-gray-400 text-sm">{step.description}</p>
                </div>

                {/* Status Indicator */}
                <div className="flex items-center gap-2">
                  {status === 'completed' && (
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      className="text-green-400"
                    >
                      <CheckCircle className="h-5 w-5" />
                    </motion.div>
                  )}
                  {status === 'current' && (
                    <motion.div
                      animate={{ opacity: [0.5, 1, 0.5] }}
                      transition={{ duration: 2, repeat: Infinity }}
                      className="text-neural-purple"
                    >
                      <Sparkles className="h-5 w-5" />
                    </motion.div>
                  )}
                  {isClickable && (
                    <ArrowRight className="h-4 w-4 text-gray-400" />
                  )}
                </div>
              </motion.div>
            );
          })}
        </div>


      </div>
    );
  }

  // Default modern variant
  return (
    <div className="mb-8">
      {/* Progress Header */}
      {showProgress && (
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <h3 className="text-lg font-semibold text-white">Progress</h3>
            <span className="px-3 py-1 bg-neural-purple/20 text-neural-purple text-sm rounded-full border border-neural-purple/30">
              Step {currentStep + 1} of {steps.length}
            </span>
          </div>
          <div className="text-neural-purple font-semibold">
            {Math.round(totalProgress)}%
          </div>
        </div>
      )}

      {/* Progress Bar */}
      {showProgress && (
        <div className="w-full bg-gray-700 rounded-full h-2 mb-6">
          <motion.div
            className="bg-gradient-to-r from-neural-purple to-neural-pink h-2 rounded-full"
            initial={{ width: 0 }}
            animate={{ width: `${totalProgress}%` }}
            transition={{ duration: 0.5, ease: "easeOut" }}
          />
        </div>
      )}

      {/* Steps Navigation */}
      <div className="flex items-center justify-between">
        {steps.map((step, index) => {
          const status = getStepStatus(index);
          const Icon = getStepIcon(step, index);
          const isClickable = onStepClick && (status === 'completed' || status === 'current');
          
          return (
            <div key={index} className="flex flex-col items-center flex-1">
              {/* Step Button */}
              <motion.button
                onClick={() => isClickable && onStepClick(index)}
                className={`w-12 h-12 rounded-xl border-2 flex items-center justify-center mb-2 transition-all duration-300 ${getStepColor(status)} ${
                  isClickable ? 'hover:scale-110 cursor-pointer' : 'cursor-default'
                }`}
                whileHover={isClickable ? { scale: 1.1 } : {}}
                whileTap={isClickable ? { scale: 0.95 } : {}}
                disabled={!isClickable}
              >
                {status === 'completed' ? (
                  <CheckCircle className="h-6 w-6" />
                ) : (
                  <Icon className="h-6 w-6" />
                )}
              </motion.button>

              {/* Step Label */}
              {showLabels && (
                <div className="text-center">
                  <div className={`text-sm font-medium ${
                    status === 'current' ? 'text-neural-purple' : 
                    status === 'completed' ? 'text-green-400' : 'text-gray-400'
                  }`}>
                    {step.title}
                  </div>

                </div>
              )}

              {/* Connector Line */}
              {index < steps.length - 1 && (
                <div className="hidden md:block absolute top-6 left-1/2 w-full h-0.5 bg-gray-700 -z-10">
                  <motion.div
                    className="h-full bg-gradient-to-r from-neural-purple to-neural-pink"
                    initial={{ width: 0 }}
                    animate={{ 
                      width: index < currentStep ? '100%' : '0%' 
                    }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                  />
                </div>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default EnhancedProgressIndicator;

'use client';

import React from 'react';

const Services = () => {
  return (
    <section className="py-16 px-4 md:px-8 bg-gradient-to-b from-[#0A0A0A] to-black">
      <div className="max-w-6xl mx-auto">
        <h2 className="text-3xl md:text-4xl font-bold text-center mb-12 text-white">Our Services</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {[
            {
              title: "Missing Person Search",
              description: "Advanced facial recognition technology to help locate missing individuals quickly and efficiently."
            },
            {
              title: "Real-time Alerts",
              description: "Instant notifications when potential matches are found in our database."
            },
            {
              title: "Database Management",
              description: "Secure storage and management of missing person records and related information."
            },
            {
              title: "Community Support",
              description: "Connect with local communities and organizations to expand search efforts."
            },
            {
              title: "24/7 Support",
              description: "Round-the-clock assistance and support for urgent cases."
            },
            {
              title: "Data Analytics",
              description: "Advanced analytics to identify patterns and improve search effectiveness."
            }
          ].map((service, index) => (
            <div 
              key={index}
              className="bg-gray-900/5 rounded-lg p-6 hover:shadow-xl transition-all backdrop-blur-md border border-white/10"
            >
              <h3 className="text-xl font-semibold mb-4 text-white">{service.title}</h3>
              <p className="text-gray-400">{service.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Services;
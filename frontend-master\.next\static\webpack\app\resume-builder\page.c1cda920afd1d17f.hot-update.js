"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/resume-builder/page",{

/***/ "(app-pages-browser)/./src/components/resume/forms/ReviewForm.jsx":
/*!****************************************************!*\
  !*** ./src/components/resume/forms/ReviewForm.jsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EnhancedReviewForm: () => (/* binding */ EnhancedReviewForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowRight,Award,Briefcase,Building,Calendar,CheckCircle,Clock,Code,Crown,Download,Edit3,Eye,FileText,Folder,Globe,GraduationCap,Info,Link,Mail,MapPin,Palette,Phone,Save,Shield,Sparkles,Star,Target,TrendingUp,User,Wand2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowRight,Award,Briefcase,Building,Calendar,CheckCircle,Clock,Code,Crown,Download,Edit3,Eye,FileText,Folder,Globe,GraduationCap,Info,Link,Mail,MapPin,Palette,Phone,Save,Shield,Sparkles,Star,Target,TrendingUp,User,Wand2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowRight,Award,Briefcase,Building,Calendar,CheckCircle,Clock,Code,Crown,Download,Edit3,Eye,FileText,Folder,Globe,GraduationCap,Info,Link,Mail,MapPin,Palette,Phone,Save,Shield,Sparkles,Star,Target,TrendingUp,User,Wand2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowRight,Award,Briefcase,Building,Calendar,CheckCircle,Clock,Code,Crown,Download,Edit3,Eye,FileText,Folder,Globe,GraduationCap,Info,Link,Mail,MapPin,Palette,Phone,Save,Shield,Sparkles,Star,Target,TrendingUp,User,Wand2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowRight,Award,Briefcase,Building,Calendar,CheckCircle,Clock,Code,Crown,Download,Edit3,Eye,FileText,Folder,Globe,GraduationCap,Info,Link,Mail,MapPin,Palette,Phone,Save,Shield,Sparkles,Star,Target,TrendingUp,User,Wand2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/graduation-cap.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowRight,Award,Briefcase,Building,Calendar,CheckCircle,Clock,Code,Crown,Download,Edit3,Eye,FileText,Folder,Globe,GraduationCap,Info,Link,Mail,MapPin,Palette,Phone,Save,Shield,Sparkles,Star,Target,TrendingUp,User,Wand2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowRight,Award,Briefcase,Building,Calendar,CheckCircle,Clock,Code,Crown,Download,Edit3,Eye,FileText,Folder,Globe,GraduationCap,Info,Link,Mail,MapPin,Palette,Phone,Save,Shield,Sparkles,Star,Target,TrendingUp,User,Wand2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowRight,Award,Briefcase,Building,Calendar,CheckCircle,Clock,Code,Crown,Download,Edit3,Eye,FileText,Folder,Globe,GraduationCap,Info,Link,Mail,MapPin,Palette,Phone,Save,Shield,Sparkles,Star,Target,TrendingUp,User,Wand2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowRight,Award,Briefcase,Building,Calendar,CheckCircle,Clock,Code,Crown,Download,Edit3,Eye,FileText,Folder,Globe,GraduationCap,Info,Link,Mail,MapPin,Palette,Phone,Save,Shield,Sparkles,Star,Target,TrendingUp,User,Wand2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowRight,Award,Briefcase,Building,Calendar,CheckCircle,Clock,Code,Crown,Download,Edit3,Eye,FileText,Folder,Globe,GraduationCap,Info,Link,Mail,MapPin,Palette,Phone,Save,Shield,Sparkles,Star,Target,TrendingUp,User,Wand2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowRight,Award,Briefcase,Building,Calendar,CheckCircle,Clock,Code,Crown,Download,Edit3,Eye,FileText,Folder,Globe,GraduationCap,Info,Link,Mail,MapPin,Palette,Phone,Save,Shield,Sparkles,Star,Target,TrendingUp,User,Wand2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowRight,Award,Briefcase,Building,Calendar,CheckCircle,Clock,Code,Crown,Download,Edit3,Eye,FileText,Folder,Globe,GraduationCap,Info,Link,Mail,MapPin,Palette,Phone,Save,Shield,Sparkles,Star,Target,TrendingUp,User,Wand2,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _EnhancedFormField__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./EnhancedFormField */ \"(app-pages-browser)/./src/components/resume/forms/EnhancedFormField.jsx\");\n/* harmony import */ var _EnhancedTemplateSelector__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../EnhancedTemplateSelector */ \"(app-pages-browser)/./src/components/resume/EnhancedTemplateSelector.jsx\");\n/* __next_internal_client_entry_do_not_use__ EnhancedReviewForm auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst EnhancedReviewForm = (param)=>{\n    let { formData, updateFormData, atsAnalysis, onSave, onAISuggest, selectedTemplate, onTemplateSelect } = param;\n    var _formData_personal_summary, _formData_personal_summary1;\n    _s();\n    const [showTemplateSelector, setShowTemplateSelector] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [jobDescription, setJobDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(formData.jobDescription || '');\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('overview'); // 'overview', 'content', 'optimization'\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const reviewRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Update job description in parent state\n    const handleJobDescriptionChange = (e)=>{\n        setJobDescription(e.target.value);\n        updateFormData('jobDescription', '', e.target.value);\n    };\n    // Calculate completion score\n    const getCompletionScore = ()=>{\n        let score = 0;\n        let maxScore = 0;\n        // Personal info (25 points)\n        maxScore += 25;\n        if (formData.personal.firstName && formData.personal.lastName) score += 10;\n        if (formData.personal.email && formData.personal.phone) score += 10;\n        if (formData.personal.summary && formData.personal.summary.length >= 100) score += 5;\n        // Education (20 points)\n        maxScore += 20;\n        if (formData.education.some((edu)=>edu.degree && edu.institution)) score += 20;\n        // Experience (35 points)\n        maxScore += 35;\n        if (formData.experience.length > 0) score += 15;\n        if (formData.experience.some((exp)=>exp.description && exp.description.length >= 50)) score += 20;\n        // Skills (20 points)\n        maxScore += 20;\n        if (formData.skills.technical && formData.skills.technical.length > 0) score += 10;\n        if (formData.skills.languages && formData.skills.languages.length > 0) score += 5;\n        if (formData.skills.certifications && formData.skills.certifications.length > 0) score += 5;\n        return Math.round(score / maxScore * 100);\n    };\n    const completionScore = getCompletionScore();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n        initial: {\n            opacity: 0,\n            y: 20\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        exit: {\n            opacity: 0,\n            y: -20\n        },\n        className: \"space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-10 h-10 bg-neural-purple/20 rounded-xl flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"h-5 w-5 text-neural-purple\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                lineNumber: 70,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-white\",\n                                        children: \"Review & Generate\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Review your information and generate your professional resume\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                lineNumber: 73,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onSave,\n                            className: \"flex items-center gap-2 px-3 py-2 bg-gray-800 hover:bg-gray-700 border border-gray-600 rounded-lg transition-colors text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 13\n                                }, undefined),\n                                \" Save Draft\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                            lineNumber: 79,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                        lineNumber: 78,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setShowTemplateSelector(true),\n                        className: \"px-4 py-2 bg-gradient-to-r from-neural-purple to-neural-pink text-white rounded-lg font-medium transition-all duration-300 shadow-lg hover:shadow-xl\",\n                        children: \"Choose Template\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                        lineNumber: 87,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"ml-4 text-sm text-gray-400\",\n                        children: [\n                            \"Selected: \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-white font-medium\",\n                                children: selectedTemplate\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                lineNumber: 90,\n                                columnNumber: 64\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                        lineNumber: 90,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                lineNumber: 86,\n                columnNumber: 7\n            }, undefined),\n            showTemplateSelector && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TemplateSelector, {\n                selectedTemplate: selectedTemplate,\n                onTemplateSelect: (templateId)=>{\n                    onTemplateSelect(templateId);\n                    setShowTemplateSelector(false);\n                },\n                onClose: ()=>setShowTemplateSelector(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                lineNumber: 93,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-neural-purple/10 to-neural-pink/10 border border-neural-purple/30 rounded-xl p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold mb-2 flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"h-5 w-5 text-neural-purple\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                lineNumber: 106,\n                                columnNumber: 11\n                            }, undefined),\n                            \"Target Job Description (Optional)\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                        lineNumber: 105,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-300 text-sm mb-3\",\n                        children: \"Paste a job description to optimize your resume for a specific role. The AI will tailor your summary, skills, and experience accordingly.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                        lineNumber: 109,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                        value: jobDescription,\n                        onChange: handleJobDescriptionChange,\n                        rows: 6,\n                        className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400 resize-none mb-2\",\n                        placeholder: \"Paste the job description here for AI-powered resume optimization...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                        lineNumber: 110,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-400\",\n                        children: \"\\uD83D\\uDCA1 Adding a job description helps our AI tailor your resume to match specific requirements and keywords.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                        lineNumber: 117,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                lineNumber: 104,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-900/40 backdrop-blur-sm rounded-2xl p-6 border border-white/10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-semibold text-white mb-4\",\n                        children: \"Your Resume Information\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                        lineNumber: 122,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-semibold text-neural-purple mb-2 flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                lineNumber: 125,\n                                                columnNumber: 91\n                                            }, undefined),\n                                            \" Personal Info\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-300\",\n                                        children: [\n                                            formData.personal.firstName,\n                                            \" \",\n                                            formData.personal.lastName,\n                                            \" | \",\n                                            formData.personal.email,\n                                            \" | \",\n                                            formData.personal.phone\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-400\",\n                                        children: [\n                                            formData.personal.location,\n                                            \" | \",\n                                            formData.personal.linkedin,\n                                            \" | \",\n                                            formData.personal.portfolio\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-2 text-gray-200 text-sm\",\n                                        children: formData.personal.summary\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                lineNumber: 124,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-semibold text-neural-purple mb-2 flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                lineNumber: 131,\n                                                columnNumber: 91\n                                            }, undefined),\n                                            \" Education\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    formData.education.map((edu, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-1 text-sm text-gray-300\",\n                                            children: [\n                                                edu.degree,\n                                                \" - \",\n                                                edu.institution,\n                                                \" (\",\n                                                edu.startDate,\n                                                \" - \",\n                                                edu.endDate,\n                                                \") | \",\n                                                edu.location,\n                                                edu.gpa && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-2 text-xs text-gray-400\",\n                                                    children: [\n                                                        \"GPA: \",\n                                                        edu.gpa\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                    lineNumber: 135,\n                                                    columnNumber: 29\n                                                }, undefined),\n                                                edu.relevant && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-gray-400\",\n                                                    children: edu.relevant\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                    lineNumber: 136,\n                                                    columnNumber: 34\n                                                }, undefined)\n                                            ]\n                                        }, edu.id, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                            lineNumber: 133,\n                                            columnNumber: 15\n                                        }, undefined))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                lineNumber: 130,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-semibold text-neural-purple mb-2 flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 91\n                                            }, undefined),\n                                            \" Experience\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    formData.experience.map((exp, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-1 text-sm text-gray-300\",\n                                            children: [\n                                                exp.title,\n                                                \" at \",\n                                                exp.company,\n                                                \" (\",\n                                                exp.startDate,\n                                                \" - \",\n                                                exp.current ? 'Present' : exp.endDate,\n                                                \") | \",\n                                                exp.location,\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-gray-400 whitespace-pre-line\",\n                                                    children: exp.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                    lineNumber: 145,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, exp.id, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 15\n                                        }, undefined))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                lineNumber: 140,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-semibold text-neural-purple mb-2 flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 91\n                                            }, undefined),\n                                            \" Skills & Projects\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-400 mb-1\",\n                                        children: [\n                                            \"Technical: \",\n                                            formData.skills.technical.join(', ')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-400 mb-1\",\n                                        children: [\n                                            \"Languages: \",\n                                            formData.skills.languages.join(', ')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-400 mb-1\",\n                                        children: [\n                                            \"Certifications: \",\n                                            formData.skills.certifications.join(', ')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-2\",\n                                        children: formData.projects.map((proj, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-1 text-sm text-gray-300\",\n                                                children: [\n                                                    proj.name,\n                                                    \" (\",\n                                                    proj.technologies,\n                                                    \") \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-gray-400\",\n                                                        children: proj.link\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                        lineNumber: 157,\n                                                        columnNumber: 53\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-400 whitespace-pre-line\",\n                                                        children: proj.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                        lineNumber: 158,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, proj.id, true, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                lineNumber: 156,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                lineNumber: 149,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                        lineNumber: 123,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                lineNumber: 121,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-neural-purple/10 to-neural-pink/10 border border-neural-purple/30 rounded-xl p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3 mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-10 h-10 bg-neural-purple/20 rounded-xl flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"h-5 w-5 text-neural-purple\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                lineNumber: 169,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold text-white\",\n                                        children: \"Ready to Generate Your Resume?\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-300 text-sm\",\n                                        children: \"Review your information above and generate your professional resume\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                lineNumber: 172,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                        lineNumber: 168,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-sm font-medium text-white mb-3\",\n                                children: \"Content Requirements:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                lineNumber: 180,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            formData.personal.firstName && formData.personal.lastName ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-4 w-4 text-green-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 17\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"h-4 w-4 text-yellow-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm \".concat(formData.personal.firstName && formData.personal.lastName ? 'text-green-400' : 'text-yellow-400'),\n                                                children: \"Personal Information Complete\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            formData.education.some((edu)=>edu.degree && edu.institution) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-4 w-4 text-green-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 17\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"h-4 w-4 text-yellow-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm \".concat(formData.education.some((edu)=>edu.degree && edu.institution) ? 'text-green-400' : 'text-yellow-400'),\n                                                children: \"Education Information\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            formData.experience.some((exp)=>exp.title && exp.company && exp.description && exp.description.length >= 50) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-4 w-4 text-green-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 17\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"h-4 w-4 text-yellow-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm \".concat(formData.experience.some((exp)=>exp.title && exp.company && exp.description && exp.description.length >= 50) ? 'text-green-400' : 'text-yellow-400'),\n                                                children: \"Work Experience (minimum 50 characters per description)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            formData.personal.summary && formData.personal.summary.length >= 100 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-4 w-4 text-green-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                lineNumber: 217,\n                                                columnNumber: 17\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"h-4 w-4 text-yellow-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm \".concat(formData.personal.summary && formData.personal.summary.length >= 100 ? 'text-green-400' : 'text-yellow-400'),\n                                                children: \"Professional Summary (minimum 100 characters)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                lineNumber: 221,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                lineNumber: 181,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                        lineNumber: 179,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-800/30 rounded-lg p-4 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-sm font-medium text-white mb-2\",\n                                children: \"Content Summary:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                lineNumber: 230,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 gap-4 text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-400\",\n                                                children: \"Professional Summary:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                lineNumber: 233,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2 font-medium \".concat(((_formData_personal_summary = formData.personal.summary) === null || _formData_personal_summary === void 0 ? void 0 : _formData_personal_summary.length) >= 100 ? 'text-green-400' : 'text-yellow-400'),\n                                                children: [\n                                                    ((_formData_personal_summary1 = formData.personal.summary) === null || _formData_personal_summary1 === void 0 ? void 0 : _formData_personal_summary1.length) || 0,\n                                                    \" characters\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                lineNumber: 234,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-400\",\n                                                children: \"Total Experience Descriptions:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                lineNumber: 239,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2 font-medium \".concat(formData.experience.reduce((total, exp)=>{\n                                                    var _exp_description;\n                                                    return total + (((_exp_description = exp.description) === null || _exp_description === void 0 ? void 0 : _exp_description.length) || 0);\n                                                }, 0) >= 200 ? 'text-green-400' : 'text-yellow-400'),\n                                                children: [\n                                                    formData.experience.reduce((total, exp)=>{\n                                                        var _exp_description;\n                                                        return total + (((_exp_description = exp.description) === null || _exp_description === void 0 ? void 0 : _exp_description.length) || 0);\n                                                    }, 0),\n                                                    \" characters\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                                lineNumber: 240,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                lineNumber: 231,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                        lineNumber: 229,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onSave,\n                                className: \"flex items-center justify-center gap-2 px-6 py-3 bg-gray-800 hover:bg-gray-700 text-white font-medium rounded-lg transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Save Progress\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                lineNumber: 249,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    var _formData_personal_summary;\n                                    // Check minimum requirements\n                                    const hasMinimumContent = formData.personal.firstName && formData.personal.lastName && ((_formData_personal_summary = formData.personal.summary) === null || _formData_personal_summary === void 0 ? void 0 : _formData_personal_summary.length) >= 100 && formData.education.some((edu)=>edu.degree && edu.institution) && formData.experience.some((exp)=>{\n                                        var _exp_description;\n                                        return exp.title && exp.company && ((_exp_description = exp.description) === null || _exp_description === void 0 ? void 0 : _exp_description.length) >= 50;\n                                    });\n                                    if (!hasMinimumContent) {\n                                        alert('Please ensure all required fields are completed with minimum content requirements before generating your resume.');\n                                        return;\n                                    }\n                                    // Trigger resume generation\n                                    if (true) {\n                                        const event = new CustomEvent('generateResume');\n                                        window.dispatchEvent(event);\n                                    }\n                                },\n                                className: \"flex-1 flex items-center justify-center gap-2 px-6 py-3 bg-gradient-to-r from-neural-purple to-neural-pink text-white font-semibold rounded-lg hover:opacity-90 transition-opacity shadow-lg hover:shadow-xl\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                        lineNumber: 280,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Generate AI-Enhanced Resume\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                lineNumber: 257,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                        lineNumber: 248,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 p-3 bg-blue-500/10 border border-blue-500/20 rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-blue-300 text-sm flex items-start gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowRight_Award_Briefcase_Building_Calendar_CheckCircle_Clock_Code_Crown_Download_Edit3_Eye_FileText_Folder_Globe_GraduationCap_Info_Link_Mail_MapPin_Palette_Phone_Save_Shield_Sparkles_Star_Target_TrendingUp_User_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    className: \"h-4 w-4 mt-0.5 flex-shrink-0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                    lineNumber: 289,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Tip:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                            lineNumber: 291,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \" Make sure your professional summary is at least 100 characters and each work experience description is at least 50 characters for the best AI enhancement results.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                                    lineNumber: 290,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                            lineNumber: 288,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                        lineNumber: 287,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n                lineNumber: 167,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ReviewForm.jsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, undefined);\n};\n_s(EnhancedReviewForm, \"6UkKSbXdkAY97MFqygISNkFmYGs=\");\n_c = EnhancedReviewForm;\nvar _c;\n$RefreshReg$(_c, \"EnhancedReviewForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/resume/forms/ReviewForm.jsx\n"));

/***/ })

});
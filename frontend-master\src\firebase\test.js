// Firebase connection test utility
// This file can be used to test Firebase connectivity
import { firestore, database } from './config.js';
import { doc, getDoc, collection, getDocs, limit, query } from 'firebase/firestore';
import { ref, get } from 'firebase/database';

export const testFirebaseConnection = async () => {
  const results = {
    firestore: false,
    realtimeDatabase: false,
    errors: []
  };

  try {
    console.log('🧪 Testing Firebase connection...');

    // Test Firestore connection
    try {
      const testQuery = query(collection(firestore, 'users'), limit(1));
      await getDocs(testQuery);
      results.firestore = true;
      console.log('✅ Firestore connection successful');
    } catch (error) {
      results.errors.push(`Firestore: ${error.message}`);
      console.warn('⚠️ Firestore connection failed:', error.message);
    }

    // Test Realtime Database connection
    try {
      const testRef = ref(database, 'test');
      await get(testRef);
      results.realtimeDatabase = true;
      console.log('✅ Realtime Database connection successful');
    } catch (error) {
      results.errors.push(`Realtime Database: ${error.message}`);
      console.warn('⚠️ Realtime Database connection failed:', error.message);
    }

    console.log('🔍 Firebase connection test results:', results);
    return results;
  } catch (error) {
    console.error('❌ Firebase connection test failed:', error);
    results.errors.push(`General: ${error.message}`);
    return results;
  }
};

// Optional: Auto-run test in development
if (process.env.NODE_ENV === 'development') {
  // Delay the test to ensure Firebase is initialized
  setTimeout(() => {
    testFirebaseConnection();
  }, 1000);
}

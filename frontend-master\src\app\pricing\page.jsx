'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import PricingPage from '@/components/payment/PricingPage';
import SmartNavigationBar from '@/components/layout/SmartNavigationBar';
import { toast } from 'react-hot-toast';

export default function Pricing() {
  const { user, loading } = useAuth();
  const [userRegion, setUserRegion] = useState('US');
  const [currentPlan, setCurrentPlan] = useState('free');

  useEffect(() => {
    // Detect user region (you could use a geolocation service)
    const detectRegion = async () => {
      try {
        // Simple region detection based on timezone or IP
        const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
        if (timezone.includes('Asia/Kolkata') || timezone.includes('Asia/Calcutta')) {
          setUserRegion('IN');
        }
        
        // You could also use a geolocation API here
        // const response = await fetch('https://ipapi.co/json/');
        // const data = await response.json();
        // setUserRegion(data.country_code);
      } catch (error) {
        console.error('Failed to detect region:', error);
        // Default to US
        setUserRegion('US');
      }
    };

    detectRegion();
  }, []);

  useEffect(() => {
    // Get user's current plan from user data or API
    if (user) {
      // This would typically come from your user management system
      setCurrentPlan(user.plan || 'free');
    }
  }, [user]);

  const handlePaymentSuccess = (paymentData) => {
    toast.success('Payment successful! Your plan has been upgraded.');
    
    // Update user's plan in your system
    // This would typically involve calling your user management API
    setCurrentPlan(paymentData.plan);
    
    // Redirect to dashboard or success page
    setTimeout(() => {
      window.location.href = '/dashboard';
    }, 2000);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-neural-purple border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-400">Loading pricing information...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900">
      <SmartNavigationBar />
      <PricingPage 
        userRegion={userRegion}
        currentPlan={currentPlan}
        onPaymentSuccess={handlePaymentSuccess}
      />
    </div>
  );
}

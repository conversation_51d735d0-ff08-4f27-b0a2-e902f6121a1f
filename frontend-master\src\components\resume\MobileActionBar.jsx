'use client';
import { motion } from 'framer-motion';
import { ArrowLef<PERSON>, ArrowR<PERSON>, <PERSON>, Save } from 'lucide-react';

const MobileActionBar = ({
  currentStep,
  totalSteps,
  onPrevious,
  onNext,
  onSave,
  onTogglePreview,
  canProceed = true,
  isSaving = false,
  showPreview = false
}) => {
  const isFirstStep = currentStep === 0;
  const isLastStep = currentStep === totalSteps - 1;

  return (
    <div className="fixed bottom-0 left-0 right-0 z-40 bg-gray-900/95 backdrop-blur-sm border-t border-gray-700/50 p-4 xl:hidden">
      <div className="flex items-center justify-between gap-3 max-w-lg mx-auto">
        {/* Previous Button */}
        <button
          onClick={onPrevious}
          disabled={isFirstStep}
          className={`
            flex items-center gap-2 px-4 py-3 rounded-xl font-medium transition-all duration-200 flex-1
            ${isFirstStep 
              ? 'bg-gray-800 text-gray-500 cursor-not-allowed' 
              : 'bg-gray-800 text-gray-300 hover:bg-gray-700 border border-gray-600'
            }
          `}
        >
          <ArrowLeft className="h-4 w-4" />
          <span className="text-sm">Previous</span>
        </button>

        {/* Middle Actions */}
        <div className="flex items-center gap-2">
          {/* Save Button */}
          <button
            onClick={onSave}
            disabled={isSaving}
            className="p-3 bg-gray-800 hover:bg-gray-700 text-gray-300 rounded-xl transition-colors disabled:opacity-50 border border-gray-600"
            title="Save Progress"
          >
            <Save className="h-4 w-4" />
          </button>

          {/* Preview Toggle */}
          <button
            onClick={onTogglePreview}
            className={`
              p-3 rounded-xl transition-colors border
              ${showPreview 
                ? 'bg-neural-purple text-white border-neural-purple' 
                : 'bg-gray-800 text-gray-300 hover:bg-gray-700 border-gray-600'
              }
            `}
            title="Toggle Preview"
          >
            <Eye className="h-4 w-4" />
          </button>
        </div>

        {/* Next Button */}
        <button
          onClick={onNext}
          disabled={!canProceed}
          className={`
            flex items-center gap-2 px-4 py-3 rounded-xl font-medium transition-all duration-200 flex-1
            ${canProceed
              ? 'bg-gradient-to-r from-neural-purple to-neural-pink text-white shadow-lg hover:opacity-90'
              : 'bg-gray-800 text-gray-500 cursor-not-allowed border border-gray-700'
            }
          `}
        >
          <span className="text-sm">{isLastStep ? 'Complete' : 'Next'}</span>
          <ArrowRight className="h-4 w-4" />
        </button>
      </div>

      {/* Progress Indicator */}
      <div className="mt-3 max-w-lg mx-auto">
        <div className="flex items-center justify-center gap-1">
          {Array.from({ length: totalSteps }, (_, index) => (
            <div
              key={index}
              className={`
                h-1 rounded-full transition-all duration-300
                ${index <= currentStep 
                  ? 'bg-gradient-to-r from-neural-purple to-neural-pink w-8' 
                  : 'bg-gray-700 w-4'
                }
              `}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default MobileActionBar;

'use client';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  ArrowLeft, ArrowRight, Save, Download, Eye, EyeOff, 
  Settings, HelpCircle, Sparkles, CheckCircle, AlertTriangle,
  Clock, RotateCcw, Home
} from 'lucide-react';

const EnhancedNavigation = ({
  currentStep,
  totalSteps,
  canProceed = true,
  canGoBack = true,
  onNext,
  onPrevious,
  onSave,
  onPreview,
  onDownload,
  onHome,
  showPreview = false,
  isSaving = false,
  lastSaved,
  hasUnsavedChanges = false,
  validationErrors = {},
  completionStatus = {},
  estimatedTimeRemaining = 0
}) => {
  const isFirstStep = currentStep === 0;
  const isLastStep = currentStep === totalSteps - 1;
  const hasErrors = Object.keys(validationErrors).length > 0;
  
  const getStepCompletion = () => {
    const completed = Object.values(completionStatus).filter(Boolean).length;
    const total = Object.keys(completionStatus).length;
    return total > 0 ? Math.round((completed / total) * 100) : 0;
  };

  const formatLastSaved = (timestamp) => {
    if (!timestamp) return 'Never';
    const now = new Date();
    const saved = new Date(timestamp);
    const diffMinutes = Math.floor((now - saved) / (1000 * 60));
    
    if (diffMinutes < 1) return 'Just now';
    if (diffMinutes < 60) return `${diffMinutes}m ago`;
    if (diffMinutes < 1440) return `${Math.floor(diffMinutes / 60)}h ago`;
    return saved.toLocaleDateString();
  };

  return (
    <div className="sticky bottom-0 bg-gray-900/95 backdrop-blur-sm border-t border-gray-700 p-4 z-40">
      <div className="max-w-7xl mx-auto">
        {/* Top Row - Status and Actions */}
        <div className="flex items-center justify-between mb-4">
          {/* Left - Status */}
          <div className="flex items-center gap-4">
            {/* Save Status */}
            <div className="flex items-center gap-2">
              {isSaving ? (
                <>
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                  >
                    <RotateCcw className="h-4 w-4 text-neural-purple" />
                  </motion.div>
                  <span className="text-neural-purple text-sm">Saving...</span>
                </>
              ) : hasUnsavedChanges ? (
                <>
                  <AlertTriangle className="h-4 w-4 text-yellow-400" />
                  <span className="text-yellow-400 text-sm">Unsaved changes</span>
                </>
              ) : (
                <>
                  <CheckCircle className="h-4 w-4 text-green-400" />
                  <span className="text-green-400 text-sm">
                    Saved {formatLastSaved(lastSaved)}
                  </span>
                </>
              )}
            </div>

            {/* Completion Status */}
            <div className="hidden md:flex items-center gap-2">
              <div className="w-16 bg-gray-700 rounded-full h-2">
                <motion.div
                  className="bg-gradient-to-r from-neural-purple to-neural-pink h-2 rounded-full"
                  initial={{ width: 0 }}
                  animate={{ width: `${getStepCompletion()}%` }}
                  transition={{ duration: 0.5 }}
                />
              </div>
              <span className="text-gray-400 text-sm">{getStepCompletion()}%</span>
            </div>

            {/* Time Estimate */}
            {estimatedTimeRemaining > 0 && (
              <div className="hidden lg:flex items-center gap-1 text-gray-400 text-sm">
                <Clock className="h-4 w-4" />
                <span>{estimatedTimeRemaining} min left</span>
              </div>
            )}
          </div>

          {/* Right - Quick Actions */}
          <div className="flex items-center gap-2">
            {/* Preview Toggle */}
            <button
              onClick={onPreview}
              className={`p-2 rounded-lg transition-colors ${
                showPreview 
                  ? 'bg-neural-purple text-white' 
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              }`}
              title={showPreview ? 'Hide Preview' : 'Show Preview'}
            >
              {showPreview ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
            </button>

            {/* Save Button */}
            <button
              onClick={onSave}
              disabled={isSaving}
              className="flex items-center gap-2 px-3 py-2 bg-gray-700 hover:bg-gray-600 disabled:opacity-50 text-white rounded-lg transition-colors"
              title="Save Progress"
            >
              <Save className="h-4 w-4" />
              <span className="hidden sm:inline">Save</span>
            </button>

            {/* Download Button */}
            {isLastStep && (
              <button
                onClick={onDownload}
                className="flex items-center gap-2 px-3 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors"
                title="Download Resume"
              >
                <Download className="h-4 w-4" />
                <span className="hidden sm:inline">Download</span>
              </button>
            )}

            {/* Home Button */}
            <button
              onClick={onHome}
              className="p-2 bg-gray-700 hover:bg-gray-600 text-gray-300 rounded-lg transition-colors"
              title="Back to Home"
            >
              <Home className="h-4 w-4" />
            </button>
          </div>
        </div>

        {/* Bottom Row - Navigation */}
        <div className="flex items-center justify-between">
          {/* Previous Button */}
          <button
            onClick={onPrevious}
            disabled={!canGoBack || isFirstStep}
            className="flex items-center gap-2 px-4 py-2 bg-gray-700 hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-xl transition-colors"
          >
            <ArrowLeft className="h-4 w-4" />
            <span>Previous</span>
          </button>

          {/* Step Indicator */}
          <div className="flex items-center gap-4">
            {/* Current Step Info */}
            <div className="text-center">
              <div className="text-white font-medium">
                Step {currentStep + 1} of {totalSteps}
              </div>
              <div className="text-gray-400 text-sm">
                {hasErrors ? (
                  <span className="text-red-400">Please fix errors to continue</span>
                ) : canProceed ? (
                  'Ready to proceed'
                ) : (
                  'Complete required fields'
                )}
              </div>
            </div>

            {/* Step Dots */}
            <div className="hidden md:flex items-center gap-2">
              {Array.from({ length: totalSteps }, (_, index) => (
                <motion.div
                  key={index}
                  className={`w-2 h-2 rounded-full transition-colors ${
                    index === currentStep
                      ? 'bg-neural-purple'
                      : index < currentStep
                      ? 'bg-green-400'
                      : 'bg-gray-600'
                  }`}
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: index * 0.1 }}
                />
              ))}
            </div>
          </div>

          {/* Next Button */}
          <button
            onClick={onNext}
            disabled={!canProceed || hasErrors}
            className={`flex items-center gap-2 px-4 py-2 rounded-xl transition-colors font-medium ${
              isLastStep
                ? 'bg-green-600 hover:bg-green-700 disabled:bg-green-600/50'
                : 'bg-neural-purple hover:bg-neural-purple/80 disabled:bg-neural-purple/50'
            } disabled:cursor-not-allowed text-white`}
          >
            <span>{isLastStep ? 'Complete' : 'Next'}</span>
            {isLastStep ? (
              <CheckCircle className="h-4 w-4" />
            ) : (
              <ArrowRight className="h-4 w-4" />
            )}
          </button>
        </div>

        {/* Error Summary */}
        <AnimatePresence>
          {hasErrors && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="mt-4 p-3 bg-red-500/10 border border-red-500/20 rounded-lg"
            >
              <div className="flex items-center gap-2 mb-2">
                <AlertTriangle className="h-4 w-4 text-red-400" />
                <span className="text-red-400 font-medium text-sm">
                  {Object.keys(validationErrors).length} error(s) found
                </span>
              </div>
              <div className="space-y-1">
                {Object.entries(validationErrors).slice(0, 3).map(([field, error], index) => (
                  <div key={index} className="text-red-300 text-sm">
                    • {error}
                  </div>
                ))}
                {Object.keys(validationErrors).length > 3 && (
                  <div className="text-red-300 text-sm">
                    • And {Object.keys(validationErrors).length - 3} more...
                  </div>
                )}
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Help Text */}
        <AnimatePresence>
          {!hasErrors && !canProceed && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="mt-4 p-3 bg-blue-500/10 border border-blue-500/20 rounded-lg"
            >
              <div className="flex items-center gap-2">
                <HelpCircle className="h-4 w-4 text-blue-400" />
                <span className="text-blue-300 text-sm">
                  Complete the required fields above to continue to the next step
                </span>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

export default EnhancedNavigation;

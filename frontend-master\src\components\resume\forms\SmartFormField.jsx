'use client';
import { useState } from 'react';
import { AlertCircle, CheckCircle, Info, ChevronDown, Sparkles } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

const SmartFormField = ({
  label,
  type = 'text',
  value,
  onChange,
  placeholder,
  required = false,
  disabled = false,
  error = null,
  success = null,
  hint = null,
  icon: Icon,
  maxLength,
  showCharCount = false,
  rows = 4,
  suggestions = [],
  aiSuggestions = [],
  onAISuggestionApply,
  className = ''
}) => {
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [showAISuggestions, setShowAISuggestions] = useState(false);

  const handleSuggestionClick = (suggestion) => {
    onChange({ target: { value: suggestion } });
    setShowSuggestions(false);
  };

  const handleAISuggestionApply = (suggestion) => {
    if (onAISuggestionApply) {
      onAISuggestionApply(suggestion);
    } else {
      onChange({ target: { value: suggestion } });
    }
    setShowAISuggestions(false);
  };

  const fieldId = `field-${label.toLowerCase().replace(/\s+/g, '-')}`;
  const hasError = error && error.trim() !== '';
  const hasSuccess = success && success.trim() !== '';

  const inputClasses = `
    w-full px-4 py-3 
    bg-gray-800/50 border rounded-lg text-white placeholder-gray-400
    transition-all duration-200
    focus:outline-none focus:ring-2 focus:ring-neural-purple/50 focus:border-neural-purple
    ${hasError ? 'border-red-500 focus:ring-red-500/50 focus:border-red-500' : ''}
    ${hasSuccess ? 'border-green-500 focus:ring-green-500/50 focus:border-green-500' : ''}
    ${disabled ? 'opacity-50 cursor-not-allowed' : 'border-gray-600 hover:border-gray-500'}
    ${Icon ? 'pl-11' : ''}
    ${className}
  `;

  return (
    <div className="space-y-2">
      {/* Label */}
      <label htmlFor={fieldId} className="block text-sm font-medium text-gray-200">
        {label}
        {required && <span className="text-red-400 ml-1">*</span>}
      </label>

      {/* Input Container */}
      <div className="relative">
        {/* Icon */}
        {Icon && (
          <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
            <Icon className="h-5 w-5" />
          </div>
        )}

        {/* Input Field */}
        {type === 'textarea' ? (
          <textarea
            id={fieldId}
            value={value}
            onChange={onChange}
            placeholder={placeholder}
            required={required}
            disabled={disabled}
            rows={rows}
            maxLength={maxLength}
            className={inputClasses}
          />
        ) : (
          <input
            id={fieldId}
            type={type}
            value={value}
            onChange={onChange}
            placeholder={placeholder}
            required={required}
            disabled={disabled}
            maxLength={maxLength}
            className={inputClasses}
            list={suggestions.length > 0 ? `${fieldId}-suggestions` : undefined}
          />
        )}

        {/* Regular Suggestions Dropdown */}
        {suggestions.length > 0 && (
          <>
            <datalist id={`${fieldId}-suggestions`}>
              {suggestions.map((suggestion, index) => (
                <option key={index} value={suggestion} />
              ))}
            </datalist>
            
            {/* Manual suggestions dropdown for better control */}
            <button
              type="button"
              onClick={() => setShowSuggestions(!showSuggestions)}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-300"
            >
              <ChevronDown className={`h-4 w-4 transition-transform ${showSuggestions ? 'rotate-180' : ''}`} />
            </button>
          </>
        )}

        {/* AI Suggestions Button */}
        {aiSuggestions.length > 0 && (
          <button
            type="button"
            onClick={() => setShowAISuggestions(!showAISuggestions)}
            className="absolute right-10 top-1/2 transform -translate-y-1/2 text-neural-purple hover:text-neural-purple/80 transition-colors"
            title="AI Suggestions"
          >
            <Sparkles className="h-4 w-4" />
          </button>
        )}

        {/* Status Icons */}
        {(hasError || hasSuccess) && (
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
            {hasError && <AlertCircle className="h-5 w-5 text-red-400" />}
            {hasSuccess && <CheckCircle className="h-5 w-5 text-green-400" />}
          </div>
        )}
      </div>

      {/* Character Count */}
      {showCharCount && maxLength && (
        <div className="flex justify-end">
          <span className={`text-xs ${value.length > maxLength * 0.9 ? 'text-yellow-400' : 'text-gray-400'}`}>
            {value.length}/{maxLength}
          </span>
        </div>
      )}

      {/* Regular Suggestions Dropdown */}
      <AnimatePresence>
        {showSuggestions && suggestions.length > 0 && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="bg-gray-800 border border-gray-600 rounded-lg shadow-lg max-h-40 overflow-y-auto"
          >
            {suggestions.map((suggestion, index) => (
              <button
                key={index}
                type="button"
                onClick={() => handleSuggestionClick(suggestion)}
                className="w-full text-left px-4 py-2 text-sm text-gray-200 hover:bg-gray-700 first:rounded-t-lg last:rounded-b-lg"
              >
                {suggestion}
              </button>
            ))}
          </motion.div>
        )}
      </AnimatePresence>

      {/* AI Suggestions Dropdown */}
      <AnimatePresence>
        {showAISuggestions && aiSuggestions.length > 0 && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="bg-gradient-to-r from-neural-purple/10 to-neural-pink/10 border border-neural-purple/30 rounded-lg shadow-lg max-h-60 overflow-y-auto"
          >
            <div className="p-2 border-b border-neural-purple/30">
              <div className="flex items-center gap-2 text-neural-purple">
                <Sparkles className="h-4 w-4" />
                <span className="text-sm font-medium">AI Suggestions</span>
              </div>
            </div>
            {aiSuggestions.map((suggestion, index) => (
              <button
                key={index}
                type="button"
                onClick={() => handleAISuggestionApply(suggestion)}
                className="w-full text-left px-4 py-3 text-sm text-gray-200 hover:bg-neural-purple/20 border-b border-neural-purple/10 last:border-b-0 last:rounded-b-lg transition-colors"
              >
                {suggestion}
              </button>
            ))}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Error Message */}
      {hasError && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center gap-2 text-red-400 text-sm"
        >
          <AlertCircle className="h-4 w-4 flex-shrink-0" />
          <span>{error}</span>
        </motion.div>
      )}

      {/* Success Message */}
      {hasSuccess && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center gap-2 text-green-400 text-sm"
        >
          <CheckCircle className="h-4 w-4 flex-shrink-0" />
          <span>{success}</span>
        </motion.div>
      )}

      {/* Hint */}
      {hint && !hasError && (
        <div className="flex items-center gap-2 text-gray-400 text-sm">
          <Info className="h-4 w-4 flex-shrink-0" />
          <span>{hint}</span>
        </div>
      )}
    </div>
  );
};

export default SmartFormField;
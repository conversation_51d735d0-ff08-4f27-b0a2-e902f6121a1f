'use client';
import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  User, GraduationCap, Briefcase, Award, FileText, Sparkles, Save, RotateCcw, Download, Eye, Target, Clock, CheckCircle, AlertTriangle, Lightbulb
} from 'lucide-react';
import { toast } from 'react-hot-toast';
import EnhancedStepNavigation from './StepNavigation';
import SmartNavigationBar from '../layout/SmartNavigationBar';
import { PersonalInfoForm, EducationForm, ExperienceForm as BasicExperienceForm, SkillsProjectsForm as BasicSkillsProjectsForm, ReviewForm as BasicReviewForm } from './forms/ResumeFormComponents';
import { EnhancedExperienceForm } from './forms/ExperienceForm';
import { EnhancedSkillsProjectsForm } from './forms/SkillsProjectsForm';
import EnhancedProjectsForm from './forms/ProjectsForm';
import EnhancedSkillsForm from './forms/SkillsForm';
import { EnhancedReviewForm } from './forms/ReviewForm';
import EnhancedProgressIndicator from './EnhancedProgressIndicator';
import EnhancedNavigation from './EnhancedNavigation';
import ProgressBar from '../common/ProgressBar';
import EnhancedResumePreview from './ResumePreview';
import SuccessScreen from './SuccessScreen';
import useATSAnalysis from '@/hooks/useATSAnalysis';

const EnhancedResumeBuilder = ({ hideHeader = false }) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [selectedTemplate, setSelectedTemplate] = useState('modern');
  const [formData, setFormData] = useState({
    personal: {
      firstName: "",
      lastName: "",
      email: "",
      phone: "",
      location: "",
      linkedin: "",
      portfolio: "",
      summary: "",
      profileImage: ""
    },
    education: [
      { id: 1, degree: "", institution: "", location: "", startDate: "", endDate: "", gpa: "", relevant: "" }
    ],
    experience: [
      { id: 1, title: "", company: "", location: "", startDate: "", endDate: "", current: false, description: "" }
    ],
    skills: { technical: [], languages: [], certifications: [] },
    projects: [
      { id: 1, name: "", description: "", technologies: "", link: "" }
    ],
    jobDescription: ""
  });
  const [showPreview, setShowPreview] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [showProgressBar, setShowProgressBar] = useState(false);
  const [resumeGenerated, setResumeGenerated] = useState(false);
  const [validationErrors, setValidationErrors] = useState({});
  const [showValidationErrors, setShowValidationErrors] = useState(false);
  const [completedSteps, setCompletedSteps] = useState([]);
  const [autoSaveEnabled, setAutoSaveEnabled] = useState(true);
  const [lastSaved, setLastSaved] = useState(null);
  const [resumeData, setResumeData] = useState(null);
  const [resumeUrl, setResumeUrl] = useState("");
  const [atsScore, setAtsScore] = useState(null);
  const [suggestions, setSuggestions] = useState([]);
  const [aiSuggestionsEnabled, setAiSuggestionsEnabled] = useState(true);
  const [smartValidation, setSmartValidation] = useState(true);
  const [showHints, setShowHints] = useState(true);

  const steps = [
    { id: 0, title: "Personal Information", icon: User, description: "Tell us about yourself", estimatedTime: 3, required: true },
    { id: 1, title: "Education", icon: GraduationCap, description: "Your academic background", estimatedTime: 5, required: true },
    { id: 2, title: "Experience", icon: Briefcase, description: "Your work experience", estimatedTime: 8, required: true },
    { id: 3, title: "Projects", icon: FileText, description: "Showcase your projects", estimatedTime: 4, required: false },
    { id: 4, title: "Skills", icon: Award, description: "Your technical & soft skills", estimatedTime: 3, required: false },
    { id: 5, title: "Review & Generate", icon: FileText, description: "Finalize your resume", estimatedTime: 2, required: false }
  ];

  const atsAnalysis = useATSAnalysis(formData);

  useEffect(() => {
    // Check for backup data from auth flow first
    const backupData = localStorage.getItem('resumeFormDataBackup');
    const authTimestamp = localStorage.getItem('authFlowTimestamp');
    const savedData = localStorage.getItem('resumeFormData');

    // If backup data exists and is recent (within 10 minutes), use it
    if (backupData && authTimestamp) {
      const timeDiff = Date.now() - parseInt(authTimestamp);
      if (timeDiff < 10 * 60 * 1000) { // 10 minutes
        try {
          const parsed = JSON.parse(backupData);
          setFormData(parsed);
          setLastSaved(new Date());
          toast.success('Your work has been restored after sign-in!');
          // Clean up backup data
          localStorage.removeItem('resumeFormDataBackup');
          localStorage.removeItem('authFlowTimestamp');
          // Update main storage
          localStorage.setItem('resumeFormData', backupData);
          localStorage.setItem('resumeLastSaved', new Date().toISOString());
          return;
        } catch (error) {
          console.error('Failed to load backup data:', error);
        }
      }
    }

    // Fallback to regular saved data
    if (savedData) {
      try {
        const parsed = JSON.parse(savedData);
        setFormData(parsed);
        setLastSaved(new Date(localStorage.getItem('resumeLastSaved') || Date.now()));
        toast.success('Previous work restored!');
      } catch (error) {
        console.error('Failed to load saved data:', error);
      }
    }

    // Clean up old backup data
    localStorage.removeItem('resumeFormDataBackup');
    localStorage.removeItem('authFlowTimestamp');

  }, []);



  useEffect(() => {
    if (autoSaveEnabled && formData) {
      // Make form data globally accessible for auth flow
      if (typeof window !== 'undefined') {
        window.formData = formData;
      }

      const timeoutId = setTimeout(() => {
        localStorage.setItem('resumeFormData', JSON.stringify(formData));
        localStorage.setItem('resumeLastSaved', new Date().toISOString());
        setLastSaved(new Date());
      }, 2000);
      return () => clearTimeout(timeoutId);
    }
  }, [formData, autoSaveEnabled]);

  const validateStep = useCallback((stepIndex) => {
    const errors = {};
    switch (stepIndex) {
      case 0:
        if (!formData.personal.firstName?.trim()) errors.firstName = 'First name is required';
        if (!formData.personal.lastName?.trim()) errors.lastName = 'Last name is required';
        if (!formData.personal.email?.trim()) errors.email = 'Email is required';
        else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.personal.email)) errors.email = 'Please enter a valid email address';
        break;
      case 1:
        const validEducation = formData.education?.filter(edu => edu.degree?.trim() && edu.institution?.trim());
        if (!validEducation || validEducation.length === 0) errors.education = 'At least one education entry with degree and institution is required';
        break;
      case 2:
        const validExperience = formData.experience?.filter(exp => exp.title?.trim() && exp.company?.trim());
        if (!validExperience || validExperience.length === 0) errors.experience = 'At least one work experience entry with job title and company is required';
        break;
      case 3:
        // Projects are optional - no validation required
        break;
      case 4:
        // Skills are optional - no validation required
        break;
      default:
        break;
    }
    return errors;
  }, [formData]);

  const canProceedToNextStep = useCallback((stepIndex) => {
    const errors = validateStep(stepIndex);
    return Object.keys(errors).length === 0;
  }, [validateStep]);

  const getCompletedSteps = useCallback(() => {
    const completed = [];
    for (let i = 0; i < currentStep; i++) {
      const errors = validateStep(i);
      if (Object.keys(errors).length === 0) {
        completed.push(i);
      }
    }
    return completed;
  }, [currentStep, validateStep]);

  const getStepCompletionStatus = useCallback(() => {
    const status = {};
    steps.forEach((step, index) => {
      const errors = validateStep(index);
      status[index] = Object.keys(errors).length === 0;
    });
    return status;
  }, [steps, validateStep]);

  const canGenerateResume = useCallback(() => {
    for (let i = 0; i < steps.length - 1; i++) {
      if (!canProceedToNextStep(i)) return false;
    }
    return true;
  }, [canProceedToNextStep, steps.length]);

  const updateFormData = useCallback((section, field, value, index = null) => {
    setFormData(prev => {
      let newData = { ...prev };
      if (index !== null && Array.isArray(prev[section])) {
        const newArray = [...prev[section]];
        newArray[index] = { ...newArray[index], [field]: value };
        newData[section] = newArray;
      } else if (typeof prev[section] === 'object' && !Array.isArray(prev[section])) {
        newData[section] = { ...prev[section], [field]: value };
      } else {
        newData[field] = value;
      }
      return newData;
    });
    if (validationErrors[field]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  }, [validationErrors]);

  const addArrayItem = useCallback((section, template) => {
    setFormData(prev => ({
      ...prev,
      [section]: [...prev[section], { ...template, id: Math.random().toString(36).substring(2, 11) }]
    }));
  }, []);

  const removeArrayItem = useCallback((section, id) => {
    setFormData(prev => ({
      ...prev,
      [section]: prev[section].filter(item => item.id !== id)
    }));
  }, []);

  const nextStep = useCallback(() => {
    const errors = validateStep(currentStep);
    if (Object.keys(errors).length > 0) {
      setValidationErrors(errors);
      setShowValidationErrors(true);
      const errorMessages = Object.values(errors);
      toast.error(errorMessages[0]);
      return;
    }
    setCompletedSteps(prev => [...new Set([...prev, currentStep])]);
    setValidationErrors({});
    setShowValidationErrors(false);
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
      toast.success(`${steps[currentStep].title} completed!`);
    }
  }, [currentStep, validateStep, steps]);

  const prevStep = useCallback(() => {
    setValidationErrors({});
    setShowValidationErrors(false);
    if (currentStep > 0) setCurrentStep(currentStep - 1);
  }, [currentStep]);

  const handleStepClick = useCallback((stepIndex) => {
    if (stepIndex <= currentStep || completedSteps.includes(stepIndex)) {
      setCurrentStep(stepIndex);
      setValidationErrors({});
      setShowValidationErrors(false);
    }
  }, [currentStep, completedSteps]);

  const generateResume = useCallback(async () => {
    setValidationErrors({});
    setShowValidationErrors(false);
    if (!canGenerateResume()) {
      const allErrors = {};
      for (let i = 0; i < steps.length - 1; i++) {
        const stepErrors = validateStep(i);
        Object.assign(allErrors, stepErrors);
      }
      setValidationErrors(allErrors);
      setShowValidationErrors(true);
      for (let i = 0; i < steps.length - 1; i++) {
        const stepErrors = validateStep(i);
        if (Object.keys(stepErrors).length > 0) {
          setCurrentStep(i);
          toast.error(`Please complete all required fields in ${steps[i].title}`);
          return;
        }
      }
      return;
    }
    try {
      setIsGenerating(true);
      setShowProgressBar(true);
      setResumeGenerated(false);
      const response = await fetch('/api/generate-resume', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ formData, templateId: selectedTemplate }),
      });
      const contentType = response.headers.get('content-type');
      if (!contentType || !contentType.includes('application/json')) throw new Error('Server returned non-JSON response');
      const data = await response.json();
      if (!response.ok) throw new Error(data.error || 'Failed to generate resume');
      if (!data.resumeData || !data.downloadUrl) throw new Error('Generated resume data is incomplete');
      setResumeUrl(data.downloadUrl);
      setResumeData(data.resumeData);
      setAtsScore(data.atsScore || 75);
      setSuggestions(data.suggestions || []);
      setResumeGenerated(true);
      toast.success('Resume generated successfully!');
    } catch (error) {
      console.error('Resume generation error:', error);
      toast.error(error.message || 'Failed to generate resume');
    } finally {
      setIsGenerating(false);
      setShowProgressBar(false);
    }
  }, [canGenerateResume, formData, steps, validateStep, selectedTemplate]);

  const handleSave = useCallback(() => {
    localStorage.setItem('resumeFormData', JSON.stringify(formData));
    localStorage.setItem('resumeLastSaved', new Date().toISOString());
    setLastSaved(new Date());
    toast.success('Progress saved!');
  }, [formData]);

  const handleAISuggest = useCallback(async (section) => {
    if (!aiSuggestionsEnabled) return;
    toast.loading('Getting AI suggestions...');
    try {
      await new Promise(resolve => setTimeout(resolve, 1500));
      toast.dismiss();
      toast.success('AI suggestions applied!');
    } catch (error) {
      toast.dismiss();
      toast.error('Failed to get AI suggestions');
    }
  }, [aiSuggestionsEnabled]);

  // Set up event listener for resume generation from ReviewForm
  useEffect(() => {
    const handleGenerateResume = () => {
      // Check if we can generate resume and call the function
      if (canGenerateResume()) {
        generateResume();
      } else {
        toast.error('Please complete all required sections before generating your resume.');
      }
    };

    if (typeof window !== 'undefined') {
      window.addEventListener('generateResume', handleGenerateResume);
      return () => window.removeEventListener('generateResume', handleGenerateResume);
    }
  }, [canGenerateResume, generateResume]);

  const getStepValidation = useCallback(() => {
    const validation = {};
    steps.forEach((step, index) => {
      const errors = validateStep(index);
      validation[index] = Object.keys(errors).length === 0;
    });
    return validation;
  }, [steps, validateStep]);

  const renderStepContent = () => {
    const commonProps = {
      formData,
      updateFormData,
      addArrayItem,
      removeArrayItem,
      atsAnalysis,
      validationErrors,
      showValidationErrors,
      onSave: handleSave,
      onAISuggest: handleAISuggest
    };
    switch (currentStep) {
      case 0:
        return <PersonalInfoForm {...commonProps} />;
      case 1:
        return <EducationForm {...commonProps} />;
      case 2:
        return <EnhancedExperienceForm {...commonProps} />;
      case 3:
        return <EnhancedProjectsForm {...commonProps} />;
      case 4:
        return <EnhancedSkillsForm {...commonProps} />;
      case 5:
        return <EnhancedReviewForm {...commonProps} selectedTemplate={selectedTemplate} onTemplateSelect={setSelectedTemplate} />;
      default:
        return null;
    }
  };

  if (resumeGenerated && resumeData && resumeUrl) {
    return (
      <SuccessScreen 
        formData={formData}
        resumeData={resumeData}
        onStartOver={() => {
          setResumeGenerated(false);
          setResumeData(null);
          setResumeUrl("");
          setCurrentStep(0);
        }}
        onEditResume={() => {
          setResumeGenerated(false);
          setCurrentStep(4); // Go back to review step
        }}
      />
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-black to-[#0A0A0A] text-white relative">
      <div className="absolute inset-0 opacity-20">
        <div className="absolute inset-0 bg-[url('/grid.svg')] [mask-image:radial-gradient(ellipse_at_center,white,transparent)]" />
      </div>
      <ProgressBar isVisible={showProgressBar} onComplete={() => setShowProgressBar(false)} />
      <div className="relative z-10 pb-32">
        <div className="container mx-auto px-4 py-8">
          {!hideHeader && (
            <motion.div className="text-center mb-8" initial={{ opacity: 0, y: -20 }} animate={{ opacity: 1, y: 0 }}>
              <div className="flex items-center justify-center gap-3 mb-6">
                <div className="relative">
                  <Sparkles className="h-10 w-10 text-neural-pink animate-pulse" />
                  <div className="absolute inset-0 bg-neural-pink opacity-20 rounded-full blur-md"></div>
                </div>
                <h1 className="text-4xl lg:text-5xl font-bold text-white">Enhanced Resume Builder</h1>
              </div>
              <p className="text-gray-300 text-lg max-w-2xl mx-auto">Create professional, ATS-friendly resumes with our enhanced AI-powered builder</p>
            </motion.div>
          )}
          <EnhancedProgressIndicator
            steps={steps}
            currentStep={currentStep}
            completedSteps={completedSteps}
            onStepClick={handleStepClick}
            variant="modern"
            showLabels={true}
            showProgress={true}
            showEstimatedTime={true}
          />
          <div className="grid grid-cols-1 xl:grid-cols-1 gap-8 max-w-4xl xl:max-w-5xl mx-auto xl:mr-96">
            <div className="w-full">
              <AnimatePresence mode="wait">
                <motion.div key={currentStep} initial={{ opacity: 0, x: 20 }} animate={{ opacity: 1, x: 0 }} exit={{ opacity: 0, x: -20 }} transition={{ duration: 0.3 }}>
                  {renderStepContent()}
                </motion.div>
              </AnimatePresence>
            </div>
            <div className="hidden xl:block">
              {/* Fixed Preview Container */}
              <div className="xl:fixed xl:top-24 xl:right-8 xl:w-80 xl:h-[calc(100vh-8rem)] xl:overflow-hidden">
                <motion.div
                  className="bg-gray-900/60 backdrop-blur-md rounded-2xl border border-white/10 h-full flex flex-col"
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.2 }}
                >
                  {/* Preview Header */}
                  <div className="flex items-center justify-between p-4 border-b border-white/10">
                    <h3 className="text-lg font-semibold flex items-center gap-2">
                      <Eye className="h-5 w-5 text-neural-blue" />
                      Live Preview
                    </h3>
                    <div className="flex items-center gap-2">
                      <button
                        onClick={() => setShowPreview(!showPreview)}
                        className={`px-3 py-1.5 rounded-lg text-sm font-medium transition-colors ${
                          showPreview ? 'bg-neural-purple text-white' : 'bg-gray-800 text-gray-300 hover:bg-gray-700'
                        }`}
                      >
                        {showPreview ? 'Hide' : 'Show'}
                      </button>
                    </div>
                  </div>

                  {/* Preview Content */}
                  <div className="flex-1 overflow-hidden">
                    {showPreview ? (
                      <div className="h-full overflow-y-auto p-2">
                        <div className="transform scale-[0.65] origin-top-left w-[154%] h-[154%]">
                          <div className="bg-white rounded-lg shadow-lg">
                            <EnhancedResumePreview formData={formData} selectedTemplate={selectedTemplate} />
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div className="flex items-center justify-center h-full text-gray-400">
                        <div className="text-center">
                          <Eye className="h-16 w-16 mx-auto mb-4 opacity-30" />
                          <p className="text-sm">Click "Show" to preview</p>
                          <p className="text-xs text-gray-500 mt-1">Live updates as you type</p>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Preview Footer */}
                  {showPreview && (
                    <div className="p-3 border-t border-white/10 bg-gray-800/30">
                      <div className="flex items-center justify-between text-xs text-gray-400">
                        <span>Template: {selectedTemplate}</span>
                        <span>Auto-updating</span>
                      </div>
                    </div>
                  )}
                </motion.div>
              </div>

              {/* Mobile/Tablet Preview (Non-fixed) */}
              <div className="xl:hidden">
                <motion.div className="bg-gray-900/60 backdrop-blur-md rounded-2xl p-6 border border-white/10" initial={{ opacity: 0, x: 20 }} animate={{ opacity: 1, x: 0 }} transition={{ delay: 0.2 }}>
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-semibold flex items-center gap-2">
                      <Eye className="h-5 w-5 text-neural-blue" />
                      Resume Preview
                    </h3>
                    <button
                      onClick={() => setShowPreview(!showPreview)}
                      className={`px-3 py-1.5 rounded-lg text-sm font-medium transition-colors ${
                        showPreview ? 'bg-neural-purple text-white' : 'bg-gray-800 text-gray-300 hover:bg-gray-700'
                      }`}
                    >
                      {showPreview ? 'Hide' : 'Show'}
                    </button>
                  </div>
                  {showPreview ? (
                    <div className="max-h-[600px] overflow-y-auto border border-white/10 rounded-lg">
                      <EnhancedResumePreview formData={formData} selectedTemplate={selectedTemplate} />
                    </div>
                  ) : (
                    <div className="text-center py-12 text-gray-400">
                      <Eye className="h-12 w-12 mx-auto mb-3 opacity-30" />
                      <p className="text-sm">Click "Show" to preview your resume</p>
                    </div>
                  )}
                </motion.div>
              </div>
              <motion.div className="bg-gray-900/60 backdrop-blur-md rounded-2xl p-6 border border-white/10" initial={{ opacity: 0, x: 20 }} animate={{ opacity: 1, x: 0 }} transition={{ delay: 0.3 }}>
                <div className="flex items-center gap-2 mb-4">
                  <Target className="h-5 w-5 text-neural-purple" />
                  <h3 className="text-lg font-semibold">ATS Score</h3>
                </div>
                <div className="text-center mb-4">
                  <div className={`text-3xl font-bold ${
                    (atsAnalysis.overallScore || 0) >= 80 ? 'text-green-400' :
                    (atsAnalysis.overallScore || 0) >= 60 ? 'text-yellow-400' : 'text-red-400'
                  }`}>
                    {atsAnalysis.overallScore || 0}%
                  </div>
                  <div className="text-sm text-gray-400">
                    {(atsAnalysis.overallScore || 0) >= 80 ? 'Excellent' :
                     (atsAnalysis.overallScore || 0) >= 60 ? 'Good' : 'Needs Work'}
                  </div>
                </div>
                {atsAnalysis.recommendations?.length > 0 && (
                  <div className="space-y-2">
                    <div className="text-xs text-gray-500 mb-2">Top Suggestions:</div>
                    {atsAnalysis.recommendations.slice(0, 2).map((rec, index) => (
                      <div key={index} className="flex items-start gap-2 text-sm">
                        <div className="w-1.5 h-1.5 bg-yellow-400 rounded-full mt-2 flex-shrink-0"></div>
                        <span className="text-gray-300">{rec.message}</span>
                      </div>
                    ))}
                  </div>
                )}
              </motion.div>

            </div>
          </div>
        </div>
      </div>
      <SmartNavigationBar currentStep={currentStep} totalSteps={steps.length} onPrevious={prevStep} onNext={nextStep} onGenerate={generateResume} onSave={handleSave} onPreview={() => setShowPreview(!showPreview)} isGenerating={isGenerating} canProceed={currentStep === steps.length - 1 ? canGenerateResume() : canProceedToNextStep(currentStep)} showPreview={showPreview} steps={steps} formData={formData} atsScore={atsAnalysis.overallScore} autoSaveEnabled={autoSaveEnabled} />
    </div>
  );
};

export default EnhancedResumeBuilder;

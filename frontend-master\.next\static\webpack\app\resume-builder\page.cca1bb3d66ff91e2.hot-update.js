"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/resume-builder/page",{

/***/ "(app-pages-browser)/./src/components/resume/RedesignedResumeBuilder.jsx":
/*!***********************************************************!*\
  !*** ./src/components/resume/RedesignedResumeBuilder.jsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _TopStepNavigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./TopStepNavigation */ \"(app-pages-browser)/./src/components/resume/TopStepNavigation.jsx\");\n/* harmony import */ var _MobileActionBar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./MobileActionBar */ \"(app-pages-browser)/./src/components/resume/MobileActionBar.jsx\");\n/* harmony import */ var _MobilePreviewModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./MobilePreviewModal */ \"(app-pages-browser)/./src/components/resume/MobilePreviewModal.jsx\");\n/* harmony import */ var _EnhancedLivePreview__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./EnhancedLivePreview */ \"(app-pages-browser)/./src/components/resume/EnhancedLivePreview.jsx\");\n/* harmony import */ var _UniversalTemplateSelector__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./UniversalTemplateSelector */ \"(app-pages-browser)/./src/components/resume/UniversalTemplateSelector.jsx\");\n/* harmony import */ var _forms_UniversalFormFields__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./forms/UniversalFormFields */ \"(app-pages-browser)/./src/components/resume/forms/UniversalFormFields.jsx\");\n/* harmony import */ var _templates_MultiIndustryTemplates__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./templates/MultiIndustryTemplates */ \"(app-pages-browser)/./src/components/resume/templates/MultiIndustryTemplates.jsx\");\n/* harmony import */ var _config_production__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/config/production */ \"(app-pages-browser)/./src/config/production.js\");\n/* harmony import */ var _hooks_usePerformanceMonitoring__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/hooks/usePerformanceMonitoring */ \"(app-pages-browser)/./src/hooks/usePerformanceMonitoring.js\");\n/* harmony import */ var _components_seo_ResumeBuilderSEO__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/seo/ResumeBuilderSEO */ \"(app-pages-browser)/./src/components/seo/ResumeBuilderSEO.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n// Import new components\n\n\n\n\n\n\n// Import template system\n\n// Import production features\n\n\n\nconst RedesignedResumeBuilder = (param)=>{\n    let { hideHeader = false } = param;\n    _s();\n    // Performance monitoring\n    const { trackRender, trackError, trackInteraction } = (0,_hooks_usePerformanceMonitoring__WEBPACK_IMPORTED_MODULE_11__[\"default\"])();\n    // Core state\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [selectedTemplate, setSelectedTemplate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_config_production__WEBPACK_IMPORTED_MODULE_10__.RESUME_CONFIG.templates.default);\n    const [showTemplateSelector, setShowTemplateSelector] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showMobilePreview, setShowMobilePreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Form data with universal structure\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        personal: {\n            firstName: \"\",\n            lastName: \"\",\n            email: \"\",\n            phone: \"\",\n            location: \"\",\n            summary: \"\"\n        },\n        experience: [\n            {\n                id: 1,\n                title: \"\",\n                company: \"\",\n                location: \"\",\n                startDate: \"\",\n                endDate: \"\",\n                current: false,\n                description: \"\"\n            }\n        ],\n        education: [\n            {\n                id: 1,\n                degree: \"\",\n                field: \"\",\n                institution: \"\",\n                location: \"\",\n                graduationDate: \"\",\n                gpa: \"\"\n            }\n        ],\n        skills: {\n            technical: [],\n            languages: [],\n            certifications: []\n        }\n    });\n    // UI state\n    const [validationErrors, setValidationErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isSaving, setIsSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [completedSteps, setCompletedSteps] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Step configuration - simplified and universal\n    const steps = [\n        {\n            id: 0,\n            title: \"Personal Info\",\n            description: \"Your contact information and professional summary\"\n        },\n        {\n            id: 1,\n            title: \"Experience\",\n            description: \"Your work history and achievements\"\n        },\n        {\n            id: 2,\n            title: \"Education\",\n            description: \"Your educational background and qualifications\"\n        },\n        {\n            id: 3,\n            title: \"Skills\",\n            description: \"Your core competencies and abilities\"\n        },\n        {\n            id: 4,\n            title: \"Review\",\n            description: \"Review and download your resume\"\n        }\n    ];\n    // Auto-save functionality\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RedesignedResumeBuilder.useEffect\": ()=>{\n            const timeoutId = setTimeout({\n                \"RedesignedResumeBuilder.useEffect.timeoutId\": ()=>{\n                    localStorage.setItem('universalResumeData', JSON.stringify(formData));\n                    localStorage.setItem('selectedTemplate', selectedTemplate);\n                }\n            }[\"RedesignedResumeBuilder.useEffect.timeoutId\"], 2000);\n            return ({\n                \"RedesignedResumeBuilder.useEffect\": ()=>clearTimeout(timeoutId)\n            })[\"RedesignedResumeBuilder.useEffect\"];\n        }\n    }[\"RedesignedResumeBuilder.useEffect\"], [\n        formData,\n        selectedTemplate\n    ]);\n    // Load saved data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RedesignedResumeBuilder.useEffect\": ()=>{\n            const savedData = localStorage.getItem('universalResumeData');\n            const savedTemplate = localStorage.getItem('selectedTemplate');\n            if (savedData) {\n                try {\n                    setFormData(JSON.parse(savedData));\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success('Previous work restored!');\n                } catch (error) {\n                    console.error('Failed to load saved data:', error);\n                }\n            }\n            if (savedTemplate && _templates_MultiIndustryTemplates__WEBPACK_IMPORTED_MODULE_9__.INDUSTRY_TEMPLATES[savedTemplate]) {\n                setSelectedTemplate(savedTemplate);\n            }\n        }\n    }[\"RedesignedResumeBuilder.useEffect\"], []);\n    // Validation logic\n    const validateStep = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"RedesignedResumeBuilder.useCallback[validateStep]\": (stepIndex)=>{\n            const errors = {};\n            switch(stepIndex){\n                case 0:\n                    var _formData_personal_firstName, _formData_personal_lastName, _formData_personal_email;\n                    if (!((_formData_personal_firstName = formData.personal.firstName) === null || _formData_personal_firstName === void 0 ? void 0 : _formData_personal_firstName.trim())) errors.firstName = 'First name is required';\n                    if (!((_formData_personal_lastName = formData.personal.lastName) === null || _formData_personal_lastName === void 0 ? void 0 : _formData_personal_lastName.trim())) errors.lastName = 'Last name is required';\n                    if (!((_formData_personal_email = formData.personal.email) === null || _formData_personal_email === void 0 ? void 0 : _formData_personal_email.trim())) errors.email = 'Email is required';\n                    else if (!/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(formData.personal.email)) {\n                        errors.email = 'Please enter a valid email address';\n                    }\n                    break;\n                case 1:\n                    var _formData_experience;\n                    const validExperience = (_formData_experience = formData.experience) === null || _formData_experience === void 0 ? void 0 : _formData_experience.filter({\n                        \"RedesignedResumeBuilder.useCallback[validateStep]\": (exp)=>{\n                            var _exp_title, _exp_company;\n                            return ((_exp_title = exp.title) === null || _exp_title === void 0 ? void 0 : _exp_title.trim()) && ((_exp_company = exp.company) === null || _exp_company === void 0 ? void 0 : _exp_company.trim());\n                        }\n                    }[\"RedesignedResumeBuilder.useCallback[validateStep]\"]);\n                    if (!validExperience || validExperience.length === 0) {\n                        errors.experience = 'At least one work experience entry is required';\n                    }\n                    break;\n                case 2:\n                    var _formData_education;\n                    const validEducation = (_formData_education = formData.education) === null || _formData_education === void 0 ? void 0 : _formData_education.filter({\n                        \"RedesignedResumeBuilder.useCallback[validateStep]\": (edu)=>{\n                            var _edu_degree, _edu_institution;\n                            return ((_edu_degree = edu.degree) === null || _edu_degree === void 0 ? void 0 : _edu_degree.trim()) && ((_edu_institution = edu.institution) === null || _edu_institution === void 0 ? void 0 : _edu_institution.trim());\n                        }\n                    }[\"RedesignedResumeBuilder.useCallback[validateStep]\"]);\n                    if (!validEducation || validEducation.length === 0) {\n                        errors.education = 'At least one education entry is required';\n                    }\n                    break;\n                case 3:\n                    break;\n                default:\n                    break;\n            }\n            return errors;\n        }\n    }[\"RedesignedResumeBuilder.useCallback[validateStep]\"], [\n        formData\n    ]);\n    // Navigation logic\n    const canProceedToNextStep = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"RedesignedResumeBuilder.useCallback[canProceedToNextStep]\": (stepIndex)=>{\n            const errors = validateStep(stepIndex);\n            return Object.keys(errors).length === 0;\n        }\n    }[\"RedesignedResumeBuilder.useCallback[canProceedToNextStep]\"], [\n        validateStep\n    ]);\n    const nextStep = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"RedesignedResumeBuilder.useCallback[nextStep]\": ()=>{\n            const errors = validateStep(currentStep);\n            if (Object.keys(errors).length > 0) {\n                setValidationErrors(errors);\n                const errorMessages = Object.values(errors);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(errorMessages[0]);\n                return;\n            }\n            setCompletedSteps({\n                \"RedesignedResumeBuilder.useCallback[nextStep]\": (prev)=>[\n                        ...new Set([\n                            ...prev,\n                            currentStep\n                        ])\n                    ]\n            }[\"RedesignedResumeBuilder.useCallback[nextStep]\"]);\n            setValidationErrors({});\n            if (currentStep < steps.length - 1) {\n                setCurrentStep(currentStep + 1);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"\".concat(steps[currentStep].title, \" completed!\"));\n            }\n        }\n    }[\"RedesignedResumeBuilder.useCallback[nextStep]\"], [\n        currentStep,\n        validateStep,\n        steps\n    ]);\n    const prevStep = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"RedesignedResumeBuilder.useCallback[prevStep]\": ()=>{\n            setValidationErrors({});\n            if (currentStep > 0) setCurrentStep(currentStep - 1);\n        }\n    }[\"RedesignedResumeBuilder.useCallback[prevStep]\"], [\n        currentStep\n    ]);\n    // Form data management\n    const updateFormData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"RedesignedResumeBuilder.useCallback[updateFormData]\": function(section, field, value) {\n            let index = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : null;\n            setFormData({\n                \"RedesignedResumeBuilder.useCallback[updateFormData]\": (prev)=>{\n                    let newData = {\n                        ...prev\n                    };\n                    if (index !== null && Array.isArray(prev[section])) {\n                        const newArray = [\n                            ...prev[section]\n                        ];\n                        newArray[index] = {\n                            ...newArray[index],\n                            [field]: value\n                        };\n                        newData[section] = newArray;\n                    } else if (typeof prev[section] === 'object' && !Array.isArray(prev[section])) {\n                        newData[section] = {\n                            ...prev[section],\n                            [field]: value\n                        };\n                    } else {\n                        newData[field] = value;\n                    }\n                    return newData;\n                }\n            }[\"RedesignedResumeBuilder.useCallback[updateFormData]\"]);\n            // Clear validation errors for this field\n            if (validationErrors[field]) {\n                setValidationErrors({\n                    \"RedesignedResumeBuilder.useCallback[updateFormData]\": (prev)=>{\n                        const newErrors = {\n                            ...prev\n                        };\n                        delete newErrors[field];\n                        return newErrors;\n                    }\n                }[\"RedesignedResumeBuilder.useCallback[updateFormData]\"]);\n            }\n        }\n    }[\"RedesignedResumeBuilder.useCallback[updateFormData]\"], [\n        validationErrors\n    ]);\n    const addArrayItem = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"RedesignedResumeBuilder.useCallback[addArrayItem]\": (section, template)=>{\n            setFormData({\n                \"RedesignedResumeBuilder.useCallback[addArrayItem]\": (prev)=>({\n                        ...prev,\n                        [section]: [\n                            ...prev[section],\n                            {\n                                ...template,\n                                id: Math.random().toString(36).substring(2, 11)\n                            }\n                        ]\n                    })\n            }[\"RedesignedResumeBuilder.useCallback[addArrayItem]\"]);\n        }\n    }[\"RedesignedResumeBuilder.useCallback[addArrayItem]\"], []);\n    const removeArrayItem = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"RedesignedResumeBuilder.useCallback[removeArrayItem]\": (section, id)=>{\n            setFormData({\n                \"RedesignedResumeBuilder.useCallback[removeArrayItem]\": (prev)=>({\n                        ...prev,\n                        [section]: prev[section].filter({\n                            \"RedesignedResumeBuilder.useCallback[removeArrayItem]\": (item)=>item.id !== id\n                        }[\"RedesignedResumeBuilder.useCallback[removeArrayItem]\"])\n                    })\n            }[\"RedesignedResumeBuilder.useCallback[removeArrayItem]\"]);\n        }\n    }[\"RedesignedResumeBuilder.useCallback[removeArrayItem]\"], []);\n    // Save functionality\n    const handleSave = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"RedesignedResumeBuilder.useCallback[handleSave]\": ()=>{\n            setIsSaving(true);\n            localStorage.setItem('universalResumeData', JSON.stringify(formData));\n            localStorage.setItem('selectedTemplate', selectedTemplate);\n            setTimeout({\n                \"RedesignedResumeBuilder.useCallback[handleSave]\": ()=>{\n                    setIsSaving(false);\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success('Progress saved!');\n                }\n            }[\"RedesignedResumeBuilder.useCallback[handleSave]\"], 1000);\n        }\n    }[\"RedesignedResumeBuilder.useCallback[handleSave]\"], [\n        formData,\n        selectedTemplate\n    ]);\n    // Download functionality\n    const handleDownload = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"RedesignedResumeBuilder.useCallback[handleDownload]\": ()=>{\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success('Download feature coming soon!');\n        }\n    }[\"RedesignedResumeBuilder.useCallback[handleDownload]\"], []);\n    // Calculate completion percentage\n    const getCompletionPercentage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"RedesignedResumeBuilder.useCallback[getCompletionPercentage]\": ()=>{\n            var _formData_experience, _formData_education, _formData_skills_technical, _formData_skills;\n            let totalFields = 0;\n            let completedFields = 0;\n            // Personal info (4 required fields)\n            totalFields += 4;\n            if (formData.personal.firstName) completedFields++;\n            if (formData.personal.lastName) completedFields++;\n            if (formData.personal.email) completedFields++;\n            if (formData.personal.summary) completedFields++;\n            // Experience (at least 1 entry with title and company)\n            totalFields += 2;\n            const validExp = (_formData_experience = formData.experience) === null || _formData_experience === void 0 ? void 0 : _formData_experience.filter({\n                \"RedesignedResumeBuilder.useCallback[getCompletionPercentage]\": (exp)=>exp.title && exp.company\n            }[\"RedesignedResumeBuilder.useCallback[getCompletionPercentage]\"]);\n            if ((validExp === null || validExp === void 0 ? void 0 : validExp.length) > 0) completedFields += 2;\n            // Education (at least 1 entry with degree and institution)\n            totalFields += 2;\n            const validEdu = (_formData_education = formData.education) === null || _formData_education === void 0 ? void 0 : _formData_education.filter({\n                \"RedesignedResumeBuilder.useCallback[getCompletionPercentage]\": (edu)=>edu.degree && edu.institution\n            }[\"RedesignedResumeBuilder.useCallback[getCompletionPercentage]\"]);\n            if ((validEdu === null || validEdu === void 0 ? void 0 : validEdu.length) > 0) completedFields += 2;\n            // Skills (optional but counts if present)\n            if (((_formData_skills = formData.skills) === null || _formData_skills === void 0 ? void 0 : (_formData_skills_technical = _formData_skills.technical) === null || _formData_skills_technical === void 0 ? void 0 : _formData_skills_technical.length) > 0) {\n                totalFields += 1;\n                completedFields += 1;\n            }\n            return Math.round(completedFields / totalFields * 100);\n        }\n    }[\"RedesignedResumeBuilder.useCallback[getCompletionPercentage]\"], [\n        formData\n    ]);\n    // Render step content\n    const renderStepContent = ()=>{\n        const commonProps = {\n            formData,\n            updateFormData,\n            addArrayItem,\n            removeArrayItem,\n            validationErrors\n        };\n        switch(currentStep){\n            case 0:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_UniversalFormFields__WEBPACK_IMPORTED_MODULE_8__.UniversalPersonalForm, {\n                    ...commonProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                    lineNumber: 315,\n                    columnNumber: 16\n                }, undefined);\n            case 1:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_UniversalFormFields__WEBPACK_IMPORTED_MODULE_8__.UniversalExperienceForm, {\n                    ...commonProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                    lineNumber: 317,\n                    columnNumber: 16\n                }, undefined);\n            case 2:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_UniversalFormFields__WEBPACK_IMPORTED_MODULE_8__.UniversalEducationForm, {\n                    ...commonProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                    lineNumber: 319,\n                    columnNumber: 16\n                }, undefined);\n            case 3:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_UniversalFormFields__WEBPACK_IMPORTED_MODULE_8__.UniversalSkillsForm, {\n                    ...commonProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                    lineNumber: 321,\n                    columnNumber: 16\n                }, undefined);\n            case 4:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                        className: \"bg-gray-900/60 backdrop-blur-md rounded-2xl border border-gray-700/50 p-8 text-center\",\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center gap-3 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                                className: \"h-8 w-8 text-neural-pink\",\n                                                animate: {\n                                                    scale: [\n                                                        1,\n                                                        1.2,\n                                                        1\n                                                    ]\n                                                },\n                                                transition: {\n                                                    duration: 2,\n                                                    repeat: Infinity\n                                                },\n                                                children: \"⚡\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                                lineNumber: 332,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-neural-pink opacity-20 rounded-full blur-md\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                                lineNumber: 339,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                        lineNumber: 331,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-semibold text-white\",\n                                        children: \"Resume Created in Minutes!\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                lineNumber: 330,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-300 mb-4 max-w-md mx-auto\",\n                                children: \"You've turned hours of work into minutes with AI. Your professional resume is ready for job applications.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                lineNumber: 346,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center gap-4 text-sm text-gray-400 mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"flex items-center gap-1\",\n                                        children: \"✅ ATS-Optimized\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                        lineNumber: 351,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"flex items-center gap-1\",\n                                        children: \"✅ Job-Ready Format\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                        lineNumber: 354,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"flex items-center gap-1\",\n                                        children: \"✅ Professional Design\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                        lineNumber: 357,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                lineNumber: 350,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowTemplateSelector(true),\n                                        className: \"px-6 py-3 bg-gray-800 hover:bg-gray-700 text-white rounded-lg transition-colors border border-gray-600\",\n                                        children: \"Change Template\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                        lineNumber: 363,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.info('Upgrade to Pro to download your resume!'),\n                                        className: \"px-6 py-3 bg-gradient-to-r from-yellow-500 to-orange-500 text-white rounded-lg hover:opacity-90 transition-opacity\",\n                                        children: \"Upgrade to Download\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                        lineNumber: 369,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                lineNumber: 362,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 p-4 bg-gradient-to-r from-neural-purple/10 to-neural-pink/10 border border-neural-purple/20 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-neural-blue text-sm font-medium\",\n                                        children: \"\\uD83D\\uDE80 Upgrade to Pro: Download instantly, access 50+ premium templates, and get AI-powered job matching\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                        lineNumber: 378,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400 text-xs mt-1\",\n                                        children: \"Join thousands who've landed their dream jobs faster with our AI tools\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                        lineNumber: 381,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                lineNumber: 377,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                        lineNumber: 325,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                    lineNumber: 324,\n                    columnNumber: 11\n                }, undefined);\n            default:\n                return null;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-b from-black to-[#0A0A0A] text-white relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 opacity-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 bg-[url('/grid.svg')] [mask-image:radial-gradient(ellipse_at_center,white,transparent)]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                    lineNumber: 397,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                lineNumber: 396,\n                columnNumber: 7\n            }, undefined),\n            !hideHeader && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 pt-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                        className: \"text-center\",\n                        initial: {\n                            opacity: 0,\n                            y: -20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center gap-2 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                                className: \"h-8 w-8 text-neural-pink\",\n                                                animate: {\n                                                    rotate: 360\n                                                },\n                                                transition: {\n                                                    duration: 20,\n                                                    repeat: Infinity,\n                                                    ease: \"linear\"\n                                                },\n                                                children: \"✨\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                                lineNumber: 411,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-neural-pink opacity-20 rounded-full blur-md\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                                lineNumber: 418,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                        lineNumber: 410,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-semibold text-neural-blue\",\n                                        children: \"Turn Hours Into Minutes — With AI\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                        lineNumber: 420,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                lineNumber: 409,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl lg:text-4xl font-bold text-white mb-4\",\n                                children: \"AI Resume Builder\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                lineNumber: 425,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-300 text-lg max-w-2xl mx-auto mb-2\",\n                                children: \"Create ATS-optimized resumes in minutes with AI-powered suggestions\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                lineNumber: 429,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap items-center justify-center gap-4 text-sm text-gray-400\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"flex items-center gap-1\",\n                                        children: \"⚡ Create Resume in 5 minutes\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                        lineNumber: 434,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"flex items-center gap-1\",\n                                        children: \"\\uD83C\\uDFAF ATS Scoring\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                        lineNumber: 437,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"flex items-center gap-1\",\n                                        children: \"\\uD83D\\uDE80 Job-Specific Content\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                        lineNumber: 440,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                lineNumber: 433,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                        lineNumber: 404,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                    lineNumber: 403,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                lineNumber: 402,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 pb-32 xl:pb-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col xl:flex-row gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 max-w-4xl\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TopStepNavigation__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        currentStep: currentStep,\n                                        totalSteps: steps.length,\n                                        onPrevious: prevStep,\n                                        onNext: nextStep,\n                                        onSave: handleSave,\n                                        onHome: ()=>window.location.href = '/',\n                                        canProceed: canProceedToNextStep(currentStep),\n                                        isSaving: isSaving,\n                                        completionPercentage: getCompletionPercentage(),\n                                        stepTitles: steps.map((step)=>step.title),\n                                        completedSteps: completedSteps\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                        lineNumber: 456,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.AnimatePresence, {\n                                        mode: \"wait\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                x: 20\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                x: 0\n                                            },\n                                            exit: {\n                                                opacity: 0,\n                                                x: -20\n                                            },\n                                            transition: {\n                                                duration: 0.3\n                                            },\n                                            children: renderStepContent()\n                                        }, currentStep, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                            lineNumber: 472,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                        lineNumber: 471,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                lineNumber: 454,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden xl:block xl:w-80 xl:flex-shrink-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"sticky top-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EnhancedLivePreview__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        formData: formData,\n                                        selectedTemplate: selectedTemplate\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                        lineNumber: 487,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                    lineNumber: 486,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                                lineNumber: 485,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                        lineNumber: 452,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                    lineNumber: 451,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                lineNumber: 450,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MobileActionBar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                currentStep: currentStep,\n                totalSteps: steps.length,\n                onPrevious: prevStep,\n                onNext: nextStep,\n                onSave: handleSave,\n                onTogglePreview: ()=>setShowMobilePreview(true),\n                canProceed: canProceedToNextStep(currentStep),\n                isSaving: isSaving,\n                showPreview: showMobilePreview\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                lineNumber: 498,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.AnimatePresence, {\n                children: showTemplateSelector && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_UniversalTemplateSelector__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    selectedTemplate: selectedTemplate,\n                    onTemplateSelect: setSelectedTemplate,\n                    onClose: ()=>setShowTemplateSelector(false),\n                    formData: formData\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                    lineNumber: 513,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                lineNumber: 511,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.AnimatePresence, {\n                children: showMobilePreview && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MobilePreviewModal__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    isOpen: showMobilePreview,\n                    onClose: ()=>setShowMobilePreview(false),\n                    formData: formData,\n                    selectedTemplate: selectedTemplate\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                    lineNumber: 524,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n                lineNumber: 522,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\RedesignedResumeBuilder.jsx\",\n        lineNumber: 394,\n        columnNumber: 5\n    }, undefined);\n};\n_s(RedesignedResumeBuilder, \"oczsLP1gceTKgsoWe28yIigVraY=\", false, function() {\n    return [\n        _hooks_usePerformanceMonitoring__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n    ];\n});\n_c = RedesignedResumeBuilder;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (RedesignedResumeBuilder);\nvar _c;\n$RefreshReg$(_c, \"RedesignedResumeBuilder\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/resume/RedesignedResumeBuilder.jsx\n"));

/***/ })

});
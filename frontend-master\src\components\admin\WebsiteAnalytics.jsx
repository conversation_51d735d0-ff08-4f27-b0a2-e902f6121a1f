'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import visitorService from '@/services/VisitorService';
import { 
  Eye, 
  Users, 
  Calendar,
  TrendingUp,
  Globe,
  Clock,
  BarChart3,
  Monitor
} from 'lucide-react';

const WebsiteAnalytics = () => {
  const [analyticsData, setAnalyticsData] = useState({
    totalVisitors: 0,
    totalPageViews: 0,
    todayVisits: 0,
    yesterdayVisits: 0,
    recentVisits: []
  });
  const [weeklyData, setWeeklyData] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchAnalyticsData();
    
    // Refresh data every 30 seconds
    const interval = setInterval(fetchAnalyticsData, 30000);
    return () => clearInterval(interval);
  }, []);

  const fetchAnalyticsData = async () => {
    try {
      const [analytics, weekly] = await Promise.all([
        visitorService.getAnalyticsData(),
        visitorService.getWeeklyVisits()
      ]);
      
      setAnalyticsData(analytics);
      setWeeklyData(weekly);
    } catch (error) {
      console.error('Error fetching analytics data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getTrendPercentage = () => {
    if (analyticsData.yesterdayVisits === 0) return 100;
    const change = ((analyticsData.todayVisits - analyticsData.yesterdayVisits) / analyticsData.yesterdayVisits) * 100;
    return Math.round(change);
  };

  const formatTimestamp = (timestamp) => {
    if (!timestamp) return 'Unknown';
    const date = new Date(timestamp);
    return date.toLocaleString();
  };

  const getBrowserName = (userAgent) => {
    if (!userAgent) return 'Unknown';
    if (userAgent.includes('Chrome')) return 'Chrome';
    if (userAgent.includes('Firefox')) return 'Firefox';
    if (userAgent.includes('Safari')) return 'Safari';
    if (userAgent.includes('Edge')) return 'Edge';
    return 'Other';
  };

  if (loading) {
    return (
      <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[...Array(4)].map((_, i) => (
          <div key={i} className="bg-black/20 backdrop-blur-xl border border-white/20 rounded-2xl p-6 animate-pulse">
            <div className="h-4 bg-white/10 rounded mb-2"></div>
            <div className="h-8 bg-white/10 rounded"></div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Stats Overview */}
      <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Total Visitors */}
        <motion.div 
          className="bg-black/20 backdrop-blur-xl border border-white/20 rounded-2xl p-6 hover:bg-black/30 transition-all duration-300"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          whileHover={{ scale: 1.02, y: -5 }}
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-300 text-sm font-medium">Total Visitors</p>
              <motion.p 
                className="text-3xl font-bold bg-gradient-to-r from-neural-blue to-neural-purple bg-clip-text text-transparent"
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ duration: 0.5, delay: 0.1 }}
              >
                {analyticsData.totalVisitors.toLocaleString()}
              </motion.p>
            </div>
            <motion.div 
              className="p-3 bg-gradient-to-r from-neural-blue/20 to-neural-purple/20 rounded-xl"
              whileHover={{ rotate: 5 }}
              transition={{ duration: 0.2 }}
            >
              <Users className="w-8 h-8 text-neural-blue" />
            </motion.div>
          </div>
        </motion.div>

        {/* Total Page Views */}
        <motion.div 
          className="bg-black/20 backdrop-blur-xl border border-white/20 rounded-2xl p-6 hover:bg-black/30 transition-all duration-300"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          whileHover={{ scale: 1.02, y: -5 }}
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-300 text-sm font-medium">Page Views</p>
              <motion.p 
                className="text-3xl font-bold bg-gradient-to-r from-green-400 to-emerald-400 bg-clip-text text-transparent"
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ duration: 0.5, delay: 0.2 }}
              >
                {analyticsData.totalPageViews.toLocaleString()}
              </motion.p>
            </div>
            <motion.div 
              className="p-3 bg-gradient-to-r from-green-500/20 to-emerald-500/20 rounded-xl"
              whileHover={{ rotate: 5 }}
              transition={{ duration: 0.2 }}
            >
              <Eye className="w-8 h-8 text-green-400" />
            </motion.div>
          </div>
        </motion.div>

        {/* Today's Visits */}
        <motion.div 
          className="bg-black/20 backdrop-blur-xl border border-white/20 rounded-2xl p-6 hover:bg-black/30 transition-all duration-300"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          whileHover={{ scale: 1.02, y: -5 }}
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-300 text-sm font-medium">Today's Visits</p>
              <motion.p 
                className="text-3xl font-bold bg-gradient-to-r from-yellow-400 to-orange-400 bg-clip-text text-transparent"
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ duration: 0.5, delay: 0.3 }}
              >
                {analyticsData.todayVisits}
              </motion.p>
            </div>
            <motion.div 
              className="p-3 bg-gradient-to-r from-yellow-500/20 to-orange-500/20 rounded-xl"
              whileHover={{ rotate: 5 }}
              transition={{ duration: 0.2 }}
            >
              <Calendar className="w-8 h-8 text-yellow-400" />
            </motion.div>
          </div>
        </motion.div>

        {/* Trend */}
        <motion.div 
          className="bg-black/20 backdrop-blur-xl border border-white/20 rounded-2xl p-6 hover:bg-black/30 transition-all duration-300"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
          whileHover={{ scale: 1.02, y: -5 }}
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-300 text-sm font-medium">Daily Trend</p>
              <motion.div 
                className="flex items-center gap-2"
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ duration: 0.5, delay: 0.4 }}
              >
                <p className={`text-3xl font-bold ${getTrendPercentage() >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                  {getTrendPercentage() >= 0 ? '+' : ''}{getTrendPercentage()}%
                </p>
              </motion.div>
            </div>
            <motion.div 
              className={`p-3 rounded-xl ${getTrendPercentage() >= 0 ? 'bg-green-500/20' : 'bg-red-500/20'}`}
              whileHover={{ rotate: 5 }}
              transition={{ duration: 0.2 }}
            >
              <TrendingUp className={`w-8 h-8 ${getTrendPercentage() >= 0 ? 'text-green-400' : 'text-red-400'}`} />
            </motion.div>
          </div>
        </motion.div>
      </div>

      {/* Recent Visits */}
      <motion.div 
        className="bg-black/20 backdrop-blur-xl border border-white/20 rounded-2xl p-6 hover:bg-black/30 transition-all duration-300"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.4 }}
      >
        <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
          <motion.div
            animate={{ scale: [1, 1.1, 1] }}
            transition={{ duration: 2, repeat: Infinity }}
          >
            <Globe className="w-5 h-5 text-neural-blue" />
          </motion.div>
          Recent Visitors
        </h3>
        <div className="space-y-3 max-h-64 overflow-y-auto">
          {analyticsData.recentVisits.length > 0 ? (
            analyticsData.recentVisits.map((visit, index) => (
              <motion.div 
                key={visit.sessionId || index}
                className="flex items-center justify-between p-3 bg-black/30 backdrop-blur-sm rounded-xl border border-white/10 hover:bg-black/40 transition-all duration-300"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                whileHover={{ scale: 1.01 }}
              >
                <div className="flex items-center gap-3">
                  <motion.div 
                    className="w-8 h-8 bg-gradient-to-r from-neural-purple to-neural-pink rounded-full flex items-center justify-center text-white text-xs font-medium"
                    whileHover={{ rotate: 180 }}
                    transition={{ duration: 0.3 }}
                  >
                    <Monitor className="w-4 h-4" />
                  </motion.div>
                  <div>
                    <div className="text-white text-sm font-medium">
                      {getBrowserName(visit.userAgent)} • {visit.language || 'Unknown'}
                    </div>
                    <div className="text-gray-400 text-xs flex items-center gap-1">
                      <Clock className="w-3 h-3" />
                      {formatTimestamp(visit.timestamp)}
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-gray-300 text-xs">{visit.timezone || 'Unknown'}</div>
                  <div className="text-gray-400 text-xs">{visit.viewport || 'Unknown'}</div>
                </div>
              </motion.div>
            ))
          ) : (
            <motion.div 
              className="text-gray-400 text-center py-8 bg-black/10 rounded-xl border border-white/5"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
            >
              <Globe className="w-8 h-8 mx-auto mb-2 opacity-50" />
              No recent visitors
            </motion.div>
          )}
        </div>
      </motion.div>
    </div>
  );
};

export default WebsiteAnalytics;

"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/resume-builder/page",{

/***/ "(app-pages-browser)/./src/components/resume/ResumeBuilder.jsx":
/*!*************************************************!*\
  !*** ./src/components/resume/ResumeBuilder.jsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Briefcase,CheckCircle,Clock,Download,Eye,FileText,GraduationCap,Lightbulb,RotateCcw,Save,Sparkles,Target,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Briefcase,CheckCircle,Clock,Download,Eye,FileText,GraduationCap,Lightbulb,RotateCcw,Save,Sparkles,Target,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/graduation-cap.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Briefcase,CheckCircle,Clock,Download,Eye,FileText,GraduationCap,Lightbulb,RotateCcw,Save,Sparkles,Target,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Briefcase,CheckCircle,Clock,Download,Eye,FileText,GraduationCap,Lightbulb,RotateCcw,Save,Sparkles,Target,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Briefcase,CheckCircle,Clock,Download,Eye,FileText,GraduationCap,Lightbulb,RotateCcw,Save,Sparkles,Target,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Briefcase,CheckCircle,Clock,Download,Eye,FileText,GraduationCap,Lightbulb,RotateCcw,Save,Sparkles,Target,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Briefcase,CheckCircle,Clock,Download,Eye,FileText,GraduationCap,Lightbulb,RotateCcw,Save,Sparkles,Target,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Briefcase,CheckCircle,Clock,Download,Eye,FileText,GraduationCap,Lightbulb,RotateCcw,Save,Sparkles,Target,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _StepNavigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./StepNavigation */ \"(app-pages-browser)/./src/components/resume/StepNavigation.jsx\");\n/* harmony import */ var _layout_SmartNavigationBar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../layout/SmartNavigationBar */ \"(app-pages-browser)/./src/components/layout/SmartNavigationBar.jsx\");\n/* harmony import */ var _forms_ResumeFormComponents__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./forms/ResumeFormComponents */ \"(app-pages-browser)/./src/components/resume/forms/ResumeFormComponents.jsx\");\n/* harmony import */ var _forms_ExperienceForm__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./forms/ExperienceForm */ \"(app-pages-browser)/./src/components/resume/forms/ExperienceForm.jsx\");\n/* harmony import */ var _forms_SkillsProjectsForm__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./forms/SkillsProjectsForm */ \"(app-pages-browser)/./src/components/resume/forms/SkillsProjectsForm.jsx\");\n/* harmony import */ var _forms_ProjectsForm__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./forms/ProjectsForm */ \"(app-pages-browser)/./src/components/resume/forms/ProjectsForm.jsx\");\n/* harmony import */ var _forms_SkillsForm__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./forms/SkillsForm */ \"(app-pages-browser)/./src/components/resume/forms/SkillsForm.jsx\");\n/* harmony import */ var _forms_ReviewForm__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./forms/ReviewForm */ \"(app-pages-browser)/./src/components/resume/forms/ReviewForm.jsx\");\n/* harmony import */ var _EnhancedProgressIndicator__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./EnhancedProgressIndicator */ \"(app-pages-browser)/./src/components/resume/EnhancedProgressIndicator.jsx\");\n/* harmony import */ var _EnhancedNavigation__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./EnhancedNavigation */ \"(app-pages-browser)/./src/components/resume/EnhancedNavigation.jsx\");\n/* harmony import */ var _common_ProgressBar__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../common/ProgressBar */ \"(app-pages-browser)/./src/components/common/ProgressBar.jsx\");\n/* harmony import */ var _ResumePreview__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./ResumePreview */ \"(app-pages-browser)/./src/components/resume/ResumePreview.jsx\");\n/* harmony import */ var _SimpleResumePreview__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./SimpleResumePreview */ \"(app-pages-browser)/./src/components/resume/SimpleResumePreview.jsx\");\n/* harmony import */ var _SuccessScreen__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./SuccessScreen */ \"(app-pages-browser)/./src/components/resume/SuccessScreen.jsx\");\n/* harmony import */ var _hooks_useATSAnalysis__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/hooks/useATSAnalysis */ \"(app-pages-browser)/./src/hooks/useATSAnalysis.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst EnhancedResumeBuilder = (param)=>{\n    let { hideHeader = false } = param;\n    var _atsAnalysis_recommendations;\n    _s();\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [selectedTemplate, setSelectedTemplate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('modern');\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        personal: {\n            firstName: \"\",\n            lastName: \"\",\n            email: \"\",\n            phone: \"\",\n            location: \"\",\n            linkedin: \"\",\n            portfolio: \"\",\n            summary: \"\",\n            profileImage: \"\"\n        },\n        education: [\n            {\n                id: 1,\n                degree: \"\",\n                institution: \"\",\n                location: \"\",\n                startDate: \"\",\n                endDate: \"\",\n                gpa: \"\",\n                relevant: \"\"\n            }\n        ],\n        experience: [\n            {\n                id: 1,\n                title: \"\",\n                company: \"\",\n                location: \"\",\n                startDate: \"\",\n                endDate: \"\",\n                current: false,\n                description: \"\"\n            }\n        ],\n        skills: {\n            technical: [],\n            languages: [],\n            certifications: []\n        },\n        projects: [\n            {\n                id: 1,\n                name: \"\",\n                description: \"\",\n                technologies: \"\",\n                link: \"\"\n            }\n        ],\n        jobDescription: \"\"\n    });\n    const [showPreview, setShowPreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showProgressBar, setShowProgressBar] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [resumeGenerated, setResumeGenerated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [validationErrors, setValidationErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [showValidationErrors, setShowValidationErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [completedSteps, setCompletedSteps] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [autoSaveEnabled, setAutoSaveEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [lastSaved, setLastSaved] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [resumeData, setResumeData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [resumeUrl, setResumeUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [atsScore, setAtsScore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [suggestions, setSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [aiSuggestionsEnabled, setAiSuggestionsEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [smartValidation, setSmartValidation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showHints, setShowHints] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const steps = [\n        {\n            id: 0,\n            title: \"Personal Information\",\n            icon: _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n            description: \"Tell us about yourself\",\n            estimatedTime: 3,\n            required: true\n        },\n        {\n            id: 1,\n            title: \"Education\",\n            icon: _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n            description: \"Your academic background\",\n            estimatedTime: 5,\n            required: true\n        },\n        {\n            id: 2,\n            title: \"Experience\",\n            icon: _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n            description: \"Your work experience\",\n            estimatedTime: 8,\n            required: true\n        },\n        {\n            id: 3,\n            title: \"Projects\",\n            icon: _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n            description: \"Showcase your projects\",\n            estimatedTime: 4,\n            required: false\n        },\n        {\n            id: 4,\n            title: \"Skills\",\n            icon: _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"],\n            description: \"Your technical & soft skills\",\n            estimatedTime: 3,\n            required: false\n        },\n        {\n            id: 5,\n            title: \"Review & Generate\",\n            icon: _barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n            description: \"Finalize your resume\",\n            estimatedTime: 2,\n            required: false\n        }\n    ];\n    const atsAnalysis = (0,_hooks_useATSAnalysis__WEBPACK_IMPORTED_MODULE_17__[\"default\"])(formData);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EnhancedResumeBuilder.useEffect\": ()=>{\n            // Check for backup data from auth flow first\n            const backupData = localStorage.getItem('resumeFormDataBackup');\n            const authTimestamp = localStorage.getItem('authFlowTimestamp');\n            const savedData = localStorage.getItem('resumeFormData');\n            // If backup data exists and is recent (within 10 minutes), use it\n            if (backupData && authTimestamp) {\n                const timeDiff = Date.now() - parseInt(authTimestamp);\n                if (timeDiff < 10 * 60 * 1000) {\n                    try {\n                        const parsed = JSON.parse(backupData);\n                        setFormData(parsed);\n                        setLastSaved(new Date());\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success('Your work has been restored after sign-in!');\n                        // Clean up backup data\n                        localStorage.removeItem('resumeFormDataBackup');\n                        localStorage.removeItem('authFlowTimestamp');\n                        // Update main storage\n                        localStorage.setItem('resumeFormData', backupData);\n                        localStorage.setItem('resumeLastSaved', new Date().toISOString());\n                        return;\n                    } catch (error) {\n                        console.error('Failed to load backup data:', error);\n                    }\n                }\n            }\n            // Fallback to regular saved data\n            if (savedData) {\n                try {\n                    const parsed = JSON.parse(savedData);\n                    setFormData(parsed);\n                    setLastSaved(new Date(localStorage.getItem('resumeLastSaved') || Date.now()));\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success('Previous work restored!');\n                } catch (error) {\n                    console.error('Failed to load saved data:', error);\n                }\n            }\n            // Clean up old backup data\n            localStorage.removeItem('resumeFormDataBackup');\n            localStorage.removeItem('authFlowTimestamp');\n        }\n    }[\"EnhancedResumeBuilder.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EnhancedResumeBuilder.useEffect\": ()=>{\n            if (autoSaveEnabled && formData) {\n                // Make form data globally accessible for auth flow\n                if (true) {\n                    window.formData = formData;\n                }\n                const timeoutId = setTimeout({\n                    \"EnhancedResumeBuilder.useEffect.timeoutId\": ()=>{\n                        localStorage.setItem('resumeFormData', JSON.stringify(formData));\n                        localStorage.setItem('resumeLastSaved', new Date().toISOString());\n                        setLastSaved(new Date());\n                    }\n                }[\"EnhancedResumeBuilder.useEffect.timeoutId\"], 2000);\n                return ({\n                    \"EnhancedResumeBuilder.useEffect\": ()=>clearTimeout(timeoutId)\n                })[\"EnhancedResumeBuilder.useEffect\"];\n            }\n        }\n    }[\"EnhancedResumeBuilder.useEffect\"], [\n        formData,\n        autoSaveEnabled\n    ]);\n    const validateStep = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[validateStep]\": (stepIndex)=>{\n            const errors = {};\n            switch(stepIndex){\n                case 0:\n                    var _formData_personal_firstName, _formData_personal_lastName, _formData_personal_email;\n                    if (!((_formData_personal_firstName = formData.personal.firstName) === null || _formData_personal_firstName === void 0 ? void 0 : _formData_personal_firstName.trim())) errors.firstName = 'First name is required';\n                    if (!((_formData_personal_lastName = formData.personal.lastName) === null || _formData_personal_lastName === void 0 ? void 0 : _formData_personal_lastName.trim())) errors.lastName = 'Last name is required';\n                    if (!((_formData_personal_email = formData.personal.email) === null || _formData_personal_email === void 0 ? void 0 : _formData_personal_email.trim())) errors.email = 'Email is required';\n                    else if (!/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(formData.personal.email)) errors.email = 'Please enter a valid email address';\n                    break;\n                case 1:\n                    var _formData_education;\n                    const validEducation = (_formData_education = formData.education) === null || _formData_education === void 0 ? void 0 : _formData_education.filter({\n                        \"EnhancedResumeBuilder.useCallback[validateStep]\": (edu)=>{\n                            var _edu_degree, _edu_institution;\n                            return ((_edu_degree = edu.degree) === null || _edu_degree === void 0 ? void 0 : _edu_degree.trim()) && ((_edu_institution = edu.institution) === null || _edu_institution === void 0 ? void 0 : _edu_institution.trim());\n                        }\n                    }[\"EnhancedResumeBuilder.useCallback[validateStep]\"]);\n                    if (!validEducation || validEducation.length === 0) errors.education = 'At least one education entry with degree and institution is required';\n                    break;\n                case 2:\n                    var _formData_experience;\n                    const validExperience = (_formData_experience = formData.experience) === null || _formData_experience === void 0 ? void 0 : _formData_experience.filter({\n                        \"EnhancedResumeBuilder.useCallback[validateStep]\": (exp)=>{\n                            var _exp_title, _exp_company;\n                            return ((_exp_title = exp.title) === null || _exp_title === void 0 ? void 0 : _exp_title.trim()) && ((_exp_company = exp.company) === null || _exp_company === void 0 ? void 0 : _exp_company.trim());\n                        }\n                    }[\"EnhancedResumeBuilder.useCallback[validateStep]\"]);\n                    if (!validExperience || validExperience.length === 0) errors.experience = 'At least one work experience entry with job title and company is required';\n                    break;\n                case 3:\n                    break;\n                case 4:\n                    break;\n                default:\n                    break;\n            }\n            return errors;\n        }\n    }[\"EnhancedResumeBuilder.useCallback[validateStep]\"], [\n        formData\n    ]);\n    const canProceedToNextStep = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[canProceedToNextStep]\": (stepIndex)=>{\n            const errors = validateStep(stepIndex);\n            return Object.keys(errors).length === 0;\n        }\n    }[\"EnhancedResumeBuilder.useCallback[canProceedToNextStep]\"], [\n        validateStep\n    ]);\n    const getCompletedSteps = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[getCompletedSteps]\": ()=>{\n            const completed = [];\n            for(let i = 0; i < currentStep; i++){\n                const errors = validateStep(i);\n                if (Object.keys(errors).length === 0) {\n                    completed.push(i);\n                }\n            }\n            return completed;\n        }\n    }[\"EnhancedResumeBuilder.useCallback[getCompletedSteps]\"], [\n        currentStep,\n        validateStep\n    ]);\n    const getStepCompletionStatus = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[getStepCompletionStatus]\": ()=>{\n            const status = {};\n            steps.forEach({\n                \"EnhancedResumeBuilder.useCallback[getStepCompletionStatus]\": (step, index)=>{\n                    const errors = validateStep(index);\n                    status[index] = Object.keys(errors).length === 0;\n                }\n            }[\"EnhancedResumeBuilder.useCallback[getStepCompletionStatus]\"]);\n            return status;\n        }\n    }[\"EnhancedResumeBuilder.useCallback[getStepCompletionStatus]\"], [\n        steps,\n        validateStep\n    ]);\n    const canGenerateResume = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[canGenerateResume]\": ()=>{\n            for(let i = 0; i < steps.length - 1; i++){\n                if (!canProceedToNextStep(i)) return false;\n            }\n            return true;\n        }\n    }[\"EnhancedResumeBuilder.useCallback[canGenerateResume]\"], [\n        canProceedToNextStep,\n        steps.length\n    ]);\n    const updateFormData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[updateFormData]\": function(section, field, value) {\n            let index = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : null;\n            setFormData({\n                \"EnhancedResumeBuilder.useCallback[updateFormData]\": (prev)=>{\n                    let newData = {\n                        ...prev\n                    };\n                    if (index !== null && Array.isArray(prev[section])) {\n                        const newArray = [\n                            ...prev[section]\n                        ];\n                        newArray[index] = {\n                            ...newArray[index],\n                            [field]: value\n                        };\n                        newData[section] = newArray;\n                    } else if (typeof prev[section] === 'object' && !Array.isArray(prev[section])) {\n                        newData[section] = {\n                            ...prev[section],\n                            [field]: value\n                        };\n                    } else {\n                        newData[field] = value;\n                    }\n                    return newData;\n                }\n            }[\"EnhancedResumeBuilder.useCallback[updateFormData]\"]);\n            if (validationErrors[field]) {\n                setValidationErrors({\n                    \"EnhancedResumeBuilder.useCallback[updateFormData]\": (prev)=>{\n                        const newErrors = {\n                            ...prev\n                        };\n                        delete newErrors[field];\n                        return newErrors;\n                    }\n                }[\"EnhancedResumeBuilder.useCallback[updateFormData]\"]);\n            }\n        }\n    }[\"EnhancedResumeBuilder.useCallback[updateFormData]\"], [\n        validationErrors\n    ]);\n    const addArrayItem = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[addArrayItem]\": (section, template)=>{\n            setFormData({\n                \"EnhancedResumeBuilder.useCallback[addArrayItem]\": (prev)=>({\n                        ...prev,\n                        [section]: [\n                            ...prev[section],\n                            {\n                                ...template,\n                                id: Math.random().toString(36).substring(2, 11)\n                            }\n                        ]\n                    })\n            }[\"EnhancedResumeBuilder.useCallback[addArrayItem]\"]);\n        }\n    }[\"EnhancedResumeBuilder.useCallback[addArrayItem]\"], []);\n    const removeArrayItem = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[removeArrayItem]\": (section, id)=>{\n            setFormData({\n                \"EnhancedResumeBuilder.useCallback[removeArrayItem]\": (prev)=>({\n                        ...prev,\n                        [section]: prev[section].filter({\n                            \"EnhancedResumeBuilder.useCallback[removeArrayItem]\": (item)=>item.id !== id\n                        }[\"EnhancedResumeBuilder.useCallback[removeArrayItem]\"])\n                    })\n            }[\"EnhancedResumeBuilder.useCallback[removeArrayItem]\"]);\n        }\n    }[\"EnhancedResumeBuilder.useCallback[removeArrayItem]\"], []);\n    const nextStep = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[nextStep]\": ()=>{\n            const errors = validateStep(currentStep);\n            if (Object.keys(errors).length > 0) {\n                setValidationErrors(errors);\n                setShowValidationErrors(true);\n                const errorMessages = Object.values(errors);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(errorMessages[0]);\n                return;\n            }\n            setCompletedSteps({\n                \"EnhancedResumeBuilder.useCallback[nextStep]\": (prev)=>[\n                        ...new Set([\n                            ...prev,\n                            currentStep\n                        ])\n                    ]\n            }[\"EnhancedResumeBuilder.useCallback[nextStep]\"]);\n            setValidationErrors({});\n            setShowValidationErrors(false);\n            if (currentStep < steps.length - 1) {\n                setCurrentStep(currentStep + 1);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"\".concat(steps[currentStep].title, \" completed!\"));\n            }\n        }\n    }[\"EnhancedResumeBuilder.useCallback[nextStep]\"], [\n        currentStep,\n        validateStep,\n        steps\n    ]);\n    const prevStep = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[prevStep]\": ()=>{\n            setValidationErrors({});\n            setShowValidationErrors(false);\n            if (currentStep > 0) setCurrentStep(currentStep - 1);\n        }\n    }[\"EnhancedResumeBuilder.useCallback[prevStep]\"], [\n        currentStep\n    ]);\n    const handleStepClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[handleStepClick]\": (stepIndex)=>{\n            if (stepIndex <= currentStep || completedSteps.includes(stepIndex)) {\n                setCurrentStep(stepIndex);\n                setValidationErrors({});\n                setShowValidationErrors(false);\n            }\n        }\n    }[\"EnhancedResumeBuilder.useCallback[handleStepClick]\"], [\n        currentStep,\n        completedSteps\n    ]);\n    const generateResume = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[generateResume]\": async ()=>{\n            setValidationErrors({});\n            setShowValidationErrors(false);\n            if (!canGenerateResume()) {\n                const allErrors = {};\n                for(let i = 0; i < steps.length - 1; i++){\n                    const stepErrors = validateStep(i);\n                    Object.assign(allErrors, stepErrors);\n                }\n                setValidationErrors(allErrors);\n                setShowValidationErrors(true);\n                for(let i = 0; i < steps.length - 1; i++){\n                    const stepErrors = validateStep(i);\n                    if (Object.keys(stepErrors).length > 0) {\n                        setCurrentStep(i);\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Please complete all required fields in \".concat(steps[i].title));\n                        return;\n                    }\n                }\n                return;\n            }\n            try {\n                setIsGenerating(true);\n                setShowProgressBar(true);\n                setResumeGenerated(false);\n                const response = await fetch('/api/generate-resume', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        formData,\n                        templateId: selectedTemplate\n                    })\n                });\n                const contentType = response.headers.get('content-type');\n                if (!contentType || !contentType.includes('application/json')) throw new Error('Server returned non-JSON response');\n                const data = await response.json();\n                if (!response.ok) throw new Error(data.error || 'Failed to generate resume');\n                if (!data.resumeData || !data.downloadUrl) throw new Error('Generated resume data is incomplete');\n                setResumeUrl(data.downloadUrl);\n                setResumeData(data.resumeData);\n                setAtsScore(data.atsScore || 75);\n                setSuggestions(data.suggestions || []);\n                setResumeGenerated(true);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success('Resume generated successfully!');\n            } catch (error) {\n                console.error('Resume generation error:', error);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(error.message || 'Failed to generate resume');\n            } finally{\n                setIsGenerating(false);\n                setShowProgressBar(false);\n            }\n        }\n    }[\"EnhancedResumeBuilder.useCallback[generateResume]\"], [\n        canGenerateResume,\n        formData,\n        steps,\n        validateStep,\n        selectedTemplate\n    ]);\n    const handleSave = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[handleSave]\": ()=>{\n            localStorage.setItem('resumeFormData', JSON.stringify(formData));\n            localStorage.setItem('resumeLastSaved', new Date().toISOString());\n            setLastSaved(new Date());\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success('Progress saved!');\n        }\n    }[\"EnhancedResumeBuilder.useCallback[handleSave]\"], [\n        formData\n    ]);\n    const handleAISuggest = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[handleAISuggest]\": async (section)=>{\n            if (!aiSuggestionsEnabled) return;\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.loading('Getting AI suggestions...');\n            try {\n                await new Promise({\n                    \"EnhancedResumeBuilder.useCallback[handleAISuggest]\": (resolve)=>setTimeout(resolve, 1500)\n                }[\"EnhancedResumeBuilder.useCallback[handleAISuggest]\"]);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.dismiss();\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success('AI suggestions applied!');\n            } catch (error) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.dismiss();\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error('Failed to get AI suggestions');\n            }\n        }\n    }[\"EnhancedResumeBuilder.useCallback[handleAISuggest]\"], [\n        aiSuggestionsEnabled\n    ]);\n    // Set up event listener for resume generation from ReviewForm\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EnhancedResumeBuilder.useEffect\": ()=>{\n            const handleGenerateResume = {\n                \"EnhancedResumeBuilder.useEffect.handleGenerateResume\": ()=>{\n                    // Check if we can generate resume and call the function\n                    if (canGenerateResume()) {\n                        generateResume();\n                    } else {\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error('Please complete all required sections before generating your resume.');\n                    }\n                }\n            }[\"EnhancedResumeBuilder.useEffect.handleGenerateResume\"];\n            if (true) {\n                window.addEventListener('generateResume', handleGenerateResume);\n                return ({\n                    \"EnhancedResumeBuilder.useEffect\": ()=>window.removeEventListener('generateResume', handleGenerateResume)\n                })[\"EnhancedResumeBuilder.useEffect\"];\n            }\n        }\n    }[\"EnhancedResumeBuilder.useEffect\"], [\n        canGenerateResume,\n        generateResume\n    ]);\n    const getStepValidation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EnhancedResumeBuilder.useCallback[getStepValidation]\": ()=>{\n            const validation = {};\n            steps.forEach({\n                \"EnhancedResumeBuilder.useCallback[getStepValidation]\": (step, index)=>{\n                    const errors = validateStep(index);\n                    validation[index] = Object.keys(errors).length === 0;\n                }\n            }[\"EnhancedResumeBuilder.useCallback[getStepValidation]\"]);\n            return validation;\n        }\n    }[\"EnhancedResumeBuilder.useCallback[getStepValidation]\"], [\n        steps,\n        validateStep\n    ]);\n    const renderStepContent = ()=>{\n        const commonProps = {\n            formData,\n            updateFormData,\n            addArrayItem,\n            removeArrayItem,\n            atsAnalysis,\n            validationErrors,\n            showValidationErrors,\n            onSave: handleSave,\n            onAISuggest: handleAISuggest\n        };\n        switch(currentStep){\n            case 0:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_ResumeFormComponents__WEBPACK_IMPORTED_MODULE_5__.PersonalInfoForm, {\n                    ...commonProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                    lineNumber: 383,\n                    columnNumber: 16\n                }, undefined);\n            case 1:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_ResumeFormComponents__WEBPACK_IMPORTED_MODULE_5__.EducationForm, {\n                    ...commonProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                    lineNumber: 385,\n                    columnNumber: 16\n                }, undefined);\n            case 2:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_ExperienceForm__WEBPACK_IMPORTED_MODULE_6__.EnhancedExperienceForm, {\n                    ...commonProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                    lineNumber: 387,\n                    columnNumber: 16\n                }, undefined);\n            case 3:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_ProjectsForm__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    ...commonProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                    lineNumber: 389,\n                    columnNumber: 16\n                }, undefined);\n            case 4:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_SkillsForm__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    ...commonProps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                    lineNumber: 391,\n                    columnNumber: 16\n                }, undefined);\n            case 5:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_ReviewForm__WEBPACK_IMPORTED_MODULE_10__.EnhancedReviewForm, {\n                    ...commonProps,\n                    selectedTemplate: selectedTemplate,\n                    onTemplateSelect: setSelectedTemplate\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                    lineNumber: 393,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return null;\n        }\n    };\n    if (resumeGenerated && resumeData && resumeUrl) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SuccessScreen__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n            formData: formData,\n            resumeData: resumeData,\n            onStartOver: ()=>{\n                setResumeGenerated(false);\n                setResumeData(null);\n                setResumeUrl(\"\");\n                setCurrentStep(0);\n            },\n            onEditResume: ()=>{\n                setResumeGenerated(false);\n                setCurrentStep(4); // Go back to review step\n            }\n        }, void 0, false, {\n            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n            lineNumber: 401,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-b from-black to-[#0A0A0A] text-white relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 opacity-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 bg-[url('/grid.svg')] [mask-image:radial-gradient(ellipse_at_center,white,transparent)]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                    lineNumber: 421,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                lineNumber: 420,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_common_ProgressBar__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                isVisible: showProgressBar,\n                onComplete: ()=>setShowProgressBar(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                lineNumber: 423,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 pb-32\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 py-8\",\n                    children: [\n                        !hideHeader && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_23__.motion.div, {\n                            className: \"text-center mb-8\",\n                            initial: {\n                                opacity: 0,\n                                y: -20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center gap-3 mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    className: \"h-10 w-10 text-neural-pink animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                    lineNumber: 430,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-neural-pink opacity-20 rounded-full blur-md\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                    lineNumber: 431,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                            lineNumber: 429,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-4xl lg:text-5xl font-bold text-white\",\n                                            children: \"Enhanced Resume Builder\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                            lineNumber: 433,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                    lineNumber: 428,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 text-lg max-w-2xl mx-auto\",\n                                    children: \"Create professional, ATS-friendly resumes with our enhanced AI-powered builder\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                    lineNumber: 435,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                            lineNumber: 427,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col xl:flex-row gap-8 max-w-7xl mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 max-w-4xl\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"sticky top-4 z-30 bg-gray-900/95 backdrop-blur-sm rounded-2xl border border-gray-700/50 mb-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EnhancedProgressIndicator__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                steps: steps,\n                                                currentStep: currentStep,\n                                                completedSteps: completedSteps,\n                                                onStepClick: handleStepClick,\n                                                variant: \"minimal\",\n                                                showLabels: true,\n                                                showProgress: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                lineNumber: 443,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                            lineNumber: 442,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_25__.AnimatePresence, {\n                                                mode: \"wait\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_23__.motion.div, {\n                                                    initial: {\n                                                        opacity: 0,\n                                                        x: 20\n                                                    },\n                                                    animate: {\n                                                        opacity: 1,\n                                                        x: 0\n                                                    },\n                                                    exit: {\n                                                        opacity: 0,\n                                                        x: -20\n                                                    },\n                                                    transition: {\n                                                        duration: 0.3\n                                                    },\n                                                    children: renderStepContent()\n                                                }, currentStep, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                    lineNumber: 457,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                lineNumber: 456,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                            lineNumber: 455,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                    lineNumber: 440,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden xl:block xl:w-80 xl:flex-shrink-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"sticky top-4 h-[calc(100vh-2rem)]\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SimpleResumePreview__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                formData: formData,\n                                                selectedTemplate: selectedTemplate,\n                                                showPreview: showPreview,\n                                                onTogglePreview: ()=>setShowPreview(!showPreview)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                lineNumber: 468,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_23__.motion.div, {\n                                                className: \"bg-gray-900/60 backdrop-blur-md rounded-2xl p-6 border border-white/10 mt-6\",\n                                                initial: {\n                                                    opacity: 0,\n                                                    x: 20\n                                                },\n                                                animate: {\n                                                    opacity: 1,\n                                                    x: 0\n                                                },\n                                                transition: {\n                                                    delay: 0.3\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2 mb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                className: \"h-5 w-5 text-neural-purple\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                                lineNumber: 483,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-semibold\",\n                                                                children: \"ATS Score\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                                lineNumber: 484,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                        lineNumber: 482,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center mb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-3xl font-bold \".concat((atsAnalysis.overallScore || 0) >= 80 ? 'text-green-400' : (atsAnalysis.overallScore || 0) >= 60 ? 'text-yellow-400' : 'text-red-400'),\n                                                                children: [\n                                                                    atsAnalysis.overallScore || 0,\n                                                                    \"%\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                                lineNumber: 487,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-400\",\n                                                                children: (atsAnalysis.overallScore || 0) >= 80 ? 'Excellent' : (atsAnalysis.overallScore || 0) >= 60 ? 'Good' : 'Needs Work'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                                lineNumber: 493,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                        lineNumber: 486,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    ((_atsAnalysis_recommendations = atsAnalysis.recommendations) === null || _atsAnalysis_recommendations === void 0 ? void 0 : _atsAnalysis_recommendations.length) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-500 mb-2\",\n                                                                children: \"Top Suggestions:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                                lineNumber: 500,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            atsAnalysis.recommendations.slice(0, 2).map((rec, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-start gap-2 text-sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-1.5 h-1.5 bg-yellow-400 rounded-full mt-2 flex-shrink-0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                                            lineNumber: 503,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-gray-300\",\n                                                                            children: rec.message\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                                            lineNumber: 504,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, index, true, {\n                                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                                    lineNumber: 502,\n                                                                    columnNumber: 25\n                                                                }, undefined))\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                        lineNumber: 499,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                lineNumber: 476,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                        lineNumber: 467,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                    lineNumber: 465,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"xl:hidden mt-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_23__.motion.div, {\n                                        className: \"bg-gray-900/60 backdrop-blur-md rounded-2xl p-6 border border-white/10\",\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            delay: 0.2\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                className: \"h-5 w-5 text-neural-blue\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                                lineNumber: 523,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            \"Resume Preview\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                        lineNumber: 522,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setShowPreview(!showPreview),\n                                                        className: \"px-3 py-1.5 rounded-lg text-sm font-medium transition-colors \".concat(showPreview ? 'bg-neural-purple text-white' : 'bg-gray-800 text-gray-300 hover:bg-gray-700'),\n                                                        children: showPreview ? 'Hide' : 'Show'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                        lineNumber: 526,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                lineNumber: 521,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            showPreview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"max-h-[600px] overflow-y-auto border border-white/10 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ResumePreview__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    formData: formData,\n                                                    selectedTemplate: selectedTemplate\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                    lineNumber: 537,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                lineNumber: 536,\n                                                columnNumber: 19\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-12 text-gray-400\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Award_Briefcase_CheckCircle_Clock_Download_Eye_FileText_GraduationCap_Lightbulb_RotateCcw_Save_Sparkles_Target_User_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                        className: \"h-12 w-12 mx-auto mb-3 opacity-30\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                        lineNumber: 541,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm\",\n                                                        children: 'Click \"Show\" to preview your resume'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                        lineNumber: 542,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                                lineNumber: 540,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                        lineNumber: 515,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                                    lineNumber: 514,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                            lineNumber: 438,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                    lineNumber: 425,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                lineNumber: 424,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_layout_SmartNavigationBar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                currentStep: currentStep,\n                totalSteps: steps.length,\n                onPrevious: prevStep,\n                onNext: nextStep,\n                onGenerate: generateResume,\n                onSave: handleSave,\n                onPreview: ()=>setShowPreview(!showPreview),\n                isGenerating: isGenerating,\n                canProceed: currentStep === steps.length - 1 ? canGenerateResume() : canProceedToNextStep(currentStep),\n                showPreview: showPreview,\n                steps: steps,\n                formData: formData,\n                atsScore: atsAnalysis.overallScore,\n                autoSaveEnabled: autoSaveEnabled\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n                lineNumber: 550,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\ResumeBuilder.jsx\",\n        lineNumber: 419,\n        columnNumber: 5\n    }, undefined);\n};\n_s(EnhancedResumeBuilder, \"cH7kc5DhB7P/1+m/Kivxo+IObKI=\", false, function() {\n    return [\n        _hooks_useATSAnalysis__WEBPACK_IMPORTED_MODULE_17__[\"default\"]\n    ];\n});\n_c = EnhancedResumeBuilder;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (EnhancedResumeBuilder);\nvar _c;\n$RefreshReg$(_c, \"EnhancedResumeBuilder\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3Jlc3VtZS9SZXN1bWVCdWlsZGVyLmpzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUNnRTtBQUNSO0FBR2xDO0FBQ2tCO0FBQ2M7QUFDUTtBQUNzSTtBQUNwSTtBQUNRO0FBQ2hCO0FBQ0o7QUFDSTtBQUNZO0FBQ2Q7QUFDTjtBQUNJO0FBQ0k7QUFDWjtBQUNRO0FBRXBELE1BQU00Qyx3QkFBd0I7UUFBQyxFQUFFQyxhQUFhLEtBQUssRUFBRTtRQTBkbENDOztJQXpkakIsTUFBTSxDQUFDQyxhQUFhQyxlQUFlLEdBQUcvQywrQ0FBUUEsQ0FBQztJQUMvQyxNQUFNLENBQUNnRCxrQkFBa0JDLG9CQUFvQixHQUFHakQsK0NBQVFBLENBQUM7SUFDekQsTUFBTSxDQUFDa0QsVUFBVUMsWUFBWSxHQUFHbkQsK0NBQVFBLENBQUM7UUFDdkNvRCxVQUFVO1lBQ1JDLFdBQVc7WUFDWEMsVUFBVTtZQUNWQyxPQUFPO1lBQ1BDLE9BQU87WUFDUEMsVUFBVTtZQUNWQyxVQUFVO1lBQ1ZDLFdBQVc7WUFDWEMsU0FBUztZQUNUQyxjQUFjO1FBQ2hCO1FBQ0FDLFdBQVc7WUFDVDtnQkFBRUMsSUFBSTtnQkFBR0MsUUFBUTtnQkFBSUMsYUFBYTtnQkFBSVIsVUFBVTtnQkFBSVMsV0FBVztnQkFBSUMsU0FBUztnQkFBSUMsS0FBSztnQkFBSUMsVUFBVTtZQUFHO1NBQ3ZHO1FBQ0RDLFlBQVk7WUFDVjtnQkFBRVAsSUFBSTtnQkFBR1EsT0FBTztnQkFBSUMsU0FBUztnQkFBSWYsVUFBVTtnQkFBSVMsV0FBVztnQkFBSUMsU0FBUztnQkFBSU0sU0FBUztnQkFBT0MsYUFBYTtZQUFHO1NBQzVHO1FBQ0RDLFFBQVE7WUFBRUMsV0FBVyxFQUFFO1lBQUVDLFdBQVcsRUFBRTtZQUFFQyxnQkFBZ0IsRUFBRTtRQUFDO1FBQzNEQyxVQUFVO1lBQ1I7Z0JBQUVoQixJQUFJO2dCQUFHaUIsTUFBTTtnQkFBSU4sYUFBYTtnQkFBSU8sY0FBYztnQkFBSUMsTUFBTTtZQUFHO1NBQ2hFO1FBQ0RDLGdCQUFnQjtJQUNsQjtJQUNBLE1BQU0sQ0FBQ0MsYUFBYUMsZUFBZSxHQUFHckYsK0NBQVFBLENBQUM7SUFDL0MsTUFBTSxDQUFDc0YsY0FBY0MsZ0JBQWdCLEdBQUd2RiwrQ0FBUUEsQ0FBQztJQUNqRCxNQUFNLENBQUN3RixpQkFBaUJDLG1CQUFtQixHQUFHekYsK0NBQVFBLENBQUM7SUFDdkQsTUFBTSxDQUFDMEYsaUJBQWlCQyxtQkFBbUIsR0FBRzNGLCtDQUFRQSxDQUFDO0lBQ3ZELE1BQU0sQ0FBQzRGLGtCQUFrQkMsb0JBQW9CLEdBQUc3RiwrQ0FBUUEsQ0FBQyxDQUFDO0lBQzFELE1BQU0sQ0FBQzhGLHNCQUFzQkMsd0JBQXdCLEdBQUcvRiwrQ0FBUUEsQ0FBQztJQUNqRSxNQUFNLENBQUNnRyxnQkFBZ0JDLGtCQUFrQixHQUFHakcsK0NBQVFBLENBQUMsRUFBRTtJQUN2RCxNQUFNLENBQUNrRyxpQkFBaUJDLG1CQUFtQixHQUFHbkcsK0NBQVFBLENBQUM7SUFDdkQsTUFBTSxDQUFDb0csV0FBV0MsYUFBYSxHQUFHckcsK0NBQVFBLENBQUM7SUFDM0MsTUFBTSxDQUFDc0csWUFBWUMsY0FBYyxHQUFHdkcsK0NBQVFBLENBQUM7SUFDN0MsTUFBTSxDQUFDd0csV0FBV0MsYUFBYSxHQUFHekcsK0NBQVFBLENBQUM7SUFDM0MsTUFBTSxDQUFDMEcsVUFBVUMsWUFBWSxHQUFHM0csK0NBQVFBLENBQUM7SUFDekMsTUFBTSxDQUFDNEcsYUFBYUMsZUFBZSxHQUFHN0csK0NBQVFBLENBQUMsRUFBRTtJQUNqRCxNQUFNLENBQUM4RyxzQkFBc0JDLHdCQUF3QixHQUFHL0csK0NBQVFBLENBQUM7SUFDakUsTUFBTSxDQUFDZ0gsaUJBQWlCQyxtQkFBbUIsR0FBR2pILCtDQUFRQSxDQUFDO0lBQ3ZELE1BQU0sQ0FBQ2tILFdBQVdDLGFBQWEsR0FBR25ILCtDQUFRQSxDQUFDO0lBRTNDLE1BQU1vSCxRQUFRO1FBQ1o7WUFBRXJELElBQUk7WUFBR1EsT0FBTztZQUF3QjhDLE1BQU1oSCw4TUFBSUE7WUFBRXFFLGFBQWE7WUFBMEI0QyxlQUFlO1lBQUdDLFVBQVU7UUFBSztRQUM1SDtZQUFFeEQsSUFBSTtZQUFHUSxPQUFPO1lBQWE4QyxNQUFNL0csOE1BQWFBO1lBQUVvRSxhQUFhO1lBQTRCNEMsZUFBZTtZQUFHQyxVQUFVO1FBQUs7UUFDNUg7WUFBRXhELElBQUk7WUFBR1EsT0FBTztZQUFjOEMsTUFBTTlHLDhNQUFTQTtZQUFFbUUsYUFBYTtZQUF3QjRDLGVBQWU7WUFBR0MsVUFBVTtRQUFLO1FBQ3JIO1lBQUV4RCxJQUFJO1lBQUdRLE9BQU87WUFBWThDLE1BQU01Ryw4TUFBUUE7WUFBRWlFLGFBQWE7WUFBMEI0QyxlQUFlO1lBQUdDLFVBQVU7UUFBTTtRQUNySDtZQUFFeEQsSUFBSTtZQUFHUSxPQUFPO1lBQVU4QyxNQUFNN0csOE1BQUtBO1lBQUVrRSxhQUFhO1lBQWdDNEMsZUFBZTtZQUFHQyxVQUFVO1FBQU07UUFDdEg7WUFBRXhELElBQUk7WUFBR1EsT0FBTztZQUFxQjhDLE1BQU01Ryw4TUFBUUE7WUFBRWlFLGFBQWE7WUFBd0I0QyxlQUFlO1lBQUdDLFVBQVU7UUFBTTtLQUM3SDtJQUVELE1BQU0xRSxjQUFjSCxrRUFBY0EsQ0FBQ1E7SUFFbkNqRCxnREFBU0E7MkNBQUM7WUFDUiw2Q0FBNkM7WUFDN0MsTUFBTXVILGFBQWFDLGFBQWFDLE9BQU8sQ0FBQztZQUN4QyxNQUFNQyxnQkFBZ0JGLGFBQWFDLE9BQU8sQ0FBQztZQUMzQyxNQUFNRSxZQUFZSCxhQUFhQyxPQUFPLENBQUM7WUFFdkMsa0VBQWtFO1lBQ2xFLElBQUlGLGNBQWNHLGVBQWU7Z0JBQy9CLE1BQU1FLFdBQVdDLEtBQUtDLEdBQUcsS0FBS0MsU0FBU0w7Z0JBQ3ZDLElBQUlFLFdBQVcsS0FBSyxLQUFLLE1BQU07b0JBQzdCLElBQUk7d0JBQ0YsTUFBTUksU0FBU0MsS0FBS0MsS0FBSyxDQUFDWDt3QkFDMUJyRSxZQUFZOEU7d0JBQ1o1QixhQUFhLElBQUl5Qjt3QkFDakIxRyxrREFBS0EsQ0FBQ2dILE9BQU8sQ0FBQzt3QkFDZCx1QkFBdUI7d0JBQ3ZCWCxhQUFhWSxVQUFVLENBQUM7d0JBQ3hCWixhQUFhWSxVQUFVLENBQUM7d0JBQ3hCLHNCQUFzQjt3QkFDdEJaLGFBQWFhLE9BQU8sQ0FBQyxrQkFBa0JkO3dCQUN2Q0MsYUFBYWEsT0FBTyxDQUFDLG1CQUFtQixJQUFJUixPQUFPUyxXQUFXO3dCQUM5RDtvQkFDRixFQUFFLE9BQU9DLE9BQU87d0JBQ2RDLFFBQVFELEtBQUssQ0FBQywrQkFBK0JBO29CQUMvQztnQkFDRjtZQUNGO1lBRUEsaUNBQWlDO1lBQ2pDLElBQUlaLFdBQVc7Z0JBQ2IsSUFBSTtvQkFDRixNQUFNSyxTQUFTQyxLQUFLQyxLQUFLLENBQUNQO29CQUMxQnpFLFlBQVk4RTtvQkFDWjVCLGFBQWEsSUFBSXlCLEtBQUtMLGFBQWFDLE9BQU8sQ0FBQyxzQkFBc0JJLEtBQUtDLEdBQUc7b0JBQ3pFM0csa0RBQUtBLENBQUNnSCxPQUFPLENBQUM7Z0JBQ2hCLEVBQUUsT0FBT0ksT0FBTztvQkFDZEMsUUFBUUQsS0FBSyxDQUFDLDhCQUE4QkE7Z0JBQzlDO1lBQ0Y7WUFFQSwyQkFBMkI7WUFDM0JmLGFBQWFZLFVBQVUsQ0FBQztZQUN4QlosYUFBYVksVUFBVSxDQUFDO1FBRTFCOzBDQUFHLEVBQUU7SUFJTHBJLGdEQUFTQTsyQ0FBQztZQUNSLElBQUlpRyxtQkFBbUJoRCxVQUFVO2dCQUMvQixtREFBbUQ7Z0JBQ25ELElBQUksSUFBNkIsRUFBRTtvQkFDakN3RixPQUFPeEYsUUFBUSxHQUFHQTtnQkFDcEI7Z0JBRUEsTUFBTXlGLFlBQVlDO2lFQUFXO3dCQUMzQm5CLGFBQWFhLE9BQU8sQ0FBQyxrQkFBa0JKLEtBQUtXLFNBQVMsQ0FBQzNGO3dCQUN0RHVFLGFBQWFhLE9BQU8sQ0FBQyxtQkFBbUIsSUFBSVIsT0FBT1MsV0FBVzt3QkFDOURsQyxhQUFhLElBQUl5QjtvQkFDbkI7Z0VBQUc7Z0JBQ0g7dURBQU8sSUFBTWdCLGFBQWFIOztZQUM1QjtRQUNGOzBDQUFHO1FBQUN6RjtRQUFVZ0Q7S0FBZ0I7SUFFOUIsTUFBTTZDLGVBQWU3SSxrREFBV0E7MkRBQUMsQ0FBQzhJO1lBQ2hDLE1BQU1DLFNBQVMsQ0FBQztZQUNoQixPQUFRRDtnQkFDTixLQUFLO3dCQUNFOUYsOEJBQ0FBLDZCQUNBQTtvQkFGTCxJQUFJLEdBQUNBLCtCQUFBQSxTQUFTRSxRQUFRLENBQUNDLFNBQVMsY0FBM0JILG1EQUFBQSw2QkFBNkJnRyxJQUFJLEtBQUlELE9BQU81RixTQUFTLEdBQUc7b0JBQzdELElBQUksR0FBQ0gsOEJBQUFBLFNBQVNFLFFBQVEsQ0FBQ0UsUUFBUSxjQUExQkosa0RBQUFBLDRCQUE0QmdHLElBQUksS0FBSUQsT0FBTzNGLFFBQVEsR0FBRztvQkFDM0QsSUFBSSxHQUFDSiwyQkFBQUEsU0FBU0UsUUFBUSxDQUFDRyxLQUFLLGNBQXZCTCwrQ0FBQUEseUJBQXlCZ0csSUFBSSxLQUFJRCxPQUFPMUYsS0FBSyxHQUFHO3lCQUNoRCxJQUFJLENBQUMsNkJBQTZCNEYsSUFBSSxDQUFDakcsU0FBU0UsUUFBUSxDQUFDRyxLQUFLLEdBQUcwRixPQUFPMUYsS0FBSyxHQUFHO29CQUNyRjtnQkFDRixLQUFLO3dCQUNvQkw7b0JBQXZCLE1BQU1rRyxrQkFBaUJsRyxzQkFBQUEsU0FBU1ksU0FBUyxjQUFsQlosMENBQUFBLG9CQUFvQm1HLE1BQU07MkVBQUNDLENBQUFBO2dDQUFPQSxhQUFzQkE7bUNBQXRCQSxFQUFBQSxjQUFBQSxJQUFJdEYsTUFBTSxjQUFWc0Ysa0NBQUFBLFlBQVlKLElBQUksU0FBTUksbUJBQUFBLElBQUlyRixXQUFXLGNBQWZxRix1Q0FBQUEsaUJBQWlCSixJQUFJOzs7b0JBQ3BHLElBQUksQ0FBQ0Usa0JBQWtCQSxlQUFlRyxNQUFNLEtBQUssR0FBR04sT0FBT25GLFNBQVMsR0FBRztvQkFDdkU7Z0JBQ0YsS0FBSzt3QkFDcUJaO29CQUF4QixNQUFNc0csbUJBQWtCdEcsdUJBQUFBLFNBQVNvQixVQUFVLGNBQW5CcEIsMkNBQUFBLHFCQUFxQm1HLE1BQU07MkVBQUNJLENBQUFBO2dDQUFPQSxZQUFxQkE7bUNBQXJCQSxFQUFBQSxhQUFBQSxJQUFJbEYsS0FBSyxjQUFUa0YsaUNBQUFBLFdBQVdQLElBQUksU0FBTU8sZUFBQUEsSUFBSWpGLE9BQU8sY0FBWGlGLG1DQUFBQSxhQUFhUCxJQUFJOzs7b0JBQ2pHLElBQUksQ0FBQ00sbUJBQW1CQSxnQkFBZ0JELE1BQU0sS0FBSyxHQUFHTixPQUFPM0UsVUFBVSxHQUFHO29CQUMxRTtnQkFDRixLQUFLO29CQUVIO2dCQUNGLEtBQUs7b0JBRUg7Z0JBQ0Y7b0JBQ0U7WUFDSjtZQUNBLE9BQU8yRTtRQUNUOzBEQUFHO1FBQUMvRjtLQUFTO0lBRWIsTUFBTXdHLHVCQUF1QnhKLGtEQUFXQTttRUFBQyxDQUFDOEk7WUFDeEMsTUFBTUMsU0FBU0YsYUFBYUM7WUFDNUIsT0FBT1csT0FBT0MsSUFBSSxDQUFDWCxRQUFRTSxNQUFNLEtBQUs7UUFDeEM7a0VBQUc7UUFBQ1I7S0FBYTtJQUVqQixNQUFNYyxvQkFBb0IzSixrREFBV0E7Z0VBQUM7WUFDcEMsTUFBTTRKLFlBQVksRUFBRTtZQUNwQixJQUFLLElBQUlDLElBQUksR0FBR0EsSUFBSWpILGFBQWFpSCxJQUFLO2dCQUNwQyxNQUFNZCxTQUFTRixhQUFhZ0I7Z0JBQzVCLElBQUlKLE9BQU9DLElBQUksQ0FBQ1gsUUFBUU0sTUFBTSxLQUFLLEdBQUc7b0JBQ3BDTyxVQUFVRSxJQUFJLENBQUNEO2dCQUNqQjtZQUNGO1lBQ0EsT0FBT0Q7UUFDVDsrREFBRztRQUFDaEg7UUFBYWlHO0tBQWE7SUFFOUIsTUFBTWtCLDBCQUEwQi9KLGtEQUFXQTtzRUFBQztZQUMxQyxNQUFNZ0ssU0FBUyxDQUFDO1lBQ2hCOUMsTUFBTStDLE9BQU87OEVBQUMsQ0FBQ0MsTUFBTUM7b0JBQ25CLE1BQU1wQixTQUFTRixhQUFhc0I7b0JBQzVCSCxNQUFNLENBQUNHLE1BQU0sR0FBR1YsT0FBT0MsSUFBSSxDQUFDWCxRQUFRTSxNQUFNLEtBQUs7Z0JBQ2pEOztZQUNBLE9BQU9XO1FBQ1Q7cUVBQUc7UUFBQzlDO1FBQU8yQjtLQUFhO0lBRXhCLE1BQU11QixvQkFBb0JwSyxrREFBV0E7Z0VBQUM7WUFDcEMsSUFBSyxJQUFJNkosSUFBSSxHQUFHQSxJQUFJM0MsTUFBTW1DLE1BQU0sR0FBRyxHQUFHUSxJQUFLO2dCQUN6QyxJQUFJLENBQUNMLHFCQUFxQkssSUFBSSxPQUFPO1lBQ3ZDO1lBQ0EsT0FBTztRQUNUOytEQUFHO1FBQUNMO1FBQXNCdEMsTUFBTW1DLE1BQU07S0FBQztJQUV2QyxNQUFNZ0IsaUJBQWlCckssa0RBQVdBOzZEQUFDLFNBQUNzSyxTQUFTQyxPQUFPQztnQkFBT0wseUVBQVE7WUFDakVsSDtxRUFBWXdILENBQUFBO29CQUNWLElBQUlDLFVBQVU7d0JBQUUsR0FBR0QsSUFBSTtvQkFBQztvQkFDeEIsSUFBSU4sVUFBVSxRQUFRUSxNQUFNQyxPQUFPLENBQUNILElBQUksQ0FBQ0gsUUFBUSxHQUFHO3dCQUNsRCxNQUFNTyxXQUFXOytCQUFJSixJQUFJLENBQUNILFFBQVE7eUJBQUM7d0JBQ25DTyxRQUFRLENBQUNWLE1BQU0sR0FBRzs0QkFBRSxHQUFHVSxRQUFRLENBQUNWLE1BQU07NEJBQUUsQ0FBQ0ksTUFBTSxFQUFFQzt3QkFBTTt3QkFDdkRFLE9BQU8sQ0FBQ0osUUFBUSxHQUFHTztvQkFDckIsT0FBTyxJQUFJLE9BQU9KLElBQUksQ0FBQ0gsUUFBUSxLQUFLLFlBQVksQ0FBQ0ssTUFBTUMsT0FBTyxDQUFDSCxJQUFJLENBQUNILFFBQVEsR0FBRzt3QkFDN0VJLE9BQU8sQ0FBQ0osUUFBUSxHQUFHOzRCQUFFLEdBQUdHLElBQUksQ0FBQ0gsUUFBUTs0QkFBRSxDQUFDQyxNQUFNLEVBQUVDO3dCQUFNO29CQUN4RCxPQUFPO3dCQUNMRSxPQUFPLENBQUNILE1BQU0sR0FBR0M7b0JBQ25CO29CQUNBLE9BQU9FO2dCQUNUOztZQUNBLElBQUloRixnQkFBZ0IsQ0FBQzZFLE1BQU0sRUFBRTtnQkFDM0I1RTt5RUFBb0I4RSxDQUFBQTt3QkFDbEIsTUFBTUssWUFBWTs0QkFBRSxHQUFHTCxJQUFJO3dCQUFDO3dCQUM1QixPQUFPSyxTQUFTLENBQUNQLE1BQU07d0JBQ3ZCLE9BQU9PO29CQUNUOztZQUNGO1FBQ0Y7NERBQUc7UUFBQ3BGO0tBQWlCO0lBRXJCLE1BQU1xRixlQUFlL0ssa0RBQVdBOzJEQUFDLENBQUNzSyxTQUFTVTtZQUN6Qy9IO21FQUFZd0gsQ0FBQUEsT0FBUzt3QkFDbkIsR0FBR0EsSUFBSTt3QkFDUCxDQUFDSCxRQUFRLEVBQUU7K0JBQUlHLElBQUksQ0FBQ0gsUUFBUTs0QkFBRTtnQ0FBRSxHQUFHVSxRQUFRO2dDQUFFbkgsSUFBSW9ILEtBQUtDLE1BQU0sR0FBR0MsUUFBUSxDQUFDLElBQUlDLFNBQVMsQ0FBQyxHQUFHOzRCQUFJO3lCQUFFO29CQUNqRzs7UUFDRjswREFBRyxFQUFFO0lBRUwsTUFBTUMsa0JBQWtCckwsa0RBQVdBOzhEQUFDLENBQUNzSyxTQUFTekc7WUFDNUNaO3NFQUFZd0gsQ0FBQUEsT0FBUzt3QkFDbkIsR0FBR0EsSUFBSTt3QkFDUCxDQUFDSCxRQUFRLEVBQUVHLElBQUksQ0FBQ0gsUUFBUSxDQUFDbkIsTUFBTTtrRkFBQ21DLENBQUFBLE9BQVFBLEtBQUt6SCxFQUFFLEtBQUtBOztvQkFDdEQ7O1FBQ0Y7NkRBQUcsRUFBRTtJQUVMLE1BQU0wSCxXQUFXdkwsa0RBQVdBO3VEQUFDO1lBQzNCLE1BQU0rSSxTQUFTRixhQUFhakc7WUFDNUIsSUFBSTZHLE9BQU9DLElBQUksQ0FBQ1gsUUFBUU0sTUFBTSxHQUFHLEdBQUc7Z0JBQ2xDMUQsb0JBQW9Cb0Q7Z0JBQ3BCbEQsd0JBQXdCO2dCQUN4QixNQUFNMkYsZ0JBQWdCL0IsT0FBT2dDLE1BQU0sQ0FBQzFDO2dCQUNwQzdILGtEQUFLQSxDQUFDb0gsS0FBSyxDQUFDa0QsYUFBYSxDQUFDLEVBQUU7Z0JBQzVCO1lBQ0Y7WUFDQXpGOytEQUFrQjBFLENBQUFBLE9BQVE7MkJBQUksSUFBSWlCLElBQUk7K0JBQUlqQjs0QkFBTTdIO3lCQUFZO3FCQUFFOztZQUM5RCtDLG9CQUFvQixDQUFDO1lBQ3JCRSx3QkFBd0I7WUFDeEIsSUFBSWpELGNBQWNzRSxNQUFNbUMsTUFBTSxHQUFHLEdBQUc7Z0JBQ2xDeEcsZUFBZUQsY0FBYztnQkFDN0IxQixrREFBS0EsQ0FBQ2dILE9BQU8sQ0FBQyxHQUE0QixPQUF6QmhCLEtBQUssQ0FBQ3RFLFlBQVksQ0FBQ3lCLEtBQUssRUFBQztZQUM1QztRQUNGO3NEQUFHO1FBQUN6QjtRQUFhaUc7UUFBYzNCO0tBQU07SUFFckMsTUFBTXlFLFdBQVczTCxrREFBV0E7dURBQUM7WUFDM0IyRixvQkFBb0IsQ0FBQztZQUNyQkUsd0JBQXdCO1lBQ3hCLElBQUlqRCxjQUFjLEdBQUdDLGVBQWVELGNBQWM7UUFDcEQ7c0RBQUc7UUFBQ0E7S0FBWTtJQUVoQixNQUFNZ0osa0JBQWtCNUwsa0RBQVdBOzhEQUFDLENBQUM4STtZQUNuQyxJQUFJQSxhQUFhbEcsZUFBZWtELGVBQWUrRixRQUFRLENBQUMvQyxZQUFZO2dCQUNsRWpHLGVBQWVpRztnQkFDZm5ELG9CQUFvQixDQUFDO2dCQUNyQkUsd0JBQXdCO1lBQzFCO1FBQ0Y7NkRBQUc7UUFBQ2pEO1FBQWFrRDtLQUFlO0lBRWhDLE1BQU1nRyxpQkFBaUI5TCxrREFBV0E7NkRBQUM7WUFDakMyRixvQkFBb0IsQ0FBQztZQUNyQkUsd0JBQXdCO1lBQ3hCLElBQUksQ0FBQ3VFLHFCQUFxQjtnQkFDeEIsTUFBTTJCLFlBQVksQ0FBQztnQkFDbkIsSUFBSyxJQUFJbEMsSUFBSSxHQUFHQSxJQUFJM0MsTUFBTW1DLE1BQU0sR0FBRyxHQUFHUSxJQUFLO29CQUN6QyxNQUFNbUMsYUFBYW5ELGFBQWFnQjtvQkFDaENKLE9BQU93QyxNQUFNLENBQUNGLFdBQVdDO2dCQUMzQjtnQkFDQXJHLG9CQUFvQm9HO2dCQUNwQmxHLHdCQUF3QjtnQkFDeEIsSUFBSyxJQUFJZ0UsSUFBSSxHQUFHQSxJQUFJM0MsTUFBTW1DLE1BQU0sR0FBRyxHQUFHUSxJQUFLO29CQUN6QyxNQUFNbUMsYUFBYW5ELGFBQWFnQjtvQkFDaEMsSUFBSUosT0FBT0MsSUFBSSxDQUFDc0MsWUFBWTNDLE1BQU0sR0FBRyxHQUFHO3dCQUN0Q3hHLGVBQWVnSDt3QkFDZjNJLGtEQUFLQSxDQUFDb0gsS0FBSyxDQUFDLDBDQUF5RCxPQUFmcEIsS0FBSyxDQUFDMkMsRUFBRSxDQUFDeEYsS0FBSzt3QkFDcEU7b0JBQ0Y7Z0JBQ0Y7Z0JBQ0E7WUFDRjtZQUNBLElBQUk7Z0JBQ0ZnQixnQkFBZ0I7Z0JBQ2hCRSxtQkFBbUI7Z0JBQ25CRSxtQkFBbUI7Z0JBQ25CLE1BQU15RyxXQUFXLE1BQU1DLE1BQU0sd0JBQXdCO29CQUNuREMsUUFBUTtvQkFDUkMsU0FBUzt3QkFBRSxnQkFBZ0I7b0JBQW1CO29CQUM5Q0MsTUFBTXRFLEtBQUtXLFNBQVMsQ0FBQzt3QkFBRTNGO3dCQUFVdUosWUFBWXpKO29CQUFpQjtnQkFDaEU7Z0JBQ0EsTUFBTTBKLGNBQWNOLFNBQVNHLE9BQU8sQ0FBQ0ksR0FBRyxDQUFDO2dCQUN6QyxJQUFJLENBQUNELGVBQWUsQ0FBQ0EsWUFBWVgsUUFBUSxDQUFDLHFCQUFxQixNQUFNLElBQUlhLE1BQU07Z0JBQy9FLE1BQU1DLE9BQU8sTUFBTVQsU0FBU1UsSUFBSTtnQkFDaEMsSUFBSSxDQUFDVixTQUFTVyxFQUFFLEVBQUUsTUFBTSxJQUFJSCxNQUFNQyxLQUFLckUsS0FBSyxJQUFJO2dCQUNoRCxJQUFJLENBQUNxRSxLQUFLdkcsVUFBVSxJQUFJLENBQUN1RyxLQUFLRyxXQUFXLEVBQUUsTUFBTSxJQUFJSixNQUFNO2dCQUMzRG5HLGFBQWFvRyxLQUFLRyxXQUFXO2dCQUM3QnpHLGNBQWNzRyxLQUFLdkcsVUFBVTtnQkFDN0JLLFlBQVlrRyxLQUFLbkcsUUFBUSxJQUFJO2dCQUM3QkcsZUFBZWdHLEtBQUtqRyxXQUFXLElBQUksRUFBRTtnQkFDckNqQixtQkFBbUI7Z0JBQ25CdkUsa0RBQUtBLENBQUNnSCxPQUFPLENBQUM7WUFDaEIsRUFBRSxPQUFPSSxPQUFPO2dCQUNkQyxRQUFRRCxLQUFLLENBQUMsNEJBQTRCQTtnQkFDMUNwSCxrREFBS0EsQ0FBQ29ILEtBQUssQ0FBQ0EsTUFBTXlFLE9BQU8sSUFBSTtZQUMvQixTQUFVO2dCQUNSMUgsZ0JBQWdCO2dCQUNoQkUsbUJBQW1CO1lBQ3JCO1FBQ0Y7NERBQUc7UUFBQzZFO1FBQW1CcEg7UUFBVWtFO1FBQU8yQjtRQUFjL0Y7S0FBaUI7SUFFdkUsTUFBTWtLLGFBQWFoTixrREFBV0E7eURBQUM7WUFDN0J1SCxhQUFhYSxPQUFPLENBQUMsa0JBQWtCSixLQUFLVyxTQUFTLENBQUMzRjtZQUN0RHVFLGFBQWFhLE9BQU8sQ0FBQyxtQkFBbUIsSUFBSVIsT0FBT1MsV0FBVztZQUM5RGxDLGFBQWEsSUFBSXlCO1lBQ2pCMUcsa0RBQUtBLENBQUNnSCxPQUFPLENBQUM7UUFDaEI7d0RBQUc7UUFBQ2xGO0tBQVM7SUFFYixNQUFNaUssa0JBQWtCak4sa0RBQVdBOzhEQUFDLE9BQU9zSztZQUN6QyxJQUFJLENBQUMxRCxzQkFBc0I7WUFDM0IxRixrREFBS0EsQ0FBQ2dNLE9BQU8sQ0FBQztZQUNkLElBQUk7Z0JBQ0YsTUFBTSxJQUFJQzswRUFBUUMsQ0FBQUEsVUFBVzFFLFdBQVcwRSxTQUFTOztnQkFDakRsTSxrREFBS0EsQ0FBQ21NLE9BQU87Z0JBQ2JuTSxrREFBS0EsQ0FBQ2dILE9BQU8sQ0FBQztZQUNoQixFQUFFLE9BQU9JLE9BQU87Z0JBQ2RwSCxrREFBS0EsQ0FBQ21NLE9BQU87Z0JBQ2JuTSxrREFBS0EsQ0FBQ29ILEtBQUssQ0FBQztZQUNkO1FBQ0Y7NkRBQUc7UUFBQzFCO0tBQXFCO0lBRXpCLDhEQUE4RDtJQUM5RDdHLGdEQUFTQTsyQ0FBQztZQUNSLE1BQU11Tjt3RUFBdUI7b0JBQzNCLHdEQUF3RDtvQkFDeEQsSUFBSWxELHFCQUFxQjt3QkFDdkIwQjtvQkFDRixPQUFPO3dCQUNMNUssa0RBQUtBLENBQUNvSCxLQUFLLENBQUM7b0JBQ2Q7Z0JBQ0Y7O1lBRUEsSUFBSSxJQUE2QixFQUFFO2dCQUNqQ0UsT0FBTytFLGdCQUFnQixDQUFDLGtCQUFrQkQ7Z0JBQzFDO3VEQUFPLElBQU05RSxPQUFPZ0YsbUJBQW1CLENBQUMsa0JBQWtCRjs7WUFDNUQ7UUFDRjswQ0FBRztRQUFDbEQ7UUFBbUIwQjtLQUFlO0lBRXRDLE1BQU0yQixvQkFBb0J6TixrREFBV0E7Z0VBQUM7WUFDcEMsTUFBTTBOLGFBQWEsQ0FBQztZQUNwQnhHLE1BQU0rQyxPQUFPO3dFQUFDLENBQUNDLE1BQU1DO29CQUNuQixNQUFNcEIsU0FBU0YsYUFBYXNCO29CQUM1QnVELFVBQVUsQ0FBQ3ZELE1BQU0sR0FBR1YsT0FBT0MsSUFBSSxDQUFDWCxRQUFRTSxNQUFNLEtBQUs7Z0JBQ3JEOztZQUNBLE9BQU9xRTtRQUNUOytEQUFHO1FBQUN4RztRQUFPMkI7S0FBYTtJQUV4QixNQUFNOEUsb0JBQW9CO1FBQ3hCLE1BQU1DLGNBQWM7WUFDbEI1SztZQUNBcUg7WUFDQVU7WUFDQU07WUFDQTFJO1lBQ0ErQztZQUNBRTtZQUNBaUksUUFBUWI7WUFDUmMsYUFBYWI7UUFDZjtRQUNBLE9BQVFySztZQUNOLEtBQUs7Z0JBQ0gscUJBQU8sOERBQUN2Qix5RUFBZ0JBO29CQUFFLEdBQUd1TSxXQUFXOzs7Ozs7WUFDMUMsS0FBSztnQkFDSCxxQkFBTyw4REFBQ3RNLHNFQUFhQTtvQkFBRSxHQUFHc00sV0FBVzs7Ozs7O1lBQ3ZDLEtBQUs7Z0JBQ0gscUJBQU8sOERBQUMvTCx5RUFBc0JBO29CQUFFLEdBQUcrTCxXQUFXOzs7Ozs7WUFDaEQsS0FBSztnQkFDSCxxQkFBTyw4REFBQzdMLDJEQUFvQkE7b0JBQUUsR0FBRzZMLFdBQVc7Ozs7OztZQUM5QyxLQUFLO2dCQUNILHFCQUFPLDhEQUFDNUwseURBQWtCQTtvQkFBRSxHQUFHNEwsV0FBVzs7Ozs7O1lBQzVDLEtBQUs7Z0JBQ0gscUJBQU8sOERBQUMzTCxrRUFBa0JBO29CQUFFLEdBQUcyTCxXQUFXO29CQUFFOUssa0JBQWtCQTtvQkFBa0JpTCxrQkFBa0JoTDs7Ozs7O1lBQ3BHO2dCQUNFLE9BQU87UUFDWDtJQUNGO0lBRUEsSUFBSXlDLG1CQUFtQlksY0FBY0UsV0FBVztRQUM5QyxxQkFDRSw4REFBQy9ELHVEQUFhQTtZQUNaUyxVQUFVQTtZQUNWb0QsWUFBWUE7WUFDWjRILGFBQWE7Z0JBQ1h2SSxtQkFBbUI7Z0JBQ25CWSxjQUFjO2dCQUNkRSxhQUFhO2dCQUNiMUQsZUFBZTtZQUNqQjtZQUNBb0wsY0FBYztnQkFDWnhJLG1CQUFtQjtnQkFDbkI1QyxlQUFlLElBQUkseUJBQXlCO1lBQzlDOzs7Ozs7SUFHTjtJQUVBLHFCQUNFLDhEQUFDcUw7UUFBSUMsV0FBVTs7MEJBQ2IsOERBQUNEO2dCQUFJQyxXQUFVOzBCQUNiLDRFQUFDRDtvQkFBSUMsV0FBVTs7Ozs7Ozs7Ozs7MEJBRWpCLDhEQUFDL0wsNERBQVdBO2dCQUFDZ00sV0FBVzlJO2dCQUFpQitJLFlBQVksSUFBTTlJLG1CQUFtQjs7Ozs7OzBCQUM5RSw4REFBQzJJO2dCQUFJQyxXQUFVOzBCQUNiLDRFQUFDRDtvQkFBSUMsV0FBVTs7d0JBQ1osQ0FBQ3pMLDRCQUNBLDhEQUFDekMsa0RBQU1BLENBQUNpTyxHQUFHOzRCQUFDQyxXQUFVOzRCQUFtQkcsU0FBUztnQ0FBRUMsU0FBUztnQ0FBR0MsR0FBRyxDQUFDOzRCQUFHOzRCQUFHQyxTQUFTO2dDQUFFRixTQUFTO2dDQUFHQyxHQUFHOzRCQUFFOzs4Q0FDcEcsOERBQUNOO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDM04sOE1BQVFBO29EQUFDMk4sV0FBVTs7Ozs7OzhEQUNwQiw4REFBQ0Q7b0RBQUlDLFdBQVU7Ozs7Ozs7Ozs7OztzREFFakIsOERBQUNPOzRDQUFHUCxXQUFVO3NEQUE0Qzs7Ozs7Ozs7Ozs7OzhDQUU1RCw4REFBQ1E7b0NBQUVSLFdBQVU7OENBQTBDOzs7Ozs7Ozs7Ozs7c0NBRzNELDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBRWIsOERBQUNEO29DQUFJQyxXQUFVOztzREFFYiw4REFBQ0Q7NENBQUlDLFdBQVU7c0RBQ2IsNEVBQUNqTSxtRUFBeUJBO2dEQUN4QmdGLE9BQU9BO2dEQUNQdEUsYUFBYUE7Z0RBQ2JrRCxnQkFBZ0JBO2dEQUNoQjhJLGFBQWFoRDtnREFDYmlELFNBQVE7Z0RBQ1JDLFlBQVk7Z0RBQ1pDLGNBQWM7Ozs7Ozs7Ozs7O3NEQUtsQiw4REFBQ2I7NENBQUlDLFdBQVU7c0RBQ2IsNEVBQUNqTywyREFBZUE7Z0RBQUM4TyxNQUFLOzBEQUNwQiw0RUFBQy9PLGtEQUFNQSxDQUFDaU8sR0FBRztvREFBbUJJLFNBQVM7d0RBQUVDLFNBQVM7d0RBQUdVLEdBQUc7b0RBQUc7b0RBQUdSLFNBQVM7d0RBQUVGLFNBQVM7d0RBQUdVLEdBQUc7b0RBQUU7b0RBQUdDLE1BQU07d0RBQUVYLFNBQVM7d0RBQUdVLEdBQUcsQ0FBQztvREFBRztvREFBR0UsWUFBWTt3REFBRUMsVUFBVTtvREFBSTs4REFDcEp6QjttREFEYy9LOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBUXZCLDhEQUFDc0w7b0NBQUlDLFdBQVU7OENBRWIsNEVBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQzdMLDZEQUFtQkE7Z0RBQ2xCVSxVQUFVQTtnREFDVkYsa0JBQWtCQTtnREFDbEJvQyxhQUFhQTtnREFDYm1LLGlCQUFpQixJQUFNbEssZUFBZSxDQUFDRDs7Ozs7OzBEQUl6Qyw4REFBQ2pGLGtEQUFNQSxDQUFDaU8sR0FBRztnREFDVEMsV0FBVTtnREFDVkcsU0FBUztvREFBRUMsU0FBUztvREFBR1UsR0FBRztnREFBRztnREFDN0JSLFNBQVM7b0RBQUVGLFNBQVM7b0RBQUdVLEdBQUc7Z0RBQUU7Z0RBQzVCRSxZQUFZO29EQUFFRyxPQUFPO2dEQUFJOztrRUFFekIsOERBQUNwQjt3REFBSUMsV0FBVTs7MEVBQ2IsOERBQUN0Tiw4TUFBTUE7Z0VBQUNzTixXQUFVOzs7Ozs7MEVBQ2xCLDhEQUFDb0I7Z0VBQUdwQixXQUFVOzBFQUF3Qjs7Ozs7Ozs7Ozs7O2tFQUV4Qyw4REFBQ0Q7d0RBQUlDLFdBQVU7OzBFQUNiLDhEQUFDRDtnRUFBSUMsV0FBVyxzQkFHZixPQUZDLENBQUN4TCxZQUFZNk0sWUFBWSxJQUFJLE1BQU0sS0FBSyxtQkFDeEMsQ0FBQzdNLFlBQVk2TSxZQUFZLElBQUksTUFBTSxLQUFLLG9CQUFvQjs7b0VBRTNEN00sWUFBWTZNLFlBQVksSUFBSTtvRUFBRTs7Ozs7OzswRUFFakMsOERBQUN0QjtnRUFBSUMsV0FBVTswRUFDWixDQUFDeEwsWUFBWTZNLFlBQVksSUFBSSxNQUFNLEtBQUssY0FDeEMsQ0FBQzdNLFlBQVk2TSxZQUFZLElBQUksTUFBTSxLQUFLLFNBQVM7Ozs7Ozs7Ozs7OztvREFHckQ3TSxFQUFBQSwrQkFBQUEsWUFBWThNLGVBQWUsY0FBM0I5TSxtREFBQUEsNkJBQTZCMEcsTUFBTSxJQUFHLG1CQUNyQyw4REFBQzZFO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ0Q7Z0VBQUlDLFdBQVU7MEVBQTZCOzs7Ozs7NERBQzNDeEwsWUFBWThNLGVBQWUsQ0FBQ0MsS0FBSyxDQUFDLEdBQUcsR0FBR0MsR0FBRyxDQUFDLENBQUNDLEtBQUt6RixzQkFDakQsOERBQUMrRDtvRUFBZ0JDLFdBQVU7O3NGQUN6Qiw4REFBQ0Q7NEVBQUlDLFdBQVU7Ozs7OztzRkFDZiw4REFBQzBCOzRFQUFLMUIsV0FBVTtzRkFBaUJ5QixJQUFJN0MsT0FBTzs7Ozs7OzttRUFGcEM1Qzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FZdEIsOERBQUMrRDtvQ0FBSUMsV0FBVTs4Q0FDYiw0RUFBQ2xPLGtEQUFNQSxDQUFDaU8sR0FBRzt3Q0FDVEMsV0FBVTt3Q0FDVkcsU0FBUzs0Q0FBRUMsU0FBUzs0Q0FBR0MsR0FBRzt3Q0FBRzt3Q0FDN0JDLFNBQVM7NENBQUVGLFNBQVM7NENBQUdDLEdBQUc7d0NBQUU7d0NBQzVCVyxZQUFZOzRDQUFFRyxPQUFPO3dDQUFJOzswREFFekIsOERBQUNwQjtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUNvQjt3REFBR3BCLFdBQVU7OzBFQUNaLDhEQUFDdk4sOE1BQUdBO2dFQUFDdU4sV0FBVTs7Ozs7OzREQUE2Qjs7Ozs7OztrRUFHOUMsOERBQUMyQjt3REFDQ0MsU0FBUyxJQUFNNUssZUFBZSxDQUFDRDt3REFDL0JpSixXQUFXLGdFQUVWLE9BRENqSixjQUFjLGdDQUFnQztrRUFHL0NBLGNBQWMsU0FBUzs7Ozs7Ozs7Ozs7OzRDQUczQkEsNEJBQ0MsOERBQUNnSjtnREFBSUMsV0FBVTswREFDYiw0RUFBQzlMLHVEQUFxQkE7b0RBQUNXLFVBQVVBO29EQUFVRixrQkFBa0JBOzs7Ozs7Ozs7OzBFQUcvRCw4REFBQ29MO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ3ZOLDhNQUFHQTt3REFBQ3VOLFdBQVU7Ozs7OztrRUFDZiw4REFBQ1E7d0RBQUVSLFdBQVU7a0VBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBUXJDLDhEQUFDL00sa0VBQWtCQTtnQkFBQ3dCLGFBQWFBO2dCQUFhb04sWUFBWTlJLE1BQU1tQyxNQUFNO2dCQUFFNEcsWUFBWXRFO2dCQUFVdUUsUUFBUTNFO2dCQUFVNEUsWUFBWXJFO2dCQUFnQitCLFFBQVFiO2dCQUFZb0QsV0FBVyxJQUFNakwsZUFBZSxDQUFDRDtnQkFBY0UsY0FBY0E7Z0JBQWNpTCxZQUFZek4sZ0JBQWdCc0UsTUFBTW1DLE1BQU0sR0FBRyxJQUFJZSxzQkFBc0JaLHFCQUFxQjVHO2dCQUFjc0MsYUFBYUE7Z0JBQWFnQyxPQUFPQTtnQkFBT2xFLFVBQVVBO2dCQUFVd0QsVUFBVTdELFlBQVk2TSxZQUFZO2dCQUFFeEosaUJBQWlCQTs7Ozs7Ozs7Ozs7O0FBRzFjO0dBamhCTXZEOztRQXFEZ0JELDhEQUFjQTs7O0tBckQ5QkM7QUFtaEJOLGlFQUFlQSxxQkFBcUJBLEVBQUMiLCJzb3VyY2VzIjpbIkM6XFxQcm9qZWN0c1xcTmV3QmxpbmtGaW5kQUlcXGZyb250ZW5kLW1hc3Rlclxcc3JjXFxjb21wb25lbnRzXFxyZXN1bWVcXFJlc3VtZUJ1aWxkZXIuanN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcbmltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0LCB1c2VDYWxsYmFjayB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IG1vdGlvbiwgQW5pbWF0ZVByZXNlbmNlIH0gZnJvbSAnZnJhbWVyLW1vdGlvbic7XG5pbXBvcnQgeyBcbiAgVXNlciwgR3JhZHVhdGlvbkNhcCwgQnJpZWZjYXNlLCBBd2FyZCwgRmlsZVRleHQsIFNwYXJrbGVzLCBTYXZlLCBSb3RhdGVDY3csIERvd25sb2FkLCBFeWUsIFRhcmdldCwgQ2xvY2ssIENoZWNrQ2lyY2xlLCBBbGVydFRyaWFuZ2xlLCBMaWdodGJ1bGJcbn0gZnJvbSAnbHVjaWRlLXJlYWN0JztcbmltcG9ydCB7IHRvYXN0IH0gZnJvbSAncmVhY3QtaG90LXRvYXN0JztcbmltcG9ydCBFbmhhbmNlZFN0ZXBOYXZpZ2F0aW9uIGZyb20gJy4vU3RlcE5hdmlnYXRpb24nO1xuaW1wb3J0IFNtYXJ0TmF2aWdhdGlvbkJhciBmcm9tICcuLi9sYXlvdXQvU21hcnROYXZpZ2F0aW9uQmFyJztcbmltcG9ydCB7IFBlcnNvbmFsSW5mb0Zvcm0sIEVkdWNhdGlvbkZvcm0sIEV4cGVyaWVuY2VGb3JtIGFzIEJhc2ljRXhwZXJpZW5jZUZvcm0sIFNraWxsc1Byb2plY3RzRm9ybSBhcyBCYXNpY1NraWxsc1Byb2plY3RzRm9ybSwgUmV2aWV3Rm9ybSBhcyBCYXNpY1Jldmlld0Zvcm0gfSBmcm9tICcuL2Zvcm1zL1Jlc3VtZUZvcm1Db21wb25lbnRzJztcbmltcG9ydCB7IEVuaGFuY2VkRXhwZXJpZW5jZUZvcm0gfSBmcm9tICcuL2Zvcm1zL0V4cGVyaWVuY2VGb3JtJztcbmltcG9ydCB7IEVuaGFuY2VkU2tpbGxzUHJvamVjdHNGb3JtIH0gZnJvbSAnLi9mb3Jtcy9Ta2lsbHNQcm9qZWN0c0Zvcm0nO1xuaW1wb3J0IEVuaGFuY2VkUHJvamVjdHNGb3JtIGZyb20gJy4vZm9ybXMvUHJvamVjdHNGb3JtJztcbmltcG9ydCBFbmhhbmNlZFNraWxsc0Zvcm0gZnJvbSAnLi9mb3Jtcy9Ta2lsbHNGb3JtJztcbmltcG9ydCB7IEVuaGFuY2VkUmV2aWV3Rm9ybSB9IGZyb20gJy4vZm9ybXMvUmV2aWV3Rm9ybSc7XG5pbXBvcnQgRW5oYW5jZWRQcm9ncmVzc0luZGljYXRvciBmcm9tICcuL0VuaGFuY2VkUHJvZ3Jlc3NJbmRpY2F0b3InO1xuaW1wb3J0IEVuaGFuY2VkTmF2aWdhdGlvbiBmcm9tICcuL0VuaGFuY2VkTmF2aWdhdGlvbic7XG5pbXBvcnQgUHJvZ3Jlc3NCYXIgZnJvbSAnLi4vY29tbW9uL1Byb2dyZXNzQmFyJztcbmltcG9ydCBFbmhhbmNlZFJlc3VtZVByZXZpZXcgZnJvbSAnLi9SZXN1bWVQcmV2aWV3JztcbmltcG9ydCBTaW1wbGVSZXN1bWVQcmV2aWV3IGZyb20gJy4vU2ltcGxlUmVzdW1lUHJldmlldyc7XG5pbXBvcnQgU3VjY2Vzc1NjcmVlbiBmcm9tICcuL1N1Y2Nlc3NTY3JlZW4nO1xuaW1wb3J0IHVzZUFUU0FuYWx5c2lzIGZyb20gJ0AvaG9va3MvdXNlQVRTQW5hbHlzaXMnO1xuXG5jb25zdCBFbmhhbmNlZFJlc3VtZUJ1aWxkZXIgPSAoeyBoaWRlSGVhZGVyID0gZmFsc2UgfSkgPT4ge1xuICBjb25zdCBbY3VycmVudFN0ZXAsIHNldEN1cnJlbnRTdGVwXSA9IHVzZVN0YXRlKDApO1xuICBjb25zdCBbc2VsZWN0ZWRUZW1wbGF0ZSwgc2V0U2VsZWN0ZWRUZW1wbGF0ZV0gPSB1c2VTdGF0ZSgnbW9kZXJuJyk7XG4gIGNvbnN0IFtmb3JtRGF0YSwgc2V0Rm9ybURhdGFdID0gdXNlU3RhdGUoe1xuICAgIHBlcnNvbmFsOiB7XG4gICAgICBmaXJzdE5hbWU6IFwiXCIsXG4gICAgICBsYXN0TmFtZTogXCJcIixcbiAgICAgIGVtYWlsOiBcIlwiLFxuICAgICAgcGhvbmU6IFwiXCIsXG4gICAgICBsb2NhdGlvbjogXCJcIixcbiAgICAgIGxpbmtlZGluOiBcIlwiLFxuICAgICAgcG9ydGZvbGlvOiBcIlwiLFxuICAgICAgc3VtbWFyeTogXCJcIixcbiAgICAgIHByb2ZpbGVJbWFnZTogXCJcIlxuICAgIH0sXG4gICAgZWR1Y2F0aW9uOiBbXG4gICAgICB7IGlkOiAxLCBkZWdyZWU6IFwiXCIsIGluc3RpdHV0aW9uOiBcIlwiLCBsb2NhdGlvbjogXCJcIiwgc3RhcnREYXRlOiBcIlwiLCBlbmREYXRlOiBcIlwiLCBncGE6IFwiXCIsIHJlbGV2YW50OiBcIlwiIH1cbiAgICBdLFxuICAgIGV4cGVyaWVuY2U6IFtcbiAgICAgIHsgaWQ6IDEsIHRpdGxlOiBcIlwiLCBjb21wYW55OiBcIlwiLCBsb2NhdGlvbjogXCJcIiwgc3RhcnREYXRlOiBcIlwiLCBlbmREYXRlOiBcIlwiLCBjdXJyZW50OiBmYWxzZSwgZGVzY3JpcHRpb246IFwiXCIgfVxuICAgIF0sXG4gICAgc2tpbGxzOiB7IHRlY2huaWNhbDogW10sIGxhbmd1YWdlczogW10sIGNlcnRpZmljYXRpb25zOiBbXSB9LFxuICAgIHByb2plY3RzOiBbXG4gICAgICB7IGlkOiAxLCBuYW1lOiBcIlwiLCBkZXNjcmlwdGlvbjogXCJcIiwgdGVjaG5vbG9naWVzOiBcIlwiLCBsaW5rOiBcIlwiIH1cbiAgICBdLFxuICAgIGpvYkRlc2NyaXB0aW9uOiBcIlwiXG4gIH0pO1xuICBjb25zdCBbc2hvd1ByZXZpZXcsIHNldFNob3dQcmV2aWV3XSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2lzR2VuZXJhdGluZywgc2V0SXNHZW5lcmF0aW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW3Nob3dQcm9ncmVzc0Jhciwgc2V0U2hvd1Byb2dyZXNzQmFyXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW3Jlc3VtZUdlbmVyYXRlZCwgc2V0UmVzdW1lR2VuZXJhdGVkXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW3ZhbGlkYXRpb25FcnJvcnMsIHNldFZhbGlkYXRpb25FcnJvcnNdID0gdXNlU3RhdGUoe30pO1xuICBjb25zdCBbc2hvd1ZhbGlkYXRpb25FcnJvcnMsIHNldFNob3dWYWxpZGF0aW9uRXJyb3JzXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2NvbXBsZXRlZFN0ZXBzLCBzZXRDb21wbGV0ZWRTdGVwc10gPSB1c2VTdGF0ZShbXSk7XG4gIGNvbnN0IFthdXRvU2F2ZUVuYWJsZWQsIHNldEF1dG9TYXZlRW5hYmxlZF0gPSB1c2VTdGF0ZSh0cnVlKTtcbiAgY29uc3QgW2xhc3RTYXZlZCwgc2V0TGFzdFNhdmVkXSA9IHVzZVN0YXRlKG51bGwpO1xuICBjb25zdCBbcmVzdW1lRGF0YSwgc2V0UmVzdW1lRGF0YV0gPSB1c2VTdGF0ZShudWxsKTtcbiAgY29uc3QgW3Jlc3VtZVVybCwgc2V0UmVzdW1lVXJsXSA9IHVzZVN0YXRlKFwiXCIpO1xuICBjb25zdCBbYXRzU2NvcmUsIHNldEF0c1Njb3JlXSA9IHVzZVN0YXRlKG51bGwpO1xuICBjb25zdCBbc3VnZ2VzdGlvbnMsIHNldFN1Z2dlc3Rpb25zXSA9IHVzZVN0YXRlKFtdKTtcbiAgY29uc3QgW2FpU3VnZ2VzdGlvbnNFbmFibGVkLCBzZXRBaVN1Z2dlc3Rpb25zRW5hYmxlZF0gPSB1c2VTdGF0ZSh0cnVlKTtcbiAgY29uc3QgW3NtYXJ0VmFsaWRhdGlvbiwgc2V0U21hcnRWYWxpZGF0aW9uXSA9IHVzZVN0YXRlKHRydWUpO1xuICBjb25zdCBbc2hvd0hpbnRzLCBzZXRTaG93SGludHNdID0gdXNlU3RhdGUodHJ1ZSk7XG5cbiAgY29uc3Qgc3RlcHMgPSBbXG4gICAgeyBpZDogMCwgdGl0bGU6IFwiUGVyc29uYWwgSW5mb3JtYXRpb25cIiwgaWNvbjogVXNlciwgZGVzY3JpcHRpb246IFwiVGVsbCB1cyBhYm91dCB5b3Vyc2VsZlwiLCBlc3RpbWF0ZWRUaW1lOiAzLCByZXF1aXJlZDogdHJ1ZSB9LFxuICAgIHsgaWQ6IDEsIHRpdGxlOiBcIkVkdWNhdGlvblwiLCBpY29uOiBHcmFkdWF0aW9uQ2FwLCBkZXNjcmlwdGlvbjogXCJZb3VyIGFjYWRlbWljIGJhY2tncm91bmRcIiwgZXN0aW1hdGVkVGltZTogNSwgcmVxdWlyZWQ6IHRydWUgfSxcbiAgICB7IGlkOiAyLCB0aXRsZTogXCJFeHBlcmllbmNlXCIsIGljb246IEJyaWVmY2FzZSwgZGVzY3JpcHRpb246IFwiWW91ciB3b3JrIGV4cGVyaWVuY2VcIiwgZXN0aW1hdGVkVGltZTogOCwgcmVxdWlyZWQ6IHRydWUgfSxcbiAgICB7IGlkOiAzLCB0aXRsZTogXCJQcm9qZWN0c1wiLCBpY29uOiBGaWxlVGV4dCwgZGVzY3JpcHRpb246IFwiU2hvd2Nhc2UgeW91ciBwcm9qZWN0c1wiLCBlc3RpbWF0ZWRUaW1lOiA0LCByZXF1aXJlZDogZmFsc2UgfSxcbiAgICB7IGlkOiA0LCB0aXRsZTogXCJTa2lsbHNcIiwgaWNvbjogQXdhcmQsIGRlc2NyaXB0aW9uOiBcIllvdXIgdGVjaG5pY2FsICYgc29mdCBza2lsbHNcIiwgZXN0aW1hdGVkVGltZTogMywgcmVxdWlyZWQ6IGZhbHNlIH0sXG4gICAgeyBpZDogNSwgdGl0bGU6IFwiUmV2aWV3ICYgR2VuZXJhdGVcIiwgaWNvbjogRmlsZVRleHQsIGRlc2NyaXB0aW9uOiBcIkZpbmFsaXplIHlvdXIgcmVzdW1lXCIsIGVzdGltYXRlZFRpbWU6IDIsIHJlcXVpcmVkOiBmYWxzZSB9XG4gIF07XG5cbiAgY29uc3QgYXRzQW5hbHlzaXMgPSB1c2VBVFNBbmFseXNpcyhmb3JtRGF0YSk7XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICAvLyBDaGVjayBmb3IgYmFja3VwIGRhdGEgZnJvbSBhdXRoIGZsb3cgZmlyc3RcbiAgICBjb25zdCBiYWNrdXBEYXRhID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3Jlc3VtZUZvcm1EYXRhQmFja3VwJyk7XG4gICAgY29uc3QgYXV0aFRpbWVzdGFtcCA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdhdXRoRmxvd1RpbWVzdGFtcCcpO1xuICAgIGNvbnN0IHNhdmVkRGF0YSA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdyZXN1bWVGb3JtRGF0YScpO1xuXG4gICAgLy8gSWYgYmFja3VwIGRhdGEgZXhpc3RzIGFuZCBpcyByZWNlbnQgKHdpdGhpbiAxMCBtaW51dGVzKSwgdXNlIGl0XG4gICAgaWYgKGJhY2t1cERhdGEgJiYgYXV0aFRpbWVzdGFtcCkge1xuICAgICAgY29uc3QgdGltZURpZmYgPSBEYXRlLm5vdygpIC0gcGFyc2VJbnQoYXV0aFRpbWVzdGFtcCk7XG4gICAgICBpZiAodGltZURpZmYgPCAxMCAqIDYwICogMTAwMCkgeyAvLyAxMCBtaW51dGVzXG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgY29uc3QgcGFyc2VkID0gSlNPTi5wYXJzZShiYWNrdXBEYXRhKTtcbiAgICAgICAgICBzZXRGb3JtRGF0YShwYXJzZWQpO1xuICAgICAgICAgIHNldExhc3RTYXZlZChuZXcgRGF0ZSgpKTtcbiAgICAgICAgICB0b2FzdC5zdWNjZXNzKCdZb3VyIHdvcmsgaGFzIGJlZW4gcmVzdG9yZWQgYWZ0ZXIgc2lnbi1pbiEnKTtcbiAgICAgICAgICAvLyBDbGVhbiB1cCBiYWNrdXAgZGF0YVxuICAgICAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCdyZXN1bWVGb3JtRGF0YUJhY2t1cCcpO1xuICAgICAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCdhdXRoRmxvd1RpbWVzdGFtcCcpO1xuICAgICAgICAgIC8vIFVwZGF0ZSBtYWluIHN0b3JhZ2VcbiAgICAgICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSgncmVzdW1lRm9ybURhdGEnLCBiYWNrdXBEYXRhKTtcbiAgICAgICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSgncmVzdW1lTGFzdFNhdmVkJywgbmV3IERhdGUoKS50b0lTT1N0cmluZygpKTtcbiAgICAgICAgICByZXR1cm47XG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIGxvYWQgYmFja3VwIGRhdGE6JywgZXJyb3IpO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuXG4gICAgLy8gRmFsbGJhY2sgdG8gcmVndWxhciBzYXZlZCBkYXRhXG4gICAgaWYgKHNhdmVkRGF0YSkge1xuICAgICAgdHJ5IHtcbiAgICAgICAgY29uc3QgcGFyc2VkID0gSlNPTi5wYXJzZShzYXZlZERhdGEpO1xuICAgICAgICBzZXRGb3JtRGF0YShwYXJzZWQpO1xuICAgICAgICBzZXRMYXN0U2F2ZWQobmV3IERhdGUobG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3Jlc3VtZUxhc3RTYXZlZCcpIHx8IERhdGUubm93KCkpKTtcbiAgICAgICAgdG9hc3Quc3VjY2VzcygnUHJldmlvdXMgd29yayByZXN0b3JlZCEnKTtcbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBsb2FkIHNhdmVkIGRhdGE6JywgZXJyb3IpO1xuICAgICAgfVxuICAgIH1cblxuICAgIC8vIENsZWFuIHVwIG9sZCBiYWNrdXAgZGF0YVxuICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCdyZXN1bWVGb3JtRGF0YUJhY2t1cCcpO1xuICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCdhdXRoRmxvd1RpbWVzdGFtcCcpO1xuXG4gIH0sIFtdKTtcblxuXG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAoYXV0b1NhdmVFbmFibGVkICYmIGZvcm1EYXRhKSB7XG4gICAgICAvLyBNYWtlIGZvcm0gZGF0YSBnbG9iYWxseSBhY2Nlc3NpYmxlIGZvciBhdXRoIGZsb3dcbiAgICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICB3aW5kb3cuZm9ybURhdGEgPSBmb3JtRGF0YTtcbiAgICAgIH1cblxuICAgICAgY29uc3QgdGltZW91dElkID0gc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCdyZXN1bWVGb3JtRGF0YScsIEpTT04uc3RyaW5naWZ5KGZvcm1EYXRhKSk7XG4gICAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCdyZXN1bWVMYXN0U2F2ZWQnLCBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCkpO1xuICAgICAgICBzZXRMYXN0U2F2ZWQobmV3IERhdGUoKSk7XG4gICAgICB9LCAyMDAwKTtcbiAgICAgIHJldHVybiAoKSA9PiBjbGVhclRpbWVvdXQodGltZW91dElkKTtcbiAgICB9XG4gIH0sIFtmb3JtRGF0YSwgYXV0b1NhdmVFbmFibGVkXSk7XG5cbiAgY29uc3QgdmFsaWRhdGVTdGVwID0gdXNlQ2FsbGJhY2soKHN0ZXBJbmRleCkgPT4ge1xuICAgIGNvbnN0IGVycm9ycyA9IHt9O1xuICAgIHN3aXRjaCAoc3RlcEluZGV4KSB7XG4gICAgICBjYXNlIDA6XG4gICAgICAgIGlmICghZm9ybURhdGEucGVyc29uYWwuZmlyc3ROYW1lPy50cmltKCkpIGVycm9ycy5maXJzdE5hbWUgPSAnRmlyc3QgbmFtZSBpcyByZXF1aXJlZCc7XG4gICAgICAgIGlmICghZm9ybURhdGEucGVyc29uYWwubGFzdE5hbWU/LnRyaW0oKSkgZXJyb3JzLmxhc3ROYW1lID0gJ0xhc3QgbmFtZSBpcyByZXF1aXJlZCc7XG4gICAgICAgIGlmICghZm9ybURhdGEucGVyc29uYWwuZW1haWw/LnRyaW0oKSkgZXJyb3JzLmVtYWlsID0gJ0VtYWlsIGlzIHJlcXVpcmVkJztcbiAgICAgICAgZWxzZSBpZiAoIS9eW15cXHNAXStAW15cXHNAXStcXC5bXlxcc0BdKyQvLnRlc3QoZm9ybURhdGEucGVyc29uYWwuZW1haWwpKSBlcnJvcnMuZW1haWwgPSAnUGxlYXNlIGVudGVyIGEgdmFsaWQgZW1haWwgYWRkcmVzcyc7XG4gICAgICAgIGJyZWFrO1xuICAgICAgY2FzZSAxOlxuICAgICAgICBjb25zdCB2YWxpZEVkdWNhdGlvbiA9IGZvcm1EYXRhLmVkdWNhdGlvbj8uZmlsdGVyKGVkdSA9PiBlZHUuZGVncmVlPy50cmltKCkgJiYgZWR1Lmluc3RpdHV0aW9uPy50cmltKCkpO1xuICAgICAgICBpZiAoIXZhbGlkRWR1Y2F0aW9uIHx8IHZhbGlkRWR1Y2F0aW9uLmxlbmd0aCA9PT0gMCkgZXJyb3JzLmVkdWNhdGlvbiA9ICdBdCBsZWFzdCBvbmUgZWR1Y2F0aW9uIGVudHJ5IHdpdGggZGVncmVlIGFuZCBpbnN0aXR1dGlvbiBpcyByZXF1aXJlZCc7XG4gICAgICAgIGJyZWFrO1xuICAgICAgY2FzZSAyOlxuICAgICAgICBjb25zdCB2YWxpZEV4cGVyaWVuY2UgPSBmb3JtRGF0YS5leHBlcmllbmNlPy5maWx0ZXIoZXhwID0+IGV4cC50aXRsZT8udHJpbSgpICYmIGV4cC5jb21wYW55Py50cmltKCkpO1xuICAgICAgICBpZiAoIXZhbGlkRXhwZXJpZW5jZSB8fCB2YWxpZEV4cGVyaWVuY2UubGVuZ3RoID09PSAwKSBlcnJvcnMuZXhwZXJpZW5jZSA9ICdBdCBsZWFzdCBvbmUgd29yayBleHBlcmllbmNlIGVudHJ5IHdpdGggam9iIHRpdGxlIGFuZCBjb21wYW55IGlzIHJlcXVpcmVkJztcbiAgICAgICAgYnJlYWs7XG4gICAgICBjYXNlIDM6XG4gICAgICAgIC8vIFByb2plY3RzIGFyZSBvcHRpb25hbCAtIG5vIHZhbGlkYXRpb24gcmVxdWlyZWRcbiAgICAgICAgYnJlYWs7XG4gICAgICBjYXNlIDQ6XG4gICAgICAgIC8vIFNraWxscyBhcmUgb3B0aW9uYWwgLSBubyB2YWxpZGF0aW9uIHJlcXVpcmVkXG4gICAgICAgIGJyZWFrO1xuICAgICAgZGVmYXVsdDpcbiAgICAgICAgYnJlYWs7XG4gICAgfVxuICAgIHJldHVybiBlcnJvcnM7XG4gIH0sIFtmb3JtRGF0YV0pO1xuXG4gIGNvbnN0IGNhblByb2NlZWRUb05leHRTdGVwID0gdXNlQ2FsbGJhY2soKHN0ZXBJbmRleCkgPT4ge1xuICAgIGNvbnN0IGVycm9ycyA9IHZhbGlkYXRlU3RlcChzdGVwSW5kZXgpO1xuICAgIHJldHVybiBPYmplY3Qua2V5cyhlcnJvcnMpLmxlbmd0aCA9PT0gMDtcbiAgfSwgW3ZhbGlkYXRlU3RlcF0pO1xuXG4gIGNvbnN0IGdldENvbXBsZXRlZFN0ZXBzID0gdXNlQ2FsbGJhY2soKCkgPT4ge1xuICAgIGNvbnN0IGNvbXBsZXRlZCA9IFtdO1xuICAgIGZvciAobGV0IGkgPSAwOyBpIDwgY3VycmVudFN0ZXA7IGkrKykge1xuICAgICAgY29uc3QgZXJyb3JzID0gdmFsaWRhdGVTdGVwKGkpO1xuICAgICAgaWYgKE9iamVjdC5rZXlzKGVycm9ycykubGVuZ3RoID09PSAwKSB7XG4gICAgICAgIGNvbXBsZXRlZC5wdXNoKGkpO1xuICAgICAgfVxuICAgIH1cbiAgICByZXR1cm4gY29tcGxldGVkO1xuICB9LCBbY3VycmVudFN0ZXAsIHZhbGlkYXRlU3RlcF0pO1xuXG4gIGNvbnN0IGdldFN0ZXBDb21wbGV0aW9uU3RhdHVzID0gdXNlQ2FsbGJhY2soKCkgPT4ge1xuICAgIGNvbnN0IHN0YXR1cyA9IHt9O1xuICAgIHN0ZXBzLmZvckVhY2goKHN0ZXAsIGluZGV4KSA9PiB7XG4gICAgICBjb25zdCBlcnJvcnMgPSB2YWxpZGF0ZVN0ZXAoaW5kZXgpO1xuICAgICAgc3RhdHVzW2luZGV4XSA9IE9iamVjdC5rZXlzKGVycm9ycykubGVuZ3RoID09PSAwO1xuICAgIH0pO1xuICAgIHJldHVybiBzdGF0dXM7XG4gIH0sIFtzdGVwcywgdmFsaWRhdGVTdGVwXSk7XG5cbiAgY29uc3QgY2FuR2VuZXJhdGVSZXN1bWUgPSB1c2VDYWxsYmFjaygoKSA9PiB7XG4gICAgZm9yIChsZXQgaSA9IDA7IGkgPCBzdGVwcy5sZW5ndGggLSAxOyBpKyspIHtcbiAgICAgIGlmICghY2FuUHJvY2VlZFRvTmV4dFN0ZXAoaSkpIHJldHVybiBmYWxzZTtcbiAgICB9XG4gICAgcmV0dXJuIHRydWU7XG4gIH0sIFtjYW5Qcm9jZWVkVG9OZXh0U3RlcCwgc3RlcHMubGVuZ3RoXSk7XG5cbiAgY29uc3QgdXBkYXRlRm9ybURhdGEgPSB1c2VDYWxsYmFjaygoc2VjdGlvbiwgZmllbGQsIHZhbHVlLCBpbmRleCA9IG51bGwpID0+IHtcbiAgICBzZXRGb3JtRGF0YShwcmV2ID0+IHtcbiAgICAgIGxldCBuZXdEYXRhID0geyAuLi5wcmV2IH07XG4gICAgICBpZiAoaW5kZXggIT09IG51bGwgJiYgQXJyYXkuaXNBcnJheShwcmV2W3NlY3Rpb25dKSkge1xuICAgICAgICBjb25zdCBuZXdBcnJheSA9IFsuLi5wcmV2W3NlY3Rpb25dXTtcbiAgICAgICAgbmV3QXJyYXlbaW5kZXhdID0geyAuLi5uZXdBcnJheVtpbmRleF0sIFtmaWVsZF06IHZhbHVlIH07XG4gICAgICAgIG5ld0RhdGFbc2VjdGlvbl0gPSBuZXdBcnJheTtcbiAgICAgIH0gZWxzZSBpZiAodHlwZW9mIHByZXZbc2VjdGlvbl0gPT09ICdvYmplY3QnICYmICFBcnJheS5pc0FycmF5KHByZXZbc2VjdGlvbl0pKSB7XG4gICAgICAgIG5ld0RhdGFbc2VjdGlvbl0gPSB7IC4uLnByZXZbc2VjdGlvbl0sIFtmaWVsZF06IHZhbHVlIH07XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBuZXdEYXRhW2ZpZWxkXSA9IHZhbHVlO1xuICAgICAgfVxuICAgICAgcmV0dXJuIG5ld0RhdGE7XG4gICAgfSk7XG4gICAgaWYgKHZhbGlkYXRpb25FcnJvcnNbZmllbGRdKSB7XG4gICAgICBzZXRWYWxpZGF0aW9uRXJyb3JzKHByZXYgPT4ge1xuICAgICAgICBjb25zdCBuZXdFcnJvcnMgPSB7IC4uLnByZXYgfTtcbiAgICAgICAgZGVsZXRlIG5ld0Vycm9yc1tmaWVsZF07XG4gICAgICAgIHJldHVybiBuZXdFcnJvcnM7XG4gICAgICB9KTtcbiAgICB9XG4gIH0sIFt2YWxpZGF0aW9uRXJyb3JzXSk7XG5cbiAgY29uc3QgYWRkQXJyYXlJdGVtID0gdXNlQ2FsbGJhY2soKHNlY3Rpb24sIHRlbXBsYXRlKSA9PiB7XG4gICAgc2V0Rm9ybURhdGEocHJldiA9PiAoe1xuICAgICAgLi4ucHJldixcbiAgICAgIFtzZWN0aW9uXTogWy4uLnByZXZbc2VjdGlvbl0sIHsgLi4udGVtcGxhdGUsIGlkOiBNYXRoLnJhbmRvbSgpLnRvU3RyaW5nKDM2KS5zdWJzdHJpbmcoMiwgMTEpIH1dXG4gICAgfSkpO1xuICB9LCBbXSk7XG5cbiAgY29uc3QgcmVtb3ZlQXJyYXlJdGVtID0gdXNlQ2FsbGJhY2soKHNlY3Rpb24sIGlkKSA9PiB7XG4gICAgc2V0Rm9ybURhdGEocHJldiA9PiAoe1xuICAgICAgLi4ucHJldixcbiAgICAgIFtzZWN0aW9uXTogcHJldltzZWN0aW9uXS5maWx0ZXIoaXRlbSA9PiBpdGVtLmlkICE9PSBpZClcbiAgICB9KSk7XG4gIH0sIFtdKTtcblxuICBjb25zdCBuZXh0U3RlcCA9IHVzZUNhbGxiYWNrKCgpID0+IHtcbiAgICBjb25zdCBlcnJvcnMgPSB2YWxpZGF0ZVN0ZXAoY3VycmVudFN0ZXApO1xuICAgIGlmIChPYmplY3Qua2V5cyhlcnJvcnMpLmxlbmd0aCA+IDApIHtcbiAgICAgIHNldFZhbGlkYXRpb25FcnJvcnMoZXJyb3JzKTtcbiAgICAgIHNldFNob3dWYWxpZGF0aW9uRXJyb3JzKHRydWUpO1xuICAgICAgY29uc3QgZXJyb3JNZXNzYWdlcyA9IE9iamVjdC52YWx1ZXMoZXJyb3JzKTtcbiAgICAgIHRvYXN0LmVycm9yKGVycm9yTWVzc2FnZXNbMF0pO1xuICAgICAgcmV0dXJuO1xuICAgIH1cbiAgICBzZXRDb21wbGV0ZWRTdGVwcyhwcmV2ID0+IFsuLi5uZXcgU2V0KFsuLi5wcmV2LCBjdXJyZW50U3RlcF0pXSk7XG4gICAgc2V0VmFsaWRhdGlvbkVycm9ycyh7fSk7XG4gICAgc2V0U2hvd1ZhbGlkYXRpb25FcnJvcnMoZmFsc2UpO1xuICAgIGlmIChjdXJyZW50U3RlcCA8IHN0ZXBzLmxlbmd0aCAtIDEpIHtcbiAgICAgIHNldEN1cnJlbnRTdGVwKGN1cnJlbnRTdGVwICsgMSk7XG4gICAgICB0b2FzdC5zdWNjZXNzKGAke3N0ZXBzW2N1cnJlbnRTdGVwXS50aXRsZX0gY29tcGxldGVkIWApO1xuICAgIH1cbiAgfSwgW2N1cnJlbnRTdGVwLCB2YWxpZGF0ZVN0ZXAsIHN0ZXBzXSk7XG5cbiAgY29uc3QgcHJldlN0ZXAgPSB1c2VDYWxsYmFjaygoKSA9PiB7XG4gICAgc2V0VmFsaWRhdGlvbkVycm9ycyh7fSk7XG4gICAgc2V0U2hvd1ZhbGlkYXRpb25FcnJvcnMoZmFsc2UpO1xuICAgIGlmIChjdXJyZW50U3RlcCA+IDApIHNldEN1cnJlbnRTdGVwKGN1cnJlbnRTdGVwIC0gMSk7XG4gIH0sIFtjdXJyZW50U3RlcF0pO1xuXG4gIGNvbnN0IGhhbmRsZVN0ZXBDbGljayA9IHVzZUNhbGxiYWNrKChzdGVwSW5kZXgpID0+IHtcbiAgICBpZiAoc3RlcEluZGV4IDw9IGN1cnJlbnRTdGVwIHx8IGNvbXBsZXRlZFN0ZXBzLmluY2x1ZGVzKHN0ZXBJbmRleCkpIHtcbiAgICAgIHNldEN1cnJlbnRTdGVwKHN0ZXBJbmRleCk7XG4gICAgICBzZXRWYWxpZGF0aW9uRXJyb3JzKHt9KTtcbiAgICAgIHNldFNob3dWYWxpZGF0aW9uRXJyb3JzKGZhbHNlKTtcbiAgICB9XG4gIH0sIFtjdXJyZW50U3RlcCwgY29tcGxldGVkU3RlcHNdKTtcblxuICBjb25zdCBnZW5lcmF0ZVJlc3VtZSA9IHVzZUNhbGxiYWNrKGFzeW5jICgpID0+IHtcbiAgICBzZXRWYWxpZGF0aW9uRXJyb3JzKHt9KTtcbiAgICBzZXRTaG93VmFsaWRhdGlvbkVycm9ycyhmYWxzZSk7XG4gICAgaWYgKCFjYW5HZW5lcmF0ZVJlc3VtZSgpKSB7XG4gICAgICBjb25zdCBhbGxFcnJvcnMgPSB7fTtcbiAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgc3RlcHMubGVuZ3RoIC0gMTsgaSsrKSB7XG4gICAgICAgIGNvbnN0IHN0ZXBFcnJvcnMgPSB2YWxpZGF0ZVN0ZXAoaSk7XG4gICAgICAgIE9iamVjdC5hc3NpZ24oYWxsRXJyb3JzLCBzdGVwRXJyb3JzKTtcbiAgICAgIH1cbiAgICAgIHNldFZhbGlkYXRpb25FcnJvcnMoYWxsRXJyb3JzKTtcbiAgICAgIHNldFNob3dWYWxpZGF0aW9uRXJyb3JzKHRydWUpO1xuICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBzdGVwcy5sZW5ndGggLSAxOyBpKyspIHtcbiAgICAgICAgY29uc3Qgc3RlcEVycm9ycyA9IHZhbGlkYXRlU3RlcChpKTtcbiAgICAgICAgaWYgKE9iamVjdC5rZXlzKHN0ZXBFcnJvcnMpLmxlbmd0aCA+IDApIHtcbiAgICAgICAgICBzZXRDdXJyZW50U3RlcChpKTtcbiAgICAgICAgICB0b2FzdC5lcnJvcihgUGxlYXNlIGNvbXBsZXRlIGFsbCByZXF1aXJlZCBmaWVsZHMgaW4gJHtzdGVwc1tpXS50aXRsZX1gKTtcbiAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICAgIHJldHVybjtcbiAgICB9XG4gICAgdHJ5IHtcbiAgICAgIHNldElzR2VuZXJhdGluZyh0cnVlKTtcbiAgICAgIHNldFNob3dQcm9ncmVzc0Jhcih0cnVlKTtcbiAgICAgIHNldFJlc3VtZUdlbmVyYXRlZChmYWxzZSk7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL2dlbmVyYXRlLXJlc3VtZScsIHtcbiAgICAgICAgbWV0aG9kOiAnUE9TVCcsXG4gICAgICAgIGhlYWRlcnM6IHsgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyB9LFxuICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7IGZvcm1EYXRhLCB0ZW1wbGF0ZUlkOiBzZWxlY3RlZFRlbXBsYXRlIH0pLFxuICAgICAgfSk7XG4gICAgICBjb25zdCBjb250ZW50VHlwZSA9IHJlc3BvbnNlLmhlYWRlcnMuZ2V0KCdjb250ZW50LXR5cGUnKTtcbiAgICAgIGlmICghY29udGVudFR5cGUgfHwgIWNvbnRlbnRUeXBlLmluY2x1ZGVzKCdhcHBsaWNhdGlvbi9qc29uJykpIHRocm93IG5ldyBFcnJvcignU2VydmVyIHJldHVybmVkIG5vbi1KU09OIHJlc3BvbnNlJyk7XG4gICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgaWYgKCFyZXNwb25zZS5vaykgdGhyb3cgbmV3IEVycm9yKGRhdGEuZXJyb3IgfHwgJ0ZhaWxlZCB0byBnZW5lcmF0ZSByZXN1bWUnKTtcbiAgICAgIGlmICghZGF0YS5yZXN1bWVEYXRhIHx8ICFkYXRhLmRvd25sb2FkVXJsKSB0aHJvdyBuZXcgRXJyb3IoJ0dlbmVyYXRlZCByZXN1bWUgZGF0YSBpcyBpbmNvbXBsZXRlJyk7XG4gICAgICBzZXRSZXN1bWVVcmwoZGF0YS5kb3dubG9hZFVybCk7XG4gICAgICBzZXRSZXN1bWVEYXRhKGRhdGEucmVzdW1lRGF0YSk7XG4gICAgICBzZXRBdHNTY29yZShkYXRhLmF0c1Njb3JlIHx8IDc1KTtcbiAgICAgIHNldFN1Z2dlc3Rpb25zKGRhdGEuc3VnZ2VzdGlvbnMgfHwgW10pO1xuICAgICAgc2V0UmVzdW1lR2VuZXJhdGVkKHRydWUpO1xuICAgICAgdG9hc3Quc3VjY2VzcygnUmVzdW1lIGdlbmVyYXRlZCBzdWNjZXNzZnVsbHkhJyk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ1Jlc3VtZSBnZW5lcmF0aW9uIGVycm9yOicsIGVycm9yKTtcbiAgICAgIHRvYXN0LmVycm9yKGVycm9yLm1lc3NhZ2UgfHwgJ0ZhaWxlZCB0byBnZW5lcmF0ZSByZXN1bWUnKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0SXNHZW5lcmF0aW5nKGZhbHNlKTtcbiAgICAgIHNldFNob3dQcm9ncmVzc0JhcihmYWxzZSk7XG4gICAgfVxuICB9LCBbY2FuR2VuZXJhdGVSZXN1bWUsIGZvcm1EYXRhLCBzdGVwcywgdmFsaWRhdGVTdGVwLCBzZWxlY3RlZFRlbXBsYXRlXSk7XG5cbiAgY29uc3QgaGFuZGxlU2F2ZSA9IHVzZUNhbGxiYWNrKCgpID0+IHtcbiAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSgncmVzdW1lRm9ybURhdGEnLCBKU09OLnN0cmluZ2lmeShmb3JtRGF0YSkpO1xuICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCdyZXN1bWVMYXN0U2F2ZWQnLCBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCkpO1xuICAgIHNldExhc3RTYXZlZChuZXcgRGF0ZSgpKTtcbiAgICB0b2FzdC5zdWNjZXNzKCdQcm9ncmVzcyBzYXZlZCEnKTtcbiAgfSwgW2Zvcm1EYXRhXSk7XG5cbiAgY29uc3QgaGFuZGxlQUlTdWdnZXN0ID0gdXNlQ2FsbGJhY2soYXN5bmMgKHNlY3Rpb24pID0+IHtcbiAgICBpZiAoIWFpU3VnZ2VzdGlvbnNFbmFibGVkKSByZXR1cm47XG4gICAgdG9hc3QubG9hZGluZygnR2V0dGluZyBBSSBzdWdnZXN0aW9ucy4uLicpO1xuICAgIHRyeSB7XG4gICAgICBhd2FpdCBuZXcgUHJvbWlzZShyZXNvbHZlID0+IHNldFRpbWVvdXQocmVzb2x2ZSwgMTUwMCkpO1xuICAgICAgdG9hc3QuZGlzbWlzcygpO1xuICAgICAgdG9hc3Quc3VjY2VzcygnQUkgc3VnZ2VzdGlvbnMgYXBwbGllZCEnKTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgdG9hc3QuZGlzbWlzcygpO1xuICAgICAgdG9hc3QuZXJyb3IoJ0ZhaWxlZCB0byBnZXQgQUkgc3VnZ2VzdGlvbnMnKTtcbiAgICB9XG4gIH0sIFthaVN1Z2dlc3Rpb25zRW5hYmxlZF0pO1xuXG4gIC8vIFNldCB1cCBldmVudCBsaXN0ZW5lciBmb3IgcmVzdW1lIGdlbmVyYXRpb24gZnJvbSBSZXZpZXdGb3JtXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgaGFuZGxlR2VuZXJhdGVSZXN1bWUgPSAoKSA9PiB7XG4gICAgICAvLyBDaGVjayBpZiB3ZSBjYW4gZ2VuZXJhdGUgcmVzdW1lIGFuZCBjYWxsIHRoZSBmdW5jdGlvblxuICAgICAgaWYgKGNhbkdlbmVyYXRlUmVzdW1lKCkpIHtcbiAgICAgICAgZ2VuZXJhdGVSZXN1bWUoKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHRvYXN0LmVycm9yKCdQbGVhc2UgY29tcGxldGUgYWxsIHJlcXVpcmVkIHNlY3Rpb25zIGJlZm9yZSBnZW5lcmF0aW5nIHlvdXIgcmVzdW1lLicpO1xuICAgICAgfVxuICAgIH07XG5cbiAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKCdnZW5lcmF0ZVJlc3VtZScsIGhhbmRsZUdlbmVyYXRlUmVzdW1lKTtcbiAgICAgIHJldHVybiAoKSA9PiB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcignZ2VuZXJhdGVSZXN1bWUnLCBoYW5kbGVHZW5lcmF0ZVJlc3VtZSk7XG4gICAgfVxuICB9LCBbY2FuR2VuZXJhdGVSZXN1bWUsIGdlbmVyYXRlUmVzdW1lXSk7XG5cbiAgY29uc3QgZ2V0U3RlcFZhbGlkYXRpb24gPSB1c2VDYWxsYmFjaygoKSA9PiB7XG4gICAgY29uc3QgdmFsaWRhdGlvbiA9IHt9O1xuICAgIHN0ZXBzLmZvckVhY2goKHN0ZXAsIGluZGV4KSA9PiB7XG4gICAgICBjb25zdCBlcnJvcnMgPSB2YWxpZGF0ZVN0ZXAoaW5kZXgpO1xuICAgICAgdmFsaWRhdGlvbltpbmRleF0gPSBPYmplY3Qua2V5cyhlcnJvcnMpLmxlbmd0aCA9PT0gMDtcbiAgICB9KTtcbiAgICByZXR1cm4gdmFsaWRhdGlvbjtcbiAgfSwgW3N0ZXBzLCB2YWxpZGF0ZVN0ZXBdKTtcblxuICBjb25zdCByZW5kZXJTdGVwQ29udGVudCA9ICgpID0+IHtcbiAgICBjb25zdCBjb21tb25Qcm9wcyA9IHtcbiAgICAgIGZvcm1EYXRhLFxuICAgICAgdXBkYXRlRm9ybURhdGEsXG4gICAgICBhZGRBcnJheUl0ZW0sXG4gICAgICByZW1vdmVBcnJheUl0ZW0sXG4gICAgICBhdHNBbmFseXNpcyxcbiAgICAgIHZhbGlkYXRpb25FcnJvcnMsXG4gICAgICBzaG93VmFsaWRhdGlvbkVycm9ycyxcbiAgICAgIG9uU2F2ZTogaGFuZGxlU2F2ZSxcbiAgICAgIG9uQUlTdWdnZXN0OiBoYW5kbGVBSVN1Z2dlc3RcbiAgICB9O1xuICAgIHN3aXRjaCAoY3VycmVudFN0ZXApIHtcbiAgICAgIGNhc2UgMDpcbiAgICAgICAgcmV0dXJuIDxQZXJzb25hbEluZm9Gb3JtIHsuLi5jb21tb25Qcm9wc30gLz47XG4gICAgICBjYXNlIDE6XG4gICAgICAgIHJldHVybiA8RWR1Y2F0aW9uRm9ybSB7Li4uY29tbW9uUHJvcHN9IC8+O1xuICAgICAgY2FzZSAyOlxuICAgICAgICByZXR1cm4gPEVuaGFuY2VkRXhwZXJpZW5jZUZvcm0gey4uLmNvbW1vblByb3BzfSAvPjtcbiAgICAgIGNhc2UgMzpcbiAgICAgICAgcmV0dXJuIDxFbmhhbmNlZFByb2plY3RzRm9ybSB7Li4uY29tbW9uUHJvcHN9IC8+O1xuICAgICAgY2FzZSA0OlxuICAgICAgICByZXR1cm4gPEVuaGFuY2VkU2tpbGxzRm9ybSB7Li4uY29tbW9uUHJvcHN9IC8+O1xuICAgICAgY2FzZSA1OlxuICAgICAgICByZXR1cm4gPEVuaGFuY2VkUmV2aWV3Rm9ybSB7Li4uY29tbW9uUHJvcHN9IHNlbGVjdGVkVGVtcGxhdGU9e3NlbGVjdGVkVGVtcGxhdGV9IG9uVGVtcGxhdGVTZWxlY3Q9e3NldFNlbGVjdGVkVGVtcGxhdGV9IC8+O1xuICAgICAgZGVmYXVsdDpcbiAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgfVxuICB9O1xuXG4gIGlmIChyZXN1bWVHZW5lcmF0ZWQgJiYgcmVzdW1lRGF0YSAmJiByZXN1bWVVcmwpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPFN1Y2Nlc3NTY3JlZW4gXG4gICAgICAgIGZvcm1EYXRhPXtmb3JtRGF0YX1cbiAgICAgICAgcmVzdW1lRGF0YT17cmVzdW1lRGF0YX1cbiAgICAgICAgb25TdGFydE92ZXI9eygpID0+IHtcbiAgICAgICAgICBzZXRSZXN1bWVHZW5lcmF0ZWQoZmFsc2UpO1xuICAgICAgICAgIHNldFJlc3VtZURhdGEobnVsbCk7XG4gICAgICAgICAgc2V0UmVzdW1lVXJsKFwiXCIpO1xuICAgICAgICAgIHNldEN1cnJlbnRTdGVwKDApO1xuICAgICAgICB9fVxuICAgICAgICBvbkVkaXRSZXN1bWU9eygpID0+IHtcbiAgICAgICAgICBzZXRSZXN1bWVHZW5lcmF0ZWQoZmFsc2UpO1xuICAgICAgICAgIHNldEN1cnJlbnRTdGVwKDQpOyAvLyBHbyBiYWNrIHRvIHJldmlldyBzdGVwXG4gICAgICAgIH19XG4gICAgICAvPlxuICAgICk7XG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLWdyYWRpZW50LXRvLWIgZnJvbS1ibGFjayB0by1bIzBBMEEwQV0gdGV4dC13aGl0ZSByZWxhdGl2ZVwiPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIG9wYWNpdHktMjBcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIGJnLVt1cmwoJy9ncmlkLnN2ZycpXSBbbWFzay1pbWFnZTpyYWRpYWwtZ3JhZGllbnQoZWxsaXBzZV9hdF9jZW50ZXIsd2hpdGUsdHJhbnNwYXJlbnQpXVwiIC8+XG4gICAgICA8L2Rpdj5cbiAgICAgIDxQcm9ncmVzc0JhciBpc1Zpc2libGU9e3Nob3dQcm9ncmVzc0Jhcn0gb25Db21wbGV0ZT17KCkgPT4gc2V0U2hvd1Byb2dyZXNzQmFyKGZhbHNlKX0gLz5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgei0xMCBwYi0zMlwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbnRhaW5lciBteC1hdXRvIHB4LTQgcHktOFwiPlxuICAgICAgICAgIHshaGlkZUhlYWRlciAmJiAoXG4gICAgICAgICAgICA8bW90aW9uLmRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBtYi04XCIgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB5OiAtMjAgfX0gYW5pbWF0ZT17eyBvcGFjaXR5OiAxLCB5OiAwIH19PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGdhcC0zIG1iLTZcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlXCI+XG4gICAgICAgICAgICAgICAgICA8U3BhcmtsZXMgY2xhc3NOYW1lPVwiaC0xMCB3LTEwIHRleHQtbmV1cmFsLXBpbmsgYW5pbWF0ZS1wdWxzZVwiIC8+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgYmctbmV1cmFsLXBpbmsgb3BhY2l0eS0yMCByb3VuZGVkLWZ1bGwgYmx1ci1tZFwiPjwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTR4bCBsZzp0ZXh0LTV4bCBmb250LWJvbGQgdGV4dC13aGl0ZVwiPkVuaGFuY2VkIFJlc3VtZSBCdWlsZGVyPC9oMT5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS0zMDAgdGV4dC1sZyBtYXgtdy0yeGwgbXgtYXV0b1wiPkNyZWF0ZSBwcm9mZXNzaW9uYWwsIEFUUy1mcmllbmRseSByZXN1bWVzIHdpdGggb3VyIGVuaGFuY2VkIEFJLXBvd2VyZWQgYnVpbGRlcjwvcD5cbiAgICAgICAgICAgIDwvbW90aW9uLmRpdj5cbiAgICAgICAgICApfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCB4bDpmbGV4LXJvdyBnYXAtOCBtYXgtdy03eGwgbXgtYXV0b1wiPlxuICAgICAgICAgICAgey8qIE1haW4gQ29udGVudCBBcmVhICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgbWF4LXctNHhsXCI+XG4gICAgICAgICAgICAgIHsvKiBTdGlja3kgUHJvZ3Jlc3MgSW5kaWNhdG9yICovfVxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInN0aWNreSB0b3AtNCB6LTMwIGJnLWdyYXktOTAwLzk1IGJhY2tkcm9wLWJsdXItc20gcm91bmRlZC0yeGwgYm9yZGVyIGJvcmRlci1ncmF5LTcwMC81MCBtYi02XCI+XG4gICAgICAgICAgICAgICAgPEVuaGFuY2VkUHJvZ3Jlc3NJbmRpY2F0b3JcbiAgICAgICAgICAgICAgICAgIHN0ZXBzPXtzdGVwc31cbiAgICAgICAgICAgICAgICAgIGN1cnJlbnRTdGVwPXtjdXJyZW50U3RlcH1cbiAgICAgICAgICAgICAgICAgIGNvbXBsZXRlZFN0ZXBzPXtjb21wbGV0ZWRTdGVwc31cbiAgICAgICAgICAgICAgICAgIG9uU3RlcENsaWNrPXtoYW5kbGVTdGVwQ2xpY2t9XG4gICAgICAgICAgICAgICAgICB2YXJpYW50PVwibWluaW1hbFwiXG4gICAgICAgICAgICAgICAgICBzaG93TGFiZWxzPXt0cnVlfVxuICAgICAgICAgICAgICAgICAgc2hvd1Byb2dyZXNzPXt0cnVlfVxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIHsvKiBGb3JtIENvbnRlbnQgKi99XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy1mdWxsXCI+XG4gICAgICAgICAgICAgICAgPEFuaW1hdGVQcmVzZW5jZSBtb2RlPVwid2FpdFwiPlxuICAgICAgICAgICAgICAgICAgPG1vdGlvbi5kaXYga2V5PXtjdXJyZW50U3RlcH0gaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB4OiAyMCB9fSBhbmltYXRlPXt7IG9wYWNpdHk6IDEsIHg6IDAgfX0gZXhpdD17eyBvcGFjaXR5OiAwLCB4OiAtMjAgfX0gdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMC4zIH19PlxuICAgICAgICAgICAgICAgICAgICB7cmVuZGVyU3RlcENvbnRlbnQoKX1cbiAgICAgICAgICAgICAgICAgIDwvbW90aW9uLmRpdj5cbiAgICAgICAgICAgICAgICA8L0FuaW1hdGVQcmVzZW5jZT5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIFByZXZpZXcgU2lkZWJhciAtIERlc2t0b3AgT25seSAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaGlkZGVuIHhsOmJsb2NrIHhsOnctODAgeGw6ZmxleC1zaHJpbmstMFwiPlxuICAgICAgICAgICAgICB7LyogU3RpY2t5IFByZXZpZXcgQ29udGFpbmVyICovfVxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInN0aWNreSB0b3AtNCBoLVtjYWxjKDEwMHZoLTJyZW0pXVwiPlxuICAgICAgICAgICAgICAgIDxTaW1wbGVSZXN1bWVQcmV2aWV3XG4gICAgICAgICAgICAgICAgICBmb3JtRGF0YT17Zm9ybURhdGF9XG4gICAgICAgICAgICAgICAgICBzZWxlY3RlZFRlbXBsYXRlPXtzZWxlY3RlZFRlbXBsYXRlfVxuICAgICAgICAgICAgICAgICAgc2hvd1ByZXZpZXc9e3Nob3dQcmV2aWV3fVxuICAgICAgICAgICAgICAgICAgb25Ub2dnbGVQcmV2aWV3PXsoKSA9PiBzZXRTaG93UHJldmlldyghc2hvd1ByZXZpZXcpfVxuICAgICAgICAgICAgICAgIC8+XG5cbiAgICAgICAgICAgICAgICB7LyogQVRTIFNjb3JlIFNlY3Rpb24gKi99XG4gICAgICAgICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWdyYXktOTAwLzYwIGJhY2tkcm9wLWJsdXItbWQgcm91bmRlZC0yeGwgcC02IGJvcmRlciBib3JkZXItd2hpdGUvMTAgbXQtNlwiXG4gICAgICAgICAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHg6IDIwIH19XG4gICAgICAgICAgICAgICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEsIHg6IDAgfX1cbiAgICAgICAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZGVsYXk6IDAuMyB9fVxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTIgbWItNFwiPlxuICAgICAgICAgICAgICAgICAgICA8VGFyZ2V0IGNsYXNzTmFtZT1cImgtNSB3LTUgdGV4dC1uZXVyYWwtcHVycGxlXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZFwiPkFUUyBTY29yZTwvaDM+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgbWItNFwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YHRleHQtM3hsIGZvbnQtYm9sZCAke1xuICAgICAgICAgICAgICAgICAgICAgIChhdHNBbmFseXNpcy5vdmVyYWxsU2NvcmUgfHwgMCkgPj0gODAgPyAndGV4dC1ncmVlbi00MDAnIDpcbiAgICAgICAgICAgICAgICAgICAgICAoYXRzQW5hbHlzaXMub3ZlcmFsbFNjb3JlIHx8IDApID49IDYwID8gJ3RleHQteWVsbG93LTQwMCcgOiAndGV4dC1yZWQtNDAwJ1xuICAgICAgICAgICAgICAgICAgICB9YH0+XG4gICAgICAgICAgICAgICAgICAgICAge2F0c0FuYWx5c2lzLm92ZXJhbGxTY29yZSB8fCAwfSVcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNDAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgeyhhdHNBbmFseXNpcy5vdmVyYWxsU2NvcmUgfHwgMCkgPj0gODAgPyAnRXhjZWxsZW50JyA6XG4gICAgICAgICAgICAgICAgICAgICAgIChhdHNBbmFseXNpcy5vdmVyYWxsU2NvcmUgfHwgMCkgPj0gNjAgPyAnR29vZCcgOiAnTmVlZHMgV29yayd9XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICB7YXRzQW5hbHlzaXMucmVjb21tZW5kYXRpb25zPy5sZW5ndGggPiAwICYmIChcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMCBtYi0yXCI+VG9wIFN1Z2dlc3Rpb25zOjwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIHthdHNBbmFseXNpcy5yZWNvbW1lbmRhdGlvbnMuc2xpY2UoMCwgMikubWFwKChyZWMsIGluZGV4KSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGtleT17aW5kZXh9IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtc3RhcnQgZ2FwLTIgdGV4dC1zbVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMS41IGgtMS41IGJnLXllbGxvdy00MDAgcm91bmRlZC1mdWxsIG10LTIgZmxleC1zaHJpbmstMFwiPjwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktMzAwXCI+e3JlYy5tZXNzYWdlfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7LyogTW9iaWxlL1RhYmxldCBQcmV2aWV3IChOb24tc3RpY2t5KSAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwieGw6aGlkZGVuIG10LThcIj5cbiAgICAgICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1ncmF5LTkwMC82MCBiYWNrZHJvcC1ibHVyLW1kIHJvdW5kZWQtMnhsIHAtNiBib3JkZXIgYm9yZGVyLXdoaXRlLzEwXCJcbiAgICAgICAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHk6IDIwIH19XG4gICAgICAgICAgICAgICAgYW5pbWF0ZT17eyBvcGFjaXR5OiAxLCB5OiAwIH19XG4gICAgICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkZWxheTogMC4yIH19XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBtYi00XCI+XG4gICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIGZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgICAgICAgICAgIDxFeWUgY2xhc3NOYW1lPVwiaC01IHctNSB0ZXh0LW5ldXJhbC1ibHVlXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgUmVzdW1lIFByZXZpZXdcbiAgICAgICAgICAgICAgICAgIDwvaDM+XG4gICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dQcmV2aWV3KCFzaG93UHJldmlldyl9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHB4LTMgcHktMS41IHJvdW5kZWQtbGcgdGV4dC1zbSBmb250LW1lZGl1bSB0cmFuc2l0aW9uLWNvbG9ycyAke1xuICAgICAgICAgICAgICAgICAgICAgIHNob3dQcmV2aWV3ID8gJ2JnLW5ldXJhbC1wdXJwbGUgdGV4dC13aGl0ZScgOiAnYmctZ3JheS04MDAgdGV4dC1ncmF5LTMwMCBob3ZlcjpiZy1ncmF5LTcwMCdcbiAgICAgICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIHtzaG93UHJldmlldyA/ICdIaWRlJyA6ICdTaG93J31cbiAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIHtzaG93UHJldmlldyA/IChcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LWgtWzYwMHB4XSBvdmVyZmxvdy15LWF1dG8gYm9yZGVyIGJvcmRlci13aGl0ZS8xMCByb3VuZGVkLWxnXCI+XG4gICAgICAgICAgICAgICAgICAgIDxFbmhhbmNlZFJlc3VtZVByZXZpZXcgZm9ybURhdGE9e2Zvcm1EYXRhfSBzZWxlY3RlZFRlbXBsYXRlPXtzZWxlY3RlZFRlbXBsYXRlfSAvPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgcHktMTIgdGV4dC1ncmF5LTQwMFwiPlxuICAgICAgICAgICAgICAgICAgICA8RXllIGNsYXNzTmFtZT1cImgtMTIgdy0xMiBteC1hdXRvIG1iLTMgb3BhY2l0eS0zMFwiIC8+XG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc21cIj5DbGljayBcIlNob3dcIiB0byBwcmV2aWV3IHlvdXIgcmVzdW1lPC9wPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgICA8U21hcnROYXZpZ2F0aW9uQmFyIGN1cnJlbnRTdGVwPXtjdXJyZW50U3RlcH0gdG90YWxTdGVwcz17c3RlcHMubGVuZ3RofSBvblByZXZpb3VzPXtwcmV2U3RlcH0gb25OZXh0PXtuZXh0U3RlcH0gb25HZW5lcmF0ZT17Z2VuZXJhdGVSZXN1bWV9IG9uU2F2ZT17aGFuZGxlU2F2ZX0gb25QcmV2aWV3PXsoKSA9PiBzZXRTaG93UHJldmlldyghc2hvd1ByZXZpZXcpfSBpc0dlbmVyYXRpbmc9e2lzR2VuZXJhdGluZ30gY2FuUHJvY2VlZD17Y3VycmVudFN0ZXAgPT09IHN0ZXBzLmxlbmd0aCAtIDEgPyBjYW5HZW5lcmF0ZVJlc3VtZSgpIDogY2FuUHJvY2VlZFRvTmV4dFN0ZXAoY3VycmVudFN0ZXApfSBzaG93UHJldmlldz17c2hvd1ByZXZpZXd9IHN0ZXBzPXtzdGVwc30gZm9ybURhdGE9e2Zvcm1EYXRhfSBhdHNTY29yZT17YXRzQW5hbHlzaXMub3ZlcmFsbFNjb3JlfSBhdXRvU2F2ZUVuYWJsZWQ9e2F1dG9TYXZlRW5hYmxlZH0gLz5cbiAgICA8L2Rpdj5cbiAgKTtcbn07XG5cbmV4cG9ydCBkZWZhdWx0IEVuaGFuY2VkUmVzdW1lQnVpbGRlcjtcbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwidXNlRWZmZWN0IiwidXNlQ2FsbGJhY2siLCJtb3Rpb24iLCJBbmltYXRlUHJlc2VuY2UiLCJVc2VyIiwiR3JhZHVhdGlvbkNhcCIsIkJyaWVmY2FzZSIsIkF3YXJkIiwiRmlsZVRleHQiLCJTcGFya2xlcyIsIlNhdmUiLCJSb3RhdGVDY3ciLCJEb3dubG9hZCIsIkV5ZSIsIlRhcmdldCIsIkNsb2NrIiwiQ2hlY2tDaXJjbGUiLCJBbGVydFRyaWFuZ2xlIiwiTGlnaHRidWxiIiwidG9hc3QiLCJFbmhhbmNlZFN0ZXBOYXZpZ2F0aW9uIiwiU21hcnROYXZpZ2F0aW9uQmFyIiwiUGVyc29uYWxJbmZvRm9ybSIsIkVkdWNhdGlvbkZvcm0iLCJFeHBlcmllbmNlRm9ybSIsIkJhc2ljRXhwZXJpZW5jZUZvcm0iLCJTa2lsbHNQcm9qZWN0c0Zvcm0iLCJCYXNpY1NraWxsc1Byb2plY3RzRm9ybSIsIlJldmlld0Zvcm0iLCJCYXNpY1Jldmlld0Zvcm0iLCJFbmhhbmNlZEV4cGVyaWVuY2VGb3JtIiwiRW5oYW5jZWRTa2lsbHNQcm9qZWN0c0Zvcm0iLCJFbmhhbmNlZFByb2plY3RzRm9ybSIsIkVuaGFuY2VkU2tpbGxzRm9ybSIsIkVuaGFuY2VkUmV2aWV3Rm9ybSIsIkVuaGFuY2VkUHJvZ3Jlc3NJbmRpY2F0b3IiLCJFbmhhbmNlZE5hdmlnYXRpb24iLCJQcm9ncmVzc0JhciIsIkVuaGFuY2VkUmVzdW1lUHJldmlldyIsIlNpbXBsZVJlc3VtZVByZXZpZXciLCJTdWNjZXNzU2NyZWVuIiwidXNlQVRTQW5hbHlzaXMiLCJFbmhhbmNlZFJlc3VtZUJ1aWxkZXIiLCJoaWRlSGVhZGVyIiwiYXRzQW5hbHlzaXMiLCJjdXJyZW50U3RlcCIsInNldEN1cnJlbnRTdGVwIiwic2VsZWN0ZWRUZW1wbGF0ZSIsInNldFNlbGVjdGVkVGVtcGxhdGUiLCJmb3JtRGF0YSIsInNldEZvcm1EYXRhIiwicGVyc29uYWwiLCJmaXJzdE5hbWUiLCJsYXN0TmFtZSIsImVtYWlsIiwicGhvbmUiLCJsb2NhdGlvbiIsImxpbmtlZGluIiwicG9ydGZvbGlvIiwic3VtbWFyeSIsInByb2ZpbGVJbWFnZSIsImVkdWNhdGlvbiIsImlkIiwiZGVncmVlIiwiaW5zdGl0dXRpb24iLCJzdGFydERhdGUiLCJlbmREYXRlIiwiZ3BhIiwicmVsZXZhbnQiLCJleHBlcmllbmNlIiwidGl0bGUiLCJjb21wYW55IiwiY3VycmVudCIsImRlc2NyaXB0aW9uIiwic2tpbGxzIiwidGVjaG5pY2FsIiwibGFuZ3VhZ2VzIiwiY2VydGlmaWNhdGlvbnMiLCJwcm9qZWN0cyIsIm5hbWUiLCJ0ZWNobm9sb2dpZXMiLCJsaW5rIiwiam9iRGVzY3JpcHRpb24iLCJzaG93UHJldmlldyIsInNldFNob3dQcmV2aWV3IiwiaXNHZW5lcmF0aW5nIiwic2V0SXNHZW5lcmF0aW5nIiwic2hvd1Byb2dyZXNzQmFyIiwic2V0U2hvd1Byb2dyZXNzQmFyIiwicmVzdW1lR2VuZXJhdGVkIiwic2V0UmVzdW1lR2VuZXJhdGVkIiwidmFsaWRhdGlvbkVycm9ycyIsInNldFZhbGlkYXRpb25FcnJvcnMiLCJzaG93VmFsaWRhdGlvbkVycm9ycyIsInNldFNob3dWYWxpZGF0aW9uRXJyb3JzIiwiY29tcGxldGVkU3RlcHMiLCJzZXRDb21wbGV0ZWRTdGVwcyIsImF1dG9TYXZlRW5hYmxlZCIsInNldEF1dG9TYXZlRW5hYmxlZCIsImxhc3RTYXZlZCIsInNldExhc3RTYXZlZCIsInJlc3VtZURhdGEiLCJzZXRSZXN1bWVEYXRhIiwicmVzdW1lVXJsIiwic2V0UmVzdW1lVXJsIiwiYXRzU2NvcmUiLCJzZXRBdHNTY29yZSIsInN1Z2dlc3Rpb25zIiwic2V0U3VnZ2VzdGlvbnMiLCJhaVN1Z2dlc3Rpb25zRW5hYmxlZCIsInNldEFpU3VnZ2VzdGlvbnNFbmFibGVkIiwic21hcnRWYWxpZGF0aW9uIiwic2V0U21hcnRWYWxpZGF0aW9uIiwic2hvd0hpbnRzIiwic2V0U2hvd0hpbnRzIiwic3RlcHMiLCJpY29uIiwiZXN0aW1hdGVkVGltZSIsInJlcXVpcmVkIiwiYmFja3VwRGF0YSIsImxvY2FsU3RvcmFnZSIsImdldEl0ZW0iLCJhdXRoVGltZXN0YW1wIiwic2F2ZWREYXRhIiwidGltZURpZmYiLCJEYXRlIiwibm93IiwicGFyc2VJbnQiLCJwYXJzZWQiLCJKU09OIiwicGFyc2UiLCJzdWNjZXNzIiwicmVtb3ZlSXRlbSIsInNldEl0ZW0iLCJ0b0lTT1N0cmluZyIsImVycm9yIiwiY29uc29sZSIsIndpbmRvdyIsInRpbWVvdXRJZCIsInNldFRpbWVvdXQiLCJzdHJpbmdpZnkiLCJjbGVhclRpbWVvdXQiLCJ2YWxpZGF0ZVN0ZXAiLCJzdGVwSW5kZXgiLCJlcnJvcnMiLCJ0cmltIiwidGVzdCIsInZhbGlkRWR1Y2F0aW9uIiwiZmlsdGVyIiwiZWR1IiwibGVuZ3RoIiwidmFsaWRFeHBlcmllbmNlIiwiZXhwIiwiY2FuUHJvY2VlZFRvTmV4dFN0ZXAiLCJPYmplY3QiLCJrZXlzIiwiZ2V0Q29tcGxldGVkU3RlcHMiLCJjb21wbGV0ZWQiLCJpIiwicHVzaCIsImdldFN0ZXBDb21wbGV0aW9uU3RhdHVzIiwic3RhdHVzIiwiZm9yRWFjaCIsInN0ZXAiLCJpbmRleCIsImNhbkdlbmVyYXRlUmVzdW1lIiwidXBkYXRlRm9ybURhdGEiLCJzZWN0aW9uIiwiZmllbGQiLCJ2YWx1ZSIsInByZXYiLCJuZXdEYXRhIiwiQXJyYXkiLCJpc0FycmF5IiwibmV3QXJyYXkiLCJuZXdFcnJvcnMiLCJhZGRBcnJheUl0ZW0iLCJ0ZW1wbGF0ZSIsIk1hdGgiLCJyYW5kb20iLCJ0b1N0cmluZyIsInN1YnN0cmluZyIsInJlbW92ZUFycmF5SXRlbSIsIml0ZW0iLCJuZXh0U3RlcCIsImVycm9yTWVzc2FnZXMiLCJ2YWx1ZXMiLCJTZXQiLCJwcmV2U3RlcCIsImhhbmRsZVN0ZXBDbGljayIsImluY2x1ZGVzIiwiZ2VuZXJhdGVSZXN1bWUiLCJhbGxFcnJvcnMiLCJzdGVwRXJyb3JzIiwiYXNzaWduIiwicmVzcG9uc2UiLCJmZXRjaCIsIm1ldGhvZCIsImhlYWRlcnMiLCJib2R5IiwidGVtcGxhdGVJZCIsImNvbnRlbnRUeXBlIiwiZ2V0IiwiRXJyb3IiLCJkYXRhIiwianNvbiIsIm9rIiwiZG93bmxvYWRVcmwiLCJtZXNzYWdlIiwiaGFuZGxlU2F2ZSIsImhhbmRsZUFJU3VnZ2VzdCIsImxvYWRpbmciLCJQcm9taXNlIiwicmVzb2x2ZSIsImRpc21pc3MiLCJoYW5kbGVHZW5lcmF0ZVJlc3VtZSIsImFkZEV2ZW50TGlzdGVuZXIiLCJyZW1vdmVFdmVudExpc3RlbmVyIiwiZ2V0U3RlcFZhbGlkYXRpb24iLCJ2YWxpZGF0aW9uIiwicmVuZGVyU3RlcENvbnRlbnQiLCJjb21tb25Qcm9wcyIsIm9uU2F2ZSIsIm9uQUlTdWdnZXN0Iiwib25UZW1wbGF0ZVNlbGVjdCIsIm9uU3RhcnRPdmVyIiwib25FZGl0UmVzdW1lIiwiZGl2IiwiY2xhc3NOYW1lIiwiaXNWaXNpYmxlIiwib25Db21wbGV0ZSIsImluaXRpYWwiLCJvcGFjaXR5IiwieSIsImFuaW1hdGUiLCJoMSIsInAiLCJvblN0ZXBDbGljayIsInZhcmlhbnQiLCJzaG93TGFiZWxzIiwic2hvd1Byb2dyZXNzIiwibW9kZSIsIngiLCJleGl0IiwidHJhbnNpdGlvbiIsImR1cmF0aW9uIiwib25Ub2dnbGVQcmV2aWV3IiwiZGVsYXkiLCJoMyIsIm92ZXJhbGxTY29yZSIsInJlY29tbWVuZGF0aW9ucyIsInNsaWNlIiwibWFwIiwicmVjIiwic3BhbiIsImJ1dHRvbiIsIm9uQ2xpY2siLCJ0b3RhbFN0ZXBzIiwib25QcmV2aW91cyIsIm9uTmV4dCIsIm9uR2VuZXJhdGUiLCJvblByZXZpZXciLCJjYW5Qcm9jZWVkIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/resume/ResumeBuilder.jsx\n"));

/***/ })

});
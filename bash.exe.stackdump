Stack trace:
Frame         Function      Args
0007FFFFA170  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF9070) msys-2.0.dll+0x1FE8E
0007FFFFA170  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFA448) msys-2.0.dll+0x67F9
0007FFFFA170  000210046832 (000210286019, 0007FFFFA028, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFA170  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFA170  000210068E24 (0007FFFFA180, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFA450  00021006A225 (0007FFFFA180, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFAB8420000 ntdll.dll
7FFAB7620000 KERNEL32.DLL
7FFAB5D40000 KERNELBASE.dll
7FFAB80D0000 USER32.dll
7FFAB58F0000 win32u.dll
7FFAB7DC0000 GDI32.dll
7FFAB5920000 gdi32full.dll
7FFAB5570000 msvcp_win.dll
7FFAB57A0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFAB6A10000 advapi32.dll
7FFAB6550000 msvcrt.dll
7FFAB7D10000 sechost.dll
7FFAB82C0000 RPCRT4.dll
7FFAB4A80000 CRYPTBASE.DLL
7FFAB5A60000 bcryptPrimitives.dll
7FFAB6B30000 IMM32.DLL

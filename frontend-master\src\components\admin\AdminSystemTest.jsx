'use client';

import testAdminAPI from '@/utils/testAdminAPI';
import { useState } from 'react';
import { useAdmin } from '@/contexts/AdminContext';
import { testFirebaseConnection } from '@/firebase/test';
import { CheckCircle, AlertCircle, Info, Play, User, Shield } from 'lucide-react';

const AdminSystemTest = () => {
  const { user, isAdmin, loading, error, adminLogin } = useAdmin();
  const [testResults, setTestResults] = useState({});
  const [isTesting, setIsTesting] = useState(false);

  const runSystemTests = async () => {
    setIsTesting(true);
    const results = {};

    try {
      // Test 1: Firebase Connection
      console.log('🧪 Testing Firebase connection...');
      const firebaseTest = await testFirebaseConnection();
      results.firebase = {
        status: firebaseTest.firestore && firebaseTest.realtimeDatabase ? 'pass' : 'fail',
        details: firebaseTest
      };      // Test 2: Admin API
      console.log('🧪 Testing Admin API...');
      try {
        const apiTest = await testAdminAPI();
        results.adminAPI = {
          status: apiTest.success ? 'pass' : 'fail',
          details: apiTest.results || { error: apiTest.error }
        };
      } catch (error) {
        results.adminAPI = {
          status: 'fail',
          error: error.message
        };
      }

      // Test 3: Environment Configuration
      console.log('🧪 Testing Environment Configuration...');
      const hasFirebaseConfig = !!(
        process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID &&
        process.env.NEXT_PUBLIC_FIREBASE_API_KEY
      );
      const hasAdminConfig = !!(process.env.NEXT_PUBLIC_ADMIN_DOMAIN);
      
      results.environment = {
        status: hasFirebaseConfig && hasAdminConfig ? 'pass' : 'warn',
        details: {
          firebase: hasFirebaseConfig,
          admin: hasAdminConfig,
          projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
          adminDomain: process.env.NEXT_PUBLIC_ADMIN_DOMAIN
        }
      };

      // Test 4: Authentication State
      console.log('🧪 Testing Authentication State...');
      results.auth = {
        status: user ? (isAdmin ? 'pass' : 'warn') : 'info',
        details: {
          authenticated: !!user,
          isAdmin: isAdmin,
          email: user?.email,
          loading: loading
        }
      };

      setTestResults(results);
      console.log('✅ System tests completed:', results);

    } catch (error) {
      console.error('❌ System test failed:', error);
      setTestResults({
        error: {
          status: 'fail',
          message: error.message
        }
      });
    } finally {
      setIsTesting(false);
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'pass': return <CheckCircle className="w-5 h-5 text-green-400" />;
      case 'warn': return <AlertCircle className="w-5 h-5 text-yellow-400" />;
      case 'fail': return <AlertCircle className="w-5 h-5 text-red-400" />;
      default: return <Info className="w-5 h-5 text-blue-400" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'pass': return 'text-green-400';
      case 'warn': return 'text-yellow-400';
      case 'fail': return 'text-red-400';
      default: return 'text-blue-400';
    }
  };

  return (
    <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg p-6">
      <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
        <Shield className="w-5 h-5 text-purple-400" />
        Admin System Diagnostics
      </h3>

      {/* Current Status */}
      <div className="mb-6 p-4 bg-black/20 rounded-lg">
        <div className="flex items-center gap-4 mb-3">
          <User className="w-5 h-5 text-blue-400" />
          <div>
            <div className="text-white font-medium">Current Session</div>
            <div className="text-gray-400 text-sm">
              {user ? `${user.email} (${isAdmin ? 'Admin' : 'User'})` : 'Not authenticated'}
            </div>
          </div>
        </div>
        
        {error && (
          <div className="mt-3 p-3 bg-red-500/10 border border-red-500/20 rounded text-red-300 text-sm">
            {error}
          </div>
        )}
      </div>

      {/* Test Button */}
      <button
        onClick={runSystemTests}
        disabled={isTesting}
        className="w-full bg-purple-500 hover:bg-purple-600 disabled:bg-purple-500/50 text-white px-4 py-3 rounded-lg transition-colors flex items-center justify-center gap-2 mb-6"
      >
        {isTesting ? (
          <>
            <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
            Running Diagnostics...
          </>
        ) : (
          <>
            <Play className="w-4 h-4" />
            Run System Diagnostics
          </>
        )}
      </button>

      {/* Test Results */}
      {Object.keys(testResults).length > 0 && (
        <div className="space-y-4">
          <h4 className="text-white font-medium">Test Results</h4>
          
          {testResults.firebase && (
            <div className="p-3 bg-black/20 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                {getStatusIcon(testResults.firebase.status)}
                <span className={`font-medium ${getStatusColor(testResults.firebase.status)}`}>
                  Firebase Connection
                </span>
              </div>
              <div className="text-sm text-gray-300 ml-7">
                Firestore: {testResults.firebase.details.firestore ? '✅' : '❌'} | 
                Realtime DB: {testResults.firebase.details.realtimeDatabase ? '✅' : '❌'}
              </div>
            </div>
          )}

          {testResults.adminAPI && (
            <div className="p-3 bg-black/20 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                {getStatusIcon(testResults.adminAPI.status)}
                <span className={`font-medium ${getStatusColor(testResults.adminAPI.status)}`}>
                  Admin API
                </span>
              </div>
              <div className="text-sm text-gray-300 ml-7">
                {testResults.adminAPI.status === 'pass' ? 
                  `Version: ${testResults.adminAPI.details.version}` : 
                  `Error: ${testResults.adminAPI.error}`
                }
              </div>
            </div>
          )}

          {testResults.environment && (
            <div className="p-3 bg-black/20 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                {getStatusIcon(testResults.environment.status)}
                <span className={`font-medium ${getStatusColor(testResults.environment.status)}`}>
                  Environment Configuration
                </span>
              </div>
              <div className="text-sm text-gray-300 ml-7 space-y-1">
                <div>Firebase Config: {testResults.environment.details.firebase ? '✅' : '❌'}</div>
                <div>Admin Config: {testResults.environment.details.admin ? '✅' : '❌'}</div>
                <div>Project: {testResults.environment.details.projectId || 'Not set'}</div>
              </div>
            </div>
          )}

          {testResults.auth && (
            <div className="p-3 bg-black/20 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                {getStatusIcon(testResults.auth.status)}
                <span className={`font-medium ${getStatusColor(testResults.auth.status)}`}>
                  Authentication Status
                </span>
              </div>
              <div className="text-sm text-gray-300 ml-7 space-y-1">
                <div>Authenticated: {testResults.auth.details.authenticated ? '✅' : '❌'}</div>
                <div>Admin Access: {testResults.auth.details.isAdmin ? '✅' : '❌'}</div>
                {testResults.auth.details.email && (
                  <div>Email: {testResults.auth.details.email}</div>
                )}
              </div>
            </div>
          )}

          {testResults.error && (
            <div className="p-3 bg-red-500/10 border border-red-500/20 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <AlertCircle className="w-5 h-5 text-red-400" />
                <span className="font-medium text-red-400">System Error</span>
              </div>
              <div className="text-sm text-red-300 ml-7">
                {testResults.error.message}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Quick Actions */}
      <div className="mt-6 pt-4 border-t border-white/10">
        <div className="text-sm text-gray-400 mb-2">Quick Actions</div>
        <div className="flex gap-2 flex-wrap">
          <button
            onClick={() => window.location.reload()}
            className="text-xs bg-gray-500/20 hover:bg-gray-500/30 text-gray-300 px-3 py-1 rounded transition-colors"
          >
            Refresh Page
          </button>
          <button
            onClick={() => console.log('Admin Context:', { user, isAdmin, loading, error })}
            className="text-xs bg-blue-500/20 hover:bg-blue-500/30 text-blue-300 px-3 py-1 rounded transition-colors"
          >
            Log Context
          </button>
          <button
            onClick={() => localStorage.clear()}
            className="text-xs bg-red-500/20 hover:bg-red-500/30 text-red-300 px-3 py-1 rounded transition-colors"
          >
            Clear Storage
          </button>
        </div>
      </div>
    </div>
  );
};

export default AdminSystemTest;

'use client'
import { useState, useEffect, useCallback } from 'react'
import Link from 'next/link'
import { usePathname, useRouter } from 'next/navigation'
import { Sparkles, Menu, X, LogIn, UserPlus, LogOut, User } from 'lucide-react'
import { useAuth } from '@/contexts/AuthContext'
import toast from 'react-hot-toast'

const Navbar = () => {
  const [isOpen, setIsOpen] = useState(false)
  const { currentUser, logout } = useAuth()
  const [role, setRole] = useState(null)
  const [mounted, setMounted] = useState(false)
  const pathname = usePathname()
  const router = useRouter()

  const links = [
    { name: 'Home', href: '/' },
    { name: 'About Us', href: '/aboutus' },
    { name: 'Resume Builder', href: '/resume-builder' },
  ]  // Check mounting state to avoid hydration mismatch
  useEffect(() => {
    setMounted(true)
    console.log('Navbar mounted, links:', links) // Debug log
    console.log('Current user:', currentUser) // Debug log
  }, [])

  // Check if the user is logged in and set the role on initial render
  useEffect(() => {
    if (mounted) {
      const storedRole = localStorage.getItem('role')
      setRole(storedRole)
    }
  }, [currentUser, mounted])

  const handleLoginLogout = useCallback(async () => {
    if (currentUser) {
      // User is logged in, perform logout
      try {
        await logout()
        if (mounted) {
          localStorage.removeItem('role')
          setRole(null)
        }
        toast.success('Logged out successfully')
        router.push('/')
        setIsOpen(false)
      } catch (error) {
        toast.error('Error logging out')
      }
    } else {
      // User is not logged in, redirect to login
      router.push('/login')
      setIsOpen(false)
    }
  }, [currentUser, logout, router, mounted])
  const handleNavItemClick = useCallback((href) => {
    if (href.startsWith('#')) {
      if (pathname !== '/') {
        router.push(`/${href}`)
      } else {
        // Scroll to section if on home page
        const element = document.querySelector(href)
        if (element) {
          element.scrollIntoView({ behavior: 'smooth' })
        }
      }
    }
    setIsOpen(false)
  }, [pathname, router])
  // Prevent body scroll when menu is open and handle escape key
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden'
      document.body.style.position = 'fixed'
      document.body.style.top = '-' + window.scrollY + 'px'
      document.body.style.width = '100%'
      
      const handleEscape = (e) => {
        if (e.key === 'Escape') {
          setIsOpen(false)
        }
      }
      document.addEventListener('keydown', handleEscape)
      
      return () => {
        const scrollY = document.body.style.top
        document.body.style.overflow = ''
        document.body.style.position = ''
        document.body.style.top = ''
        document.body.style.width = ''
        window.scrollTo(0, parseInt(scrollY || '0') * -1)
        document.removeEventListener('keydown', handleEscape)
      }
    } else {
      document.body.style.overflow = ''
      document.body.style.position = ''
      document.body.style.top = ''
      document.body.style.width = ''
    }
  }, [isOpen])
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth >= 1024) {
        setIsOpen(false)
      }
    }
    
    const handleOrientationChange = () => {
      // Close menu on orientation change
      setIsOpen(false)
    }
    
    window.addEventListener('resize', handleResize)
    window.addEventListener('orientationchange', handleOrientationChange)
    
    return () => {
      window.removeEventListener('resize', handleResize)
      window.removeEventListener('orientationchange', handleOrientationChange)
    }
  }, [])
  if (!mounted) {
    return (
      <header className="fixed inset-x-0 top-0 z-50 backdrop-blur-md bg-black/50 border-b border-white/10">
        <nav className="flex items-center justify-between px-4 py-3 sm:px-6 lg:px-8 min-h-[64px]" aria-label="Global">
          <div className="flex lg:flex-1">            <Link href="/" className="-m-1.5 p-1.5 flex items-center gap-2 touch-manipulation">
              <Sparkles className="h-6 w-6 sm:h-7 sm:w-7 text-purple-500 flex-shrink-0" />
              <span className="font-mono font-bold text-white text-base sm:text-lg">
                Blink<span className="text-purple-500">Find</span>
              </span>
            </Link>
          </div>
        </nav>
      </header>
    )
  }
  return (
    <header className="fixed inset-x-0 top-0 z-50 backdrop-blur-md bg-black/50 border-b border-white/10">
      <nav className="flex items-center justify-between px-4 py-3 sm:px-6 lg:px-8 min-h-[64px]" aria-label="Global">        {/* Logo */}
        <div className="flex lg:flex-1">
          <Link 
            href="/" 
            className="-m-1.5 p-1.5 flex items-center gap-2 touch-manipulation"
            onClick={() => setIsOpen(false)}
          >
            <Sparkles className="h-6 w-6 sm:h-7 sm:w-7 text-purple-500 flex-shrink-0" />
            <span className="font-mono font-bold text-white text-base sm:text-lg">
              Blink<span className="text-purple-500">Find</span>
            </span>
          </Link>
        </div>
          {/* Mobile menu button */}
        <div className="flex lg:hidden">
          <button
            type="button"
            className="-m-2.5 inline-flex items-center justify-center rounded-md p-2.5 text-gray-400 hover:text-white transition-colors touch-manipulation min-w-[44px] min-h-[44px]"
            onClick={() => {
              console.log('Mobile menu button clicked, current state:', isOpen);
              setIsOpen(!isOpen);
            }}
            aria-expanded={isOpen}
            aria-label={isOpen ? "Close menu" : "Open menu"}
          >
            <span className="sr-only">{isOpen ? "Close" : "Open"} main menu</span>
            {isOpen ? (
              <X className="h-6 w-6" aria-hidden="true" />
            ) : (
              <Menu className="h-6 w-6" aria-hidden="true" />
            )}
          </button>
        </div>
          {/* Desktop navigation */}
        <div className="hidden lg:flex lg:gap-x-8 xl:gap-x-12">
          {links.map((item) => (
            <Link
              key={item.name}
              href={item.href}
              onClick={() => handleNavItemClick(item.href)}
              className={`text-sm font-medium leading-6 transition-colors ${
                pathname === item.href ? 'text-white' : 'text-gray-400 hover:text-white'
              }`}
            >
              {item.name}
            </Link>
          ))}
          {role === 'admin' && (
            <Link
              href="/admin/contact-forms"
              className="text-sm font-medium leading-6 text-gray-400 hover:text-white transition-colors"
            >
              Admin
            </Link>
          )}
        </div>

        {/* Desktop auth buttons */}
        <div className="hidden lg:flex lg:flex-1 lg:justify-end lg:items-center lg:gap-3 xl:gap-4">
          {currentUser ? (
            <div className="flex items-center gap-3 xl:gap-4">
              <Link
                href="/dashboard"
                className="flex items-center gap-2 text-sm font-medium leading-6 text-gray-400 hover:text-white transition-colors"
              >
                <User className="h-4 w-4" />
                <span className="hidden xl:inline">Dashboard</span>
                <span className="xl:hidden">Profile</span>
              </Link>
              <button
                onClick={handleLoginLogout}
                className="flex items-center gap-2 text-sm font-medium leading-6 text-gray-400 hover:text-white transition-colors"
              >
                <LogOut className="h-4 w-4" />
                <span>Logout</span>
              </button>
            </div>
          ) : (
            <div className="flex items-center gap-3 xl:gap-4">
              <Link
                href="/login"
                className="flex items-center gap-2 text-sm font-medium leading-6 text-gray-400 hover:text-white transition-colors"
              >
                <LogIn className="h-4 w-4" />
                <span>Login</span>
              </Link>              <Link
                href="/signup"
                className="flex items-center gap-2 bg-gradient-to-r from-purple-500 to-pink-500 text-white px-3 py-2 xl:px-4 xl:py-2 rounded-lg text-sm font-medium hover:opacity-90 transition-opacity"
              >
                <UserPlus className="h-4 w-4" />
                <span>Sign Up</span>
              </Link>
            </div>
          )}        </div>
      </nav>      {/* Mobile menu - Simplified for testing */}
      {isOpen && (
        <div className="lg:hidden">
          {console.log('Mobile menu render, isOpen:', isOpen)}
          {/* Mobile menu overlay with blur */}
          <div 
            className="fixed inset-0 z-40 bg-black/50 backdrop-blur-md"
            onClick={() => setIsOpen(false)}
            aria-hidden="true"
          />

          {/* Mobile menu panel with glass effect */}
          <div className="fixed inset-y-0 right-0 z-50 w-full max-w-sm bg-gray-900/95 backdrop-blur-xl border-l border-white/10 shadow-2xl">
            <div className="flex h-full flex-col">
              {/* Mobile menu header */}
              <div className="flex items-center justify-between px-4 py-4 border-b border-gray-700/50 backdrop-blur-sm">
                <Link 
                  href="/" 
                  className="flex items-center gap-2" 
                  onClick={() => setIsOpen(false)}
                >
                  <Sparkles className="h-6 w-6 text-purple-500" />
                  <span className="font-mono font-bold text-white">
                    BlinkFind<span className="text-purple-500">AI</span>
                  </span>
                </Link>
                <button
                  type="button"
                  className="p-2 text-gray-400 hover:text-white rounded-lg hover:bg-white/10 transition-all"
                  onClick={() => setIsOpen(false)}
                >
                  <X className="h-6 w-6" />
                </button>
              </div>              {/* Mobile menu content */}
              <div className="flex-1 px-4 py-6 backdrop-blur-sm">
                {/* Navigation links */}
                <div className="space-y-1">
                  {links.map((item) => (
                    <Link
                      key={item.name}
                      href={item.href}
                      onClick={() => {
                        handleNavItemClick(item.href);
                        setIsOpen(false);
                      }}
                      className="block px-3 py-2 text-sm font-medium text-gray-300 hover:bg-white/20 hover:text-white rounded-md backdrop-blur-lg transition-all border border-white/10 hover:border-white/30 bg-white/5"
                    >
                      {item.name}
                    </Link>
                  ))}
                </div>

                {/* Auth section */}
                <div className="border-t border-gray-700/50 mt-4 pt-4 space-y-1">
                  {currentUser ? (
                    <>
                      <Link
                        href="/dashboard"
                        onClick={() => setIsOpen(false)}
                        className="flex items-center gap-2 px-3 py-2 text-sm font-medium text-gray-300 hover:bg-white/20 hover:text-white rounded-md backdrop-blur-lg transition-all border border-white/10 hover:border-white/30 bg-white/5"
                      >
                        <User className="h-4 w-4" />
                        <span>Dashboard</span>
                      </Link>
                      <button
                        onClick={handleLoginLogout}
                        className="flex items-center gap-2 px-3 py-2 text-sm font-medium text-gray-300 hover:bg-white/20 hover:text-white w-full text-left rounded-md backdrop-blur-lg transition-all border border-white/10 hover:border-white/30 bg-white/5"
                      >
                        <LogOut className="h-4 w-4" />
                        <span>Logout</span>
                      </button>
                    </>
                  ) : (
                    <>
                      <Link
                        href="/login"
                        onClick={() => setIsOpen(false)}
                        className="flex items-center gap-2 px-3 py-2 text-sm font-medium text-gray-300 hover:bg-white/20 hover:text-white rounded-md backdrop-blur-lg transition-all border border-white/10 hover:border-white/30 bg-white/5"
                      >
                        <LogIn className="h-4 w-4" />
                        <span>Login</span>
                      </Link>
                      <Link
                        href="/signup"
                        onClick={() => setIsOpen(false)}
                        className="flex items-center gap-2 px-3 py-2 text-sm font-medium bg-gradient-to-r from-purple-500/90 to-pink-500/90 text-white hover:from-purple-500 hover:to-pink-500 rounded-md backdrop-blur-lg transition-all shadow-md hover:shadow-purple-500/25 border border-purple-400/20"
                      >
                        <UserPlus className="h-4 w-4" />
                        <span>Sign Up</span>
                      </Link>
                    </>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </header>
  )
}

export default Navbar
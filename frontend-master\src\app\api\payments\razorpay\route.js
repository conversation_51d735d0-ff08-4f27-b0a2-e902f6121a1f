import { NextResponse } from 'next/server';
import Razorpay from 'razorpay';
import crypto from 'crypto';

const razorpay = new Razorpay({
  key_id: process.env.RAZORPAY_KEY_ID,
  key_secret: process.env.RAZORPAY_KEY_SECRET,
});

export async function POST(request) {
  try {
    const { planId, amount, currency, billingInterval, userId } = await request.json();

    // Validate required fields
    if (!planId || !amount || !currency) {
      return NextResponse.json(
        { error: 'Missing required fields: planId, amount, currency' },
        { status: 400 }
      );
    }

    // Create order
    const orderOptions = {
      amount: Math.round(amount * 100), // Convert to paise
      currency: currency.toUpperCase(),
      receipt: `receipt_${Date.now()}_${userId || 'guest'}`,
      notes: {
        planId,
        billingInterval: billingInterval || 'monthly',
        userId: userId || 'guest',
        type: 'subscription'
      }
    };

    const order = await razorpay.orders.create(orderOptions);

    return NextResponse.json({
      success: true,
      orderId: order.id,
      amount: order.amount,
      currency: order.currency,
      key: process.env.RAZORPAY_KEY_ID
    });

  } catch (error) {
    console.error('Razorpay order creation error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to create order',
        details: error.message 
      },
      { status: 500 }
    );
  }
}

// Verify payment signature
export async function PUT(request) {
  try {
    const { 
      razorpay_order_id, 
      razorpay_payment_id, 
      razorpay_signature,
      planId,
      userId 
    } = await request.json();

    if (!razorpay_order_id || !razorpay_payment_id || !razorpay_signature) {
      return NextResponse.json(
        { error: 'Missing payment verification data' },
        { status: 400 }
      );
    }

    // Verify signature
    const body = razorpay_order_id + '|' + razorpay_payment_id;
    const expectedSignature = crypto
      .createHmac('sha256', process.env.RAZORPAY_KEY_SECRET)
      .update(body.toString())
      .digest('hex');

    if (expectedSignature !== razorpay_signature) {
      return NextResponse.json(
        { error: 'Invalid payment signature' },
        { status: 400 }
      );
    }

    // Fetch payment details from Razorpay
    const payment = await razorpay.payments.fetch(razorpay_payment_id);
    
    if (payment.status !== 'captured') {
      return NextResponse.json(
        { error: 'Payment not captured' },
        { status: 400 }
      );
    }

    // Here you would typically:
    // 1. Update user's subscription in your database
    // 2. Send confirmation email
    // 3. Update user permissions
    // 4. Create invoice record

    // For now, we'll simulate this
    const subscriptionData = {
      userId,
      planId,
      status: 'active',
      paymentId: razorpay_payment_id,
      orderId: razorpay_order_id,
      amount: payment.amount / 100,
      currency: payment.currency,
      method: payment.method,
      startDate: new Date().toISOString(),
      nextBillingDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() // 30 days from now
    };

    return NextResponse.json({
      success: true,
      subscription: subscriptionData,
      payment: {
        id: payment.id,
        amount: payment.amount / 100,
        currency: payment.currency,
        method: payment.method,
        status: payment.status
      },
      message: 'Payment verified and subscription activated'
    });

  } catch (error) {
    console.error('Payment verification error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to verify payment',
        details: error.message 
      },
      { status: 500 }
    );
  }
}

// Handle subscription plans (for recurring payments)
export async function PATCH(request) {
  try {
    const { planId, amount, currency, interval } = await request.json();

    if (!planId || !amount || !currency || !interval) {
      return NextResponse.json(
        { error: 'Missing required fields for subscription plan' },
        { status: 400 }
      );
    }

    // Create subscription plan
    const planOptions = {
      period: interval === 'yearly' ? 'yearly' : 'monthly',
      interval: interval === 'yearly' ? 12 : 1,
      item: {
        name: `BlinkFind AI - ${planId}`,
        amount: Math.round(amount * 100), // Convert to paise
        currency: currency.toUpperCase(),
        description: `${planId} subscription plan`
      },
      notes: {
        planId,
        type: 'subscription'
      }
    };

    const plan = await razorpay.plans.create(planOptions);

    return NextResponse.json({
      success: true,
      planId: plan.id,
      amount: plan.item.amount,
      currency: plan.item.currency,
      interval: plan.period
    });

  } catch (error) {
    console.error('Subscription plan creation error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to create subscription plan',
        details: error.message 
      },
      { status: 500 }
    );
  }
}

// Handle webhooks
export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const event = searchParams.get('event');
    
    // This would typically handle Razorpay webhooks
    // For now, return a simple status
    return NextResponse.json({
      success: true,
      message: 'Webhook endpoint ready',
      event: event || 'none'
    });

  } catch (error) {
    console.error('Webhook error:', error);
    return NextResponse.json(
      { error: 'Webhook processing failed' },
      { status: 500 }
    );
  }
}

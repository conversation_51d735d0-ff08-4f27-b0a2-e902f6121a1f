'use client';
import { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  X, Download, ZoomIn, ZoomOut, RotateCcw, Printer, 
  Palette, Settings, Maximize2, Minimize2, FileText,
  Eye, Share2
} from 'lucide-react';
import { convertHTMLToPDF } from '@/utils/enhancedPdfGenerator';

const FullscreenPreviewModal = ({ 
  isOpen, 
  onClose, 
  formData, 
  selectedTemplate = 'classic_ats',
  onTemplateChange 
}) => {
  const [zoom, setZoom] = useState(0.85);
  const [isGenerating, setIsGenerating] = useState(false);
  const [showTemplateSelector, setShowTemplateSelector] = useState(false);
  const previewRef = useRef(null);
  const modalRef = useRef(null);

  // Handle escape key
  useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden'; // Prevent background scroll
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  // Handle backdrop click
  const handleBackdropClick = (e) => {
    if (e.target === modalRef.current) {
      onClose();
    }
  };

  // Zoom controls
  const handleZoomIn = () => setZoom(prev => Math.min(prev + 0.1, 1.5));
  const handleZoomOut = () => setZoom(prev => Math.max(prev - 0.1, 0.4));
  const handleResetZoom = () => setZoom(0.85);

  // Download PDF
  const handleDownloadPDF = async () => {
    if (!formData || !previewRef.current) return;
    
    setIsGenerating(true);
    try {
      const filename = `${formData.personal?.firstName || 'Resume'}_${formData.personal?.lastName || 'Document'}.pdf`;
      await convertHTMLToPDF(previewRef.current, filename);
    } catch (error) {
      console.error('Error generating PDF:', error);
      alert('Error generating PDF. Please try again.');
    } finally {
      setIsGenerating(false);
    }
  };

  // Print functionality
  const handlePrint = () => {
    if (!previewRef.current) return;
    
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
      <html>
        <head>
          <title>Resume - ${formData.personal?.firstName || ''} ${formData.personal?.lastName || ''}</title>
          <style>
            body { 
              margin: 0; 
              padding: 20px; 
              font-family: 'Inter', 'Helvetica Neue', Arial, sans-serif;
              font-size: 11pt;
              line-height: 1.4;
              color: #000000;
            }
            @media print { 
              body { padding: 0; }
              .no-print { display: none; }
            }
            .resume-content { 
              max-width: 8.5in; 
              margin: 0 auto; 
              background: white;
            }
          </style>
        </head>
        <body>
          <div class="resume-content">
            ${previewRef.current.innerHTML}
          </div>
        </body>
      </html>
    `);
    printWindow.document.close();
    printWindow.print();
  };

  // Render resume content (same as SimpleResumePreview but optimized for fullscreen)
  const renderResumeContent = () => {
    if (!formData || !formData.personal || !formData.personal.firstName) {
      return (
        <div className="flex items-center justify-center h-full text-gray-400">
          <div className="text-center">
            <FileText className="h-16 w-16 mx-auto mb-4 opacity-50" />
            <h3 className="text-xl font-medium mb-2">No Resume Data</h3>
            <p className="text-sm">Fill out the form to see your resume preview</p>
          </div>
        </div>
      );
    }

    return (
      <div className="p-8 space-y-6 max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center border-b-2 border-gray-300 pb-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-3">
            {formData.personal.firstName} {formData.personal.lastName}
          </h1>
          <div className="text-base text-gray-600 space-y-1">
            {formData.personal.email && <div>{formData.personal.email}</div>}
            {formData.personal.phone && <div>{formData.personal.phone}</div>}
            {formData.personal.location && <div>{formData.personal.location}</div>}
            {formData.personal.linkedin && <div>{formData.personal.linkedin}</div>}
          </div>
        </div>

        {/* Professional Summary */}
        {formData.personal.summary && (
          <div>
            <h2 className="text-lg font-bold text-gray-900 mb-3 uppercase border-b border-gray-300 pb-1">
              Professional Summary
            </h2>
            <p className="text-sm text-gray-800 leading-relaxed text-justify">
              {formData.personal.summary}
            </p>
          </div>
        )}

        {/* Experience */}
        {formData.experience && formData.experience.length > 0 && (
          <div>
            <h2 className="text-lg font-bold text-gray-900 mb-3 uppercase border-b border-gray-300 pb-1">
              Professional Experience
            </h2>
            <div className="space-y-4">
              {formData.experience.map((exp, index) => (
                <div key={index} className="text-sm">
                  <div className="flex justify-between items-start mb-1">
                    <h3 className="text-base font-semibold text-gray-900">{exp.title}</h3>
                    <span className="text-gray-600 text-sm font-medium">
                      {exp.startDate} - {exp.current ? 'Present' : exp.endDate}
                    </span>
                  </div>
                  <div className="text-gray-700 font-medium mb-2">
                    {exp.company} {exp.location && `• ${exp.location}`}
                  </div>
                  {exp.description && (
                    <div className="text-gray-600 text-sm leading-relaxed">
                      {exp.description.split('\n').map((line, i) => (
                        <div key={i} className="mb-1">
                          {line.trim().startsWith('•') ? line : `• ${line.trim()}`}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Education */}
        {formData.education && formData.education.length > 0 && (
          <div>
            <h2 className="text-lg font-bold text-gray-900 mb-3 uppercase border-b border-gray-300 pb-1">
              Education
            </h2>
            <div className="space-y-3">
              {formData.education.map((edu, index) => (
                <div key={index} className="text-sm">
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="text-base font-semibold text-gray-900">
                        {edu.degree} {edu.field && `in ${edu.field}`}
                      </h3>
                      <div className="text-gray-700 font-medium">{edu.institution}</div>
                      {edu.location && <div className="text-gray-600">{edu.location}</div>}
                      {edu.gpa && <div className="text-gray-600">GPA: {edu.gpa}</div>}
                    </div>
                    <span className="text-gray-600 text-sm font-medium">{edu.graduationDate}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Skills */}
        {formData.skills && (
          <div>
            <h2 className="text-lg font-bold text-gray-900 mb-3 uppercase border-b border-gray-300 pb-1">
              Skills
            </h2>
            <div className="text-sm space-y-2">
              {formData.skills.technical && formData.skills.technical.length > 0 && (
                <div>
                  <span className="font-semibold text-gray-900">Technical: </span>
                  <span className="text-gray-700">
                    {formData.skills.technical.join(', ')}
                  </span>
                </div>
              )}
              {formData.skills.languages && formData.skills.languages.length > 0 && (
                <div>
                  <span className="font-semibold text-gray-900">Languages: </span>
                  <span className="text-gray-700">
                    {formData.skills.languages.join(', ')}
                  </span>
                </div>
              )}
              {formData.skills.certifications && formData.skills.certifications.length > 0 && (
                <div>
                  <span className="font-semibold text-gray-900">Certifications: </span>
                  <span className="text-gray-700">
                    {formData.skills.certifications.join(', ')}
                  </span>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Projects */}
        {formData.projects && formData.projects.length > 0 && (
          <div>
            <h2 className="text-lg font-bold text-gray-900 mb-3 uppercase border-b border-gray-300 pb-1">
              Projects
            </h2>
            <div className="space-y-3">
              {formData.projects.map((project, index) => (
                <div key={index} className="text-sm">
                  <h3 className="text-base font-semibold text-gray-900 mb-1">
                    {project.name}
                    {project.technologies && (
                      <span className="text-sm font-normal text-gray-600 ml-2">
                        ({project.technologies})
                      </span>
                    )}
                  </h3>
                  {project.description && (
                    <p className="text-gray-700 leading-relaxed mb-2 text-justify">
                      {project.description}
                    </p>
                  )}
                  {(project.link || project.github) && (
                    <div className="text-gray-600 text-sm">
                      {project.link && <span>Demo: {project.link}</span>}
                      {project.link && project.github && <span> • </span>}
                      {project.github && <span>Code: {project.github}</span>}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        ref={modalRef}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center"
        onClick={handleBackdropClick}
      >
        {/* Modal Content */}
        <motion.div
          initial={{ opacity: 0, scale: 0.95, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.95, y: 20 }}
          className="bg-gray-900 rounded-2xl border border-gray-700 w-full h-full max-w-7xl max-h-[95vh] m-4 flex flex-col overflow-hidden"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-gray-700 bg-gray-800/50 flex-shrink-0">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-neural-purple/20 rounded-lg flex items-center justify-center">
                <Eye className="h-4 w-4 text-neural-purple" />
              </div>
              <div>
                <h2 className="text-lg font-semibold text-white">Resume Preview</h2>
                <p className="text-gray-400 text-sm">
                  {formData?.personal?.firstName} {formData?.personal?.lastName} • {selectedTemplate?.replace('_', ' ') || 'Classic ATS'}
                </p>
              </div>
            </div>

            <div className="flex items-center gap-2">
              {/* Zoom Controls */}
              <div className="flex items-center gap-1 bg-gray-700/50 rounded-lg p-1">
                <button
                  onClick={handleZoomOut}
                  className="p-1 text-gray-400 hover:text-white transition-colors"
                  title="Zoom Out"
                >
                  <ZoomOut className="h-4 w-4" />
                </button>
                <span className="px-2 text-sm text-gray-300 min-w-[3rem] text-center">
                  {Math.round(zoom * 100)}%
                </span>
                <button
                  onClick={handleZoomIn}
                  className="p-1 text-gray-400 hover:text-white transition-colors"
                  title="Zoom In"
                >
                  <ZoomIn className="h-4 w-4" />
                </button>
                <button
                  onClick={handleResetZoom}
                  className="p-1 text-gray-400 hover:text-white transition-colors"
                  title="Reset Zoom"
                >
                  <RotateCcw className="h-4 w-4" />
                </button>
              </div>

              {/* Action Buttons */}
              <button
                onClick={() => setShowTemplateSelector(!showTemplateSelector)}
                className="flex items-center gap-2 px-3 py-2 bg-neural-purple/20 hover:bg-neural-purple/30 text-neural-purple border border-neural-purple/30 rounded-lg transition-colors"
                title="Change Template"
              >
                <Palette className="h-4 w-4" />
                <span className="hidden sm:inline">Template</span>
              </button>

              <button
                onClick={handlePrint}
                className="p-2 text-gray-400 hover:text-white transition-colors"
                title="Print"
              >
                <Printer className="h-4 w-4" />
              </button>

              <button
                onClick={handleDownloadPDF}
                disabled={isGenerating}
                className="flex items-center gap-2 px-3 py-2 bg-green-600 hover:bg-green-700 disabled:opacity-50 text-white rounded-lg transition-colors"
                title="Download PDF"
              >
                {isGenerating ? (
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                  >
                    <RotateCcw className="h-4 w-4" />
                  </motion.div>
                ) : (
                  <Download className="h-4 w-4" />
                )}
                <span className="hidden sm:inline">
                  {isGenerating ? 'Generating...' : 'Download'}
                </span>
              </button>

              <button
                onClick={onClose}
                className="p-2 text-gray-400 hover:text-white transition-colors"
                title="Close"
              >
                <X className="h-5 w-5" />
              </button>
            </div>
          </div>

          {/* Preview Content */}
          <div className="flex-1 overflow-auto bg-gray-100 p-6">
            <div className="flex justify-center">
              <motion.div
                className="bg-white shadow-2xl rounded-lg"
                style={{
                  transform: `scale(${zoom})`,
                  transformOrigin: 'top center',
                  minWidth: '8.5in',
                  minHeight: '11in'
                }}
                animate={{ scale: zoom }}
                transition={{ duration: 0.2 }}
              >
                <div 
                  ref={previewRef}
                  className="w-full h-full"
                  style={{
                    fontFamily: "'Inter', 'Helvetica Neue', Arial, sans-serif",
                    fontSize: '11pt',
                    lineHeight: '1.4',
                    color: '#000000',
                    backgroundColor: '#ffffff',
                    minHeight: '11in',
                    width: '8.5in'
                  }}
                >
                  {renderResumeContent()}
                </div>
              </motion.div>
            </div>
          </div>

          {/* Footer */}
          <div className="p-3 border-t border-gray-700 bg-gray-800/50 flex items-center justify-between text-sm flex-shrink-0">
            <div className="flex items-center gap-4 text-gray-400">
              <span>Template: {selectedTemplate?.replace('_', ' ') || 'Classic ATS'}</span>
              <span>•</span>
              <span>A4 Format (8.5" × 11")</span>
              <span>•</span>
              <span>ATS Optimized</span>
            </div>
            
            <div className="text-gray-400">
              Press <kbd className="px-1 py-0.5 bg-gray-700 rounded text-xs">Esc</kbd> to close
            </div>
          </div>
        </motion.div>

        {/* Loading Overlay */}
        <AnimatePresence>
          {isGenerating && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="absolute inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-10"
            >
              <div className="bg-gray-900 rounded-xl p-6 text-center border border-gray-700">
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                  className="w-8 h-8 border-4 border-neural-purple border-t-transparent rounded-full mx-auto mb-4"
                />
                <p className="text-white font-medium">Generating PDF...</p>
                <p className="text-gray-400 text-sm mt-1">This may take a few seconds</p>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>
    </AnimatePresence>
  );
};

export default FullscreenPreviewModal;

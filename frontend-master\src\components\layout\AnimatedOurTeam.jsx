'use client'
import { motion } from 'framer-motion'
import { Linkedin, Github, Twitter } from 'lucide-react'
import Image from 'next/image'

const TeamCard = ({ name, role, image, socialLinks, description }) => (
  <div className="glass-effect rounded-xl overflow-hidden border border-white/10 hover:border-primary/30 transition-colors">
    <div className="relative h-64 w-full">
      <Image 
        src={image} 
        alt={name} 
        fill
        className="object-cover"
      />
    </div>
    <div className="p-6">
      <h3 className="text-xl font-bold text-white">{name}</h3>
      <p className="text-primary mb-3">{role}</p>
      <p className="text-gray-400 mb-4">{description}</p>
      <div className="flex space-x-3">
        {socialLinks.linkedin && (
          <a href={socialLinks.linkedin} target="_blank" rel="noopener noreferrer" className="text-gray-400 hover:text-white">
            <Linkedin className="h-5 w-5" />
          </a>
        )}
        {socialLinks.github && (
          <a href={socialLinks.github} target="_blank" rel="noopener noreferrer" className="text-gray-400 hover:text-white">
            <Github className="h-5 w-5" />
          </a>
        )}
        {socialLinks.twitter && (
          <a href={socialLinks.twitter} target="_blank" rel="noopener noreferrer" className="text-gray-400 hover:text-white">
            <Twitter className="h-5 w-5" />
          </a>
        )}
      </div>
    </div>
  </div>
)

const OurTeam = () => {
  const teamMembers = [
   {
    name: "Abdullah Khan",
    role: "Founder | AI Lead",
    image: "/OurTeamImages/avatar/abdullah.jpg",
    description: "As the founder of BlinkFind, I’m Abdullah, and I’ve turned my passion for technology into a mission to help businesses grow.",
    socialLinks: {
      linkedin: "https://www.linkedin.com/in/abdullahkhanspn/",
      github: "https://github.com/Abdullahkhanspn"
    }
  },
  {
    name: "Zainab Fatima",
    role: "Co-Founder | AI & ML Lead",
    image: "/OurTeamImages/avatar/zainab.jpg",
    description: "As the co-founder of BlinkFind, I'm Zainab with 4 years of experience in AI and machine learning, I focus on developing smart and efficient solutions.",
    socialLinks: {
      linkedin: "https://www.linkedin.com/in/zainaboptique/",
      github: ""
    }
  },
  {
    name: "Md Sadique",
    role: "Web developer",
    image: "/OurTeamImages/avatar/sadique.jpg",
    description: "Dedicated to building interactive and responsive interfaces with a passion for delivering clean and efficient design",
    socialLinks: {
      linkedin: "https://www.linkedin.com/in/mdsadique5",
      github: "https://github.com/sadique-2004"
    }
  },
  {
    name: "Nitendra Singh",
    role: "Marketing Lead",
    image: "/OurTeamImages/avatar/nitendra.jpg",
    description: "Hi, I'm a Marketing lead at BlinkFind and a CSE final year student building my career in software development.",
    socialLinks: {
      linkedin: "https://www.linkedin.com/in/nitendra-singh-66018225b",
      github: ""
    }
  },
  {
    name: "Hamza Khan",
    role: "Penetration Tester",
    image: "/OurTeamImages/avatar/hamzah.jpg",
    description: "Experienced penetration tester committed to securing websites and applications through vulnerability analysis",
    socialLinks: {
      linkedin: "www.linkedin.com/in/hamzakhanspn",
      github: ""
    }
  }
  ]

  return (
    <section className="py-20 bg-gradient-to-b from-[#0A0A0A] to-black">
      <div className="container mx-auto px-6">
        <div className="mb-16">
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="flex items-center gap-2 mb-4"
          >
            <span className="text-sm font-medium text-primary">OUR TEAM</span>
          </motion.div>
          
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            viewport={{ once: true }}
            className="text-3xl md:text-4xl font-bold text-white max-w-2xl"
          >
            The brilliant minds behind our AI revolution
          </motion.h2>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {teamMembers.map((member, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <TeamCard {...member} />
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default OurTeam
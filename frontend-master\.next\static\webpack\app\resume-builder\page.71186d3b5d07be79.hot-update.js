"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/resume-builder/page",{

/***/ "(app-pages-browser)/./src/components/resume/forms/ProjectsForm.jsx":
/*!******************************************************!*\
  !*** ./src/components/resume/forms/ProjectsForm.jsx ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_ExternalLink_FileText_Github_Globe_Info_Plus_Save_Sparkles_Target_Trash2_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,ExternalLink,FileText,Github,Globe,Info,Plus,Save,Sparkles,Target,Trash2,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_ExternalLink_FileText_Github_Globe_Info_Plus_Save_Sparkles_Target_Trash2_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,ExternalLink,FileText,Github,Globe,Info,Plus,Save,Sparkles,Target,Trash2,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_ExternalLink_FileText_Github_Globe_Info_Plus_Save_Sparkles_Target_Trash2_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,ExternalLink,FileText,Github,Globe,Info,Plus,Save,Sparkles,Target,Trash2,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_ExternalLink_FileText_Github_Globe_Info_Plus_Save_Sparkles_Target_Trash2_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,ExternalLink,FileText,Github,Globe,Info,Plus,Save,Sparkles,Target,Trash2,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_ExternalLink_FileText_Github_Globe_Info_Plus_Save_Sparkles_Target_Trash2_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,ExternalLink,FileText,Github,Globe,Info,Plus,Save,Sparkles,Target,Trash2,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_ExternalLink_FileText_Github_Globe_Info_Plus_Save_Sparkles_Target_Trash2_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,ExternalLink,FileText,Github,Globe,Info,Plus,Save,Sparkles,Target,Trash2,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wand-sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_ExternalLink_FileText_Github_Globe_Info_Plus_Save_Sparkles_Target_Trash2_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,ExternalLink,FileText,Github,Globe,Info,Plus,Save,Sparkles,Target,Trash2,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_ExternalLink_FileText_Github_Globe_Info_Plus_Save_Sparkles_Target_Trash2_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,ExternalLink,FileText,Github,Globe,Info,Plus,Save,Sparkles,Target,Trash2,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/github.js\");\n/* harmony import */ var _SmartFormField__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./SmartFormField */ \"(app-pages-browser)/./src/components/resume/forms/SmartFormField.jsx\");\n/* harmony import */ var _common_ClientOnly__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../common/ClientOnly */ \"(app-pages-browser)/./src/components/common/ClientOnly.jsx\");\n/* harmony import */ var _ats_FieldIndicator__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../ats/FieldIndicator */ \"(app-pages-browser)/./src/components/resume/ats/FieldIndicator.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst EnhancedProjectsForm = (param)=>{\n    let { formData, updateFormData, addArrayItem, removeArrayItem, atsAnalysis, validationErrors = {}, showValidationErrors = false, onSave, onAISuggest } = param;\n    _s();\n    const [expandedItems, setExpandedItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set([\n        0\n    ]));\n    const [showAIHelper, setShowAIHelper] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const toggleExpanded = (index)=>{\n        const newExpanded = new Set(expandedItems);\n        if (newExpanded.has(index)) {\n            newExpanded.delete(index);\n        } else {\n            newExpanded.add(index);\n        }\n        setExpandedItems(newExpanded);\n    };\n    const addProject = ()=>{\n        const newIndex = formData.projects.length;\n        addArrayItem('projects', {\n            name: \"\",\n            description: \"\",\n            technologies: \"\",\n            link: \"\",\n            github: \"\",\n            startDate: \"\",\n            endDate: \"\"\n        });\n        setExpandedItems(new Set([\n            ...expandedItems,\n            newIndex\n        ]));\n    };\n    const removeProject = (index)=>{\n        removeArrayItem('projects', index);\n        const newExpanded = new Set(expandedItems);\n        newExpanded.delete(index);\n        setExpandedItems(newExpanded);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n        initial: {\n            opacity: 0,\n            y: 20\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        exit: {\n            opacity: 0,\n            y: -20\n        },\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-10 h-10 bg-neural-purple/20 rounded-xl flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_ExternalLink_FileText_Github_Globe_Info_Plus_Save_Sparkles_Target_Trash2_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-5 w-5 text-neural-purple\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ProjectsForm.jsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ProjectsForm.jsx\",\n                                lineNumber: 80,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-white\",\n                                        children: \"Projects\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ProjectsForm.jsx\",\n                                        lineNumber: 84,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: \"Showcase your personal and professional projects (Optional)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ProjectsForm.jsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ProjectsForm.jsx\",\n                                lineNumber: 83,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ProjectsForm.jsx\",\n                        lineNumber: 79,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: addProject,\n                            className: \"flex items-center gap-2 px-4 py-2 bg-neural-purple hover:bg-neural-purple/80 text-white rounded-xl transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_ExternalLink_FileText_Github_Globe_Info_Plus_Save_Sparkles_Target_Trash2_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ProjectsForm.jsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 13\n                                }, undefined),\n                                \"Add Project\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ProjectsForm.jsx\",\n                            lineNumber: 90,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ProjectsForm.jsx\",\n                        lineNumber: 89,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ProjectsForm.jsx\",\n                lineNumber: 78,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: -10\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                className: \"bg-blue-500/10 border border-blue-500/20 rounded-xl p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_ExternalLink_FileText_Github_Globe_Info_Plus_Save_Sparkles_Target_Trash2_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"h-4 w-4 text-blue-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ProjectsForm.jsx\",\n                                lineNumber: 107,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-blue-400 font-medium\",\n                                children: \"Optional Section\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ProjectsForm.jsx\",\n                                lineNumber: 108,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ProjectsForm.jsx\",\n                        lineNumber: 106,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-300 text-sm\",\n                        children: \"Projects help showcase your practical skills and experience. You can skip this section if you don't have relevant projects to include.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ProjectsForm.jsx\",\n                        lineNumber: 110,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ProjectsForm.jsx\",\n                lineNumber: 101,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.AnimatePresence, {\n                children: showValidationErrors && validationErrors.projects && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: -10\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    exit: {\n                        opacity: 0,\n                        y: -10\n                    },\n                    className: \"bg-red-500/10 border border-red-500/20 rounded-xl p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 mb-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_ExternalLink_FileText_Github_Globe_Info_Plus_Save_Sparkles_Target_Trash2_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-4 w-4 text-red-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ProjectsForm.jsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-red-400 font-medium\",\n                                    children: \"Projects Information\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ProjectsForm.jsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ProjectsForm.jsx\",\n                            lineNumber: 124,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-300 text-sm\",\n                            children: validationErrors.projects\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ProjectsForm.jsx\",\n                            lineNumber: 128,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ProjectsForm.jsx\",\n                    lineNumber: 118,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ProjectsForm.jsx\",\n                lineNumber: 116,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: formData.projects.map((project, index)=>{\n                    var _atsAnalysis_fieldAnalysis;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        className: \"bg-gray-900/40 backdrop-blur-sm rounded-2xl border border-white/10 overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 cursor-pointer hover:bg-white/5 transition-colors\",\n                                onClick: ()=>toggleExpanded(index),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 bg-neural-purple/20 rounded-lg flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_ExternalLink_FileText_Github_Globe_Info_Plus_Save_Sparkles_Target_Trash2_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"h-4 w-4 text-neural-purple\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ProjectsForm.jsx\",\n                                                        lineNumber: 150,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ProjectsForm.jsx\",\n                                                    lineNumber: 149,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold text-white\",\n                                                            children: project.name || \"Project \".concat(index + 1)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ProjectsForm.jsx\",\n                                                            lineNumber: 153,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-400 text-sm\",\n                                                            children: project.technologies || 'Technologies not specified'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ProjectsForm.jsx\",\n                                                            lineNumber: 156,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ProjectsForm.jsx\",\n                                                    lineNumber: 152,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ProjectsForm.jsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: (e)=>{\n                                                        e.stopPropagation();\n                                                        removeProject(index);\n                                                    },\n                                                    className: \"p-2 text-red-400 hover:bg-red-500/20 rounded-lg transition-colors\",\n                                                    title: \"Remove project\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_ExternalLink_FileText_Github_Globe_Info_Plus_Save_Sparkles_Target_Trash2_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ProjectsForm.jsx\",\n                                                        lineNumber: 170,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ProjectsForm.jsx\",\n                                                    lineNumber: 162,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                                    animate: {\n                                                        rotate: expandedItems.has(index) ? 180 : 0\n                                                    },\n                                                    transition: {\n                                                        duration: 0.2\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-5 h-5 text-gray-400\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M19 9l-7 7-7-7\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ProjectsForm.jsx\",\n                                                            lineNumber: 177,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ProjectsForm.jsx\",\n                                                        lineNumber: 176,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ProjectsForm.jsx\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ProjectsForm.jsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ProjectsForm.jsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ProjectsForm.jsx\",\n                                lineNumber: 143,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.AnimatePresence, {\n                                children: expandedItems.has(index) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                    initial: {\n                                        height: 0,\n                                        opacity: 0\n                                    },\n                                    animate: {\n                                        height: 'auto',\n                                        opacity: 1\n                                    },\n                                    exit: {\n                                        height: 0,\n                                        opacity: 0\n                                    },\n                                    transition: {\n                                        duration: 0.3\n                                    },\n                                    className: \"border-t border-white/10\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6 space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SmartFormField__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        label: \"Project Name\",\n                                                        type: \"text\",\n                                                        value: project.name,\n                                                        onChange: (e)=>updateFormData('projects', 'name', e.target.value, index),\n                                                        placeholder: \"My Awesome Project\",\n                                                        icon: _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_ExternalLink_FileText_Github_Globe_Info_Plus_Save_Sparkles_Target_Trash2_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                                                        hint: \"Give your project a clear, descriptive name\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ProjectsForm.jsx\",\n                                                        lineNumber: 197,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SmartFormField__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        label: \"Technologies Used\",\n                                                        type: \"text\",\n                                                        value: project.technologies,\n                                                        onChange: (e)=>updateFormData('projects', 'technologies', e.target.value, index),\n                                                        placeholder: \"React, Node.js, MongoDB, AWS\",\n                                                        icon: _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_ExternalLink_FileText_Github_Globe_Info_Plus_Save_Sparkles_Target_Trash2_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n                                                        hint: \"List the main technologies, frameworks, and tools used\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ProjectsForm.jsx\",\n                                                        lineNumber: 208,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SmartFormField__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        label: \"Live Demo URL\",\n                                                        type: \"url\",\n                                                        value: project.link,\n                                                        onChange: (e)=>updateFormData('projects', 'link', e.target.value, index),\n                                                        placeholder: \"https://myproject.com\",\n                                                        icon: _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_ExternalLink_FileText_Github_Globe_Info_Plus_Save_Sparkles_Target_Trash2_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n                                                        hint: \"Link to the live project or demo\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ProjectsForm.jsx\",\n                                                        lineNumber: 219,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SmartFormField__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        label: \"GitHub Repository\",\n                                                        type: \"url\",\n                                                        value: project.github,\n                                                        onChange: (e)=>updateFormData('projects', 'github', e.target.value, index),\n                                                        placeholder: \"https://github.com/username/project\",\n                                                        icon: _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_ExternalLink_FileText_Github_Globe_Info_Plus_Save_Sparkles_Target_Trash2_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n                                                        hint: \"Link to the source code repository\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ProjectsForm.jsx\",\n                                                        lineNumber: 230,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ProjectsForm.jsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SmartFormField__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                label: \"Project Description\",\n                                                type: \"textarea\",\n                                                value: project.description,\n                                                onChange: (e)=>updateFormData('projects', 'description', e.target.value, index),\n                                                placeholder: \"Describe what the project does, the problem it solves, and your role in developing it...\",\n                                                rows: 4,\n                                                maxLength: 500,\n                                                showCharCount: true,\n                                                hint: \"Focus on the impact, challenges solved, and your specific contributions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ProjectsForm.jsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_common_ClientOnly__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ats_FieldIndicator__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    fieldName: \"project_\".concat(index, \"_description\"),\n                                                    value: project.description,\n                                                    analysis: atsAnalysis === null || atsAnalysis === void 0 ? void 0 : (_atsAnalysis_fieldAnalysis = atsAnalysis.fieldAnalysis) === null || _atsAnalysis_fieldAnalysis === void 0 ? void 0 : _atsAnalysis_fieldAnalysis[\"project_\".concat(index, \"_description\")],\n                                                    showDetails: true\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ProjectsForm.jsx\",\n                                                    lineNumber: 255,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ProjectsForm.jsx\",\n                                                lineNumber: 254,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ProjectsForm.jsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ProjectsForm.jsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ProjectsForm.jsx\",\n                                lineNumber: 185,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, index, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ProjectsForm.jsx\",\n                        lineNumber: 136,\n                        columnNumber: 11\n                    }, undefined);\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ProjectsForm.jsx\",\n                lineNumber: 134,\n                columnNumber: 7\n            }, undefined),\n            formData.projects.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                initial: {\n                    opacity: 0\n                },\n                animate: {\n                    opacity: 1\n                },\n                className: \"text-center py-12 bg-gray-900/20 rounded-2xl border border-white/10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_ExternalLink_FileText_Github_Globe_Info_Plus_Save_Sparkles_Target_Trash2_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"h-12 w-12 text-gray-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ProjectsForm.jsx\",\n                        lineNumber: 277,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-400 mb-2\",\n                        children: \"No Projects Added\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ProjectsForm.jsx\",\n                        lineNumber: 278,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-500 mb-4\",\n                        children: \"Projects help showcase your practical skills and experience\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ProjectsForm.jsx\",\n                        lineNumber: 279,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: addProject,\n                        className: \"inline-flex items-center gap-2 px-4 py-2 bg-neural-purple hover:bg-neural-purple/80 text-white rounded-lg transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_ExternalLink_FileText_Github_Globe_Info_Plus_Save_Sparkles_Target_Trash2_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ProjectsForm.jsx\",\n                                lineNumber: 284,\n                                columnNumber: 13\n                            }, undefined),\n                            \"Add Your First Project\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ProjectsForm.jsx\",\n                        lineNumber: 280,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ProjectsForm.jsx\",\n                lineNumber: 272,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-900/40 backdrop-blur-sm rounded-xl p-4 border border-white/10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between text-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-400\",\n                                children: \"Projects Section Progress\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ProjectsForm.jsx\",\n                                lineNumber: 293,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-neural-purple font-medium\",\n                                children: [\n                                    formData.projects.filter((proj)=>proj.name && proj.description).length,\n                                    \" / \",\n                                    formData.projects.length,\n                                    \" completed\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ProjectsForm.jsx\",\n                                lineNumber: 294,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ProjectsForm.jsx\",\n                        lineNumber: 292,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full bg-gray-700 rounded-full h-2 mt-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                            className: \"bg-gradient-to-r from-neural-purple to-neural-pink h-2 rounded-full\",\n                            initial: {\n                                width: 0\n                            },\n                            animate: {\n                                width: \"\".concat(formData.projects.length > 0 ? formData.projects.filter((proj)=>proj.name && proj.description).length / formData.projects.length * 100 : 0, \"%\")\n                            },\n                            transition: {\n                                duration: 0.5\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ProjectsForm.jsx\",\n                            lineNumber: 299,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ProjectsForm.jsx\",\n                        lineNumber: 298,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ProjectsForm.jsx\",\n                lineNumber: 291,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\ProjectsForm.jsx\",\n        lineNumber: 71,\n        columnNumber: 5\n    }, undefined);\n};\n_s(EnhancedProjectsForm, \"ds7NBpuTfX1bU6cXsV6rEVzyiyc=\");\n_c = EnhancedProjectsForm;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (EnhancedProjectsForm);\nvar _c;\n$RefreshReg$(_c, \"EnhancedProjectsForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/resume/forms/ProjectsForm.jsx\n"));

/***/ })

});
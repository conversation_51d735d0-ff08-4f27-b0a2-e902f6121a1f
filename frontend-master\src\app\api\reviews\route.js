import { NextResponse } from 'next/server';
import fs from 'fs/promises';
import path from 'path';

const reviewsFilePath = path.join(process.cwd(), 'reviews-data.json');

export async function GET() {
  try {
    const data = await fs.readFile(reviewsFilePath, 'utf8');
    const reviews = JSON.parse(data);
    return NextResponse.json(reviews);
  } catch (error) {
    // If file doesn't exist, return empty array
    const initialData = { reviews: [] };
    await fs.writeFile(reviewsFilePath, JSON.stringify(initialData, null, 2));
    return NextResponse.json(initialData);
  }
}

export async function POST(request) {
  try {
    const reviewData = await request.json();

    // Validate required fields
    if (!reviewData.rating || !reviewData.review || !reviewData.userId) {
      return NextResponse.json(
        { error: 'Missing required fields: rating, review, userId' },
        { status: 400 }
      );
    }

    // Validate rating range
    if (reviewData.rating < 1 || reviewData.rating > 5) {
      return NextResponse.json(
        { error: 'Rating must be between 1 and 5' },
        { status: 400 }
      );
    }

    // Validate review length
    if (reviewData.review.trim().length < 10) {
      return NextResponse.json(
        { error: 'Review must be at least 10 characters long' },
        { status: 400 }
      );
    }

    // Read existing reviews
    let existingData = { reviews: [] };
    try {
      const fileContent = await fs.readFile(reviewsFilePath, 'utf8');
      existingData = JSON.parse(fileContent);
    } catch (error) {
      // File doesn't exist or is corrupted, use initial data
      console.log('Creating new reviews file or using empty data:', error.message);
      existingData = { reviews: [] };
    }

    // Check if user has already reviewed
    const existingReview = existingData.reviews.find(
      review => review.userId === reviewData.userId
    );

    if (existingReview) {
      return NextResponse.json(
        { error: 'User has already submitted a review' },
        { status: 409 }
      );
    }

    // Create new review
    const newReview = {
      id: Date.now().toString(),
      rating: parseInt(reviewData.rating),
      review: reviewData.review.trim(),
      name: reviewData.name?.trim() || 'Anonymous User',
      userId: reviewData.userId,
      date: reviewData.date || new Date().toISOString(),
      approved: true, // Auto-approve for now
      featured: false
    };

    // Add to reviews array
    existingData.reviews.push(newReview);

    // Sort by date (newest first)
    existingData.reviews.sort((a, b) => new Date(b.date) - new Date(a.date));

    // Save to file with better error handling
    try {
      await fs.writeFile(reviewsFilePath, JSON.stringify(existingData, null, 2));
    } catch (writeError) {
      console.error('Error writing reviews file:', writeError);
      return NextResponse.json(
        { error: 'Failed to save review to storage' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Review submitted successfully',
      review: newReview
    });

  } catch (error) {
    console.error('Error processing review submission:', error);
    return NextResponse.json(
      {
        error: 'Failed to process review submission',
        details: error.message
      },
      { status: 500 }
    );
  }
}

'use client';

import { useState, useEffect } from 'react';
import { Play, CheckCircle, AlertCircle, Database, Shield, Users } from 'lucide-react';

const AdminIntegrationTest = () => {
  const [testResults, setTestResults] = useState({});
  const [isRunning, setIsRunning] = useState(false);
  const [currentTest, setCurrentTest] = useState('');

  const runCompleteTest = async () => {
    setIsRunning(true);
    const results = {};

    try {
      // Test 1: Environment Configuration
      setCurrentTest('Testing Environment Configuration...');
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const envTest = {
        firebaseConfig: !!(
          process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID &&
          process.env.NEXT_PUBLIC_FIREBASE_API_KEY &&
          process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN
        ),
        adminConfig: !!(process.env.NEXT_PUBLIC_ADMIN_DOMAIN),
        projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
        authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
        adminDomain: process.env.NEXT_PUBLIC_ADMIN_DOMAIN
      };
      
      results.environment = {
        status: envTest.firebaseConfig ? 'pass' : 'fail',
        details: envTest
      };

      // Test 2: Admin API Endpoints
      setCurrentTest('Testing Admin API Endpoints...');
      await new Promise(resolve => setTimeout(resolve, 500));
      
      try {
        // Test GET endpoint
        const getResponse = await fetch('/api/admin');
        const getResult = getResponse.ok ? await getResponse.json() : null;
        
        // Test POST endpoint
        const postResponse = await fetch('/api/admin', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            action: 'checkAdmin',
            email: '<EMAIL>'
          })
        });
        const postResult = postResponse.ok ? await postResponse.json() : null;
        
        results.adminAPI = {
          status: getResponse.ok && postResponse.ok ? 'pass' : 'fail',
          details: {
            get: getResult,
            post: postResult,
            getStatus: getResponse.status,
            postStatus: postResponse.status
          }
        };
      } catch (error) {
        results.adminAPI = {
          status: 'fail',
          error: error.message
        };
      }

      // Test 3: Firebase Connection
      setCurrentTest('Testing Firebase Connection...');
      await new Promise(resolve => setTimeout(resolve, 500));
      
      try {
        const { testFirebaseConnection } = await import('@/firebase/test');
        const firebaseTest = await testFirebaseConnection();
        results.firebase = {
          status: firebaseTest.firestore && firebaseTest.realtimeDatabase ? 'pass' : 'warn',
          details: firebaseTest
        };
      } catch (error) {
        results.firebase = {
          status: 'fail',
          error: error.message
        };
      }

      // Test 4: Component Imports
      setCurrentTest('Testing Component Imports...');
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const componentTests = {
        adminContext: false,
        adminLogin: false,
        adminDashboard: false,
        adminGuard: false
      };

      try {
        await import('@/contexts/AdminContext');
        componentTests.adminContext = true;
      } catch (error) {
        console.warn('AdminContext import failed:', error.message);
      }

      try {
        await import('@/components/admin/AdminLogin');
        componentTests.adminLogin = true;
      } catch (error) {
        console.warn('AdminLogin import failed:', error.message);
      }

      try {
        await import('@/components/admin/AdminDashboard');
        componentTests.adminDashboard = true;
      } catch (error) {
        console.warn('AdminDashboard import failed:', error.message);
      }

      try {
        await import('@/components/admin/AdminGuard');
        componentTests.adminGuard = true;
      } catch (error) {
        console.warn('AdminGuard import failed:', error.message);
      }

      const allComponentsLoaded = Object.values(componentTests).every(test => test);
      results.components = {
        status: allComponentsLoaded ? 'pass' : 'warn',
        details: componentTests
      };

      // Test 5: Route Protection
      setCurrentTest('Testing Route Protection...');
      await new Promise(resolve => setTimeout(resolve, 500));
      
      try {
        const adminPageResponse = await fetch('/admin');
        results.routeProtection = {
          status: adminPageResponse.ok ? 'pass' : 'warn',
          details: {
            adminPageStatus: adminPageResponse.status,
            adminPageAccessible: adminPageResponse.ok
          }
        };
      } catch (error) {
        results.routeProtection = {
          status: 'fail',
          error: error.message
        };
      }

      setCurrentTest('Tests Complete!');
      setTestResults(results);

    } catch (error) {
      setTestResults({
        error: {
          status: 'fail',
          message: error.message
        }
      });
    } finally {
      setIsRunning(false);
      setCurrentTest('');
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'pass': return <CheckCircle className="w-5 h-5 text-green-400" />;
      case 'warn': return <AlertCircle className="w-5 h-5 text-yellow-400" />;
      case 'fail': return <AlertCircle className="w-5 h-5 text-red-400" />;
      default: return <AlertCircle className="w-5 h-5 text-gray-400" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'pass': return 'text-green-400';
      case 'warn': return 'text-yellow-400';
      case 'fail': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  const getOverallStatus = () => {
    const statuses = Object.values(testResults).map(result => result.status);
    if (statuses.includes('fail')) return 'fail';
    if (statuses.includes('warn')) return 'warn';
    if (statuses.length > 0 && statuses.every(status => status === 'pass')) return 'pass';
    return 'unknown';
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 p-8">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center gap-3 mb-4">
            <Shield className="w-8 h-8 text-purple-400" />
            <h1 className="text-3xl font-bold text-white">Admin System Integration Test</h1>
          </div>
          <p className="text-gray-300">Comprehensive testing of the BlinkFind admin authentication system</p>
        </div>

        {/* Test Runner */}
        <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg p-6 mb-8">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold text-white">System Integration Test</h2>
            <button
              onClick={runCompleteTest}
              disabled={isRunning}
              className="bg-purple-500 hover:bg-purple-600 disabled:bg-purple-500/50 text-white px-6 py-2 rounded-lg transition-colors flex items-center gap-2"
            >
              {isRunning ? (
                <>
                  <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                  Running...
                </>
              ) : (
                <>
                  <Play className="w-4 h-4" />
                  Run Complete Test
                </>
              )}
            </button>
          </div>

          {isRunning && currentTest && (
            <div className="mb-4 p-3 bg-blue-500/10 border border-blue-500/20 rounded-lg">
              <div className="text-blue-300 text-sm">{currentTest}</div>
            </div>
          )}

          {Object.keys(testResults).length > 0 && (
            <div className="mb-4">
              <div className={`text-lg font-medium mb-4 ${getStatusColor(getOverallStatus())}`}>
                Overall Status: {getOverallStatus().toUpperCase()}
              </div>
            </div>
          )}
        </div>

        {/* Test Results */}
        {Object.keys(testResults).length > 0 && (
          <div className="space-y-6">
            {testResults.environment && (
              <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg p-6">
                <div className="flex items-center gap-3 mb-4">
                  {getStatusIcon(testResults.environment.status)}
                  <h3 className="text-lg font-semibold text-white">Environment Configuration</h3>
                </div>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-300">Firebase Config:</span>
                    <span className={testResults.environment.details.firebaseConfig ? 'text-green-400' : 'text-red-400'}>
                      {testResults.environment.details.firebaseConfig ? '✅ Valid' : '❌ Missing'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-300">Admin Config:</span>
                    <span className={testResults.environment.details.adminConfig ? 'text-green-400' : 'text-red-400'}>
                      {testResults.environment.details.adminConfig ? '✅ Valid' : '❌ Missing'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-300">Project ID:</span>
                    <span className="text-white font-mono text-xs">
                      {testResults.environment.details.projectId || 'Not configured'}
                    </span>
                  </div>
                </div>
              </div>
            )}

            {testResults.adminAPI && (
              <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg p-6">
                <div className="flex items-center gap-3 mb-4">
                  {getStatusIcon(testResults.adminAPI.status)}
                  <h3 className="text-lg font-semibold text-white">Admin API Endpoints</h3>
                </div>
                <div className="space-y-2 text-sm">
                  {testResults.adminAPI.details && (
                    <>
                      <div className="flex justify-between">
                        <span className="text-gray-300">GET /api/admin:</span>
                        <span className={testResults.adminAPI.details.getStatus === 200 ? 'text-green-400' : 'text-red-400'}>
                          {testResults.adminAPI.details.getStatus === 200 ? '✅ Success' : `❌ ${testResults.adminAPI.details.getStatus}`}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-300">POST /api/admin:</span>
                        <span className={testResults.adminAPI.details.postStatus === 200 ? 'text-green-400' : 'text-red-400'}>
                          {testResults.adminAPI.details.postStatus === 200 ? '✅ Success' : `❌ ${testResults.adminAPI.details.postStatus}`}
                        </span>
                      </div>
                    </>
                  )}
                  {testResults.adminAPI.error && (
                    <div className="text-red-300">Error: {testResults.adminAPI.error}</div>
                  )}
                </div>
              </div>
            )}

            {testResults.firebase && (
              <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg p-6">
                <div className="flex items-center gap-3 mb-4">
                  {getStatusIcon(testResults.firebase.status)}
                  <h3 className="text-lg font-semibold text-white">Firebase Connection</h3>
                </div>
                <div className="space-y-2 text-sm">
                  {testResults.firebase.details && (
                    <>
                      <div className="flex justify-between">
                        <span className="text-gray-300">Firestore:</span>
                        <span className={testResults.firebase.details.firestore ? 'text-green-400' : 'text-red-400'}>
                          {testResults.firebase.details.firestore ? '✅ Connected' : '❌ Failed'}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-300">Realtime DB:</span>
                        <span className={testResults.firebase.details.realtimeDatabase ? 'text-green-400' : 'text-red-400'}>
                          {testResults.firebase.details.realtimeDatabase ? '✅ Connected' : '❌ Failed'}
                        </span>
                      </div>
                    </>
                  )}
                  {testResults.firebase.error && (
                    <div className="text-red-300">Error: {testResults.firebase.error}</div>
                  )}
                </div>
              </div>
            )}

            {testResults.components && (
              <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg p-6">
                <div className="flex items-center gap-3 mb-4">
                  {getStatusIcon(testResults.components.status)}
                  <h3 className="text-lg font-semibold text-white">Component Imports</h3>
                </div>
                <div className="space-y-2 text-sm">
                  {Object.entries(testResults.components.details).map(([component, status]) => (
                    <div key={component} className="flex justify-between">
                      <span className="text-gray-300">{component}:</span>
                      <span className={status ? 'text-green-400' : 'text-red-400'}>
                        {status ? '✅ Loaded' : '❌ Failed'}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {testResults.routeProtection && (
              <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg p-6">
                <div className="flex items-center gap-3 mb-4">
                  {getStatusIcon(testResults.routeProtection.status)}
                  <h3 className="text-lg font-semibold text-white">Route Protection</h3>
                </div>
                <div className="space-y-2 text-sm">
                  {testResults.routeProtection.details && (
                    <div className="flex justify-between">
                      <span className="text-gray-300">Admin Page Access:</span>
                      <span className={testResults.routeProtection.details.adminPageAccessible ? 'text-green-400' : 'text-yellow-400'}>
                        {testResults.routeProtection.details.adminPageAccessible ? '✅ Accessible' : '⚠️ Protected'}
                      </span>
                    </div>
                  )}
                  {testResults.routeProtection.error && (
                    <div className="text-red-300">Error: {testResults.routeProtection.error}</div>
                  )}
                </div>
              </div>
            )}
          </div>
        )}

        {/* Quick Actions */}
        <div className="mt-8 bg-white/5 backdrop-blur-sm border border-white/10 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-white mb-4">Quick Actions</h3>
          <div className="flex gap-4 flex-wrap">
            <button
              onClick={() => window.open('/admin', '_blank')}
              className="bg-purple-500/20 hover:bg-purple-500/30 text-purple-300 px-4 py-2 rounded-lg transition-colors flex items-center gap-2"
            >
              <Shield className="w-4 h-4" />
              Open Admin Panel
            </button>
            <button
              onClick={() => window.open('/api/admin', '_blank')}
              className="bg-blue-500/20 hover:bg-blue-500/30 text-blue-300 px-4 py-2 rounded-lg transition-colors flex items-center gap-2"
            >
              <Database className="w-4 h-4" />
              Test API Endpoint
            </button>
            <button
              onClick={() => window.open('/', '_blank')}
              className="bg-green-500/20 hover:bg-green-500/30 text-green-300 px-4 py-2 rounded-lg transition-colors flex items-center gap-2"
            >
              <Users className="w-4 h-4" />
              Open Main Site
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminIntegrationTest;

"use client";
import React from 'react';
import { motion } from 'framer-motion';
import { 
  CheckCircle, 
  AlertTriangle, 
  XCircle, 
  TrendingUp, 
  Target,
  Award,
  FileText,
  Zap
} from 'lucide-react';
import ATSScoreCircle from './ScoreCircle';

const ATSAnalysisDisplay = ({ analysisData, analysisType = 'full' }) => {
  const { atsScore, analysis, keywordAnalysis, enhancements, fallback } = analysisData;

  const getScoreColor = (score) => {
    if (score >= 81) return 'text-green-400';
    if (score >= 61) return 'text-yellow-400';
    return 'text-red-400';
  };

  const getScoreIcon = (score) => {
    if (score >= 81) return <CheckCircle className="h-5 w-5 text-green-400" />;
    if (score >= 61) return <AlertTriangle className="h-5 w-5 text-yellow-400" />;
    return <XCircle className="h-5 w-5 text-red-400" />;
  };

  const ScoreBreakdownCard = ({ title, score, icon: Icon }) => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-gray-800/50 backdrop-blur-md rounded-xl p-4 border border-gray-700"
    >
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center gap-2">
          <Icon className="h-4 w-4 text-neural-purple" />
          <span className="text-sm font-medium text-gray-300">{title}</span>
        </div>
        {getScoreIcon(score)}
      </div>
      <div className="flex items-center justify-between">
        <span className={`text-2xl font-bold ${getScoreColor(score)}`}>
          {score}%
        </span>
        <div className="w-16 bg-gray-700 rounded-full h-2">
          <div
            className={`h-2 rounded-full ${
              score >= 81 ? 'bg-green-400' : score >= 61 ? 'bg-yellow-400' : 'bg-red-400'
            }`}
            style={{ width: `${score}%` }}
          />
        </div>
      </div>
    </motion.div>
  );

  return (
    <div className="space-y-6">
      {/* Fallback Notice */}
      {fallback && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-yellow-900/20 backdrop-blur-md rounded-xl p-4 border border-yellow-500/30"
        >
          <div className="flex items-center gap-2 text-yellow-400">
            <AlertTriangle className="h-5 w-5" />
            <span className="font-semibold">Manual Review Required</span>
          </div>
          <p className="text-yellow-300 text-sm mt-2">
            Your file was uploaded successfully, but automatic text extraction was limited.
            Please review and edit the form manually for the best results.
          </p>
        </motion.div>
      )}

      {/* Overall ATS Score */}
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className="text-center"
      >
        <h2 className="text-2xl font-bold text-white mb-6">
          {analysisType === 'quick' ? 'Quick ATS Analysis' : 'Comprehensive Resume Analysis'}
        </h2>
        <ATSScoreCircle score={atsScore?.overall || 0} size={160} />
      </motion.div>

      {/* Score Breakdown */}
      {atsScore?.breakdown && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
            <Target className="h-5 w-5 text-neural-purple" />
            Score Breakdown
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <ScoreBreakdownCard
              title="Keywords"
              score={atsScore.breakdown.keywords}
              icon={Zap}
            />
            <ScoreBreakdownCard
              title="Formatting"
              score={atsScore.breakdown.formatting}
              icon={FileText}
            />
            <ScoreBreakdownCard
              title="Structure"
              score={atsScore.breakdown.structure}
              icon={Award}
            />
            {atsScore.breakdown.achievements && (
              <ScoreBreakdownCard
                title="Achievements"
                score={atsScore.breakdown.achievements}
                icon={TrendingUp}
              />
            )}
            {atsScore.breakdown.skills && (
              <ScoreBreakdownCard
                title="Skills"
                score={atsScore.breakdown.skills}
                icon={Target}
              />
            )}
          </div>
        </motion.div>
      )}

      {/* Analysis Results */}
      {analysis && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Strengths */}
          {analysis.strengths && analysis.strengths.length > 0 && (
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.3 }}
              className="bg-green-900/20 backdrop-blur-md rounded-xl p-6 border border-green-500/30"
            >
              <h4 className="text-lg font-semibold text-green-400 mb-4 flex items-center gap-2">
                <CheckCircle className="h-5 w-5" />
                Strengths
              </h4>
              <ul className="space-y-2">
                {analysis.strengths.map((strength, index) => (
                  <li key={index} className="flex items-start gap-2 text-gray-300">
                    <div className="w-2 h-2 bg-green-400 rounded-full mt-2 flex-shrink-0" />
                    {strength}
                  </li>
                ))}
              </ul>
            </motion.div>
          )}

          {/* Weaknesses */}
          {analysis.weaknesses && analysis.weaknesses.length > 0 && (
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.4 }}
              className="bg-red-900/20 backdrop-blur-md rounded-xl p-6 border border-red-500/30"
            >
              <h4 className="text-lg font-semibold text-red-400 mb-4 flex items-center gap-2">
                <XCircle className="h-5 w-5" />
                Areas for Improvement
              </h4>
              <ul className="space-y-2">
                {analysis.weaknesses.map((weakness, index) => (
                  <li key={index} className="flex items-start gap-2 text-gray-300">
                    <div className="w-2 h-2 bg-red-400 rounded-full mt-2 flex-shrink-0" />
                    {weakness}
                  </li>
                ))}
              </ul>
            </motion.div>
          )}
        </div>
      )}

      {/* Recommendations */}
      {(analysis?.recommendations || enhancements?.suggestions) && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="bg-neural-purple/10 backdrop-blur-md rounded-xl p-6 border border-neural-purple/30"
        >
          <h4 className="text-lg font-semibold text-neural-purple mb-4 flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            AI Recommendations
          </h4>
          <div className="space-y-3">
            {(analysis?.recommendations || enhancements?.suggestions || []).map((recommendation, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.6 + index * 0.1 }}
                className="flex items-start gap-3 p-3 bg-neural-purple/5 rounded-lg border border-neural-purple/20"
              >
                <div className="w-6 h-6 bg-neural-purple/20 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-xs font-bold text-neural-purple">{index + 1}</span>
                </div>
                <p className="text-gray-300 text-sm leading-relaxed">
                  {typeof recommendation === 'string' ? recommendation : recommendation.message || recommendation}
                </p>
              </motion.div>
            ))}
          </div>
        </motion.div>
      )}

      {/* Keyword Analysis */}
      {keywordAnalysis && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="bg-gray-800/50 backdrop-blur-md rounded-xl p-6 border border-gray-700"
        >
          <h4 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
            <Zap className="h-5 w-5 text-neural-blue" />
            Keyword Analysis
          </h4>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {keywordAnalysis.found && keywordAnalysis.found.length > 0 && (
              <div>
                <h5 className="font-semibold text-green-400 mb-2">Found Keywords</h5>
                <div className="flex flex-wrap gap-2">
                  {keywordAnalysis.found.map((keyword, index) => (
                    <span
                      key={index}
                      className="px-2 py-1 bg-green-900/30 text-green-300 rounded-md text-xs border border-green-500/30"
                    >
                      {keyword}
                    </span>
                  ))}
                </div>
              </div>
            )}

            {keywordAnalysis.missing && keywordAnalysis.missing.length > 0 && (
              <div>
                <h5 className="font-semibold text-red-400 mb-2">Missing Keywords</h5>
                <div className="flex flex-wrap gap-2">
                  {keywordAnalysis.missing.map((keyword, index) => (
                    <span
                      key={index}
                      className="px-2 py-1 bg-red-900/30 text-red-300 rounded-md text-xs border border-red-500/30"
                    >
                      {keyword}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>

          {keywordAnalysis.suggestions && keywordAnalysis.suggestions.length > 0 && (
            <div className="mt-4">
              <h5 className="font-semibold text-neural-blue mb-2">Keyword Suggestions</h5>
              <ul className="space-y-1">
                {keywordAnalysis.suggestions.map((suggestion, index) => (
                  <li key={index} className="text-sm text-gray-300 flex items-center gap-2">
                    <div className="w-1 h-1 bg-neural-blue rounded-full" />
                    {suggestion}
                  </li>
                ))}
              </ul>
            </div>
          )}
        </motion.div>
      )}
    </div>
  );
};

export default ATSAnalysisDisplay;

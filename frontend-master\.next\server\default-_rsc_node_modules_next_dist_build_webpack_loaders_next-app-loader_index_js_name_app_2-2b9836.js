/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "default-_rsc_node_modules_next_dist_build_webpack_loaders_next-app-loader_index_js_name_app_2-2b9836";
exports.ids = ["default-_rsc_node_modules_next_dist_build_webpack_loaders_next-app-loader_index_js_name_app_2-2b9836"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.jsx&appDir=C%3A%5CProjects%5CNewBlinkFindAI%5Cfrontend-master%5Csrc%5Capp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&rootDir=C%3A%5CProjects%5CNewBlinkFindAI%5Cfrontend-master&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.jsx&appDir=C%3A%5CProjects%5CNewBlinkFindAI%5Cfrontend-master%5Csrc%5Capp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&rootDir=C%3A%5CProjects%5CNewBlinkFindAI%5Cfrontend-master&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.jsx */ \"(rsc)/./src/app/layout.jsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.jsx */ \"(rsc)/./src/app/page.jsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\page.jsx\"],\n          metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n        }]\n      },\n        {\n        'layout': [module0, \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\layout.jsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\page.jsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.jsx&appDir=C%3A%5CProjects%5CNewBlinkFindAI%5Cfrontend-master%5Csrc%5Capp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&rootDir=C%3A%5CProjects%5CNewBlinkFindAI%5Cfrontend-master&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CNewBlinkFindAI%5C%5Cfrontend-master%5C%5Csrc%5C%5Capp%5C%5Cpage.jsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CNewBlinkFindAI%5C%5Cfrontend-master%5C%5Csrc%5C%5Capp%5C%5Cpage.jsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.jsx */ \"(rsc)/./src/app/page.jsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNQcm9qZWN0cyU1QyU1Q05ld0JsaW5rRmluZEFJJTVDJTVDZnJvbnRlbmQtbWFzdGVyJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDcGFnZS5qc3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdKQUFzRyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcUHJvamVjdHNcXFxcTmV3QmxpbmtGaW5kQUlcXFxcZnJvbnRlbmQtbWFzdGVyXFxcXHNyY1xcXFxhcHBcXFxccGFnZS5qc3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CNewBlinkFindAI%5C%5Cfrontend-master%5C%5Csrc%5C%5Capp%5C%5Cpage.jsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./src/app/page.jsx":
/*!**************************!*\
  !*** ./src/app/page.jsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\page.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Projects\\NewBlinkFindAI\\frontend-master\\src\\app\\page.jsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CNewBlinkFindAI%5C%5Cfrontend-master%5C%5Csrc%5C%5Capp%5C%5Cpage.jsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CNewBlinkFindAI%5C%5Cfrontend-master%5C%5Csrc%5C%5Capp%5C%5Cpage.jsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.jsx */ \"(ssr)/./src/app/page.jsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNQcm9qZWN0cyU1QyU1Q05ld0JsaW5rRmluZEFJJTVDJTVDZnJvbnRlbmQtbWFzdGVyJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDcGFnZS5qc3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdKQUFzRyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcUHJvamVjdHNcXFxcTmV3QmxpbmtGaW5kQUlcXFxcZnJvbnRlbmQtbWFzdGVyXFxcXHNyY1xcXFxhcHBcXFxccGFnZS5qc3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CNewBlinkFindAI%5C%5Cfrontend-master%5C%5Csrc%5C%5Capp%5C%5Cpage.jsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.jsx":
/*!**************************!*\
  !*** ./src/app/page.jsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_features_ContactUs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/features/ContactUs */ \"(ssr)/./src/components/features/ContactUs.jsx\");\n/* harmony import */ var _components_layout_Hero__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/Hero */ \"(ssr)/./src/components/layout/Hero.jsx\");\n/* harmony import */ var _components_features_ShowcaseProduct__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/features/ShowcaseProduct */ \"(ssr)/./src/components/features/ShowcaseProduct.jsx\");\n/* harmony import */ var _components_features_ReviewsSection__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/features/ReviewsSection */ \"(ssr)/./src/components/features/ReviewsSection.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n// import NewsAndUpdate from \"@/oldComponents/NewsAndUpdate\";\n// import SimplifiedTestimonialCarousel from \"@/components/QuickfindTestimonial\";\n// import FAQ from \"@/components/FAQ\";\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \" bg-[#F8FFF8]\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Hero__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\page.jsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_features_ShowcaseProduct__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\page.jsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_features_ReviewsSection__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\page.jsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_features_ContactUs__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\page.jsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\app\\\\page.jsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL3BhZ2UuanN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBRXdEO0FBQ1o7QUFDd0I7QUFDRjtBQUNsRSw2REFBNkQ7QUFDN0QsaUZBQWlGO0FBQ2pGLHNDQUFzQztBQUV2QixTQUFTSTtJQUN0QixxQkFDRSw4REFBQ0M7UUFBSUMsV0FBVTs7MEJBQ2IsOERBQUNMLCtEQUFJQTs7Ozs7MEJBQ0wsOERBQUNDLDRFQUFlQTs7Ozs7MEJBQ2hCLDhEQUFDQywyRUFBY0E7Ozs7OzBCQUtmLDhEQUFDSCxzRUFBU0E7Ozs7Ozs7Ozs7O0FBR2hCIiwic291cmNlcyI6WyJDOlxcUHJvamVjdHNcXE5ld0JsaW5rRmluZEFJXFxmcm9udGVuZC1tYXN0ZXJcXHNyY1xcYXBwXFxwYWdlLmpzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IENvbnRhY3RVcyBmcm9tIFwiQC9jb21wb25lbnRzL2ZlYXR1cmVzL0NvbnRhY3RVc1wiO1xuaW1wb3J0IEhlcm8gZnJvbSBcIkAvY29tcG9uZW50cy9sYXlvdXQvSGVyb1wiO1xuaW1wb3J0IFNob3djYXNlUHJvZHVjdCBmcm9tIFwiQC9jb21wb25lbnRzL2ZlYXR1cmVzL1Nob3djYXNlUHJvZHVjdFwiO1xuaW1wb3J0IFJldmlld3NTZWN0aW9uIGZyb20gXCJAL2NvbXBvbmVudHMvZmVhdHVyZXMvUmV2aWV3c1NlY3Rpb25cIjtcbi8vIGltcG9ydCBOZXdzQW5kVXBkYXRlIGZyb20gXCJAL29sZENvbXBvbmVudHMvTmV3c0FuZFVwZGF0ZVwiO1xuLy8gaW1wb3J0IFNpbXBsaWZpZWRUZXN0aW1vbmlhbENhcm91c2VsIGZyb20gXCJAL2NvbXBvbmVudHMvUXVpY2tmaW5kVGVzdGltb25pYWxcIjtcbi8vIGltcG9ydCBGQVEgZnJvbSBcIkAvY29tcG9uZW50cy9GQVFcIjtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gSG9tZSgpIHtcbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cIiBiZy1bI0Y4RkZGOF1cIj5cbiAgICAgIDxIZXJvIC8+XG4gICAgICA8U2hvd2Nhc2VQcm9kdWN0IC8+XG4gICAgICA8UmV2aWV3c1NlY3Rpb24gLz5cbiAgICAgIHsvKiA8TmV3c0FuZFVwZGF0ZS8+ICovfVxuICAgICAgey8qIDxPdXJUZWFtMS8+ICovfVxuICAgICAgey8qIDxTaW1wbGlmaWVkVGVzdGltb25pYWxDYXJvdXNlbCAvPiAqL31cbiAgICAgIHsvKiA8RkFRIC8+ICovfVxuICAgICAgPENvbnRhY3RVcyAvPlxuICAgIDwvZGl2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbIkNvbnRhY3RVcyIsIkhlcm8iLCJTaG93Y2FzZVByb2R1Y3QiLCJSZXZpZXdzU2VjdGlvbiIsIkhvbWUiLCJkaXYiLCJjbGFzc05hbWUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.jsx\n");

/***/ }),

/***/ "(ssr)/./src/components/features/ContactUs.jsx":
/*!***********************************************!*\
  !*** ./src/components/features/ContactUs.jsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Mail,MapPin,Phone!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Mail,MapPin,Phone!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Mail,MapPin,Phone!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst ContactUs = ()=>{\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        email: '',\n        message: ''\n    });\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [submitStatus, setSubmitStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null) // 'success', 'error', null\n    ;\n    const handleChange = (e)=>{\n        setFormData({\n            ...formData,\n            [e.target.name]: e.target.value\n        });\n        // Clear status when user starts typing\n        if (submitStatus) {\n            setSubmitStatus(null);\n        }\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setIsSubmitting(true);\n        setSubmitStatus(null);\n        try {\n            const response = await fetch('/api/contact', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(formData)\n            });\n            const result = await response.json();\n            if (response.ok) {\n                setSubmitStatus('success');\n                // Reset form\n                setFormData({\n                    name: '',\n                    email: '',\n                    message: ''\n                });\n            } else {\n                setSubmitStatus('error');\n                console.error('Contact form error:', result.error);\n            }\n        } catch (error) {\n            console.error('Contact form submission failed:', error);\n            setSubmitStatus('error');\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-20 bg-black\",\n        id: \"contact\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.5\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                className: \"mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-medium text-primary\",\n                                    children: \"CONTACT US\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ContactUs.jsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ContactUs.jsx\",\n                                lineNumber: 67,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.h2, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.5,\n                                    delay: 0.1\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                className: \"text-3xl md:text-4xl font-bold text-white mb-6\",\n                                children: \"Let's build something amazing together\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ContactUs.jsx\",\n                                lineNumber: 77,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.p, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.5,\n                                    delay: 0.2\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                className: \"text-gray-400 mb-8\",\n                                children: \"Have a project in mind or want to learn more about our AI solutions? Reach out to our team.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ContactUs.jsx\",\n                                lineNumber: 87,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            duration: 0.5,\n                                            delay: 0.3\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        className: \"flex items-start gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                className: \"h-5 w-5 text-primary mt-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ContactUs.jsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-white font-medium\",\n                                                        children: \"Email\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ContactUs.jsx\",\n                                                        lineNumber: 107,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"mailto:<EMAIL>\",\n                                                        className: \"text-gray-400 hover:text-primary transition-colors\",\n                                                        children: \"<EMAIL>\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ContactUs.jsx\",\n                                                        lineNumber: 108,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ContactUs.jsx\",\n                                                lineNumber: 106,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ContactUs.jsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            duration: 0.5,\n                                            delay: 0.4\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        className: \"flex items-start gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"h-5 w-5 text-primary mt-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ContactUs.jsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-white font-medium\",\n                                                        children: \"Phone\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ContactUs.jsx\",\n                                                        lineNumber: 123,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"tel:+916390168645\",\n                                                        className: \"text-gray-400 hover:text-primary transition-colors\",\n                                                        children: \"+91 6390168645\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ContactUs.jsx\",\n                                                        lineNumber: 124,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ContactUs.jsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ContactUs.jsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            duration: 0.5,\n                                            delay: 0.5\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        className: \"flex items-start gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"h-5 w-5 text-primary mt-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ContactUs.jsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-white font-medium\",\n                                                        children: \"Location\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ContactUs.jsx\",\n                                                        lineNumber: 139,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-400\",\n                                                        children: \"India\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ContactUs.jsx\",\n                                                        lineNumber: 140,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ContactUs.jsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ContactUs.jsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ContactUs.jsx\",\n                                lineNumber: 97,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ContactUs.jsx\",\n                        lineNumber: 66,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.5\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"glass-effect p-8 rounded-xl\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"name\",\n                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                children: \"Your Name\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ContactUs.jsx\",\n                                                lineNumber: 155,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                id: \"name\",\n                                                name: \"name\",\n                                                value: formData.name,\n                                                onChange: handleChange,\n                                                className: \"w-full bg-gray-900/50 border border-white/10 rounded-lg px-4 py-3 text-white focus:ring-2 focus:ring-primary focus:border-transparent\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ContactUs.jsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ContactUs.jsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"email\",\n                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                children: \"Email Address\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ContactUs.jsx\",\n                                                lineNumber: 170,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"email\",\n                                                id: \"email\",\n                                                name: \"email\",\n                                                value: formData.email,\n                                                onChange: handleChange,\n                                                className: \"w-full bg-gray-900/50 border border-white/10 rounded-lg px-4 py-3 text-white focus:ring-2 focus:ring-primary focus:border-transparent\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ContactUs.jsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ContactUs.jsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"message\",\n                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                children: \"Your Message\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ContactUs.jsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                id: \"message\",\n                                                name: \"message\",\n                                                rows: \"5\",\n                                                value: formData.message,\n                                                onChange: handleChange,\n                                                className: \"w-full bg-gray-900/50 border border-white/10 rounded-lg px-4 py-3 text-white focus:ring-2 focus:ring-primary focus:border-transparent\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ContactUs.jsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ContactUs.jsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: isSubmitting,\n                                        className: \"w-full bg-gradient-to-r from-primary to-purple-600 text-white py-3 px-6 rounded-lg font-medium hover:opacity-90 transition-opacity disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        children: isSubmitting ? 'Sending...' : 'Send Message'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ContactUs.jsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    submitStatus === 'success' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 10\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        className: \"p-4 bg-green-500/20 border border-green-500/30 rounded-lg text-green-400 text-sm\",\n                                        children: \"✅ Thank you for your message! We'll get back to you soon.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ContactUs.jsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    submitStatus === 'error' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 10\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        className: \"p-4 bg-red-500/20 border border-red-500/30 rounded-lg text-red-400 text-sm\",\n                                        children: \"❌ Failed to send message. Please try again or contact us directly.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ContactUs.jsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ContactUs.jsx\",\n                                lineNumber: 153,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ContactUs.jsx\",\n                            lineNumber: 152,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ContactUs.jsx\",\n                        lineNumber: 146,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ContactUs.jsx\",\n                lineNumber: 65,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ContactUs.jsx\",\n            lineNumber: 64,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ContactUs.jsx\",\n        lineNumber: 63,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ContactUs);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/features/ContactUs.jsx\n");

/***/ }),

/***/ "(ssr)/./src/components/features/PlatformStats.jsx":
/*!***************************************************!*\
  !*** ./src/components/features/PlatformStats.jsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"(ssr)/./node_modules/firebase/firestore/dist/index.mjs\");\n/* harmony import */ var _firebase_config__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/firebase/config */ \"(ssr)/./src/firebase/config.js\");\n/* harmony import */ var _barrel_optimize_names_MessageSquare_Star_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=MessageSquare,Star,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_MessageSquare_Star_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=MessageSquare,Star,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_MessageSquare_Star_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=MessageSquare,Star,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_MessageSquare_Star_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=MessageSquare,Star,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/utils/use-in-view.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n// Simple animated number hook\nconst useAnimatedNumber = (endValue, duration = 2, shouldAnimate = false)=>{\n    const [displayValue, setDisplayValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"useAnimatedNumber.useEffect\": ()=>{\n            if (!shouldAnimate) return;\n            let startTime;\n            let animationFrame;\n            const animate = {\n                \"useAnimatedNumber.useEffect.animate\": (timestamp)=>{\n                    if (!startTime) startTime = timestamp;\n                    const progress = Math.min((timestamp - startTime) / (duration * 1000), 1);\n                    const easeOutQuart = 1 - Math.pow(1 - progress, 4);\n                    const currentValue = Math.floor(easeOutQuart * endValue);\n                    setDisplayValue(currentValue);\n                    if (progress < 1) {\n                        animationFrame = requestAnimationFrame(animate);\n                    } else {\n                        setDisplayValue(endValue);\n                    }\n                }\n            }[\"useAnimatedNumber.useEffect.animate\"];\n            animationFrame = requestAnimationFrame(animate);\n            return ({\n                \"useAnimatedNumber.useEffect\": ()=>{\n                    if (animationFrame) {\n                        cancelAnimationFrame(animationFrame);\n                    }\n                }\n            })[\"useAnimatedNumber.useEffect\"];\n        }\n    }[\"useAnimatedNumber.useEffect\"], [\n        endValue,\n        duration,\n        shouldAnimate\n    ]);\n    return displayValue;\n};\nconst PlatformStatsSimple = ()=>{\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalUsers: 0,\n        totalReviews: 0,\n        averageRating: 0,\n        successRate: 98\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const isInView = (0,framer_motion__WEBPACK_IMPORTED_MODULE_4__.useInView)(ref, {\n        once: true,\n        margin: \"-100px\"\n    });\n    const animatedUsers = useAnimatedNumber(stats.totalUsers, 2.5, isInView && !loading);\n    const animatedReviews = useAnimatedNumber(stats.totalReviews, 2, isInView && !loading);\n    const animatedRating = useAnimatedNumber(stats.averageRating * 10, 2, isInView && !loading) / 10;\n    const animatedSuccess = useAnimatedNumber(stats.successRate, 2, isInView && !loading);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PlatformStatsSimple.useEffect\": ()=>{\n            // Try to connect to Firebase\n            const setupFirebase = {\n                \"PlatformStatsSimple.useEffect.setupFirebase\": async ()=>{\n                    try {\n                        if (!_firebase_config__WEBPACK_IMPORTED_MODULE_3__.firestore || !_firebase_config__WEBPACK_IMPORTED_MODULE_3__.firestore.app) {\n                            console.log('🔥 Firestore not available, using demo data');\n                            setStats({\n                                totalUsers: 5847,\n                                totalReviews: 247,\n                                averageRating: 4.8,\n                                successRate: 98\n                            });\n                            setLoading(false);\n                            setIsConnected(false);\n                            return;\n                        }\n                        console.log('🔥 Setting up Firebase listeners...');\n                        // Set up real-time listener for users\n                        const unsubscribeUsers = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.onSnapshot)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_firebase_config__WEBPACK_IMPORTED_MODULE_3__.firestore, 'users'), {\n                            \"PlatformStatsSimple.useEffect.setupFirebase.unsubscribeUsers\": (snapshot)=>{\n                                const userCount = snapshot.size;\n                                console.log('👥 Users updated:', userCount);\n                                setStats({\n                                    \"PlatformStatsSimple.useEffect.setupFirebase.unsubscribeUsers\": (prev)=>({\n                                            ...prev,\n                                            totalUsers: Math.max(userCount, 5847)\n                                        })\n                                }[\"PlatformStatsSimple.useEffect.setupFirebase.unsubscribeUsers\"]);\n                                setIsConnected(true);\n                            }\n                        }[\"PlatformStatsSimple.useEffect.setupFirebase.unsubscribeUsers\"], {\n                            \"PlatformStatsSimple.useEffect.setupFirebase.unsubscribeUsers\": (error)=>{\n                                console.error('❌ Users listener error:', error);\n                                setIsConnected(false);\n                            }\n                        }[\"PlatformStatsSimple.useEffect.setupFirebase.unsubscribeUsers\"]);\n                        // Set up real-time listener for reviews\n                        const unsubscribeReviews = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.onSnapshot)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_firebase_config__WEBPACK_IMPORTED_MODULE_3__.firestore, 'reviews'), {\n                            \"PlatformStatsSimple.useEffect.setupFirebase.unsubscribeReviews\": (snapshot)=>{\n                                let totalRating = 0;\n                                let reviewCount = 0;\n                                snapshot.forEach({\n                                    \"PlatformStatsSimple.useEffect.setupFirebase.unsubscribeReviews\": (doc)=>{\n                                        const data = doc.data();\n                                        if (data.rating && typeof data.rating === 'number') {\n                                            totalRating += data.rating;\n                                            reviewCount++;\n                                        }\n                                    }\n                                }[\"PlatformStatsSimple.useEffect.setupFirebase.unsubscribeReviews\"]);\n                                const averageRating = reviewCount > 0 ? totalRating / reviewCount : 4.8;\n                                console.log('⭐ Reviews updated:', {\n                                    reviewCount,\n                                    averageRating\n                                });\n                                setStats({\n                                    \"PlatformStatsSimple.useEffect.setupFirebase.unsubscribeReviews\": (prev)=>({\n                                            ...prev,\n                                            totalReviews: reviewCount,\n                                            averageRating: parseFloat(averageRating.toFixed(1))\n                                        })\n                                }[\"PlatformStatsSimple.useEffect.setupFirebase.unsubscribeReviews\"]);\n                                setIsConnected(true);\n                            }\n                        }[\"PlatformStatsSimple.useEffect.setupFirebase.unsubscribeReviews\"], {\n                            \"PlatformStatsSimple.useEffect.setupFirebase.unsubscribeReviews\": (error)=>{\n                                console.error('❌ Reviews listener error:', error);\n                                setIsConnected(false);\n                            }\n                        }[\"PlatformStatsSimple.useEffect.setupFirebase.unsubscribeReviews\"]);\n                        setLoading(false);\n                        return ({\n                            \"PlatformStatsSimple.useEffect.setupFirebase\": ()=>{\n                                if (unsubscribeUsers) unsubscribeUsers();\n                                if (unsubscribeReviews) unsubscribeReviews();\n                                console.log('🧹 Firebase listeners cleaned up');\n                            }\n                        })[\"PlatformStatsSimple.useEffect.setupFirebase\"];\n                    } catch (error) {\n                        console.error('❌ Firebase setup error:', error);\n                        setStats({\n                            totalUsers: 5847,\n                            totalReviews: 247,\n                            averageRating: 4.8,\n                            successRate: 98\n                        });\n                        setLoading(false);\n                        setIsConnected(false);\n                    }\n                }\n            }[\"PlatformStatsSimple.useEffect.setupFirebase\"];\n            setupFirebase();\n        }\n    }[\"PlatformStatsSimple.useEffect\"], []);\n    const statItems = [\n        {\n            icon: _barrel_optimize_names_MessageSquare_Star_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            label: 'Active Users',\n            value: loading ? '...' : `${animatedUsers.toLocaleString()}+`,\n            gradient: 'from-blue-500 via-cyan-500 to-teal-500',\n            iconBg: 'bg-blue-500/20',\n            isLive: true\n        },\n        {\n            icon: _barrel_optimize_names_MessageSquare_Star_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            label: 'User Reviews',\n            value: loading ? '...' : animatedReviews.toLocaleString(),\n            gradient: 'from-purple-500 via-pink-500 to-rose-500',\n            iconBg: 'bg-purple-500/20',\n            isLive: true\n        },\n        {\n            icon: _barrel_optimize_names_MessageSquare_Star_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            label: 'Average Rating',\n            value: loading ? '...' : `${animatedRating.toFixed(1)}/5`,\n            gradient: 'from-yellow-500 via-orange-500 to-red-500',\n            iconBg: 'bg-yellow-500/20',\n            isLive: false\n        },\n        {\n            icon: _barrel_optimize_names_MessageSquare_Star_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            label: 'Success Rate',\n            value: loading ? '...' : `${animatedSuccess}%`,\n            gradient: 'from-green-500 via-emerald-500 to-teal-500',\n            iconBg: 'bg-green-500/20',\n            isLive: false\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n        ref: ref,\n        className: \"mt-8 w-full\",\n        initial: {\n            opacity: 0,\n            y: 20\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        transition: {\n            duration: 0.6,\n            delay: 0.5\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-wrap items-center justify-center gap-6 lg:gap-8\",\n            children: statItems.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                    className: \"flex items-center gap-3 group\",\n                    initial: {\n                        opacity: 0,\n                        scale: 0.9\n                    },\n                    animate: {\n                        opacity: 1,\n                        scale: 1\n                    },\n                    transition: {\n                        duration: 0.5,\n                        delay: 0.6 + index * 0.1\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `${stat.iconBg} p-2 rounded-lg backdrop-blur-sm border border-white/10 relative`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(stat.icon, {\n                                    className: \"h-4 w-4 text-white\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\PlatformStats.jsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 15\n                                }, undefined),\n                                stat.isLive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `absolute -top-1 -right-1 w-2 h-2 rounded-full animate-pulse ${isConnected ? 'bg-green-400' : 'bg-yellow-400'}`\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\PlatformStats.jsx\",\n                                    lineNumber: 212,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\PlatformStats.jsx\",\n                            lineNumber: 209,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-left\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `text-lg font-bold bg-gradient-to-r ${stat.gradient} bg-clip-text text-transparent`,\n                                    children: stat.value\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\PlatformStats.jsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-400 leading-tight\",\n                                    children: stat.label\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\PlatformStats.jsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\PlatformStats.jsx\",\n                            lineNumber: 217,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, stat.label, true, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\PlatformStats.jsx\",\n                    lineNumber: 199,\n                    columnNumber: 11\n                }, undefined))\n        }, void 0, false, {\n            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\PlatformStats.jsx\",\n            lineNumber: 197,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\PlatformStats.jsx\",\n        lineNumber: 190,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PlatformStatsSimple);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/features/PlatformStats.jsx\n");

/***/ }),

/***/ "(ssr)/./src/components/features/ReviewsSection.jsx":
/*!****************************************************!*\
  !*** ./src/components/features/ReviewsSection.jsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.jsx\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/firestore */ \"(ssr)/./node_modules/firebase/firestore/dist/index.mjs\");\n/* harmony import */ var _firebase_config__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/firebase/config */ \"(ssr)/./src/firebase/config.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nconst StarRating = ({ rating, onRatingChange, readonly = false })=>{\n    const [hover, setHover] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center\",\n        children: [\n            ...Array(5)\n        ].map((_, index)=>{\n            const ratingValue = index + 1;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                type: \"button\",\n                className: `text-2xl ${ratingValue <= (hover || rating) ? 'text-yellow-400' : 'text-gray-600'} ${readonly ? 'cursor-default' : 'cursor-pointer hover:text-yellow-300'} transition-colors`,\n                onClick: ()=>!readonly && onRatingChange && onRatingChange(ratingValue),\n                onMouseEnter: ()=>!readonly && setHover(ratingValue),\n                onMouseLeave: ()=>!readonly && setHover(0),\n                disabled: readonly,\n                children: \"★\"\n            }, index, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                lineNumber: 25,\n                columnNumber: 11\n            }, undefined);\n        })\n    }, void 0, false, {\n        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, undefined);\n};\nconst ReviewCard = ({ review })=>{\n    const formatDate = (timestamp)=>{\n        if (!timestamp) return '';\n        const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);\n        return date.toLocaleDateString('en-US', {\n            year: 'numeric',\n            month: 'short',\n            day: 'numeric'\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gray-900/20 backdrop-blur-md p-6 rounded-2xl border border-white/10 mb-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-start justify-between mb-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-10 h-10 bg-gradient-to-r from-neural-purple to-neural-pink rounded-full flex items-center justify-center text-white font-semibold mr-3\",\n                                children: review.userEmail ? review.userEmail.charAt(0).toUpperCase() : 'A'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                                lineNumber: 61,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white font-medium\",\n                                        children: review.userEmail ? review.userEmail.split('@')[0] : 'Anonymous'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                                        lineNumber: 65,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: formatDate(review.createdAt)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                                lineNumber: 64,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                        lineNumber: 60,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StarRating, {\n                        rating: review.rating,\n                        readonly: true\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                        lineNumber: 71,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-gray-300 leading-relaxed\",\n                children: review.comment\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                lineNumber: 73,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n        lineNumber: 58,\n        columnNumber: 5\n    }, undefined);\n};\nconst ReviewForm = ({ onReviewSubmitted })=>{\n    const [rating, setRating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [comment, setComment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const { currentUser } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!currentUser) {\n            setError('You must be logged in to submit a review.');\n            return;\n        }\n        if (rating === 0) {\n            setError('Please select a rating.');\n            return;\n        }\n        if (comment.trim().length < 10) {\n            setError('Please write at least 10 characters in your review.');\n            return;\n        }\n        setLoading(true);\n        setError('');\n        try {\n            const reviewData = {\n                userId: currentUser.uid,\n                userEmail: currentUser.email,\n                rating: rating,\n                comment: comment.trim(),\n                createdAt: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.serverTimestamp)(),\n                approved: false,\n                status: 'pending' // Track approval status\n            };\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.addDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.collection)(_firebase_config__WEBPACK_IMPORTED_MODULE_4__.firestore, 'reviews'), reviewData);\n            // Reset form\n            setRating(0);\n            setComment('');\n            // Show success message\n            alert('Thank you for your review! It will be displayed after admin approval.');\n            // Notify parent component\n            if (onReviewSubmitted) {\n                onReviewSubmitted();\n            }\n        } catch (err) {\n            console.error('Error submitting review:', err);\n            setError('Failed to submit review. Please try again.');\n        } finally{\n            setLoading(false);\n        }\n    };\n    if (!currentUser) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-gray-900/20 backdrop-blur-md p-6 rounded-2xl border border-white/10 text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-300 mb-4\",\n                    children: \"Please log in to submit a review.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                    lineNumber: 141,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                    href: \"/login\",\n                    className: \"bg-gradient-to-r from-neural-purple to-neural-pink text-white px-6 py-2 rounded-lg hover:opacity-90 transition-opacity\",\n                    children: \"Log In\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                    lineNumber: 142,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n            lineNumber: 140,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gray-900/20 backdrop-blur-md p-6 rounded-2xl border border-white/10\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-xl font-bold text-white mb-4\",\n                children: \"Share Your Experience\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                lineNumber: 154,\n                columnNumber: 7\n            }, undefined),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4 p-3 bg-red-500/20 border border-red-500/30 rounded-lg text-red-300 text-sm\",\n                children: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                lineNumber: 157,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit,\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                children: \"Your Rating\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                                lineNumber: 164,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StarRating, {\n                                rating: rating,\n                                onRatingChange: setRating\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                                lineNumber: 167,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                        lineNumber: 163,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"comment\",\n                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                children: \"Your Review\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                                lineNumber: 171,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                id: \"comment\",\n                                value: comment,\n                                onChange: (e)=>setComment(e.target.value),\n                                rows: 4,\n                                className: \"w-full px-4 py-3 bg-gray-900/50 border border-white/10 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-neural-purple/50 focus:border-neural-purple text-white placeholder-gray-400 transition-all duration-300 ease-in-out backdrop-blur-sm resize-none\",\n                                placeholder: \"Tell us about your experience with BlinkFind...\",\n                                required: true\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                                lineNumber: 174,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500 text-xs mt-1\",\n                                children: [\n                                    comment.length,\n                                    \"/500 characters\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                                lineNumber: 183,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                        lineNumber: 170,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"submit\",\n                        disabled: loading || rating === 0 || comment.trim().length < 10,\n                        className: \"w-full bg-gradient-to-r from-neural-purple to-neural-pink text-white font-semibold py-3 px-4 rounded-lg hover:opacity-90 focus:outline-none focus:ring-2 focus:ring-neural-purple/50 disabled:opacity-50 transition-all duration-300 ease-in-out\",\n                        children: loading ? 'Submitting...' : 'Submit Review'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                        lineNumber: 186,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                lineNumber: 162,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n        lineNumber: 153,\n        columnNumber: 5\n    }, undefined);\n};\nconst ReviewsSection = ()=>{\n    const [reviews, setReviews] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalReviews: 0,\n        averageRating: 0\n    });\n    const fetchReviews = async ()=>{\n        try {\n            const reviewsQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.collection)(_firebase_config__WEBPACK_IMPORTED_MODULE_4__.firestore, 'reviews'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.orderBy)('createdAt', 'desc'));\n            const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.getDocs)(reviewsQuery);\n            const allReviewsData = querySnapshot.docs.map((doc)=>({\n                    id: doc.id,\n                    ...doc.data()\n                }));\n            // Filter to only show approved reviews\n            const approvedReviews = allReviewsData.filter((review)=>review.approved === true);\n            setReviews(approvedReviews);\n            // Calculate stats based on approved reviews only\n            const totalReviews = approvedReviews.length;\n            const averageRating = totalReviews > 0 ? approvedReviews.reduce((sum, review)=>sum + review.rating, 0) / totalReviews : 0;\n            setStats({\n                totalReviews,\n                averageRating\n            });\n        } catch (error) {\n            console.error('Error fetching reviews:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ReviewsSection.useEffect\": ()=>{\n            fetchReviews();\n        }\n    }[\"ReviewsSection.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative overflow-hidden bg-gradient-to-b from-black to-[#0A0A0A] min-h-screen py-12\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 opacity-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 bg-[url('/grid.svg')] [mask-image:radial-gradient(ellipse_at_center,white,transparent)]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                    lineNumber: 242,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                lineNumber: 241,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 overflow-hidden\",\n                children: [\n                    ...Array(6)\n                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute rounded-full bg-neural-purple opacity-10 blur-xl animate-pulse\",\n                        style: {\n                            left: `${Math.random() * 100}%`,\n                            top: `${Math.random() * 100}%`,\n                            width: `${Math.random() * 200 + 100}px`,\n                            height: `${Math.random() * 200 + 100}px`,\n                            animationDelay: `${Math.random() * 5}s`,\n                            animationDuration: `${Math.random() * 10 + 10}s`\n                        }\n                    }, i, false, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                        lineNumber: 248,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                lineNumber: 246,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 max-w-4xl mx-auto px-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl font-bold bg-gradient-to-r from-neural-purple to-neural-pink bg-clip-text text-transparent mb-4\",\n                                children: \"User Reviews\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                                lineNumber: 266,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-300 text-lg mb-6\",\n                                children: \"See what our users are saying about BlinkFind\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                                lineNumber: 269,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center items-center space-x-8 mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-3xl font-bold text-white\",\n                                                children: stats.totalReviews\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400\",\n                                                children: \"Total Reviews\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                                                lineNumber: 277,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                                        lineNumber: 275,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center mb-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-3xl font-bold text-white mr-2\",\n                                                        children: stats.averageRating.toFixed(1)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                                                        lineNumber: 281,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StarRating, {\n                                                        rating: Math.round(stats.averageRating),\n                                                        readonly: true\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                                                        lineNumber: 284,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400\",\n                                                children: \"Average Rating\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                                                lineNumber: 286,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                                        lineNumber: 279,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                                lineNumber: 274,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                        lineNumber: 265,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-12\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ReviewForm, {\n                            onReviewSubmitted: fetchReviews\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                            lineNumber: 293,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                        lineNumber: 292,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-white mb-6\",\n                                children: \"Recent Reviews\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                                lineNumber: 298,\n                                columnNumber: 11\n                            }, undefined),\n                            loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-neural-purple\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                                        lineNumber: 302,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-300 mt-2\",\n                                        children: \"Loading reviews...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                                        lineNumber: 303,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                                lineNumber: 301,\n                                columnNumber: 13\n                            }, undefined) : reviews.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400\",\n                                    children: \"No reviews yet. Be the first to share your experience!\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                                    lineNumber: 307,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                                lineNumber: 306,\n                                columnNumber: 13\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: reviews.map((review)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ReviewCard, {\n                                        review: review\n                                    }, review.id, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                                        lineNumber: 312,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                                lineNumber: 310,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                        lineNumber: 297,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n                lineNumber: 263,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ReviewsSection.jsx\",\n        lineNumber: 239,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ReviewsSection);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/features/ReviewsSection.jsx\n");

/***/ }),

/***/ "(ssr)/./src/components/features/ShowcaseProduct.jsx":
/*!*****************************************************!*\
  !*** ./src/components/features/ShowcaseProduct.jsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_FileText_Heart_Play_Search_Smartphone_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=FileText,Heart,Play,Search,Smartphone,Target,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_FileText_Heart_Play_Search_Smartphone_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=FileText,Heart,Play,Search,Smartphone,Target,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/smartphone.js\");\n/* harmony import */ var _barrel_optimize_names_FileText_Heart_Play_Search_Smartphone_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=FileText,Heart,Play,Search,Smartphone,Target,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_FileText_Heart_Play_Search_Smartphone_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=FileText,Heart,Play,Search,Smartphone,Target,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_FileText_Heart_Play_Search_Smartphone_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=FileText,Heart,Play,Search,Smartphone,Target,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_FileText_Heart_Play_Search_Smartphone_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=FileText,Heart,Play,Search,Smartphone,Target,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_FileText_Heart_Play_Search_Smartphone_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=FileText,Heart,Play,Search,Smartphone,Target,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_SparklesIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=SparklesIcon!=!@heroicons/react/24/solid */ \"(ssr)/./node_modules/@heroicons/react/24/solid/esm/SparklesIcon.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nconst ProductShowcase = ()=>{\n    const [isQuickFindPlaying, setIsQuickFindPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isResumeBuilderPlaying, setIsResumeBuilderPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const quickFindFeatures = [\n        {\n            icon: _barrel_optimize_names_FileText_Heart_Play_Search_Smartphone_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n            title: \"Easy Reporting\",\n            description: \"Found something valuable? Upload it and help someone reconnect.\"\n        },\n        {\n            icon: _barrel_optimize_names_FileText_Heart_Play_Search_Smartphone_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            title: \"Simple Search\",\n            description: \"Effortlessly find your lost items through easy search filters.\"\n        },\n        {\n            icon: _barrel_optimize_names_FileText_Heart_Play_Search_Smartphone_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            title: \"Emotional Value\",\n            description: \"Restoring memories and connections that can never be replaced.\"\n        }\n    ];\n    const resumeBuilderFeatures = [\n        {\n            icon: _barrel_optimize_names_FileText_Heart_Play_Search_Smartphone_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            title: \"AI-Powered Writing\",\n            description: \"Generate professional content tailored to your experience and industry.\"\n        },\n        {\n            icon: _barrel_optimize_names_FileText_Heart_Play_Search_Smartphone_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            title: \"Instant Creation\",\n            description: \"Build a complete resume in minutes with our intelligent templates.\"\n        },\n        {\n            icon: _barrel_optimize_names_FileText_Heart_Play_Search_Smartphone_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            title: \"ATS Optimized\",\n            description: \"Ensure your resume passes through applicant tracking systems.\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-20 bg-gradient-to-b from-black to-[#0A0A0A]\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.5\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"flex items-center justify-center gap-2 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_SparklesIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-5 w-5 text-primary\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-medium text-primary\",\n                                    children: \"OUR PRODUCTS\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                    lineNumber: 59,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                            lineNumber: 51,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.h2, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.5,\n                                delay: 0.1\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"text-3xl md:text-4xl font-bold text-white\",\n                            children: \"Innovative AI Solutions for Everyday Challenges\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                            lineNumber: 62,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                    lineNumber: 50,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.5,\n                                        delay: 0.2\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    className: \"relative aspect-[9/16] max-w-[300px] mx-auto lg:mx-0 order-2 lg:order-1\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"glass-effect rounded-2xl overflow-hidden border border-white/10 h-full\",\n                                        children: isResumeBuilderPlaying ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                            className: \"absolute inset-0 w-full h-full object-cover\",\n                                            src: \"/Videos/resume_builder_demo.mp4\",\n                                            title: \"AI Resume Builder Demo\",\n                                            autoPlay: true,\n                                            controls: true,\n                                            muted: true,\n                                            loop: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                            lineNumber: 85,\n                                            columnNumber: 19\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 flex items-center justify-center bg-gradient-to-br from-neural-purple/20 via-neural-pink/20 to-neural-blue/20 backdrop-blur-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-gradient-to-br from-purple-600/30 to-pink-600/30\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                                    lineNumber: 97,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-[url('/ai.jpg')] bg-cover bg-center opacity-40\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                                    lineNumber: 98,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-black/30\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                                    lineNumber: 99,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative z-10 text-center p-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mb-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileText_Heart_Play_Search_Smartphone_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                    className: \"h-12 w-12 text-white mx-auto mb-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                                                    lineNumber: 104,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"text-white font-bold text-lg\",\n                                                                    children: \"AI Resume Builder\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                                                    lineNumber: 105,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-200 text-sm\",\n                                                                    children: \"Professional resumes in minutes\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                                                    lineNumber: 106,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                                            lineNumber: 103,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setIsResumeBuilderPlaying(true),\n                                                            className: \"bg-gradient-to-r from-primary to-purple-600 text-white rounded-full p-3 transition-transform transform hover:scale-110 shadow-lg\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileText_Heart_Play_Search_Smartphone_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: \"h-6 w-6\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                                                lineNumber: 112,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                                            lineNumber: 108,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                                    lineNumber: 102,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                            lineNumber: 95,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"order-1 lg:order-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.h3, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 20\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.5,\n                                                delay: 0.2\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            className: \"text-2xl font-bold text-white mb-6\",\n                                            children: \"AI Resume Builder: Professional Resumes in Minutes\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: resumeBuilderFeatures.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                                    initial: {\n                                                        opacity: 0,\n                                                        y: 20\n                                                    },\n                                                    whileInView: {\n                                                        opacity: 1,\n                                                        y: 0\n                                                    },\n                                                    transition: {\n                                                        duration: 0.5,\n                                                        delay: 0.3 + index * 0.1\n                                                    },\n                                                    viewport: {\n                                                        once: true\n                                                    },\n                                                    className: \"flex items-start gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-primary/10 p-2 rounded-lg\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                                className: \"h-5 w-5 text-primary\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                                                lineNumber: 142,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                                            lineNumber: 141,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"text-lg font-semibold text-white\",\n                                                                    children: feature.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                                                    lineNumber: 145,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-400\",\n                                                                    children: feature.description\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                                                    lineNumber: 146,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                                            lineNumber: 144,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                                    lineNumber: 133,\n                                                    columnNumber: 19\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                            lineNumber: 131,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 20\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.5,\n                                                delay: 0.6\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            className: \"mt-8\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"/resume-builder\",\n                                                className: \"inline-flex items-center justify-center px-6 py-3 bg-gradient-to-r from-primary to-purple-600 text-white rounded-lg font-medium hover:opacity-90 transition-opacity\",\n                                                children: \"Try AI Resume Builder\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                                lineNumber: 159,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                            lineNumber: 75,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.h3, {\n                                                initial: {\n                                                    opacity: 0,\n                                                    y: 20\n                                                },\n                                                whileInView: {\n                                                    opacity: 1,\n                                                    y: 0\n                                                },\n                                                transition: {\n                                                    duration: 0.5,\n                                                    delay: 0.2\n                                                },\n                                                viewport: {\n                                                    once: true\n                                                },\n                                                className: \"text-2xl font-bold text-white mb-6\",\n                                                children: \"QuickFind: AI-Powered Lost & Found Platform\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-6\",\n                                                children: quickFindFeatures.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                                        initial: {\n                                                            opacity: 0,\n                                                            y: 20\n                                                        },\n                                                        whileInView: {\n                                                            opacity: 1,\n                                                            y: 0\n                                                        },\n                                                        transition: {\n                                                            duration: 0.5,\n                                                            delay: 0.3 + index * 0.1\n                                                        },\n                                                        viewport: {\n                                                            once: true\n                                                        },\n                                                        className: \"flex items-start gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-primary/10 p-2 rounded-lg\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                                    className: \"h-5 w-5 text-primary\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                                                    lineNumber: 193,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                                                lineNumber: 192,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"text-lg font-semibold text-white\",\n                                                                        children: feature.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                                                        lineNumber: 196,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-400\",\n                                                                        children: feature.description\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                                                        lineNumber: 197,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                                                lineNumber: 195,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                                        lineNumber: 184,\n                                                        columnNumber: 19\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                                lineNumber: 182,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                                initial: {\n                                                    opacity: 0,\n                                                    y: 20\n                                                },\n                                                whileInView: {\n                                                    opacity: 1,\n                                                    y: 0\n                                                },\n                                                transition: {\n                                                    duration: 0.5,\n                                                    delay: 0.6\n                                                },\n                                                viewport: {\n                                                    once: true\n                                                },\n                                                className: \"mt-8\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/quickfind\",\n                                                    className: \"inline-flex items-center justify-center px-6 py-3 bg-gradient-to-r from-primary to-purple-600 text-white rounded-lg font-medium hover:opacity-90 transition-opacity\",\n                                                    children: \"Learn More About QuickFind\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                                    lineNumber: 210,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            duration: 0.5,\n                                            delay: 0.2\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        className: \"relative aspect-[9/16] max-w-[300px] mx-auto lg:mx-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"glass-effect rounded-2xl overflow-hidden border border-white/10 h-full\",\n                                            children: isQuickFindPlaying ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                                className: \"absolute inset-0 w-full h-full object-cover\",\n                                                src: \"/Videos/quickFind_video.mp4\",\n                                                title: \"QuickFind App Demo\",\n                                                autoPlay: true,\n                                                controls: true,\n                                                muted: true,\n                                                loop: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 19\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 flex items-center justify-center bg-gradient-to-br from-neural-blue/20 via-neural-purple/20 to-neural-pink/20 backdrop-blur-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 bg-gradient-to-br from-blue-600/30 to-cyan-600/30\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                                        lineNumber: 240,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 bg-[url('/lostFound/lf1.jpg')] bg-cover bg-center opacity-40\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                                        lineNumber: 241,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-black/30\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                                        lineNumber: 242,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative z-10 text-center p-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mb-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileText_Heart_Play_Search_Smartphone_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                        className: \"h-12 w-12 text-white mx-auto mb-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                                                        lineNumber: 247,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"text-white font-bold text-lg\",\n                                                                        children: \"QuickFind\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                                                        lineNumber: 248,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-200 text-sm\",\n                                                                        children: \"AI-powered lost & found\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                                                        lineNumber: 249,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                                                lineNumber: 246,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>setIsQuickFindPlaying(true),\n                                                                className: \"bg-gradient-to-r from-primary to-purple-600 text-white rounded-full p-3 transition-transform transform hover:scale-110 shadow-lg\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileText_Heart_Play_Search_Smartphone_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    className: \"h-6 w-6\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                                                    lineNumber: 255,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                                                lineNumber: 251,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                                        lineNumber: 245,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                                lineNumber: 170,\n                                columnNumber: 11\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                            lineNumber: 169,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n                    lineNumber: 74,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n            lineNumber: 49,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\features\\\\ShowcaseProduct.jsx\",\n        lineNumber: 48,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ProductShowcase);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9mZWF0dXJlcy9TaG93Y2FzZVByb2R1Y3QuanN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUNzQztBQUMrQztBQUM3QjtBQUN4QjtBQUVoQyxNQUFNVSxrQkFBa0I7SUFDdEIsTUFBTSxDQUFDQyxvQkFBb0JDLHNCQUFzQixHQUFHSCwrQ0FBUUEsQ0FBQztJQUM3RCxNQUFNLENBQUNJLHdCQUF3QkMsMEJBQTBCLEdBQUdMLCtDQUFRQSxDQUFDO0lBRXJFLE1BQU1NLG9CQUFvQjtRQUN4QjtZQUNFQyxNQUFNYiw0SEFBTUE7WUFDWmMsT0FBTztZQUNQQyxhQUFhO1FBQ2Y7UUFDQTtZQUNFRixNQUFNZCw0SEFBVUE7WUFDaEJlLE9BQU87WUFDUEMsYUFBYTtRQUNmO1FBQ0E7WUFDRUYsTUFBTVosNEhBQUtBO1lBQ1hhLE9BQU87WUFDUEMsYUFBYTtRQUNmO0tBQ0Q7SUFFRCxNQUFNQyx3QkFBd0I7UUFDNUI7WUFDRUgsTUFBTVgsNEhBQVFBO1lBQ2RZLE9BQU87WUFDUEMsYUFBYTtRQUNmO1FBQ0E7WUFDRUYsTUFBTVYsNEhBQUdBO1lBQ1RXLE9BQU87WUFDUEMsYUFBYTtRQUNmO1FBQ0E7WUFDRUYsTUFBTVQsNEhBQU1BO1lBQ1pVLE9BQU87WUFDUEMsYUFBYTtRQUNmO0tBQ0Q7SUFFRCxxQkFDRSw4REFBQ0U7UUFBUUMsV0FBVTtrQkFDakIsNEVBQUNDO1lBQUlELFdBQVU7OzhCQUNiLDhEQUFDQztvQkFBSUQsV0FBVTs7c0NBQ2IsOERBQUNyQixpREFBTUEsQ0FBQ3NCLEdBQUc7NEJBQ1RDLFNBQVM7Z0NBQUVDLFNBQVM7Z0NBQUdDLEdBQUc7NEJBQUc7NEJBQzdCQyxhQUFhO2dDQUFFRixTQUFTO2dDQUFHQyxHQUFHOzRCQUFFOzRCQUNoQ0UsWUFBWTtnQ0FBRUMsVUFBVTs0QkFBSTs0QkFDNUJDLFVBQVU7Z0NBQUVDLE1BQU07NEJBQUs7NEJBQ3ZCVCxXQUFVOzs4Q0FFViw4REFBQ2Isb0dBQVlBO29DQUFDYSxXQUFVOzs7Ozs7OENBQ3hCLDhEQUFDVTtvQ0FBS1YsV0FBVTs4Q0FBbUM7Ozs7Ozs7Ozs7OztzQ0FHckQsOERBQUNyQixpREFBTUEsQ0FBQ2dDLEVBQUU7NEJBQ1JULFNBQVM7Z0NBQUVDLFNBQVM7Z0NBQUdDLEdBQUc7NEJBQUc7NEJBQzdCQyxhQUFhO2dDQUFFRixTQUFTO2dDQUFHQyxHQUFHOzRCQUFFOzRCQUNoQ0UsWUFBWTtnQ0FBRUMsVUFBVTtnQ0FBS0ssT0FBTzs0QkFBSTs0QkFDeENKLFVBQVU7Z0NBQUVDLE1BQU07NEJBQUs7NEJBQ3ZCVCxXQUFVO3NDQUNYOzs7Ozs7Ozs7Ozs7OEJBTUgsOERBQUNDOztzQ0FDQyw4REFBQ0E7NEJBQUlELFdBQVU7OzhDQUNiLDhEQUFDckIsaURBQU1BLENBQUNzQixHQUFHO29DQUNUQyxTQUFTO3dDQUFFQyxTQUFTO3dDQUFHQyxHQUFHO29DQUFHO29DQUM3QkMsYUFBYTt3Q0FBRUYsU0FBUzt3Q0FBR0MsR0FBRztvQ0FBRTtvQ0FDaENFLFlBQVk7d0NBQUVDLFVBQVU7d0NBQUtLLE9BQU87b0NBQUk7b0NBQ3hDSixVQUFVO3dDQUFFQyxNQUFNO29DQUFLO29DQUN2QlQsV0FBVTs4Q0FFViw0RUFBQ0M7d0NBQUlELFdBQVU7a0RBQ1pSLHVDQUNDLDhEQUFDcUI7NENBQ0NiLFdBQVU7NENBQ1ZjLEtBQUk7NENBQ0psQixPQUFNOzRDQUNObUIsUUFBUTs0Q0FDUkMsUUFBUTs0Q0FDUkMsS0FBSzs0Q0FDTEMsSUFBSTs7Ozs7c0VBR04sOERBQUNqQjs0Q0FBSUQsV0FBVTs7OERBRWIsOERBQUNDO29EQUFJRCxXQUFVOzs7Ozs7OERBQ2YsOERBQUNDO29EQUFJRCxXQUFVOzs7Ozs7OERBQ2YsOERBQUNDO29EQUFJRCxXQUFVOzs7Ozs7OERBR2YsOERBQUNDO29EQUFJRCxXQUFVOztzRUFDYiw4REFBQ0M7NERBQUlELFdBQVU7OzhFQUNiLDhEQUFDaEIsNEhBQVFBO29FQUFDZ0IsV0FBVTs7Ozs7OzhFQUNwQiw4REFBQ21CO29FQUFHbkIsV0FBVTs4RUFBK0I7Ozs7Ozs4RUFDN0MsOERBQUNvQjtvRUFBRXBCLFdBQVU7OEVBQXdCOzs7Ozs7Ozs7Ozs7c0VBRXZDLDhEQUFDcUI7NERBQ0NDLFNBQVMsSUFBTTdCLDBCQUEwQjs0REFDekNPLFdBQVU7c0VBRVYsNEVBQUNwQiw2SEFBSUE7Z0VBQUNvQixXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBUTVCLDhEQUFDQztvQ0FBSUQsV0FBVTs7c0RBQ2IsOERBQUNyQixpREFBTUEsQ0FBQzRDLEVBQUU7NENBQ1JyQixTQUFTO2dEQUFFQyxTQUFTO2dEQUFHQyxHQUFHOzRDQUFHOzRDQUM3QkMsYUFBYTtnREFBRUYsU0FBUztnREFBR0MsR0FBRzs0Q0FBRTs0Q0FDaENFLFlBQVk7Z0RBQUVDLFVBQVU7Z0RBQUtLLE9BQU87NENBQUk7NENBQ3hDSixVQUFVO2dEQUFFQyxNQUFNOzRDQUFLOzRDQUN2QlQsV0FBVTtzREFDWDs7Ozs7O3NEQUlELDhEQUFDQzs0Q0FBSUQsV0FBVTtzREFDWkYsc0JBQXNCMEIsR0FBRyxDQUFDLENBQUNDLFNBQVNDLHNCQUNuQyw4REFBQy9DLGlEQUFNQSxDQUFDc0IsR0FBRztvREFFVEMsU0FBUzt3REFBRUMsU0FBUzt3REFBR0MsR0FBRztvREFBRztvREFDN0JDLGFBQWE7d0RBQUVGLFNBQVM7d0RBQUdDLEdBQUc7b0RBQUU7b0RBQ2hDRSxZQUFZO3dEQUFFQyxVQUFVO3dEQUFLSyxPQUFPLE1BQU1jLFFBQVE7b0RBQUk7b0RBQ3REbEIsVUFBVTt3REFBRUMsTUFBTTtvREFBSztvREFDdkJULFdBQVU7O3NFQUVWLDhEQUFDQzs0REFBSUQsV0FBVTtzRUFDYiw0RUFBQ3lCLFFBQVE5QixJQUFJO2dFQUFDSyxXQUFVOzs7Ozs7Ozs7OztzRUFFMUIsOERBQUNDOzs4RUFDQyw4REFBQ2tCO29FQUFHbkIsV0FBVTs4RUFBb0N5QixRQUFRN0IsS0FBSzs7Ozs7OzhFQUMvRCw4REFBQ3dCO29FQUFFcEIsV0FBVTs4RUFBaUJ5QixRQUFRNUIsV0FBVzs7Ozs7Ozs7Ozs7OzttREFaOUM2Qjs7Ozs7Ozs7OztzREFrQlgsOERBQUMvQyxpREFBTUEsQ0FBQ3NCLEdBQUc7NENBQ1RDLFNBQVM7Z0RBQUVDLFNBQVM7Z0RBQUdDLEdBQUc7NENBQUc7NENBQzdCQyxhQUFhO2dEQUFFRixTQUFTO2dEQUFHQyxHQUFHOzRDQUFFOzRDQUNoQ0UsWUFBWTtnREFBRUMsVUFBVTtnREFBS0ssT0FBTzs0Q0FBSTs0Q0FDeENKLFVBQVU7Z0RBQUVDLE1BQU07NENBQUs7NENBQ3ZCVCxXQUFVO3NEQUVWLDRFQUFDMkI7Z0RBQ0NDLE1BQUs7Z0RBQ0w1QixXQUFVOzBEQUNYOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FPVCw4REFBQ0M7NEJBQUlELFdBQVU7c0NBQ2IsNEVBQUNDO2dDQUFJRCxXQUFVOztrREFDYiw4REFBQ0M7OzBEQUNDLDhEQUFDdEIsaURBQU1BLENBQUM0QyxFQUFFO2dEQUNSckIsU0FBUztvREFBRUMsU0FBUztvREFBR0MsR0FBRztnREFBRztnREFDN0JDLGFBQWE7b0RBQUVGLFNBQVM7b0RBQUdDLEdBQUc7Z0RBQUU7Z0RBQ2hDRSxZQUFZO29EQUFFQyxVQUFVO29EQUFLSyxPQUFPO2dEQUFJO2dEQUN4Q0osVUFBVTtvREFBRUMsTUFBTTtnREFBSztnREFDdkJULFdBQVU7MERBQ1g7Ozs7OzswREFJRCw4REFBQ0M7Z0RBQUlELFdBQVU7MERBQ1pOLGtCQUFrQjhCLEdBQUcsQ0FBQyxDQUFDQyxTQUFTQyxzQkFDL0IsOERBQUMvQyxpREFBTUEsQ0FBQ3NCLEdBQUc7d0RBRVRDLFNBQVM7NERBQUVDLFNBQVM7NERBQUdDLEdBQUc7d0RBQUc7d0RBQzdCQyxhQUFhOzREQUFFRixTQUFTOzREQUFHQyxHQUFHO3dEQUFFO3dEQUNoQ0UsWUFBWTs0REFBRUMsVUFBVTs0REFBS0ssT0FBTyxNQUFNYyxRQUFRO3dEQUFJO3dEQUN0RGxCLFVBQVU7NERBQUVDLE1BQU07d0RBQUs7d0RBQ3ZCVCxXQUFVOzswRUFFViw4REFBQ0M7Z0VBQUlELFdBQVU7MEVBQ2IsNEVBQUN5QixRQUFROUIsSUFBSTtvRUFBQ0ssV0FBVTs7Ozs7Ozs7Ozs7MEVBRTFCLDhEQUFDQzs7a0ZBQ0MsOERBQUNrQjt3RUFBR25CLFdBQVU7a0ZBQW9DeUIsUUFBUTdCLEtBQUs7Ozs7OztrRkFDL0QsOERBQUN3Qjt3RUFBRXBCLFdBQVU7a0ZBQWlCeUIsUUFBUTVCLFdBQVc7Ozs7Ozs7Ozs7Ozs7dURBWjlDNkI7Ozs7Ozs7Ozs7MERBa0JYLDhEQUFDL0MsaURBQU1BLENBQUNzQixHQUFHO2dEQUNUQyxTQUFTO29EQUFFQyxTQUFTO29EQUFHQyxHQUFHO2dEQUFHO2dEQUM3QkMsYUFBYTtvREFBRUYsU0FBUztvREFBR0MsR0FBRztnREFBRTtnREFDaENFLFlBQVk7b0RBQUVDLFVBQVU7b0RBQUtLLE9BQU87Z0RBQUk7Z0RBQ3hDSixVQUFVO29EQUFFQyxNQUFNO2dEQUFLO2dEQUN2QlQsV0FBVTswREFFViw0RUFBQzJCO29EQUNDQyxNQUFLO29EQUNMNUIsV0FBVTs4REFDWDs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBTUwsOERBQUNyQixpREFBTUEsQ0FBQ3NCLEdBQUc7d0NBQ1RDLFNBQVM7NENBQUVDLFNBQVM7NENBQUdDLEdBQUc7d0NBQUc7d0NBQzdCQyxhQUFhOzRDQUFFRixTQUFTOzRDQUFHQyxHQUFHO3dDQUFFO3dDQUNoQ0UsWUFBWTs0Q0FBRUMsVUFBVTs0Q0FBS0ssT0FBTzt3Q0FBSTt3Q0FDeENKLFVBQVU7NENBQUVDLE1BQU07d0NBQUs7d0NBQ3ZCVCxXQUFVO2tEQUVWLDRFQUFDQzs0Q0FBSUQsV0FBVTtzREFDWlYsbUNBQ0MsOERBQUN1QjtnREFDQ2IsV0FBVTtnREFDVmMsS0FBSTtnREFDSmxCLE9BQU07Z0RBQ05tQixRQUFRO2dEQUNSQyxRQUFRO2dEQUNSQyxLQUFLO2dEQUNMQyxJQUFJOzs7OzswRUFHTiw4REFBQ2pCO2dEQUFJRCxXQUFVOztrRUFFYiw4REFBQ0M7d0RBQUlELFdBQVU7Ozs7OztrRUFDZiw4REFBQ0M7d0RBQUlELFdBQVU7Ozs7OztrRUFDZiw4REFBQ0M7d0RBQUlELFdBQVU7Ozs7OztrRUFHZiw4REFBQ0M7d0RBQUlELFdBQVU7OzBFQUNiLDhEQUFDQztnRUFBSUQsV0FBVTs7a0ZBQ2IsOERBQUNsQiw0SEFBTUE7d0VBQUNrQixXQUFVOzs7Ozs7a0ZBQ2xCLDhEQUFDbUI7d0VBQUduQixXQUFVO2tGQUErQjs7Ozs7O2tGQUM3Qyw4REFBQ29CO3dFQUFFcEIsV0FBVTtrRkFBd0I7Ozs7Ozs7Ozs7OzswRUFFdkMsOERBQUNxQjtnRUFDQ0MsU0FBUyxJQUFNL0Isc0JBQXNCO2dFQUNyQ1MsV0FBVTswRUFFViw0RUFBQ3BCLDZIQUFJQTtvRUFBQ29CLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFheEM7QUFFQSxpRUFBZVgsZUFBZUEsRUFBQSIsInNvdXJjZXMiOlsiQzpcXFByb2plY3RzXFxOZXdCbGlua0ZpbmRBSVxcZnJvbnRlbmQtbWFzdGVyXFxzcmNcXGNvbXBvbmVudHNcXGZlYXR1cmVzXFxTaG93Y2FzZVByb2R1Y3QuanN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuaW1wb3J0IHsgbW90aW9uIH0gZnJvbSAnZnJhbWVyLW1vdGlvbidcbmltcG9ydCB7IFBsYXksIFNtYXJ0cGhvbmUsIFNlYXJjaCwgSGVhcnQsIEZpbGVUZXh0LCBaYXAsIFRhcmdldCB9IGZyb20gJ2x1Y2lkZS1yZWFjdCdcbmltcG9ydCB7IFNwYXJrbGVzSWNvbiB9IGZyb20gJ0BoZXJvaWNvbnMvcmVhY3QvMjQvc29saWQnXG5pbXBvcnQgeyB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0J1xuXG5jb25zdCBQcm9kdWN0U2hvd2Nhc2UgPSAoKSA9PiB7XG4gIGNvbnN0IFtpc1F1aWNrRmluZFBsYXlpbmcsIHNldElzUXVpY2tGaW5kUGxheWluZ10gPSB1c2VTdGF0ZShmYWxzZSlcbiAgY29uc3QgW2lzUmVzdW1lQnVpbGRlclBsYXlpbmcsIHNldElzUmVzdW1lQnVpbGRlclBsYXlpbmddID0gdXNlU3RhdGUoZmFsc2UpXG5cbiAgY29uc3QgcXVpY2tGaW5kRmVhdHVyZXMgPSBbXG4gICAge1xuICAgICAgaWNvbjogU2VhcmNoLFxuICAgICAgdGl0bGU6IFwiRWFzeSBSZXBvcnRpbmdcIixcbiAgICAgIGRlc2NyaXB0aW9uOiBcIkZvdW5kIHNvbWV0aGluZyB2YWx1YWJsZT8gVXBsb2FkIGl0IGFuZCBoZWxwIHNvbWVvbmUgcmVjb25uZWN0LlwiXG4gICAgfSxcbiAgICB7XG4gICAgICBpY29uOiBTbWFydHBob25lLFxuICAgICAgdGl0bGU6IFwiU2ltcGxlIFNlYXJjaFwiLFxuICAgICAgZGVzY3JpcHRpb246IFwiRWZmb3J0bGVzc2x5IGZpbmQgeW91ciBsb3N0IGl0ZW1zIHRocm91Z2ggZWFzeSBzZWFyY2ggZmlsdGVycy5cIlxuICAgIH0sXG4gICAge1xuICAgICAgaWNvbjogSGVhcnQsXG4gICAgICB0aXRsZTogXCJFbW90aW9uYWwgVmFsdWVcIixcbiAgICAgIGRlc2NyaXB0aW9uOiBcIlJlc3RvcmluZyBtZW1vcmllcyBhbmQgY29ubmVjdGlvbnMgdGhhdCBjYW4gbmV2ZXIgYmUgcmVwbGFjZWQuXCJcbiAgICB9XG4gIF1cblxuICBjb25zdCByZXN1bWVCdWlsZGVyRmVhdHVyZXMgPSBbXG4gICAge1xuICAgICAgaWNvbjogRmlsZVRleHQsXG4gICAgICB0aXRsZTogXCJBSS1Qb3dlcmVkIFdyaXRpbmdcIixcbiAgICAgIGRlc2NyaXB0aW9uOiBcIkdlbmVyYXRlIHByb2Zlc3Npb25hbCBjb250ZW50IHRhaWxvcmVkIHRvIHlvdXIgZXhwZXJpZW5jZSBhbmQgaW5kdXN0cnkuXCJcbiAgICB9LFxuICAgIHtcbiAgICAgIGljb246IFphcCxcbiAgICAgIHRpdGxlOiBcIkluc3RhbnQgQ3JlYXRpb25cIixcbiAgICAgIGRlc2NyaXB0aW9uOiBcIkJ1aWxkIGEgY29tcGxldGUgcmVzdW1lIGluIG1pbnV0ZXMgd2l0aCBvdXIgaW50ZWxsaWdlbnQgdGVtcGxhdGVzLlwiXG4gICAgfSxcbiAgICB7XG4gICAgICBpY29uOiBUYXJnZXQsXG4gICAgICB0aXRsZTogXCJBVFMgT3B0aW1pemVkXCIsXG4gICAgICBkZXNjcmlwdGlvbjogXCJFbnN1cmUgeW91ciByZXN1bWUgcGFzc2VzIHRocm91Z2ggYXBwbGljYW50IHRyYWNraW5nIHN5c3RlbXMuXCJcbiAgICB9XG4gIF1cblxuICByZXR1cm4gKFxuICAgIDxzZWN0aW9uIGNsYXNzTmFtZT1cInB5LTIwIGJnLWdyYWRpZW50LXRvLWIgZnJvbS1ibGFjayB0by1bIzBBMEEwQV1cIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29udGFpbmVyIG14LWF1dG8gcHgtNlwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIG1iLTE2XCI+XG4gICAgICAgICAgPG1vdGlvbi5kaXYgXG4gICAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHk6IDIwIH19XG4gICAgICAgICAgICB3aGlsZUluVmlldz17eyBvcGFjaXR5OiAxLCB5OiAwIH19XG4gICAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjUgfX1cbiAgICAgICAgICAgIHZpZXdwb3J0PXt7IG9uY2U6IHRydWUgfX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGdhcC0yIG1iLTRcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxTcGFya2xlc0ljb24gY2xhc3NOYW1lPVwiaC01IHctNSB0ZXh0LXByaW1hcnlcIiAvPlxuICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LXByaW1hcnlcIj5PVVIgUFJPRFVDVFM8L3NwYW4+XG4gICAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgICAgIFxuICAgICAgICAgIDxtb3Rpb24uaDJcbiAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeTogMjAgfX1cbiAgICAgICAgICAgIHdoaWxlSW5WaWV3PXt7IG9wYWNpdHk6IDEsIHk6IDAgfX1cbiAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDAuNSwgZGVsYXk6IDAuMSB9fVxuICAgICAgICAgICAgdmlld3BvcnQ9e3sgb25jZTogdHJ1ZSB9fVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC0zeGwgbWQ6dGV4dC00eGwgZm9udC1ib2xkIHRleHQtd2hpdGVcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIElubm92YXRpdmUgQUkgU29sdXRpb25zIGZvciBFdmVyeWRheSBDaGFsbGVuZ2VzXG4gICAgICAgICAgPC9tb3Rpb24uaDI+XG4gICAgICAgIDwvZGl2PiAgICAgICBcblxuICAgICAgICB7LyogQUkgUmVzdW1lIEJ1aWxkZXIgUHJvZHVjdCBTZWN0aW9uICovfVxuICAgICAgICA8ZGl2PlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBsZzpncmlkLWNvbHMtMiBnYXAtMTIgaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHk6IDIwIH19XG4gICAgICAgICAgICAgIHdoaWxlSW5WaWV3PXt7IG9wYWNpdHk6IDEsIHk6IDAgfX1cbiAgICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMC41LCBkZWxheTogMC4yIH19XG4gICAgICAgICAgICAgIHZpZXdwb3J0PXt7IG9uY2U6IHRydWUgfX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicmVsYXRpdmUgYXNwZWN0LVs5LzE2XSBtYXgtdy1bMzAwcHhdIG14LWF1dG8gbGc6bXgtMCBvcmRlci0yIGxnOm9yZGVyLTFcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdsYXNzLWVmZmVjdCByb3VuZGVkLTJ4bCBvdmVyZmxvdy1oaWRkZW4gYm9yZGVyIGJvcmRlci13aGl0ZS8xMCBoLWZ1bGxcIj5cbiAgICAgICAgICAgICAgICB7aXNSZXN1bWVCdWlsZGVyUGxheWluZyA/IChcbiAgICAgICAgICAgICAgICAgIDx2aWRlb1xuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIHctZnVsbCBoLWZ1bGwgb2JqZWN0LWNvdmVyXCJcbiAgICAgICAgICAgICAgICAgICAgc3JjPVwiL1ZpZGVvcy9yZXN1bWVfYnVpbGRlcl9kZW1vLm1wNFwiXG4gICAgICAgICAgICAgICAgICAgIHRpdGxlPVwiQUkgUmVzdW1lIEJ1aWxkZXIgRGVtb1wiXG4gICAgICAgICAgICAgICAgICAgIGF1dG9QbGF5XG4gICAgICAgICAgICAgICAgICAgIGNvbnRyb2xzXG4gICAgICAgICAgICAgICAgICAgIG11dGVkXG4gICAgICAgICAgICAgICAgICAgIGxvb3BcbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBiZy1ncmFkaWVudC10by1iciBmcm9tLW5ldXJhbC1wdXJwbGUvMjAgdmlhLW5ldXJhbC1waW5rLzIwIHRvLW5ldXJhbC1ibHVlLzIwIGJhY2tkcm9wLWJsdXItc21cIj5cbiAgICAgICAgICAgICAgICAgICAgey8qIEF0dHJhY3RpdmUgYmFja2dyb3VuZCBwYXR0ZXJuICovfVxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1wdXJwbGUtNjAwLzMwIHRvLXBpbmstNjAwLzMwXCI+PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCBiZy1bdXJsKCcvYWkuanBnJyldIGJnLWNvdmVyIGJnLWNlbnRlciBvcGFjaXR5LTQwXCI+PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCBiZy1ncmFkaWVudC10by10IGZyb20tYmxhY2svNjAgdmlhLXRyYW5zcGFyZW50IHRvLWJsYWNrLzMwXCI+PC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgey8qIENvbnRlbnQgb3ZlcmxheSAqL31cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSB6LTEwIHRleHQtY2VudGVyIHAtNlwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPEZpbGVUZXh0IGNsYXNzTmFtZT1cImgtMTIgdy0xMiB0ZXh0LXdoaXRlIG14LWF1dG8gbWItMlwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwidGV4dC13aGl0ZSBmb250LWJvbGQgdGV4dC1sZ1wiPkFJIFJlc3VtZSBCdWlsZGVyPC9oND5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS0yMDAgdGV4dC1zbVwiPlByb2Zlc3Npb25hbCByZXN1bWVzIGluIG1pbnV0ZXM8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0SXNSZXN1bWVCdWlsZGVyUGxheWluZyh0cnVlKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWdyYWRpZW50LXRvLXIgZnJvbS1wcmltYXJ5IHRvLXB1cnBsZS02MDAgdGV4dC13aGl0ZSByb3VuZGVkLWZ1bGwgcC0zIHRyYW5zaXRpb24tdHJhbnNmb3JtIHRyYW5zZm9ybSBob3ZlcjpzY2FsZS0xMTAgc2hhZG93LWxnXCJcbiAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICA8UGxheSBjbGFzc05hbWU9XCJoLTYgdy02XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvbW90aW9uLmRpdj5cblxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJvcmRlci0xIGxnOm9yZGVyLTJcIj5cbiAgICAgICAgICAgICAgPG1vdGlvbi5oM1xuICAgICAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeTogMjAgfX1cbiAgICAgICAgICAgICAgICB3aGlsZUluVmlldz17eyBvcGFjaXR5OiAxLCB5OiAwIH19XG4gICAgICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMC41LCBkZWxheTogMC4yIH19XG4gICAgICAgICAgICAgICAgdmlld3BvcnQ9e3sgb25jZTogdHJ1ZSB9fVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LXdoaXRlIG1iLTZcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgQUkgUmVzdW1lIEJ1aWxkZXI6IFByb2Zlc3Npb25hbCBSZXN1bWVzIGluIE1pbnV0ZXNcbiAgICAgICAgICAgICAgPC9tb3Rpb24uaDM+XG5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgICAgICAgICAgICB7cmVzdW1lQnVpbGRlckZlYXR1cmVzLm1hcCgoZmVhdHVyZSwgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICAgICAgICAgIGtleT17aW5kZXh9XG4gICAgICAgICAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeTogMjAgfX1cbiAgICAgICAgICAgICAgICAgICAgd2hpbGVJblZpZXc9e3sgb3BhY2l0eTogMSwgeTogMCB9fVxuICAgICAgICAgICAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjUsIGRlbGF5OiAwLjMgKyBpbmRleCAqIDAuMSB9fVxuICAgICAgICAgICAgICAgICAgICB2aWV3cG9ydD17eyBvbmNlOiB0cnVlIH19XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtc3RhcnQgZ2FwLTRcIlxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXByaW1hcnkvMTAgcC0yIHJvdW5kZWQtbGdcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZmVhdHVyZS5pY29uIGNsYXNzTmFtZT1cImgtNSB3LTUgdGV4dC1wcmltYXJ5XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LXdoaXRlXCI+e2ZlYXR1cmUudGl0bGV9PC9oND5cbiAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwXCI+e2ZlYXR1cmUuZGVzY3JpcHRpb259PC9wPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvbW90aW9uLmRpdj5cbiAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHk6IDIwIH19XG4gICAgICAgICAgICAgICAgd2hpbGVJblZpZXc9e3sgb3BhY2l0eTogMSwgeTogMCB9fVxuICAgICAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDAuNSwgZGVsYXk6IDAuNiB9fVxuICAgICAgICAgICAgICAgIHZpZXdwb3J0PXt7IG9uY2U6IHRydWUgfX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJtdC04XCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxhXG4gICAgICAgICAgICAgICAgICBocmVmPVwiL3Jlc3VtZS1idWlsZGVyXCJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBweC02IHB5LTMgYmctZ3JhZGllbnQtdG8tciBmcm9tLXByaW1hcnkgdG8tcHVycGxlLTYwMCB0ZXh0LXdoaXRlIHJvdW5kZWQtbGcgZm9udC1tZWRpdW0gaG92ZXI6b3BhY2l0eS05MCB0cmFuc2l0aW9uLW9wYWNpdHlcIlxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIFRyeSBBSSBSZXN1bWUgQnVpbGRlclxuICAgICAgICAgICAgICAgIDwvYT5cbiAgICAgICAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgIHsvKiBRdWlja0ZpbmQgUHJvZHVjdCBTZWN0aW9uICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTI0XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIGxnOmdyaWQtY29scy0yIGdhcC0xMiBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxtb3Rpb24uaDNcbiAgICAgICAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHk6IDIwIH19XG4gICAgICAgICAgICAgICAgd2hpbGVJblZpZXc9e3sgb3BhY2l0eTogMSwgeTogMCB9fVxuICAgICAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDAuNSwgZGVsYXk6IDAuMiB9fVxuICAgICAgICAgICAgICAgIHZpZXdwb3J0PXt7IG9uY2U6IHRydWUgfX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC13aGl0ZSBtYi02XCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIFF1aWNrRmluZDogQUktUG93ZXJlZCBMb3N0ICYgRm91bmQgUGxhdGZvcm1cbiAgICAgICAgICAgICAgPC9tb3Rpb24uaDM+XG5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgICAgICAgICAgICB7cXVpY2tGaW5kRmVhdHVyZXMubWFwKChmZWF0dXJlLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgICAgICAgICAga2V5PXtpbmRleH1cbiAgICAgICAgICAgICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB5OiAyMCB9fVxuICAgICAgICAgICAgICAgICAgICB3aGlsZUluVmlldz17eyBvcGFjaXR5OiAxLCB5OiAwIH19XG4gICAgICAgICAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDAuNSwgZGVsYXk6IDAuMyArIGluZGV4ICogMC4xIH19XG4gICAgICAgICAgICAgICAgICAgIHZpZXdwb3J0PXt7IG9uY2U6IHRydWUgfX1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1zdGFydCBnYXAtNFwiXG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctcHJpbWFyeS8xMCBwLTIgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgICAgICAgICAgICAgIDxmZWF0dXJlLmljb24gY2xhc3NOYW1lPVwiaC01IHctNSB0ZXh0LXByaW1hcnlcIiAvPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtd2hpdGVcIj57ZmVhdHVyZS50aXRsZX08L2g0PlxuICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDBcIj57ZmVhdHVyZS5kZXNjcmlwdGlvbn08L3A+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeTogMjAgfX1cbiAgICAgICAgICAgICAgICB3aGlsZUluVmlldz17eyBvcGFjaXR5OiAxLCB5OiAwIH19XG4gICAgICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMC41LCBkZWxheTogMC42IH19XG4gICAgICAgICAgICAgICAgdmlld3BvcnQ9e3sgb25jZTogdHJ1ZSB9fVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm10LThcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPGFcbiAgICAgICAgICAgICAgICAgIGhyZWY9XCIvcXVpY2tmaW5kXCJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBweC02IHB5LTMgYmctZ3JhZGllbnQtdG8tciBmcm9tLXByaW1hcnkgdG8tcHVycGxlLTYwMCB0ZXh0LXdoaXRlIHJvdW5kZWQtbGcgZm9udC1tZWRpdW0gaG92ZXI6b3BhY2l0eS05MCB0cmFuc2l0aW9uLW9wYWNpdHlcIlxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIExlYXJuIE1vcmUgQWJvdXQgUXVpY2tGaW5kXG4gICAgICAgICAgICAgICAgPC9hPlxuICAgICAgICAgICAgICA8L21vdGlvbi5kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB5OiAyMCB9fVxuICAgICAgICAgICAgICB3aGlsZUluVmlldz17eyBvcGFjaXR5OiAxLCB5OiAwIH19XG4gICAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDAuNSwgZGVsYXk6IDAuMiB9fVxuICAgICAgICAgICAgICB2aWV3cG9ydD17eyBvbmNlOiB0cnVlIH19XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInJlbGF0aXZlIGFzcGVjdC1bOS8xNl0gbWF4LXctWzMwMHB4XSBteC1hdXRvIGxnOm14LTBcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdsYXNzLWVmZmVjdCByb3VuZGVkLTJ4bCBvdmVyZmxvdy1oaWRkZW4gYm9yZGVyIGJvcmRlci13aGl0ZS8xMCBoLWZ1bGxcIj5cbiAgICAgICAgICAgICAgICB7aXNRdWlja0ZpbmRQbGF5aW5nID8gKFxuICAgICAgICAgICAgICAgICAgPHZpZGVvXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgdy1mdWxsIGgtZnVsbCBvYmplY3QtY292ZXJcIlxuICAgICAgICAgICAgICAgICAgICBzcmM9XCIvVmlkZW9zL3F1aWNrRmluZF92aWRlby5tcDRcIlxuICAgICAgICAgICAgICAgICAgICB0aXRsZT1cIlF1aWNrRmluZCBBcHAgRGVtb1wiXG4gICAgICAgICAgICAgICAgICAgIGF1dG9QbGF5XG4gICAgICAgICAgICAgICAgICAgIGNvbnRyb2xzXG4gICAgICAgICAgICAgICAgICAgIG11dGVkXG4gICAgICAgICAgICAgICAgICAgIGxvb3BcbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBiZy1ncmFkaWVudC10by1iciBmcm9tLW5ldXJhbC1ibHVlLzIwIHZpYS1uZXVyYWwtcHVycGxlLzIwIHRvLW5ldXJhbC1waW5rLzIwIGJhY2tkcm9wLWJsdXItc21cIj5cbiAgICAgICAgICAgICAgICAgICAgey8qIEF0dHJhY3RpdmUgYmFja2dyb3VuZCBwYXR0ZXJuICovfVxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1ibHVlLTYwMC8zMCB0by1jeWFuLTYwMC8zMFwiPjwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgYmctW3VybCgnL2xvc3RGb3VuZC9sZjEuanBnJyldIGJnLWNvdmVyIGJnLWNlbnRlciBvcGFjaXR5LTQwXCI+PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCBiZy1ncmFkaWVudC10by10IGZyb20tYmxhY2svNjAgdmlhLXRyYW5zcGFyZW50IHRvLWJsYWNrLzMwXCI+PC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgey8qIENvbnRlbnQgb3ZlcmxheSAqL31cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSB6LTEwIHRleHQtY2VudGVyIHAtNlwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPFNlYXJjaCBjbGFzc05hbWU9XCJoLTEyIHctMTIgdGV4dC13aGl0ZSBteC1hdXRvIG1iLTJcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cInRleHQtd2hpdGUgZm9udC1ib2xkIHRleHQtbGdcIj5RdWlja0ZpbmQ8L2g0PlxuICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTIwMCB0ZXh0LXNtXCI+QUktcG93ZXJlZCBsb3N0ICYgZm91bmQ8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0SXNRdWlja0ZpbmRQbGF5aW5nKHRydWUpfVxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctZ3JhZGllbnQtdG8tciBmcm9tLXByaW1hcnkgdG8tcHVycGxlLTYwMCB0ZXh0LXdoaXRlIHJvdW5kZWQtZnVsbCBwLTMgdHJhbnNpdGlvbi10cmFuc2Zvcm0gdHJhbnNmb3JtIGhvdmVyOnNjYWxlLTExMCBzaGFkb3ctbGdcIlxuICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxQbGF5IGNsYXNzTmFtZT1cImgtNiB3LTZcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICA8L3NlY3Rpb24+XG4gIClcbn1cblxuZXhwb3J0IGRlZmF1bHQgUHJvZHVjdFNob3djYXNlIl0sIm5hbWVzIjpbIm1vdGlvbiIsIlBsYXkiLCJTbWFydHBob25lIiwiU2VhcmNoIiwiSGVhcnQiLCJGaWxlVGV4dCIsIlphcCIsIlRhcmdldCIsIlNwYXJrbGVzSWNvbiIsInVzZVN0YXRlIiwiUHJvZHVjdFNob3djYXNlIiwiaXNRdWlja0ZpbmRQbGF5aW5nIiwic2V0SXNRdWlja0ZpbmRQbGF5aW5nIiwiaXNSZXN1bWVCdWlsZGVyUGxheWluZyIsInNldElzUmVzdW1lQnVpbGRlclBsYXlpbmciLCJxdWlja0ZpbmRGZWF0dXJlcyIsImljb24iLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwicmVzdW1lQnVpbGRlckZlYXR1cmVzIiwic2VjdGlvbiIsImNsYXNzTmFtZSIsImRpdiIsImluaXRpYWwiLCJvcGFjaXR5IiwieSIsIndoaWxlSW5WaWV3IiwidHJhbnNpdGlvbiIsImR1cmF0aW9uIiwidmlld3BvcnQiLCJvbmNlIiwic3BhbiIsImgyIiwiZGVsYXkiLCJ2aWRlbyIsInNyYyIsImF1dG9QbGF5IiwiY29udHJvbHMiLCJtdXRlZCIsImxvb3AiLCJoNCIsInAiLCJidXR0b24iLCJvbkNsaWNrIiwiaDMiLCJtYXAiLCJmZWF0dXJlIiwiaW5kZXgiLCJhIiwiaHJlZiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/features/ShowcaseProduct.jsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Hero.jsx":
/*!****************************************!*\
  !*** ./src/components/layout/Hero.jsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_SparklesIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=SparklesIcon!=!@heroicons/react/24/solid */ \"(ssr)/./node_modules/@heroicons/react/24/solid/esm/SparklesIcon.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Bot_FileText_Globe_Search_Shield_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Bot,FileText,Globe,Search,Shield,Smartphone!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Bot_FileText_Globe_Search_Shield_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Bot,FileText,Globe,Search,Shield,Smartphone!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Bot_FileText_Globe_Search_Shield_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Bot,FileText,Globe,Search,Shield,Smartphone!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Bot_FileText_Globe_Search_Shield_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Bot,FileText,Globe,Search,Shield,Smartphone!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _components_features_PlatformStats__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/features/PlatformStats */ \"(ssr)/./src/components/features/PlatformStats.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nconst Hero = ()=>{\n    const [currentSlide, setCurrentSlide] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // AI Products data  // AI Products data (all marked as upcoming)\n    const aiProducts = [\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Bot_FileText_Globe_Search_Shield_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"h-8 w-8\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                lineNumber: 20,\n                columnNumber: 13\n            }, undefined),\n            title: \"AI Resume Builder\",\n            description: \"Create ATS-optimized resumes in minutes with AI-powered suggestions\",\n            features: [\n                \"Create Resume in 5 minutes\",\n                \"ATS Scoring\",\n                \"Job-Specific Content\"\n            ],\n            color: \"from-neural-purple to-neural-pink\",\n            link: \"/resume-builder\",\n            upcoming: true\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Bot_FileText_Globe_Search_Shield_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                className: \"h-8 w-8\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                lineNumber: 29,\n                columnNumber: 13\n            }, undefined),\n            title: \"QuickFind\",\n            description: \"Lost & Found platform with AI-powered matching and recovery\",\n            features: [\n                \"Instant Search\",\n                \"AI Matching\",\n                \"Secure Recovery\"\n            ],\n            color: \"from-neural-blue to-neural-purple\",\n            link: \"/quickfind\",\n            upcoming: true\n        }\n    ];\n    // Auto-rotate carousel\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Hero.useEffect\": ()=>{\n            const interval = setInterval({\n                \"Hero.useEffect.interval\": ()=>{\n                    setCurrentSlide({\n                        \"Hero.useEffect.interval\": (prev)=>(prev + 1) % aiProducts.length\n                    }[\"Hero.useEffect.interval\"]);\n                }\n            }[\"Hero.useEffect.interval\"], 4000);\n            return ({\n                \"Hero.useEffect\": ()=>clearInterval(interval)\n            })[\"Hero.useEffect\"];\n        }\n    }[\"Hero.useEffect\"], [\n        aiProducts.length\n    ]);\n    const nextSlide = ()=>{\n        setCurrentSlide((prev)=>(prev + 1) % aiProducts.length);\n    };\n    const prevSlide = ()=>{\n        setCurrentSlide((prev)=>(prev - 1 + aiProducts.length) % aiProducts.length);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative overflow-hidden bg-gradient-to-b from-black to-[#0A0A0A] min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 opacity-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 bg-[url('/grid.svg')] [mask-image:radial-gradient(ellipse_at_center,white,transparent)]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                    lineNumber: 57,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                lineNumber: 56,\n                columnNumber: 7\n            }, undefined),\n            \"      \",\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 overflow-hidden\",\n                children: [\n                    [\n                        ...Array(6)\n                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                            className: \"hidden sm:block absolute rounded-full bg-neural-purple opacity-15 lg:opacity-20 blur-xl\",\n                            initial: {\n                                x: Math.random() * 100 - 50,\n                                y: Math.random() * 100 - 50,\n                                width: Math.random() * 120 + 60,\n                                height: Math.random() * 120 + 60\n                            },\n                            animate: {\n                                x: Math.random() * 100 - 50,\n                                y: Math.random() * 100 - 50,\n                                transition: {\n                                    duration: Math.random() * 15 + 15,\n                                    repeat: Infinity,\n                                    repeatType: 'reverse'\n                                }\n                            }\n                        }, i, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                            lineNumber: 62,\n                            columnNumber: 11\n                        }, undefined)),\n                    [\n                        ...Array(3)\n                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                            className: \"block sm:hidden absolute rounded-full bg-neural-purple opacity-8 blur-lg\",\n                            initial: {\n                                x: Math.random() * 100 - 50,\n                                y: Math.random() * 100 - 50,\n                                width: Math.random() * 40 + 20,\n                                height: Math.random() * 40 + 20\n                            },\n                            animate: {\n                                x: Math.random() * 100 - 50,\n                                y: Math.random() * 100 - 50,\n                                transition: {\n                                    duration: Math.random() * 20 + 20,\n                                    repeat: Infinity,\n                                    repeatType: 'reverse'\n                                }\n                            }\n                        }, `mobile-${i}`, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                            lineNumber: 84,\n                            columnNumber: 11\n                        }, undefined))\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 pt-12 pb-12 sm:pt-16 sm:pb-16 lg:pt-20 lg:pb-20 xl:pt-24 xl:pb-24 min-h-screen flex items-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full lg:flex lg:items-center lg:gap-x-12\",\n                    children: [\n                        \"          \",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto max-w-2xl lg:mx-0 lg:flex-auto lg:w-[65%] text-center lg:text-left\",\n                            children: [\n                                \"            \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-x-2 mb-4 hidden sm:flex justify-center lg:justify-start\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_SparklesIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-5 w-5 text-neural-pink\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-semibold leading-6 text-neural-blue\",\n                                            children: \"Your Everyday AI for Smarter Living & Working\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 115\n                                }, undefined),\n                                \"          \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.h1, {\n                                    className: \"text-2xl font-bold tracking-tight text-white sm:text-3xl lg:text-4xl xl:text-5xl leading-tight relative\",\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.5\n                                    },\n                                    children: [\n                                        \"Turn \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"bg-gradient-to-r from-neural-purple to-neural-pink bg-clip-text text-transparent relative\",\n                                            children: [\n                                                \"Hours of Work\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    \"aria-hidden\": \"true\",\n                                                    viewBox: \"0 0 418 42\",\n                                                    className: \"absolute left-0 top-2/3 h-[0.5em] w-full fill-neural-purple/40 opacity-70\",\n                                                    preserveAspectRatio: \"none\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M203.371.916c-26.013-2.078-76.686 1.963-124.73 9.946L67.3 12.749C35.421 18.062 18.2 21.766 6.004 25.934 1.244 27.561.828 27.778.874 28.61c.07 1.214.828 1.121 9.595-1.176 9.072-2.377 17.15-3.92 39.246-7.496C123.565 7.986 157.869 4.492 195.942 5.046c7.461.108 19.25 1.696 19.17 2.582-.107 1.183-7.874 4.31-25.75 10.366-21.992 7.45-35.43 12.534-36.701 13.884-2.173 2.308-.202 4.407 4.442 4.734 2.654.187 3.263.157 15.593-.78 35.401-2.686 57.944-3.488 88.365-3.143 46.327.526 75.721 2.23 130.788 7.584 19.787 1.924 20.814 1.98 24.557 1.332l.066-.011c1.201-.203 1.53-1.825.399-2.335-2.911-1.31-4.893-1.604-22.048-3.261-57.509-5.556-87.871-7.36-132.059-7.842-23.239-.254-33.617-.116-50.627.674-11.629.54-42.371 2.494-46.696 2.967-2.359.259 8.133-3.625 26.504-9.81 23.239-7.825 27.934-10.149 28.304-14.005.417-4.348-3.529-6-16.878-7.066Z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                                        lineNumber: 125,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                                    lineNumber: 119,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                            lineNumber: 117,\n                                            columnNumber: 18\n                                        }, undefined),\n                                        \" Into \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"bg-gradient-to-r from-neural-blue to-neural-purple bg-clip-text text-transparent relative\",\n                                            children: [\n                                                \"Minutes\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    \"aria-hidden\": \"true\",\n                                                    viewBox: \"0 0 418 42\",\n                                                    className: \"absolute left-0 top-2/3 h-[0.4em] w-full fill-neural-blue/30 opacity-60\",\n                                                    preserveAspectRatio: \"none\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M203.371.916c-26.013-2.078-76.686 1.963-124.73 9.946L67.3 12.749C35.421 18.062 18.2 21.766 6.004 25.934 1.244 27.561.828 27.778.874 28.61c.07 1.214.828 1.121 9.595-1.176 9.072-2.377 17.15-3.92 39.246-7.496C123.565 7.986 157.869 4.492 195.942 5.046c7.461.108 19.25 1.696 19.17 2.582-.107 1.183-7.874 4.31-25.75 10.366-21.992 7.45-35.43 12.534-36.701 13.884-2.173 2.308-.202 4.407 4.442 4.734 2.654.187 3.263.157 15.593-.78 35.401-2.686 57.944-3.488 88.365-3.143 46.327.526 75.721 2.23 130.788 7.584 19.787 1.924 20.814 1.98 24.557 1.332l.066-.011c1.201-.203 1.53-1.825.399-2.335-2.911-1.31-4.893-1.604-22.048-3.261-57.509-5.556-87.871-7.36-132.059-7.842-23.239-.254-33.617-.116-50.627.674-11.629.54-42.371 2.494-46.696 2.967-2.359.259 8.133-3.625 26.504-9.81 23.239-7.825 27.934-10.149 28.304-14.005.417-4.348-3.529-6-16.878-7.066Z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                                        lineNumber: 135,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                                    lineNumber: 129,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 26\n                                        }, undefined),\n                                        \" — With \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"bg-gradient-to-r from-neural-pink to-neural-blue bg-clip-text text-transparent\",\n                                            children: \"AI\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 28\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 27\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.p, {\n                                    className: \"mt-4 text-base leading-6 text-gray-300 max-w-xl mx-auto lg:mx-0\",\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.5,\n                                        delay: 0.2\n                                    },\n                                    children: \"Apply for jobs, plan your day, automate your business — all without switching between tools.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.p, {\n                                    className: \"mt-3 text-xs text-gray-400 max-w-xl mx-auto lg:mx-0\",\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.5,\n                                        delay: 0.3\n                                    },\n                                    children: \"Built for busy people who want real results.          \"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 11\n                                }, undefined),\n                                \"          \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                    className: \"mt-6 flex flex-row items-center gap-3 justify-center lg:justify-start\",\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.5,\n                                        delay: 0.4\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/resume-builder\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.button, {\n                                            whileHover: {\n                                                scale: 1.05\n                                            },\n                                            whileTap: {\n                                                scale: 0.95\n                                            },\n                                            className: \"rounded-lg bg-gradient-to-r from-neural-purple to-neural-pink px-4 sm:px-6 py-2 sm:py-2.5 text-xs sm:text-sm font-semibold text-white shadow-lg hover:opacity-90 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-neural-purple transition-all duration-300\",\n                                            children: \"Start Free\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 12\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_features_PlatformStats__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                            lineNumber: 106,\n                            columnNumber: 11\n                        }, undefined),\n                        \"        \",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-8 sm:mt-12 lg:mt-0 lg:w-[30%] hidden sm:block\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                className: \"relative\",\n                                initial: {\n                                    opacity: 0,\n                                    x: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    x: 0\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: 0.3\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 rounded-xl bg-gradient-to-tr from-neural-purple to-neural-blue opacity-30 blur-xl\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative rounded-xl bg-gray-900/10 p-2 ring-1 ring-white/10 backdrop-blur-md\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-[300px] sm:h-[320px] lg:h-[340px] w-full rounded-lg bg-gradient-to-br from-black/50 to-gray-900/50 p-4 overflow-hidden\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2 text-gray-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Bot_FileText_Globe_Search_Shield_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-neural-blue\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                                                    lineNumber: 189,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs font-medium\",\n                                                                    children: \"AI Products\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                                                    lineNumber: 190,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                                            lineNumber: 188,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: prevSlide,\n                                                                    className: \"p-1 rounded-full bg-gray-800/50 hover:bg-gray-700/50 transition-colors\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Bot_FileText_Globe_Search_Shield_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                        className: \"h-3 w-3 text-gray-400 rotate-180\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                                                        lineNumber: 197,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                                                    lineNumber: 193,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: nextSlide,\n                                                                    className: \"p-1 rounded-full bg-gray-800/50 hover:bg-gray-700/50 transition-colors\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Bot_FileText_Globe_Search_Shield_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                        className: \"h-3 w-3 text-gray-400\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                                                        lineNumber: 203,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                                                    lineNumber: 199,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                                            lineNumber: 192,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                                    lineNumber: 187,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative h-full\",\n                                                    children: aiProducts.map((product, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                                            className: `absolute inset-0 ${index === currentSlide ? 'opacity-100' : 'opacity-0'} transition-opacity duration-500`,\n                                                            children: [\n                                                                \"                      \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-gray-900/60 backdrop-blur-sm rounded-lg p-4 h-full border border-white/10\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: `inline-flex p-2 rounded-lg bg-gradient-to-r ${product.color} mb-3`,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Bot_FileText_Globe_Search_Shield_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                className: \"h-6 w-6\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                                                                lineNumber: 216,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                                                            lineNumber: 215,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"text-lg font-bold text-white mb-2\",\n                                                                            children: product.title\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                                                            lineNumber: 219,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-300 text-xs mb-4 leading-relaxed\",\n                                                                            children: product.description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                                                            lineNumber: 223,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-2 mb-4\",\n                                                                            children: product.features.map((feature, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center gap-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"h-1 w-1 rounded-full bg-neural-purple\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                                                                            lineNumber: 230,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"text-xs text-gray-400\",\n                                                                                            children: feature\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                                                                            lineNumber: 231,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, idx, true, {\n                                                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                                                                    lineNumber: 229,\n                                                                                    columnNumber: 29\n                                                                                }, undefined))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                                                            lineNumber: 227,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                            href: product.link,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.button, {\n                                                                                whileHover: {\n                                                                                    scale: 1.02\n                                                                                },\n                                                                                whileTap: {\n                                                                                    scale: 0.98\n                                                                                },\n                                                                                className: `w-full py-2.5 px-3 rounded-lg bg-gradient-to-r ${product.color} text-white font-medium text-xs hover:opacity-90 transition-opacity`,\n                                                                                children: [\n                                                                                    \"Explore \",\n                                                                                    product.title\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                                                                lineNumber: 237,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                                                            lineNumber: 236,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                                                    lineNumber: 214,\n                                                                    columnNumber: 44\n                                                                }, undefined)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                                            lineNumber: 211,\n                                                            columnNumber: 21\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                                    lineNumber: 209,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                \"                \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute bottom-3 left-1/2 transform -translate-x-1/2 flex gap-1.5\",\n                                                    children: aiProducts.map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setCurrentSlide(index),\n                                                            className: `h-1.5 w-1.5 rounded-full transition-colors ${index === currentSlide ? 'bg-neural-purple' : 'bg-gray-600'}`\n                                                        }, index, false, {\n                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                                            lineNumber: 251,\n                                                            columnNumber: 21\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                                    lineNumber: 249,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                                lineNumber: 177,\n                                columnNumber: 11\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                            lineNumber: 176,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                    lineNumber: 105,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n                lineNumber: 104,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\layout\\\\Hero.jsx\",\n        lineNumber: 54,\n        columnNumber: 15\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Hero);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Hero.jsx\n");

/***/ })

};
;
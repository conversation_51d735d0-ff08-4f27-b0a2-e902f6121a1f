"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/resume-builder/page",{

/***/ "(app-pages-browser)/./src/components/resume/StreamlinedPreview.jsx":
/*!******************************************************!*\
  !*** ./src/components/resume/StreamlinedPreview.jsx ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_FileText_Mail_MapPin_Maximize2_Phone_User_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,FileText,Mail,MapPin,Maximize2,Phone,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_FileText_Mail_MapPin_Maximize2_Phone_User_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,FileText,Mail,MapPin,Maximize2,Phone,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_FileText_Mail_MapPin_Maximize2_Phone_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,FileText,Mail,MapPin,Maximize2,Phone,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_FileText_Mail_MapPin_Maximize2_Phone_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,FileText,Mail,MapPin,Maximize2,Phone,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_FileText_Mail_MapPin_Maximize2_Phone_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,FileText,Mail,MapPin,Maximize2,Phone,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/maximize-2.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_FileText_Mail_MapPin_Maximize2_Phone_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,FileText,Mail,MapPin,Maximize2,Phone,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_FileText_Mail_MapPin_Maximize2_Phone_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,FileText,Mail,MapPin,Maximize2,Phone,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst StreamlinedPreview = (param)=>{\n    let { formData, selectedTemplate = 'professional', onOpenFullscreen } = param;\n    var _formData_personal, _formData_personal1, _formData_experience, _formData_education;\n    _s();\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Check if we have meaningful data to preview\n    const hasContent = formData && (((_formData_personal = formData.personal) === null || _formData_personal === void 0 ? void 0 : _formData_personal.firstName) || ((_formData_personal1 = formData.personal) === null || _formData_personal1 === void 0 ? void 0 : _formData_personal1.email) || ((_formData_experience = formData.experience) === null || _formData_experience === void 0 ? void 0 : _formData_experience.some((exp)=>exp.title || exp.company)) || ((_formData_education = formData.education) === null || _formData_education === void 0 ? void 0 : _formData_education.some((edu)=>edu.degree || edu.institution)));\n    const renderEmptyState = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center justify-center h-full text-gray-400 p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_FileText_Mail_MapPin_Maximize2_Phone_User_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    className: \"h-12 w-12 mb-3 opacity-50\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                    lineNumber: 23,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-sm font-medium mb-1\",\n                    children: \"Resume Preview\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                    lineNumber: 24,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-xs text-center opacity-75\",\n                    children: \"Your resume will appear here as you fill out the form\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                    lineNumber: 25,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n            lineNumber: 22,\n            columnNumber: 5\n        }, undefined);\n    const renderPreviewContent = ()=>{\n        var _formData_personal, _formData_personal1, _formData_personal2, _formData_personal3, _formData_personal4, _formData_personal5, _formData_experience, _formData_education, _formData_skills_technical, _formData_skills, _formData_skills_languages, _formData_skills1, _formData_skills_technical1, _formData_skills_languages1;\n        if (!hasContent) return renderEmptyState();\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-4 space-y-4 text-sm\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center border-b border-gray-200 pb-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-lg font-bold text-gray-900 mb-2\",\n                            children: [\n                                ((_formData_personal = formData.personal) === null || _formData_personal === void 0 ? void 0 : _formData_personal.firstName) || 'Your Name',\n                                \" \",\n                                ((_formData_personal1 = formData.personal) === null || _formData_personal1 === void 0 ? void 0 : _formData_personal1.lastName) || ''\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                            lineNumber: 38,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap justify-center gap-3 text-xs text-gray-600\",\n                            children: [\n                                ((_formData_personal2 = formData.personal) === null || _formData_personal2 === void 0 ? void 0 : _formData_personal2.email) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_FileText_Mail_MapPin_Maximize2_Phone_User_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"h-3 w-3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                                            lineNumber: 45,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: formData.personal.email\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                                            lineNumber: 46,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                                    lineNumber: 44,\n                                    columnNumber: 15\n                                }, undefined),\n                                ((_formData_personal3 = formData.personal) === null || _formData_personal3 === void 0 ? void 0 : _formData_personal3.phone) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_FileText_Mail_MapPin_Maximize2_Phone_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"h-3 w-3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                                            lineNumber: 51,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: formData.personal.phone\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                                            lineNumber: 52,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                                    lineNumber: 50,\n                                    columnNumber: 15\n                                }, undefined),\n                                ((_formData_personal4 = formData.personal) === null || _formData_personal4 === void 0 ? void 0 : _formData_personal4.location) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_FileText_Mail_MapPin_Maximize2_Phone_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"h-3 w-3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                                            lineNumber: 57,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: formData.personal.location\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                                            lineNumber: 58,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                            lineNumber: 42,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                    lineNumber: 37,\n                    columnNumber: 9\n                }, undefined),\n                ((_formData_personal5 = formData.personal) === null || _formData_personal5 === void 0 ? void 0 : _formData_personal5.summary) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-sm font-semibold text-gray-900 mb-2\",\n                            children: \"Professional Summary\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                            lineNumber: 67,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-700 leading-relaxed\",\n                            children: formData.personal.summary.length > 150 ? \"\".concat(formData.personal.summary.substring(0, 150), \"...\") : formData.personal.summary\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                            lineNumber: 68,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                    lineNumber: 66,\n                    columnNumber: 11\n                }, undefined),\n                ((_formData_experience = formData.experience) === null || _formData_experience === void 0 ? void 0 : _formData_experience.some((exp)=>exp.title || exp.company)) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-sm font-semibold text-gray-900 mb-2\",\n                            children: \"Experience\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                            lineNumber: 79,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: [\n                                formData.experience.filter((exp)=>exp.title || exp.company).slice(0, 2).map((exp, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border-l-2 border-blue-100 pl-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-start mb-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xs font-medium text-gray-900\",\n                                                        children: exp.title || 'Job Title'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                                                        lineNumber: 87,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: [\n                                                            exp.startDate,\n                                                            \" \",\n                                                            exp.startDate && (exp.endDate || exp.current) && ' - ',\n                                                            exp.current ? 'Present' : exp.endDate\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                                                        lineNumber: 90,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                                                lineNumber: 86,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-600 mb-1\",\n                                                children: [\n                                                    exp.company || 'Company Name',\n                                                    exp.location && \" • \".concat(exp.location)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                                                lineNumber: 95,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            exp.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-600\",\n                                                children: exp.description.length > 100 ? \"\".concat(exp.description.substring(0, 100), \"...\") : exp.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                                                lineNumber: 100,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 19\n                                    }, undefined)),\n                                formData.experience.filter((exp)=>exp.title || exp.company).length > 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-500 italic pl-3\",\n                                    children: [\n                                        \"+\",\n                                        formData.experience.filter((exp)=>exp.title || exp.company).length - 2,\n                                        \" more positions\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                            lineNumber: 80,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                    lineNumber: 78,\n                    columnNumber: 11\n                }, undefined),\n                ((_formData_education = formData.education) === null || _formData_education === void 0 ? void 0 : _formData_education.some((edu)=>edu.degree || edu.institution)) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-sm font-semibold text-gray-900 mb-2\",\n                            children: \"Education\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                            lineNumber: 121,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: formData.education.filter((edu)=>edu.degree || edu.institution).slice(0, 2).map((edu, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-l-2 border-green-100 pl-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-start\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xs font-medium text-gray-900\",\n                                                        children: [\n                                                            edu.degree || 'Degree',\n                                                            \" \",\n                                                            edu.field && \"in \".concat(edu.field)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                                                        lineNumber: 130,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-600\",\n                                                        children: edu.institution || 'Institution'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                                                        lineNumber: 133,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                                                lineNumber: 129,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: edu.graduationDate\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 21\n                                    }, undefined)\n                                }, index, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 19\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                            lineNumber: 122,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                    lineNumber: 120,\n                    columnNumber: 11\n                }, undefined),\n                (((_formData_skills = formData.skills) === null || _formData_skills === void 0 ? void 0 : (_formData_skills_technical = _formData_skills.technical) === null || _formData_skills_technical === void 0 ? void 0 : _formData_skills_technical.length) > 0 || ((_formData_skills1 = formData.skills) === null || _formData_skills1 === void 0 ? void 0 : (_formData_skills_languages = _formData_skills1.languages) === null || _formData_skills_languages === void 0 ? void 0 : _formData_skills_languages.length) > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-sm font-semibold text-gray-900 mb-2\",\n                            children: \"Skills\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                            lineNumber: 150,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-1\",\n                            children: [\n                                ((_formData_skills_technical1 = formData.skills.technical) === null || _formData_skills_technical1 === void 0 ? void 0 : _formData_skills_technical1.length) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs font-medium text-gray-700\",\n                                            children: \"Technical: \"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-600\",\n                                            children: [\n                                                formData.skills.technical.slice(0, 8).join(', '),\n                                                formData.skills.technical.length > 8 && '...'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                                            lineNumber: 155,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 17\n                                }, undefined),\n                                ((_formData_skills_languages1 = formData.skills.languages) === null || _formData_skills_languages1 === void 0 ? void 0 : _formData_skills_languages1.length) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs font-medium text-gray-700\",\n                                            children: \"Languages: \"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-600\",\n                                            children: formData.skills.languages.join(', ')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                            lineNumber: 151,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                    lineNumber: 149,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n            lineNumber: 35,\n            columnNumber: 7\n        }, undefined);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gray-900/60 backdrop-blur-md border border-gray-700/50 rounded-2xl shadow-xl h-full flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between p-3 border-b border-gray-700/30 bg-gray-800/30\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 bg-neural-pink rounded-full animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                                lineNumber: 181,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-medium text-white\",\n                                children: \"Live Preview\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                                lineNumber: 182,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                        lineNumber: 180,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-1\",\n                        children: [\n                            isVisible && onOpenFullscreen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onOpenFullscreen,\n                                className: \"p-1.5 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors\",\n                                title: \"View Fullscreen\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_FileText_Mail_MapPin_Maximize2_Phone_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                                lineNumber: 187,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsVisible(!isVisible),\n                                className: \"p-1.5 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors\",\n                                title: isVisible ? 'Hide Preview' : 'Show Preview',\n                                children: isVisible ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_FileText_Mail_MapPin_Maximize2_Phone_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 26\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_FileText_Mail_MapPin_Maximize2_Phone_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 59\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                                lineNumber: 195,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                        lineNumber: 185,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                lineNumber: 179,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.AnimatePresence, {\n                    mode: \"wait\",\n                    children: isVisible ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 10\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        exit: {\n                            opacity: 0,\n                            y: -10\n                        },\n                        className: \"h-full overflow-y-auto\",\n                        children: renderPreviewContent()\n                    }, \"preview\", false, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                        lineNumber: 209,\n                        columnNumber: 13\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                        initial: {\n                            opacity: 0\n                        },\n                        animate: {\n                            opacity: 1\n                        },\n                        exit: {\n                            opacity: 0\n                        },\n                        className: \"flex items-center justify-center h-full text-gray-500\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_FileText_Mail_MapPin_Maximize2_Phone_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-8 w-8 mx-auto mb-2 opacity-50\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm\",\n                                    children: \"Preview Hidden\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                                    lineNumber: 228,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                            lineNumber: 226,\n                            columnNumber: 15\n                        }, undefined)\n                    }, \"hidden\", false, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                        lineNumber: 219,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                    lineNumber: 207,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                lineNumber: 206,\n                columnNumber: 7\n            }, undefined),\n            isVisible && hasContent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-2 border-t border-gray-100 bg-gray-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between text-xs text-gray-500\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"Updates automatically\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                            lineNumber: 239,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"capitalize\",\n                            children: selectedTemplate.replace('_', ' ')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                            lineNumber: 240,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                    lineNumber: 238,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                lineNumber: 237,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n        lineNumber: 177,\n        columnNumber: 5\n    }, undefined);\n};\n_s(StreamlinedPreview, \"C45KFF5iQHXNkju7O/pllv86QL4=\");\n_c = StreamlinedPreview;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (StreamlinedPreview);\nvar _c;\n$RefreshReg$(_c, \"StreamlinedPreview\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/resume/StreamlinedPreview.jsx\n"));

/***/ })

});
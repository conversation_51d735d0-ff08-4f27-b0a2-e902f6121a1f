import { database } from '@/firebase/config';
import { ref, get, set, increment, serverTimestamp } from 'firebase/database';

class VisitorService {
  constructor() {
    this.hasTrackedVisit = false;
    this.sessionKey = `blinkfind_visit_${Date.now()}`;
  }

  // Track a new visitor (called once per browser session)
  async trackVisitor() {
    // Prevent multiple tracking in the same session
    if (this.hasTrackedVisit || sessionStorage.getItem('blinkfind_visited')) {
      return;
    }

    try {
      // Get visitor's basic info (non-intrusive)
      const visitorData = {
        timestamp: serverTimestamp(),
        userAgent: navigator.userAgent,
        language: navigator.language,
        referrer: document.referrer || 'direct',
        viewport: `${window.innerWidth}x${window.innerHeight}`,
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        sessionId: this.sessionKey
      };

      // Increment total visitor count
      const visitorCountRef = ref(database, 'analytics/totalVisitors');
      await set(visitorCountRef, increment(1));

      // Store visitor session data
      const sessionRef = ref(database, `analytics/visits/${this.sessionKey}`);
      await set(sessionRef, visitorData);

      // Track daily visits
      const today = new Date().toISOString().split('T')[0];
      const dailyCountRef = ref(database, `analytics/dailyVisits/${today}`);
      await set(dailyCountRef, increment(1));

      // Mark as tracked in session storage
      sessionStorage.setItem('blinkfind_visited', 'true');
      this.hasTrackedVisit = true;

      console.log('Visitor tracked successfully');
    } catch (error) {
      console.error('Error tracking visitor:', error);
    }
  }

  // Track page views
  async trackPageView(pagePath) {
    try {
      const pageViewData = {
        timestamp: serverTimestamp(),
        path: pagePath,
        sessionId: this.sessionKey,
        title: document.title
      };

      // Increment total page views
      const pageViewCountRef = ref(database, 'analytics/totalPageViews');
      await set(pageViewCountRef, increment(1));

      // Track specific page views
      const pageRef = ref(database, `analytics/pageViews/${pagePath.replace(/\//g, '_')}`);
      await set(pageRef, increment(1));

      // Store page view details
      const sessionPageRef = ref(database, `analytics/pageViewDetails/${this.sessionKey}_${Date.now()}`);
      await set(sessionPageRef, pageViewData);

    } catch (error) {
      console.error('Error tracking page view:', error);
    }
  }

  // Get total visitor count
  async getTotalVisitors() {
    try {
      const visitorCountRef = ref(database, 'analytics/totalVisitors');
      const snapshot = await get(visitorCountRef);
      return snapshot.val() || 0;
    } catch (error) {
      console.error('Error getting visitor count:', error);
      return 0;
    }
  }

  // Get analytics data for admin dashboard
  async getAnalyticsData() {
    try {
      const analyticsRef = ref(database, 'analytics');
      const snapshot = await get(analyticsRef);
      const data = snapshot.val() || {};

      const today = new Date().toISOString().split('T')[0];
      const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString().split('T')[0];

      return {
        totalVisitors: data.totalVisitors || 0,
        totalPageViews: data.totalPageViews || 0,
        todayVisits: data.dailyVisits?.[today] || 0,
        yesterdayVisits: data.dailyVisits?.[yesterday] || 0,
        dailyVisits: data.dailyVisits || {},
        pageViews: data.pageViews || {},
        recentVisits: await this.getRecentVisits(data.visits || {})
      };
    } catch (error) {
      console.error('Error getting analytics data:', error);
      return {
        totalVisitors: 0,
        totalPageViews: 0,
        todayVisits: 0,
        yesterdayVisits: 0,
        dailyVisits: {},
        pageViews: {},
        recentVisits: []
      };
    }
  }

  // Get recent visits for admin dashboard
  async getRecentVisits(visitsData) {
    try {
      const visits = Object.entries(visitsData)
        .map(([sessionId, data]) => ({
          sessionId,
          ...data,
          timestamp: data.timestamp || Date.now()
        }))
        .sort((a, b) => b.timestamp - a.timestamp)
        .slice(0, 10); // Get last 10 visits

      return visits;
    } catch (error) {
      console.error('Error processing recent visits:', error);
      return [];
    }
  }

  // Get daily visits for the last 7 days
  async getWeeklyVisits() {
    try {
      const dailyVisitsRef = ref(database, 'analytics/dailyVisits');
      const snapshot = await get(dailyVisitsRef);
      const dailyData = snapshot.val() || {};

      const last7Days = [];
      for (let i = 6; i >= 0; i--) {
        const date = new Date(Date.now() - i * 24 * 60 * 60 * 1000);
        const dateString = date.toISOString().split('T')[0];
        last7Days.push({
          date: dateString,
          visits: dailyData[dateString] || 0,
          label: date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
        });
      }

      return last7Days;
    } catch (error) {
      console.error('Error getting weekly visits:', error);
      return [];
    }
  }
}

// Create singleton instance
const visitorService = new VisitorService();

export default visitorService;

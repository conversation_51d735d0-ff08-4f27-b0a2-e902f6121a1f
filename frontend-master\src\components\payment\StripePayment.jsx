'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  CreditCard, 
  Lock, 
  Loader2, 
  AlertTriangle,
  Check
} from 'lucide-react';
import { formatPrice } from '@/config/pricing';

const StripePayment = ({ 
  plan, 
  amount, 
  currency, 
  billingInterval,
  onSuccess, 
  onError,
  isProcessing,
  setIsProcessing 
}) => {
  const [cardDetails, setCardDetails] = useState({
    number: '',
    expiry: '',
    cvc: '',
    name: ''
  });
  const [errors, setErrors] = useState({});
  const [isStripeLoaded, setIsStripeLoaded] = useState(false);

  useEffect(() => {
    // Load Stripe.js
    const loadStripe = async () => {
      if (window.Stripe) {
        setIsStripeLoaded(true);
        return;
      }

      const script = document.createElement('script');
      script.src = 'https://js.stripe.com/v3/';
      script.onload = () => setIsStripeLoaded(true);
      document.head.appendChild(script);
    };

    loadStripe();
  }, []);

  const validateCard = () => {
    const newErrors = {};

    // Card number validation (basic)
    if (!cardDetails.number || cardDetails.number.replace(/\s/g, '').length < 13) {
      newErrors.number = 'Please enter a valid card number';
    }

    // Expiry validation
    if (!cardDetails.expiry || !/^\d{2}\/\d{2}$/.test(cardDetails.expiry)) {
      newErrors.expiry = 'Please enter expiry as MM/YY';
    }

    // CVC validation
    if (!cardDetails.cvc || cardDetails.cvc.length < 3) {
      newErrors.cvc = 'Please enter a valid CVC';
    }

    // Name validation
    if (!cardDetails.name.trim()) {
      newErrors.name = 'Please enter the cardholder name';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const formatCardNumber = (value) => {
    const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
    const matches = v.match(/\d{4,16}/g);
    const match = matches && matches[0] || '';
    const parts = [];

    for (let i = 0, len = match.length; i < len; i += 4) {
      parts.push(match.substring(i, i + 4));
    }

    if (parts.length) {
      return parts.join(' ');
    } else {
      return v;
    }
  };

  const formatExpiry = (value) => {
    const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
    if (v.length >= 2) {
      return v.substring(0, 2) + '/' + v.substring(2, 4);
    }
    return v;
  };

  const handleInputChange = (field, value) => {
    let formattedValue = value;

    if (field === 'number') {
      formattedValue = formatCardNumber(value);
    } else if (field === 'expiry') {
      formattedValue = formatExpiry(value);
    } else if (field === 'cvc') {
      formattedValue = value.replace(/[^0-9]/g, '').substring(0, 4);
    }

    setCardDetails(prev => ({
      ...prev,
      [field]: formattedValue
    }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateCard()) {
      return;
    }

    setIsProcessing(true);

    try {
      // Simulate Stripe payment processing
      // In a real implementation, you would use Stripe's API
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Simulate payment creation
      const paymentData = {
        id: 'pi_' + Math.random().toString(36).substr(2, 9),
        amount: amount * 100, // Stripe uses cents
        currency: currency.toLowerCase(),
        status: 'succeeded',
        plan: plan.id,
        billingInterval,
        paymentMethod: 'stripe',
        card: {
          last4: cardDetails.number.slice(-4),
          brand: 'visa' // Would be detected by Stripe
        }
      };

      onSuccess(paymentData);
    } catch (error) {
      onError(error);
    } finally {
      setIsProcessing(false);
    }
  };

  if (!isStripeLoaded) {
    return (
      <div className="flex items-center justify-center py-8">
        <Loader2 className="h-6 w-6 animate-spin text-neural-purple" />
        <span className="ml-2 text-gray-400">Loading payment form...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Payment Summary */}
      <div className="bg-gray-800/50 rounded-xl p-4 border border-gray-700">
        <div className="flex items-center justify-between">
          <div>
            <div className="font-medium text-white">{plan.name}</div>
            <div className="text-sm text-gray-400">
              {billingInterval === 'yearly' ? 'Annual' : 'Monthly'} subscription
            </div>
          </div>
          <div className="text-right">
            <div className="text-xl font-bold text-white">
              {formatPrice(amount, currency)}
            </div>
            <div className="text-sm text-gray-400">
              {billingInterval === 'yearly' ? '/year' : '/month'}
            </div>
          </div>
        </div>
      </div>

      {/* Payment Form */}
      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Card Number */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Card Number
          </label>
          <div className="relative">
            <input
              type="text"
              value={cardDetails.number}
              onChange={(e) => handleInputChange('number', e.target.value)}
              placeholder="1234 5678 9012 3456"
              className={`w-full px-4 py-3 bg-gray-800/50 border rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400 ${
                errors.number ? 'border-red-500' : 'border-gray-700'
              }`}
              maxLength={19}
            />
            <CreditCard className="absolute right-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
          </div>
          {errors.number && (
            <p className="text-red-400 text-sm mt-1">{errors.number}</p>
          )}
        </div>

        {/* Expiry and CVC */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Expiry Date
            </label>
            <input
              type="text"
              value={cardDetails.expiry}
              onChange={(e) => handleInputChange('expiry', e.target.value)}
              placeholder="MM/YY"
              className={`w-full px-4 py-3 bg-gray-800/50 border rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400 ${
                errors.expiry ? 'border-red-500' : 'border-gray-700'
              }`}
              maxLength={5}
            />
            {errors.expiry && (
              <p className="text-red-400 text-sm mt-1">{errors.expiry}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              CVC
            </label>
            <input
              type="text"
              value={cardDetails.cvc}
              onChange={(e) => handleInputChange('cvc', e.target.value)}
              placeholder="123"
              className={`w-full px-4 py-3 bg-gray-800/50 border rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400 ${
                errors.cvc ? 'border-red-500' : 'border-gray-700'
              }`}
              maxLength={4}
            />
            {errors.cvc && (
              <p className="text-red-400 text-sm mt-1">{errors.cvc}</p>
            )}
          </div>
        </div>

        {/* Cardholder Name */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Cardholder Name
          </label>
          <input
            type="text"
            value={cardDetails.name}
            onChange={(e) => handleInputChange('name', e.target.value)}
            placeholder="John Doe"
            className={`w-full px-4 py-3 bg-gray-800/50 border rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400 ${
              errors.name ? 'border-red-500' : 'border-gray-700'
            }`}
          />
          {errors.name && (
            <p className="text-red-400 text-sm mt-1">{errors.name}</p>
          )}
        </div>

        {/* Security Notice */}
        <div className="flex items-center gap-2 text-sm text-gray-400 bg-gray-800/30 p-3 rounded-lg">
          <Lock className="h-4 w-4" />
          <span>Your payment is secured with 256-bit SSL encryption</span>
        </div>

        {/* Submit Button */}
        <button
          type="submit"
          disabled={isProcessing}
          className="w-full py-3 px-4 bg-neural-purple hover:bg-neural-purple/80 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-xl font-semibold transition-colors flex items-center justify-center gap-2"
        >
          {isProcessing ? (
            <>
              <Loader2 className="h-4 w-4 animate-spin" />
              Processing Payment...
            </>
          ) : (
            <>
              <Lock className="h-4 w-4" />
              Pay {formatPrice(amount, currency)}
            </>
          )}
        </button>
      </form>

      {/* Powered by Stripe */}
      <div className="text-center text-xs text-gray-500">
        Powered by <span className="font-medium">Stripe</span>
      </div>
    </div>
  );
};

export default StripePayment;

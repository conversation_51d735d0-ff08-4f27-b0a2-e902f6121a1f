"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/resume-builder/page",{

/***/ "(app-pages-browser)/./src/components/resume/forms/SkillsForm.jsx":
/*!****************************************************!*\
  !*** ./src/components/resume/forms/SkillsForm.jsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Certificate_CheckCircle_Clock_Code_Globe_Info_Lightbulb_Plus_Save_Sparkles_Target_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Certificate,CheckCircle,Clock,Code,Globe,Info,Lightbulb,Plus,Save,Sparkles,Target,Wand2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Certificate_CheckCircle_Clock_Code_Globe_Info_Lightbulb_Plus_Save_Sparkles_Target_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Certificate,CheckCircle,Clock,Code,Globe,Info,Lightbulb,Plus,Save,Sparkles,Target,Wand2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Certificate_CheckCircle_Clock_Code_Globe_Info_Lightbulb_Plus_Save_Sparkles_Target_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Certificate,CheckCircle,Clock,Code,Globe,Info,Lightbulb,Plus,Save,Sparkles,Target,Wand2,X!=!lucide-react */ \"(app-pages-browser)/__barrel_optimize__?names=AlertTriangle,Award,Certificate,CheckCircle,Clock,Code,Globe,Info,Lightbulb,Plus,Save,Sparkles,Target,Wand2,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Certificate_CheckCircle_Clock_Code_Globe_Info_Lightbulb_Plus_Save_Sparkles_Target_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Certificate,CheckCircle,Clock,Code,Globe,Info,Lightbulb,Plus,Save,Sparkles,Target,Wand2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Certificate_CheckCircle_Clock_Code_Globe_Info_Lightbulb_Plus_Save_Sparkles_Target_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Certificate,CheckCircle,Clock,Code,Globe,Info,Lightbulb,Plus,Save,Sparkles,Target,Wand2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Certificate_CheckCircle_Clock_Code_Globe_Info_Lightbulb_Plus_Save_Sparkles_Target_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Certificate,CheckCircle,Clock,Code,Globe,Info,Lightbulb,Plus,Save,Sparkles,Target,Wand2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Certificate_CheckCircle_Clock_Code_Globe_Info_Lightbulb_Plus_Save_Sparkles_Target_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Certificate,CheckCircle,Clock,Code,Globe,Info,Lightbulb,Plus,Save,Sparkles,Target,Wand2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Certificate_CheckCircle_Clock_Code_Globe_Info_Lightbulb_Plus_Save_Sparkles_Target_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Certificate,CheckCircle,Clock,Code,Globe,Info,Lightbulb,Plus,Save,Sparkles,Target,Wand2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Certificate_CheckCircle_Clock_Code_Globe_Info_Lightbulb_Plus_Save_Sparkles_Target_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Certificate,CheckCircle,Clock,Code,Globe,Info,Lightbulb,Plus,Save,Sparkles,Target,Wand2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lightbulb.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Certificate_CheckCircle_Clock_Code_Globe_Info_Lightbulb_Plus_Save_Sparkles_Target_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Certificate,CheckCircle,Clock,Code,Globe,Info,Lightbulb,Plus,Save,Sparkles,Target,Wand2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Award_Certificate_CheckCircle_Clock_Code_Globe_Info_Lightbulb_Plus_Save_Sparkles_Target_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Award,Certificate,CheckCircle,Clock,Code,Globe,Info,Lightbulb,Plus,Save,Sparkles,Target,Wand2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wand-sparkles.js\");\n/* harmony import */ var _SmartFormField__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./SmartFormField */ \"(app-pages-browser)/./src/components/resume/forms/SmartFormField.jsx\");\n/* harmony import */ var _common_ClientOnly__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../common/ClientOnly */ \"(app-pages-browser)/./src/components/common/ClientOnly.jsx\");\n/* harmony import */ var _ats_FieldIndicator__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../ats/FieldIndicator */ \"(app-pages-browser)/./src/components/resume/ats/FieldIndicator.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst EnhancedSkillsForm = (param)=>{\n    let { formData, updateFormData, atsAnalysis, validationErrors = {}, showValidationErrors = false, onSave, onAISuggest } = param;\n    var _skillCategories_find, _skillCategories_find1, _formData_skills_activeCategory, _skillCategories_find2, _formData_skills_activeCategory1, _atsAnalysis_fieldAnalysis;\n    _s();\n    const [activeCategory, setActiveCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('technical');\n    const [showAIHelper, setShowAIHelper] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [newSkill, setNewSkill] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        technical: '',\n        languages: '',\n        certifications: ''\n    });\n    const skillCategories = [\n        {\n            id: 'technical',\n            label: 'Technical Skills',\n            icon: _barrel_optimize_names_AlertTriangle_Award_Certificate_CheckCircle_Clock_Code_Globe_Info_Lightbulb_Plus_Save_Sparkles_Target_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            color: 'neural-purple'\n        },\n        {\n            id: 'languages',\n            label: 'Languages',\n            icon: _barrel_optimize_names_AlertTriangle_Award_Certificate_CheckCircle_Clock_Code_Globe_Info_Lightbulb_Plus_Save_Sparkles_Target_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            color: 'neural-blue'\n        },\n        {\n            id: 'certifications',\n            label: 'Certifications',\n            icon: _barrel_optimize_names_AlertTriangle_Award_Certificate_CheckCircle_Clock_Code_Globe_Info_Lightbulb_Plus_Save_Sparkles_Target_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__.Certificate,\n            color: 'neural-pink'\n        }\n    ];\n    const addSkill = (category)=>{\n        const skill = newSkill[category].trim();\n        if (skill && !formData.skills[category].includes(skill)) {\n            const updatedSkills = [\n                ...formData.skills[category],\n                skill\n            ];\n            updateFormData('skills', category, updatedSkills);\n            setNewSkill({\n                ...newSkill,\n                [category]: ''\n            });\n        }\n    };\n    const removeSkill = (category, skillToRemove)=>{\n        const updatedSkills = formData.skills[category].filter((skill)=>skill !== skillToRemove);\n        updateFormData('skills', category, updatedSkills);\n    };\n    const handleKeyPress = (e, category)=>{\n        if (e.key === 'Enter') {\n            e.preventDefault();\n            addSkill(category);\n        }\n    };\n    const getSkillSuggestions = (category)=>{\n        const suggestions = {\n            technical: [\n                'JavaScript',\n                'Python',\n                'React',\n                'Node.js',\n                'SQL',\n                'AWS',\n                'Docker',\n                'Git',\n                'TypeScript',\n                'MongoDB'\n            ],\n            languages: [\n                'English',\n                'Spanish',\n                'French',\n                'German',\n                'Mandarin',\n                'Japanese',\n                'Portuguese',\n                'Italian',\n                'Russian',\n                'Arabic'\n            ],\n            certifications: [\n                'AWS Certified',\n                'Google Cloud Professional',\n                'Microsoft Azure',\n                'PMP',\n                'Scrum Master',\n                'CompTIA Security+',\n                'Cisco CCNA',\n                'Oracle Certified'\n            ]\n        };\n        return suggestions[category] || [];\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n        initial: {\n            opacity: 0,\n            y: 20\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        exit: {\n            opacity: 0,\n            y: -20\n        },\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-10 h-10 bg-neural-purple/20 rounded-xl flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Award_Certificate_CheckCircle_Clock_Code_Globe_Info_Lightbulb_Plus_Save_Sparkles_Target_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"h-5 w-5 text-neural-purple\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                lineNumber: 86,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                            lineNumber: 85,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold text-white\",\n                                    children: \"Skills\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400 text-sm\",\n                                    children: \"Highlight your technical and soft skills (Optional)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                            lineNumber: 88,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                    lineNumber: 84,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                lineNumber: 83,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: -10\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                className: \"bg-blue-500/10 border border-blue-500/20 rounded-xl p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Award_Certificate_CheckCircle_Clock_Code_Globe_Info_Lightbulb_Plus_Save_Sparkles_Target_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"h-4 w-4 text-blue-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                lineNumber: 102,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-blue-400 font-medium\",\n                                children: \"Optional Section\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                lineNumber: 103,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                        lineNumber: 101,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-300 text-sm\",\n                        children: \"Skills help employers quickly identify your capabilities. You can skip this section if your experience and projects already demonstrate your skills clearly.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                        lineNumber: 105,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.AnimatePresence, {\n                children: showValidationErrors && validationErrors.skills && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: -10\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    exit: {\n                        opacity: 0,\n                        y: -10\n                    },\n                    className: \"bg-red-500/10 border border-red-500/20 rounded-xl p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 mb-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Award_Certificate_CheckCircle_Clock_Code_Globe_Info_Lightbulb_Plus_Save_Sparkles_Target_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-4 w-4 text-red-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-red-400 font-medium\",\n                                    children: \"Skills Information\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                            lineNumber: 119,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-300 text-sm\",\n                            children: validationErrors.skills\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                            lineNumber: 123,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                    lineNumber: 113,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                lineNumber: 111,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex gap-2 p-1 bg-gray-900/40 rounded-xl border border-white/10\",\n                children: skillCategories.map((category)=>{\n                    var _formData_skills_category_id;\n                    const IconComponent = category.icon;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setActiveCategory(category.id),\n                        className: \"flex items-center gap-2 px-4 py-3 rounded-lg text-sm font-medium transition-all duration-300 flex-1 \".concat(activeCategory === category.id ? \"bg-\".concat(category.color, \" text-white shadow-lg\") : 'text-gray-300 hover:bg-white/5 hover:text-white'),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                lineNumber: 142,\n                                columnNumber: 15\n                            }, undefined),\n                            category.label,\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"ml-auto bg-white/20 text-xs px-2 py-1 rounded-full\",\n                                children: ((_formData_skills_category_id = formData.skills[category.id]) === null || _formData_skills_category_id === void 0 ? void 0 : _formData_skills_category_id.length) || 0\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                lineNumber: 144,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, category.id, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                        lineNumber: 133,\n                        columnNumber: 13\n                    }, undefined);\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                lineNumber: 129,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-900/40 backdrop-blur-sm rounded-2xl p-6 border border-white/10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.AnimatePresence, {\n                    mode: \"wait\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            x: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            x: 0\n                        },\n                        exit: {\n                            opacity: 0,\n                            x: -20\n                        },\n                        transition: {\n                            duration: 0.2\n                        },\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: newSkill[activeCategory],\n                                                onChange: (e)=>setNewSkill({\n                                                        ...newSkill,\n                                                        [activeCategory]: e.target.value\n                                                    }),\n                                                onKeyPress: (e)=>handleKeyPress(e, activeCategory),\n                                                placeholder: \"Add \".concat((_skillCategories_find = skillCategories.find((c)=>c.id === activeCategory)) === null || _skillCategories_find === void 0 ? void 0 : _skillCategories_find.label.toLowerCase(), \"...\"),\n                                                className: \"flex-1 px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-neural-purple focus:border-transparent text-white placeholder-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                                lineNumber: 166,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>addSkill(activeCategory),\n                                                disabled: !newSkill[activeCategory].trim(),\n                                                className: \"px-4 py-3 bg-neural-purple hover:bg-neural-purple/80 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-lg transition-colors\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Award_Certificate_CheckCircle_Clock_Code_Globe_Info_Lightbulb_Plus_Save_Sparkles_Target_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                                    lineNumber: 179,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: \"Popular suggestions:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap gap-2\",\n                                                children: getSkillSuggestions(activeCategory).filter((suggestion)=>!formData.skills[activeCategory].includes(suggestion)).slice(0, 8).map((suggestion)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>{\n                                                            setNewSkill({\n                                                                ...newSkill,\n                                                                [activeCategory]: suggestion\n                                                            });\n                                                            addSkill(activeCategory);\n                                                        },\n                                                        className: \"px-3 py-1.5 bg-gray-700 hover:bg-gray-600 text-gray-300 hover:text-white text-sm rounded-lg transition-colors\",\n                                                        children: suggestion\n                                                    }, suggestion, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                                        lineNumber: 191,\n                                                        columnNumber: 23\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                lineNumber: 164,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-medium text-white flex items-center gap-2\",\n                                        children: [\n                                            (()=>{\n                                                const category = skillCategories.find((c)=>c.id === activeCategory);\n                                                if (category === null || category === void 0 ? void 0 : category.icon) {\n                                                    const IconComponent = category.icon;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                                        lineNumber: 213,\n                                                        columnNumber: 28\n                                                    }, undefined);\n                                                }\n                                                return null;\n                                            })(),\n                                            \"Your \",\n                                            (_skillCategories_find1 = skillCategories.find((c)=>c.id === activeCategory)) === null || _skillCategories_find1 === void 0 ? void 0 : _skillCategories_find1.label\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    ((_formData_skills_activeCategory = formData.skills[activeCategory]) === null || _formData_skills_activeCategory === void 0 ? void 0 : _formData_skills_activeCategory.length) > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-2\",\n                                        children: formData.skills[activeCategory].map((skill, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                                initial: {\n                                                    opacity: 0,\n                                                    scale: 0.8\n                                                },\n                                                animate: {\n                                                    opacity: 1,\n                                                    scale: 1\n                                                },\n                                                exit: {\n                                                    opacity: 0,\n                                                    scale: 0.8\n                                                },\n                                                className: \"flex items-center gap-2 px-3 py-2 bg-gradient-to-r from-neural-purple/20 to-neural-pink/20 border border-neural-purple/30 rounded-lg text-white text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: skill\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                                        lineNumber: 230,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>removeSkill(activeCategory, skill),\n                                                        className: \"text-gray-400 hover:text-red-400 transition-colors\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Award_Certificate_CheckCircle_Clock_Code_Globe_Info_Lightbulb_Plus_Save_Sparkles_Target_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                                            lineNumber: 235,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                                        lineNumber: 231,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, skill, true, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 21\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 17\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8 text-gray-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Award_Certificate_CheckCircle_Clock_Code_Globe_Info_Lightbulb_Plus_Save_Sparkles_Target_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-8 w-8 mx-auto mb-2 opacity-50\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"No \",\n                                                    (_skillCategories_find2 = skillCategories.find((c)=>c.id === activeCategory)) === null || _skillCategories_find2 === void 0 ? void 0 : _skillCategories_find2.label.toLowerCase(),\n                                                    \" added yet\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm\",\n                                                children: \"Start typing to add your first skill\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                                lineNumber: 244,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                lineNumber: 207,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_common_ClientOnly__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ats_FieldIndicator__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    fieldName: \"skills_\".concat(activeCategory),\n                                    value: (_formData_skills_activeCategory1 = formData.skills[activeCategory]) === null || _formData_skills_activeCategory1 === void 0 ? void 0 : _formData_skills_activeCategory1.join(', '),\n                                    analysis: atsAnalysis === null || atsAnalysis === void 0 ? void 0 : (_atsAnalysis_fieldAnalysis = atsAnalysis.fieldAnalysis) === null || _atsAnalysis_fieldAnalysis === void 0 ? void 0 : _atsAnalysis_fieldAnalysis[\"skills_\".concat(activeCategory)],\n                                    showDetails: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                    lineNumber: 250,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                lineNumber: 249,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, activeCategory, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                        lineNumber: 155,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                    lineNumber: 154,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                lineNumber: 153,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Award_Certificate_CheckCircle_Clock_Code_Globe_Info_Lightbulb_Plus_Save_Sparkles_Target_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-5 w-5 text-neural-purple\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                        lineNumber: 265,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-white\",\n                                        children: \"Skills Optimization Assistant\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                lineNumber: 264,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowAIHelper(!showAIHelper),\n                                className: \"flex items-center gap-2 px-3 py-2 bg-neural-purple/20 hover:bg-neural-purple/30 border border-neural-purple/50 rounded-lg transition-colors text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Award_Certificate_CheckCircle_Clock_Code_Globe_Info_Lightbulb_Plus_Save_Sparkles_Target_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                        lineNumber: 272,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    showAIHelper ? 'Hide' : 'Show',\n                                    \" AI Helper\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                lineNumber: 268,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                        lineNumber: 263,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.AnimatePresence, {\n                        children: showAIHelper && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                height: 0\n                            },\n                            animate: {\n                                opacity: 1,\n                                height: 'auto'\n                            },\n                            exit: {\n                                opacity: 0,\n                                height: 0\n                            },\n                            className: \"bg-gradient-to-r from-neural-purple/10 to-neural-pink/10 border border-neural-purple/30 rounded-xl p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start gap-3 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-neural-purple/20 rounded-lg flex items-center justify-center flex-shrink-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Award_Certificate_CheckCircle_Clock_Code_Globe_Info_Lightbulb_Plus_Save_Sparkles_Target_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-5 w-5 text-neural-purple\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                                lineNumber: 287,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                            lineNumber: 286,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-semibold text-white mb-2\",\n                                                    children: \"Enhance Your Skills Section\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                                    lineNumber: 290,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-300 text-sm leading-relaxed mb-3\",\n                                                    children: \"Our AI can suggest relevant skills based on your experience and industry trends:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                                    lineNumber: 291,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"text-gray-300 text-sm space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-1.5 h-1.5 bg-neural-purple rounded-full\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                                                    lineNumber: 296,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                \"Identify missing technical skills for your field\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                                            lineNumber: 295,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-1.5 h-1.5 bg-neural-purple rounded-full\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                                                    lineNumber: 300,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                \"Suggest industry-relevant certifications\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                                            lineNumber: 299,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-1.5 h-1.5 bg-neural-purple rounded-full\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                                                    lineNumber: 304,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                \"Optimize skill keywords for ATS systems\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                                            lineNumber: 303,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                                    lineNumber: 294,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                    lineNumber: 285,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>onAISuggest === null || onAISuggest === void 0 ? void 0 : onAISuggest('skills'),\n                                            className: \"flex items-center gap-2 px-4 py-2 bg-neural-purple hover:bg-neural-purple/80 text-white rounded-lg text-sm font-medium transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Award_Certificate_CheckCircle_Clock_Code_Globe_Info_Lightbulb_Plus_Save_Sparkles_Target_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                                    lineNumber: 315,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"Generate Skill Suggestions\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                            lineNumber: 311,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowAIHelper(false),\n                                            className: \"px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg text-sm font-medium transition-colors\",\n                                            children: \"Close Helper\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                            lineNumber: 318,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                    lineNumber: 310,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                            lineNumber: 279,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                        lineNumber: 277,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                lineNumber: 262,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-900/40 backdrop-blur-sm rounded-xl p-4 border border-white/10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-3 gap-4 text-center\",\n                    children: skillCategories.map((category)=>{\n                        var _formData_skills_category_id;\n                        const IconComponent = category.icon;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-\".concat(category.color, \"/20 rounded-lg flex items-center justify-center mx-auto\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                        className: \"h-4 w-4 text-\".concat(category.color)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                        lineNumber: 338,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                    lineNumber: 337,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-lg font-bold text-\".concat(category.color),\n                                            children: ((_formData_skills_category_id = formData.skills[category.id]) === null || _formData_skills_category_id === void 0 ? void 0 : _formData_skills_category_id.length) || 0\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                            lineNumber: 341,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-gray-400\",\n                                            children: category.label\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                            lineNumber: 344,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                                    lineNumber: 340,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, category.id, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                            lineNumber: 336,\n                            columnNumber: 15\n                        }, undefined);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                    lineNumber: 332,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n                lineNumber: 331,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\forms\\\\SkillsForm.jsx\",\n        lineNumber: 76,\n        columnNumber: 5\n    }, undefined);\n};\n_s(EnhancedSkillsForm, \"3AMfliK8dwk4WCV+B2p/IUeFBIQ=\");\n_c = EnhancedSkillsForm;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (EnhancedSkillsForm);\nvar _c;\n$RefreshReg$(_c, \"EnhancedSkillsForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/resume/forms/SkillsForm.jsx\n"));

/***/ })

});
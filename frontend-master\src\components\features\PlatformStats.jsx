'use client';

import { useState, useEffect, useRef } from 'react';
import { collection, onSnapshot } from 'firebase/firestore';
import { firestore } from '@/firebase/config';
import { Users, TrendingUp, Star, MessageSquare } from 'lucide-react';
import { motion, useInView } from 'framer-motion';

// Simple animated number hook
const useAnimatedNumber = (endValue, duration = 2, shouldAnimate = false) => {
  const [displayValue, setDisplayValue] = useState(0);
  
  useEffect(() => {
    if (!shouldAnimate) return;
    
    let startTime;
    let animationFrame;
    
    const animate = (timestamp) => {
      if (!startTime) startTime = timestamp;
      const progress = Math.min((timestamp - startTime) / (duration * 1000), 1);
      const easeOutQuart = 1 - Math.pow(1 - progress, 4);
      const currentValue = Math.floor(easeOutQuart * endValue);
      
      setDisplayValue(currentValue);
      
      if (progress < 1) {
        animationFrame = requestAnimationFrame(animate);
      } else {
        setDisplayValue(endValue);
      }
    };
    
    animationFrame = requestAnimationFrame(animate);
    
    return () => {
      if (animationFrame) {
        cancelAnimationFrame(animationFrame);
      }
    };
  }, [endValue, duration, shouldAnimate]);
  
  return displayValue;
};

const PlatformStatsSimple = () => {
  const [stats, setStats] = useState({
    totalUsers: 0,
    totalReviews: 0,
    averageRating: 0,
    successRate: 98
  });
  const [loading, setLoading] = useState(true);
  const [isConnected, setIsConnected] = useState(false);
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  const animatedUsers = useAnimatedNumber(stats.totalUsers, 2.5, isInView && !loading);
  const animatedReviews = useAnimatedNumber(stats.totalReviews, 2, isInView && !loading);
  const animatedRating = useAnimatedNumber(stats.averageRating * 10, 2, isInView && !loading) / 10;
  const animatedSuccess = useAnimatedNumber(stats.successRate, 2, isInView && !loading);
  useEffect(() => {
    // Try to connect to Firebase
    const setupFirebase = async () => {
      try {
        if (!firestore || !firestore.app) {
          console.log('🔥 Firestore not available, using demo data');
          setStats({
            totalUsers: 5847,
            totalReviews: 247,
            averageRating: 4.8,
            successRate: 98
          });
          setLoading(false);
          setIsConnected(false);
          return;
        }

        console.log('🔥 Setting up Firebase listeners...');

        // Set up real-time listener for users
        const unsubscribeUsers = onSnapshot(
          collection(firestore, 'users'),
          (snapshot) => {
            const userCount = snapshot.size;
            console.log('👥 Users updated:', userCount);
            
            setStats(prev => ({
              ...prev,
              totalUsers: Math.max(userCount, 5847)
            }));
            setIsConnected(true);
          },
          (error) => {
            console.error('❌ Users listener error:', error);
            setIsConnected(false);
          }
        );

        // Set up real-time listener for reviews
        const unsubscribeReviews = onSnapshot(
          collection(firestore, 'reviews'),
          (snapshot) => {
            let totalRating = 0;
            let reviewCount = 0;

            snapshot.forEach((doc) => {
              const data = doc.data();
              if (data.rating && typeof data.rating === 'number') {
                totalRating += data.rating;
                reviewCount++;
              }
            });

            const averageRating = reviewCount > 0 ? totalRating / reviewCount : 4.8;
            console.log('⭐ Reviews updated:', { reviewCount, averageRating });

            setStats(prev => ({
              ...prev,
              totalReviews: reviewCount,
              averageRating: parseFloat(averageRating.toFixed(1))
            }));
            setIsConnected(true);
          },
          (error) => {
            console.error('❌ Reviews listener error:', error);
            setIsConnected(false);
          }
        );

        setLoading(false);

        return () => {
          if (unsubscribeUsers) unsubscribeUsers();
          if (unsubscribeReviews) unsubscribeReviews();
          console.log('🧹 Firebase listeners cleaned up');
        };
      } catch (error) {
        console.error('❌ Firebase setup error:', error);
        setStats({
          totalUsers: 5847,
          totalReviews: 247,
          averageRating: 4.8,
          successRate: 98
        });
        setLoading(false);
        setIsConnected(false);
      }
    };

    setupFirebase();
  }, []);

  const statItems = [
    {
      icon: Users,
      label: 'Active Users',
      value: loading ? '...' : `${animatedUsers.toLocaleString()}+`,
      gradient: 'from-blue-500 via-cyan-500 to-teal-500',
      iconBg: 'bg-blue-500/20',
      isLive: true
    },
    {
      icon: MessageSquare,
      label: 'User Reviews',
      value: loading ? '...' : animatedReviews.toLocaleString(),
      gradient: 'from-purple-500 via-pink-500 to-rose-500',
      iconBg: 'bg-purple-500/20',
      isLive: true
    },
    {
      icon: Star,
      label: 'Average Rating',
      value: loading ? '...' : `${animatedRating.toFixed(1)}/5`,
      gradient: 'from-yellow-500 via-orange-500 to-red-500',
      iconBg: 'bg-yellow-500/20',
      isLive: false
    },
    {
      icon: TrendingUp,
      label: 'Success Rate',
      value: loading ? '...' : `${animatedSuccess}%`,
      gradient: 'from-green-500 via-emerald-500 to-teal-500',
      iconBg: 'bg-green-500/20',
      isLive: false
    }
  ];

  return (
    <motion.div 
      ref={ref}
      className="mt-8 w-full"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: 0.5 }}
    >
      <div className="flex flex-wrap items-center justify-center gap-6 lg:gap-8">
        {statItems.map((stat, index) => (
          <motion.div
            key={stat.label}
            className="flex items-center gap-3 group"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ 
              duration: 0.5, 
              delay: 0.6 + index * 0.1
            }}
          >
            <div className={`${stat.iconBg} p-2 rounded-lg backdrop-blur-sm border border-white/10 relative`}>
              <stat.icon className="h-4 w-4 text-white" />
              {stat.isLive && (
                <div className={`absolute -top-1 -right-1 w-2 h-2 rounded-full animate-pulse ${
                  isConnected ? 'bg-green-400' : 'bg-yellow-400'
                }`} />
              )}
            </div>
            <div className="text-left">
              <div className={`text-lg font-bold bg-gradient-to-r ${stat.gradient} bg-clip-text text-transparent`}>
                {stat.value}
              </div>
              <div className="text-xs text-gray-400 leading-tight">
                {stat.label}
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      <motion.div 
        className="mt-4 flex items-center justify-center gap-2 text-xs text-gray-500"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 1.2 }}
      >
        <div className={`w-1.5 h-1.5 rounded-full animate-pulse ${
          isConnected ? 'bg-green-400' : 'bg-yellow-400'
        }`} />
        <span>
          {isConnected ? '🔥 Live Firebase data' : '⚡ Demo data'}
        </span>
      </motion.div>
    </motion.div>
  );
};

export default PlatformStatsSimple;

'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  collection, 
  getDocs, 
  doc, 
  updateDoc, 
  deleteDoc, 
  query, 
  orderBy,
  where 
} from 'firebase/firestore';
import { firestore } from '@/firebase/config';
import { 
  CheckCircle, 
  XCircle, 
  Clock, 
  Star, 
  Trash2, 
  Eye,
  MessageSquare,
  User,
  Calendar
} from 'lucide-react';

const ReviewManagement = () => {
  const [reviews, setReviews] = useState([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState('pending'); // 'pending', 'approved', 'all'
  const [stats, setStats] = useState({
    pending: 0,
    approved: 0,
    total: 0
  });

  const fetchReviews = async () => {
    try {
      setLoading(true);
      let reviewsQuery;
      
      if (filter === 'all') {
        reviewsQuery = query(
          collection(firestore, 'reviews'),
          orderBy('createdAt', 'desc')
        );
      } else {
        const approved = filter === 'approved';
        reviewsQuery = query(
          collection(firestore, 'reviews'),
          where('approved', '==', approved),
          orderBy('createdAt', 'desc')
        );
      }
      
      const querySnapshot = await getDocs(reviewsQuery);
      const reviewsData = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      setReviews(reviewsData);

      // Calculate stats
      const allReviewsQuery = query(collection(firestore, 'reviews'));
      const allSnapshot = await getDocs(allReviewsQuery);
      const allReviews = allSnapshot.docs.map(doc => doc.data());
      
      const pending = allReviews.filter(r => !r.approved).length;
      const approved = allReviews.filter(r => r.approved).length;
      
      setStats({
        pending,
        approved,
        total: allReviews.length
      });
    } catch (error) {
      console.error('Error fetching reviews:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleApproveReview = async (reviewId) => {
    try {
      await updateDoc(doc(firestore, 'reviews', reviewId), {
        approved: true,
        status: 'approved',
        approvedAt: new Date()
      });
      fetchReviews(); // Refresh the list
    } catch (error) {
      console.error('Error approving review:', error);
      alert('Failed to approve review');
    }
  };

  const handleRejectReview = async (reviewId) => {
    try {
      await updateDoc(doc(firestore, 'reviews', reviewId), {
        approved: false,
        status: 'rejected',
        rejectedAt: new Date()
      });
      fetchReviews(); // Refresh the list
    } catch (error) {
      console.error('Error rejecting review:', error);
      alert('Failed to reject review');
    }
  };

  const handleDeleteReview = async (reviewId) => {
    if (!confirm('Are you sure you want to delete this review? This action cannot be undone.')) {
      return;
    }
    
    try {
      await deleteDoc(doc(firestore, 'reviews', reviewId));
      fetchReviews(); // Refresh the list
    } catch (error) {
      console.error('Error deleting review:', error);
      alert('Failed to delete review');
    }
  };

  const formatDate = (timestamp) => {
    if (!timestamp) return 'Unknown date';
    
    let date;
    if (timestamp.toDate) {
      date = timestamp.toDate();
    } else if (timestamp.seconds) {
      date = new Date(timestamp.seconds * 1000);
    } else {
      date = new Date(timestamp);
    }
    
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
  };

  useEffect(() => {
    fetchReviews();
  }, [filter]);

  const getStatusColor = (review) => {
    if (review.approved) return 'text-green-400';
    if (review.status === 'rejected') return 'text-red-400';
    return 'text-yellow-400';
  };

  const getStatusIcon = (review) => {
    if (review.approved) return <CheckCircle className="h-4 w-4" />;
    if (review.status === 'rejected') return <XCircle className="h-4 w-4" />;
    return <Clock className="h-4 w-4" />;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-neural-purple/20 rounded-xl flex items-center justify-center">
            <MessageSquare className="h-5 w-5 text-neural-purple" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-white">Review Management</h2>
            <p className="text-gray-400 text-sm">Manage user reviews and feedback</p>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <motion.div 
          className="bg-black/20 backdrop-blur-xl border border-white/20 rounded-2xl p-4"
          whileHover={{ scale: 1.02 }}
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-300 text-sm">Pending Reviews</p>
              <p className="text-2xl font-bold text-yellow-400">{stats.pending}</p>
            </div>
            <Clock className="h-8 w-8 text-yellow-400" />
          </div>
        </motion.div>

        <motion.div 
          className="bg-black/20 backdrop-blur-xl border border-white/20 rounded-2xl p-4"
          whileHover={{ scale: 1.02 }}
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-300 text-sm">Approved Reviews</p>
              <p className="text-2xl font-bold text-green-400">{stats.approved}</p>
            </div>
            <CheckCircle className="h-8 w-8 text-green-400" />
          </div>
        </motion.div>

        <motion.div 
          className="bg-black/20 backdrop-blur-xl border border-white/20 rounded-2xl p-4"
          whileHover={{ scale: 1.02 }}
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-300 text-sm">Total Reviews</p>
              <p className="text-2xl font-bold text-white">{stats.total}</p>
            </div>
            <MessageSquare className="h-8 w-8 text-neural-purple" />
          </div>
        </motion.div>
      </div>

      {/* Filter Tabs */}
      <div className="flex gap-2">
        {['pending', 'approved', 'all'].map((filterOption) => (
          <button
            key={filterOption}
            onClick={() => setFilter(filterOption)}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
              filter === filterOption
                ? 'bg-neural-purple text-white'
                : 'bg-gray-800 text-gray-300 hover:bg-gray-700'
            }`}
          >
            {filterOption.charAt(0).toUpperCase() + filterOption.slice(1)}
          </button>
        ))}
      </div>

      {/* Reviews List */}
      <div className="bg-black/20 backdrop-blur-xl border border-white/20 rounded-2xl p-6">
        {loading ? (
          <div className="text-center py-8">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-neural-purple"></div>
            <p className="text-gray-300 mt-2">Loading reviews...</p>
          </div>
        ) : reviews.length === 0 ? (
          <div className="text-center py-8">
            <MessageSquare className="h-12 w-12 text-gray-600 mx-auto mb-4" />
            <p className="text-gray-400">No reviews found for the selected filter.</p>
          </div>
        ) : (
          <div className="space-y-4">
            {reviews.map((review) => (
              <motion.div
                key={review.id}
                className="bg-gray-900/40 backdrop-blur-sm rounded-xl p-4 border border-white/10"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                whileHover={{ scale: 1.01 }}
              >
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-gradient-to-r from-neural-purple to-neural-pink rounded-full flex items-center justify-center text-white font-semibold">
                      {review.userEmail ? review.userEmail.charAt(0).toUpperCase() : 'A'}
                    </div>
                    <div>
                      <div className="flex items-center gap-2">
                        <p className="text-white font-medium">
                          {review.userEmail ? review.userEmail.split('@')[0] : 'Anonymous'}
                        </p>
                        <div className={`flex items-center gap-1 ${getStatusColor(review)}`}>
                          {getStatusIcon(review)}
                          <span className="text-xs">
                            {review.approved ? 'Approved' : review.status === 'rejected' ? 'Rejected' : 'Pending'}
                          </span>
                        </div>
                      </div>
                      <div className="flex items-center gap-4 text-sm text-gray-400">
                        <span className="flex items-center gap-1">
                          <Calendar className="h-3 w-3" />
                          {formatDate(review.createdAt)}
                        </span>
                        <div className="flex items-center gap-1">
                          {[...Array(5)].map((_, i) => (
                            <Star
                              key={i}
                              className={`h-3 w-3 ${
                                i < review.rating ? 'text-yellow-400 fill-current' : 'text-gray-600'
                              }`}
                            />
                          ))}
                          <span className="ml-1">({review.rating}/5)</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    {!review.approved && review.status !== 'rejected' && (
                      <>
                        <button
                          onClick={() => handleApproveReview(review.id)}
                          className="p-2 bg-green-500/20 hover:bg-green-500/30 text-green-400 rounded-lg transition-colors"
                          title="Approve Review"
                        >
                          <CheckCircle className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleRejectReview(review.id)}
                          className="p-2 bg-red-500/20 hover:bg-red-500/30 text-red-400 rounded-lg transition-colors"
                          title="Reject Review"
                        >
                          <XCircle className="h-4 w-4" />
                        </button>
                      </>
                    )}
                    <button
                      onClick={() => handleDeleteReview(review.id)}
                      className="p-2 bg-red-500/20 hover:bg-red-500/30 text-red-400 rounded-lg transition-colors"
                      title="Delete Review"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </div>
                
                <div className="bg-black/20 rounded-lg p-3">
                  <p className="text-gray-300 leading-relaxed">{review.comment}</p>
                </div>
              </motion.div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default ReviewManagement;

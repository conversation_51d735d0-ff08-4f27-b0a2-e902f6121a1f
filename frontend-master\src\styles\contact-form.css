.contact-form input[type="text"],
.contact-form input[type="email"],
.contact-form input[type="tel"],
.contact-form textarea {
  width: 100% !important;
  height: 35px !important;
  padding: 0.5rem !important;
  font-size: 0.875rem !important;
  border: 1px solid #E5E7EB !important;
  border-radius: 0.375rem !important;
  background-color: #fff !important;
}

.contact-form textarea {
  height: auto !important;
  min-height: 80px !important;
  resize: vertical !important;
}

.contact-form input:focus,
.contact-form textarea:focus {
  border-color: #51B504 !important;
  outline: none !important;
}

.contact-form label {
  display: block !important;
  margin-bottom: 0.25rem !important;
  font-size: 0.875rem !important;
  font-weight: 500 !important;
  color: #344054 !important;
}

.contact-form .checkbox-wrapper {
  display: flex !important;
  align-items: start !important;
  gap: 0.5rem !important;
}

.contact-form input[type="checkbox"] {
  width: 1rem !important;
  height: 1rem !important;
  margin-top: 0.2rem !important;
}

.contact-form input[type="checkbox"]:checked {
  background-color: #51B504 !important;
  border-color: #51B504 !important;
}

.contact-form input[type="checkbox"]:focus {
  border-color: #51B504 !important;
  box-shadow: 0 0 0 1px #51B504 !important;
}

.contact-form .submit-button {
  width: 100% !important;
  padding: 0.5rem 1rem !important;
  font-size: 0.875rem !important;
  font-weight: 500 !important;
  color: #ffffff !important;
  background-color: #51B504 !important;
  border: none !important;
  border-radius: 0.375rem !important;
}

.contact-form .submit-button:hover {
  background-color: #428f03 !important;
}

.contact-form .submit-button:focus {
  outline: none !important;
  box-shadow: 0 0 0 2px #ffffff, 0 0 0 4px #51B504 !important;
}

.contact-form .input-group {
  margin-bottom: 0.75rem !important;
}

.contact-form .input-group:last-of-type {
  margin-bottom: 0.5rem !important;
}

.contact-form .error-message {
  color: #DC2626 !important;
  font-size: 0.875rem !important;
  margin-top: 0.25rem !important;
}

/* Placeholder styling */
.contact-form input::placeholder,
.contact-form textarea::placeholder {
  color: #9CA3AF !important;
}

.phone-input-container input {
  height: 35px !important;
  padding: 0.5rem !important;
}

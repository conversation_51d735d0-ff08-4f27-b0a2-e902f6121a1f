'use client';
import { motion } from 'framer-motion';
import { 
  ArrowLeft, ArrowRight, Save, Download, Eye, 
  CheckCircle, Circle, User, GraduationCap, Briefcase, 
  Award, FileText, Home
} from 'lucide-react';

const SimplifiedNavigation = ({
  currentStep,
  totalSteps,
  onPrevious,
  onNext,
  onSave,
  onDownload,
  onHome,
  canProceed = true,
  isSaving = false,
  completionPercentage = 0,
  stepTitles = []
}) => {
  const isFirstStep = currentStep === 0;
  const isLastStep = currentStep === totalSteps - 1;

  const getStepIcon = (stepIndex) => {
    const icons = [User, GraduationCap, Briefcase, Award, FileText];
    const Icon = icons[stepIndex] || Circle;
    return Icon;
  };

  const getStepStatus = (stepIndex) => {
    if (stepIndex < currentStep) return 'completed';
    if (stepIndex === currentStep) return 'current';
    return 'upcoming';
  };

  return (
    <div className="bg-gray-900/95 backdrop-blur-sm border-t border-gray-700/50 shadow-2xl">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Progress Steps - Desktop */}
        <div className="hidden md:flex items-center justify-center py-4 border-b border-gray-700/30">
          <div className="flex items-center space-x-8">
            {stepTitles.map((title, index) => {
              const Icon = getStepIcon(index);
              const status = getStepStatus(index);
              
              return (
                <div key={index} className="flex items-center">
                  <div className="flex flex-col items-center">
                    <div className={`
                      w-10 h-10 rounded-full border-2 flex items-center justify-center transition-all duration-200
                      ${status === 'completed'
                        ? 'bg-neural-pink border-neural-pink text-white'
                        : status === 'current'
                        ? 'bg-neural-purple border-neural-purple text-white'
                        : 'bg-gray-800 border-gray-600 text-gray-400'
                      }
                    `}>
                      {status === 'completed' ? (
                        <CheckCircle className="h-5 w-5" />
                      ) : (
                        <Icon className="h-5 w-5" />
                      )}
                    </div>
                    <span className={`
                      mt-2 text-xs font-medium
                      ${status === 'current' ? 'text-neural-purple' : 'text-gray-400'}
                    `}>
                      {title}
                    </span>
                  </div>

                  {/* Connector Line */}
                  {index < stepTitles.length - 1 && (
                    <div className="w-16 h-0.5 mx-4 bg-gray-700 relative">
                      <motion.div
                        className="h-full bg-neural-pink"
                        initial={{ width: 0 }}
                        animate={{ 
                          width: index < currentStep ? '100%' : '0%' 
                        }}
                        transition={{ duration: 0.3 }}
                      />
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>

        {/* Mobile Progress */}
        <div className="md:hidden py-3 border-b border-gray-700/30">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-white">
              Step {currentStep + 1} of {totalSteps}
            </span>
            <span className="text-sm text-gray-400">
              {Math.round(completionPercentage)}% Complete
            </span>
          </div>
          <div className="w-full bg-gray-700 rounded-full h-2">
            <motion.div
              className="bg-gradient-to-r from-neural-purple to-neural-pink h-2 rounded-full"
              initial={{ width: 0 }}
              animate={{ width: `${completionPercentage}%` }}
              transition={{ duration: 0.5 }}
            />
          </div>
        </div>

        {/* Navigation Controls */}
        <div className="flex items-center justify-between py-4">
          {/* Left Side - Back Button */}
          <div className="flex items-center space-x-3">
            <button
              onClick={onHome}
              className="p-2 text-gray-400 hover:text-white transition-colors"
              title="Back to Home"
            >
              <Home className="h-5 w-5" />
            </button>

            <button
              onClick={onPrevious}
              disabled={isFirstStep}
              className={`
                flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-all duration-200
                ${isFirstStep
                  ? 'bg-gray-800 text-gray-500 cursor-not-allowed'
                  : 'bg-gray-800 text-gray-300 hover:bg-gray-700 border border-gray-600'
                }
              `}
            >
              <ArrowLeft className="h-4 w-4" />
              <span>Previous</span>
            </button>
          </div>

          {/* Center - Current Step Info */}
          <div className="hidden sm:flex flex-col items-center">
            <h3 className="text-lg font-semibold text-white">
              {stepTitles[currentStep] || `Step ${currentStep + 1}`}
            </h3>
            <p className="text-sm text-gray-400">
              {isLastStep ? 'Your AI-powered resume is ready' : 'Building your professional resume with AI'}
            </p>
          </div>

          {/* Right Side - Action Buttons */}
          <div className="flex items-center space-x-3">
            {/* Save Button */}
            <button
              onClick={onSave}
              disabled={isSaving}
              className="flex items-center space-x-2 px-4 py-2 bg-gray-800 hover:bg-gray-700 text-gray-300 rounded-lg font-medium transition-colors disabled:opacity-50 border border-gray-600"
            >
              <Save className="h-4 w-4" />
              <span className="hidden sm:inline">
                {isSaving ? 'Saving...' : 'Save'}
              </span>
            </button>

            {/* Next Button */}
            <button
              onClick={onNext}
              disabled={!canProceed}
              className={`
                flex items-center space-x-2 px-6 py-2 rounded-lg font-medium transition-all duration-200
                ${canProceed
                  ? 'bg-gradient-to-r from-neural-purple to-neural-pink text-white shadow-lg hover:opacity-90'
                  : 'bg-gray-800 text-gray-500 cursor-not-allowed border border-gray-700'
                }
              `}
            >
              <span>{isLastStep ? 'Complete' : 'Next'}</span>
              <ArrowRight className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SimplifiedNavigation;

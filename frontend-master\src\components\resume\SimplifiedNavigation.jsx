'use client';
import { motion } from 'framer-motion';
import { 
  ArrowLeft, ArrowRight, Save, Download, Eye, 
  CheckCircle, Circle, User, GraduationCap, Briefcase, 
  Award, FileText, Home
} from 'lucide-react';

const SimplifiedNavigation = ({
  currentStep,
  totalSteps,
  onPrevious,
  onNext,
  onSave,
  onDownload,
  onHome,
  canProceed = true,
  isSaving = false,
  completionPercentage = 0,
  stepTitles = []
}) => {
  const isFirstStep = currentStep === 0;
  const isLastStep = currentStep === totalSteps - 1;

  const getStepIcon = (stepIndex) => {
    const icons = [User, GraduationCap, Briefcase, Award, FileText];
    const Icon = icons[stepIndex] || Circle;
    return Icon;
  };

  const getStepStatus = (stepIndex) => {
    if (stepIndex < currentStep) return 'completed';
    if (stepIndex === currentStep) return 'current';
    return 'upcoming';
  };

  return (
    <div className="bg-white border-t border-gray-200 shadow-lg">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Progress Steps - Desktop */}
        <div className="hidden md:flex items-center justify-center py-4 border-b border-gray-100">
          <div className="flex items-center space-x-8">
            {stepTitles.map((title, index) => {
              const Icon = getStepIcon(index);
              const status = getStepStatus(index);
              
              return (
                <div key={index} className="flex items-center">
                  <div className="flex flex-col items-center">
                    <div className={`
                      w-10 h-10 rounded-full border-2 flex items-center justify-center transition-all duration-200
                      ${status === 'completed' 
                        ? 'bg-green-500 border-green-500 text-white' 
                        : status === 'current'
                        ? 'bg-blue-500 border-blue-500 text-white'
                        : 'bg-gray-100 border-gray-300 text-gray-400'
                      }
                    `}>
                      {status === 'completed' ? (
                        <CheckCircle className="h-5 w-5" />
                      ) : (
                        <Icon className="h-5 w-5" />
                      )}
                    </div>
                    <span className={`
                      mt-2 text-xs font-medium
                      ${status === 'current' ? 'text-blue-600' : 'text-gray-500'}
                    `}>
                      {title}
                    </span>
                  </div>
                  
                  {/* Connector Line */}
                  {index < stepTitles.length - 1 && (
                    <div className="w-16 h-0.5 mx-4 bg-gray-200 relative">
                      <motion.div
                        className="h-full bg-green-500"
                        initial={{ width: 0 }}
                        animate={{ 
                          width: index < currentStep ? '100%' : '0%' 
                        }}
                        transition={{ duration: 0.3 }}
                      />
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>

        {/* Mobile Progress */}
        <div className="md:hidden py-3 border-b border-gray-100">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-700">
              Step {currentStep + 1} of {totalSteps}
            </span>
            <span className="text-sm text-gray-500">
              {Math.round(completionPercentage)}% Complete
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <motion.div
              className="bg-blue-500 h-2 rounded-full"
              initial={{ width: 0 }}
              animate={{ width: `${completionPercentage}%` }}
              transition={{ duration: 0.5 }}
            />
          </div>
        </div>

        {/* Navigation Controls */}
        <div className="flex items-center justify-between py-4">
          {/* Left Side - Back Button */}
          <div className="flex items-center space-x-3">
            <button
              onClick={onHome}
              className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
              title="Back to Home"
            >
              <Home className="h-5 w-5" />
            </button>
            
            <button
              onClick={onPrevious}
              disabled={isFirstStep}
              className={`
                flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-all duration-200
                ${isFirstStep 
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed' 
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }
              `}
            >
              <ArrowLeft className="h-4 w-4" />
              <span>Previous</span>
            </button>
          </div>

          {/* Center - Current Step Info */}
          <div className="hidden sm:flex flex-col items-center">
            <h3 className="text-lg font-semibold text-gray-900">
              {stepTitles[currentStep] || `Step ${currentStep + 1}`}
            </h3>
            <p className="text-sm text-gray-500">
              {isLastStep ? 'Review and download your resume' : 'Fill in your information'}
            </p>
          </div>

          {/* Right Side - Action Buttons */}
          <div className="flex items-center space-x-3">
            {/* Save Button */}
            <button
              onClick={onSave}
              disabled={isSaving}
              className="flex items-center space-x-2 px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg font-medium transition-colors disabled:opacity-50"
            >
              <Save className="h-4 w-4" />
              <span className="hidden sm:inline">
                {isSaving ? 'Saving...' : 'Save'}
              </span>
            </button>

            {/* Download Button - Only on last step */}
            {isLastStep && (
              <button
                onClick={onDownload}
                className="flex items-center space-x-2 px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg font-medium transition-colors"
              >
                <Download className="h-4 w-4" />
                <span className="hidden sm:inline">Download</span>
              </button>
            )}

            {/* Next Button */}
            <button
              onClick={onNext}
              disabled={!canProceed}
              className={`
                flex items-center space-x-2 px-6 py-2 rounded-lg font-medium transition-all duration-200
                ${canProceed
                  ? 'bg-blue-600 hover:bg-blue-700 text-white shadow-sm'
                  : 'bg-gray-200 text-gray-400 cursor-not-allowed'
                }
              `}
            >
              <span>{isLastStep ? 'Complete' : 'Next'}</span>
              <ArrowRight className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SimplifiedNavigation;

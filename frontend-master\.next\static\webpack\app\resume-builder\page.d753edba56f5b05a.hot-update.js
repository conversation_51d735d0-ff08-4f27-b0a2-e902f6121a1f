"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/resume-builder/page",{

/***/ "(app-pages-browser)/./src/components/resume/StreamlinedPreview.jsx":
/*!******************************************************!*\
  !*** ./src/components/resume/StreamlinedPreview.jsx ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_FileText_Mail_MapPin_Maximize2_Phone_User_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,FileText,Mail,MapPin,Maximize2,Phone,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_FileText_Mail_MapPin_Maximize2_Phone_User_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,FileText,Mail,MapPin,Maximize2,Phone,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_FileText_Mail_MapPin_Maximize2_Phone_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,FileText,Mail,MapPin,Maximize2,Phone,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_FileText_Mail_MapPin_Maximize2_Phone_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,FileText,Mail,MapPin,Maximize2,Phone,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_FileText_Mail_MapPin_Maximize2_Phone_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,FileText,Mail,MapPin,Maximize2,Phone,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/maximize-2.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_FileText_Mail_MapPin_Maximize2_Phone_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,FileText,Mail,MapPin,Maximize2,Phone,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_FileText_Mail_MapPin_Maximize2_Phone_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,FileText,Mail,MapPin,Maximize2,Phone,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst StreamlinedPreview = (param)=>{\n    let { formData, selectedTemplate = 'professional', onOpenFullscreen } = param;\n    var _formData_personal, _formData_personal1, _formData_experience, _formData_education;\n    _s();\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Check if we have meaningful data to preview\n    const hasContent = formData && (((_formData_personal = formData.personal) === null || _formData_personal === void 0 ? void 0 : _formData_personal.firstName) || ((_formData_personal1 = formData.personal) === null || _formData_personal1 === void 0 ? void 0 : _formData_personal1.email) || ((_formData_experience = formData.experience) === null || _formData_experience === void 0 ? void 0 : _formData_experience.some((exp)=>exp.title || exp.company)) || ((_formData_education = formData.education) === null || _formData_education === void 0 ? void 0 : _formData_education.some((edu)=>edu.degree || edu.institution)));\n    const renderEmptyState = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center justify-center h-full text-gray-400 p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative mb-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_FileText_Mail_MapPin_Maximize2_Phone_User_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            className: \"h-12 w-12 opacity-50\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                            lineNumber: 24,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute -top-1 -right-1 w-4 h-4 bg-neural-pink rounded-full flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs\",\n                                children: \"✨\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                                lineNumber: 26,\n                                columnNumber: 11\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                            lineNumber: 25,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                    lineNumber: 23,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-sm font-medium mb-1 text-white\",\n                    children: \"AI Resume Preview\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                    lineNumber: 29,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-xs text-center opacity-75\",\n                    children: \"Watch your professional resume build in real-time as you type\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                    lineNumber: 30,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-xs text-center opacity-50 mt-1\",\n                    children: \"⚡ Powered by AI for instant results\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                    lineNumber: 33,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n            lineNumber: 22,\n            columnNumber: 5\n        }, undefined);\n    const renderPreviewContent = ()=>{\n        var _formData_personal, _formData_personal1, _formData_personal2, _formData_personal3, _formData_personal4, _formData_personal5, _formData_experience, _formData_education, _formData_skills_technical, _formData_skills, _formData_skills_languages, _formData_skills1, _formData_skills_technical1, _formData_skills_languages1;\n        if (!hasContent) return renderEmptyState();\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-4 space-y-4 text-sm\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center border-b border-gray-200 pb-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-lg font-bold text-gray-900 mb-2\",\n                            children: [\n                                ((_formData_personal = formData.personal) === null || _formData_personal === void 0 ? void 0 : _formData_personal.firstName) || 'Your Name',\n                                \" \",\n                                ((_formData_personal1 = formData.personal) === null || _formData_personal1 === void 0 ? void 0 : _formData_personal1.lastName) || ''\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                            lineNumber: 46,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap justify-center gap-3 text-xs text-gray-600\",\n                            children: [\n                                ((_formData_personal2 = formData.personal) === null || _formData_personal2 === void 0 ? void 0 : _formData_personal2.email) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_FileText_Mail_MapPin_Maximize2_Phone_User_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"h-3 w-3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                                            lineNumber: 53,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: formData.personal.email\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                                            lineNumber: 54,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                                    lineNumber: 52,\n                                    columnNumber: 15\n                                }, undefined),\n                                ((_formData_personal3 = formData.personal) === null || _formData_personal3 === void 0 ? void 0 : _formData_personal3.phone) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_FileText_Mail_MapPin_Maximize2_Phone_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"h-3 w-3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                                            lineNumber: 59,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: formData.personal.phone\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                                            lineNumber: 60,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 15\n                                }, undefined),\n                                ((_formData_personal4 = formData.personal) === null || _formData_personal4 === void 0 ? void 0 : _formData_personal4.location) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_FileText_Mail_MapPin_Maximize2_Phone_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"h-3 w-3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                                            lineNumber: 65,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: formData.personal.location\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                                            lineNumber: 66,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                            lineNumber: 50,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                    lineNumber: 45,\n                    columnNumber: 9\n                }, undefined),\n                ((_formData_personal5 = formData.personal) === null || _formData_personal5 === void 0 ? void 0 : _formData_personal5.summary) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-sm font-semibold text-gray-900 mb-2\",\n                            children: \"Professional Summary\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                            lineNumber: 75,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-700 leading-relaxed\",\n                            children: formData.personal.summary.length > 150 ? \"\".concat(formData.personal.summary.substring(0, 150), \"...\") : formData.personal.summary\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                            lineNumber: 76,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                    lineNumber: 74,\n                    columnNumber: 11\n                }, undefined),\n                ((_formData_experience = formData.experience) === null || _formData_experience === void 0 ? void 0 : _formData_experience.some((exp)=>exp.title || exp.company)) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-sm font-semibold text-gray-900 mb-2\",\n                            children: \"Experience\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                            lineNumber: 87,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: [\n                                formData.experience.filter((exp)=>exp.title || exp.company).slice(0, 2).map((exp, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border-l-2 border-blue-100 pl-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-start mb-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xs font-medium text-gray-900\",\n                                                        children: exp.title || 'Job Title'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                                                        lineNumber: 95,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: [\n                                                            exp.startDate,\n                                                            \" \",\n                                                            exp.startDate && (exp.endDate || exp.current) && ' - ',\n                                                            exp.current ? 'Present' : exp.endDate\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                                                        lineNumber: 98,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                                                lineNumber: 94,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-600 mb-1\",\n                                                children: [\n                                                    exp.company || 'Company Name',\n                                                    exp.location && \" • \".concat(exp.location)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                                                lineNumber: 103,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            exp.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-600\",\n                                                children: exp.description.length > 100 ? \"\".concat(exp.description.substring(0, 100), \"...\") : exp.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 19\n                                    }, undefined)),\n                                formData.experience.filter((exp)=>exp.title || exp.company).length > 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-500 italic pl-3\",\n                                    children: [\n                                        \"+\",\n                                        formData.experience.filter((exp)=>exp.title || exp.company).length - 2,\n                                        \" more positions\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                            lineNumber: 88,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                    lineNumber: 86,\n                    columnNumber: 11\n                }, undefined),\n                ((_formData_education = formData.education) === null || _formData_education === void 0 ? void 0 : _formData_education.some((edu)=>edu.degree || edu.institution)) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-sm font-semibold text-gray-900 mb-2\",\n                            children: \"Education\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                            lineNumber: 129,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: formData.education.filter((edu)=>edu.degree || edu.institution).slice(0, 2).map((edu, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-l-2 border-green-100 pl-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-start\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xs font-medium text-gray-900\",\n                                                        children: [\n                                                            edu.degree || 'Degree',\n                                                            \" \",\n                                                            edu.field && \"in \".concat(edu.field)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                                                        lineNumber: 138,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-600\",\n                                                        children: edu.institution || 'Institution'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                                                        lineNumber: 141,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: edu.graduationDate\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                                                lineNumber: 145,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 21\n                                    }, undefined)\n                                }, index, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 19\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                            lineNumber: 130,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                    lineNumber: 128,\n                    columnNumber: 11\n                }, undefined),\n                (((_formData_skills = formData.skills) === null || _formData_skills === void 0 ? void 0 : (_formData_skills_technical = _formData_skills.technical) === null || _formData_skills_technical === void 0 ? void 0 : _formData_skills_technical.length) > 0 || ((_formData_skills1 = formData.skills) === null || _formData_skills1 === void 0 ? void 0 : (_formData_skills_languages = _formData_skills1.languages) === null || _formData_skills_languages === void 0 ? void 0 : _formData_skills_languages.length) > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-sm font-semibold text-gray-900 mb-2\",\n                            children: \"Skills\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                            lineNumber: 158,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-1\",\n                            children: [\n                                ((_formData_skills_technical1 = formData.skills.technical) === null || _formData_skills_technical1 === void 0 ? void 0 : _formData_skills_technical1.length) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs font-medium text-gray-700\",\n                                            children: \"Technical: \"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-600\",\n                                            children: [\n                                                formData.skills.technical.slice(0, 8).join(', '),\n                                                formData.skills.technical.length > 8 && '...'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 17\n                                }, undefined),\n                                ((_formData_skills_languages1 = formData.skills.languages) === null || _formData_skills_languages1 === void 0 ? void 0 : _formData_skills_languages1.length) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs font-medium text-gray-700\",\n                                            children: \"Languages: \"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-600\",\n                                            children: formData.skills.languages.join(', ')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                                            lineNumber: 172,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                            lineNumber: 159,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                    lineNumber: 157,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n            lineNumber: 43,\n            columnNumber: 7\n        }, undefined);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gray-900/60 backdrop-blur-md border border-gray-700/50 rounded-2xl shadow-xl h-full flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between p-3 border-b border-gray-700/30 bg-gray-800/30\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 bg-neural-pink rounded-full animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                                lineNumber: 189,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-medium text-white\",\n                                children: \"Live Preview\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                                lineNumber: 190,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                        lineNumber: 188,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-1\",\n                        children: [\n                            isVisible && onOpenFullscreen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onOpenFullscreen,\n                                className: \"p-1.5 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors\",\n                                title: \"View Fullscreen\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_FileText_Mail_MapPin_Maximize2_Phone_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                                lineNumber: 195,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsVisible(!isVisible),\n                                className: \"p-1.5 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors\",\n                                title: isVisible ? 'Hide Preview' : 'Show Preview',\n                                children: isVisible ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_FileText_Mail_MapPin_Maximize2_Phone_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                                    lineNumber: 208,\n                                    columnNumber: 26\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_FileText_Mail_MapPin_Maximize2_Phone_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                                    lineNumber: 208,\n                                    columnNumber: 59\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                                lineNumber: 203,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                        lineNumber: 193,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                lineNumber: 187,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.AnimatePresence, {\n                    mode: \"wait\",\n                    children: isVisible ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 10\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        exit: {\n                            opacity: 0,\n                            y: -10\n                        },\n                        className: \"h-full overflow-y-auto\",\n                        children: renderPreviewContent()\n                    }, \"preview\", false, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                        lineNumber: 217,\n                        columnNumber: 13\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                        initial: {\n                            opacity: 0\n                        },\n                        animate: {\n                            opacity: 1\n                        },\n                        exit: {\n                            opacity: 0\n                        },\n                        className: \"flex items-center justify-center h-full text-gray-500\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_FileText_Mail_MapPin_Maximize2_Phone_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-8 w-8 mx-auto mb-2 opacity-50\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm\",\n                                    children: \"Preview Hidden\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                            lineNumber: 234,\n                            columnNumber: 15\n                        }, undefined)\n                    }, \"hidden\", false, {\n                        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                        lineNumber: 227,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                    lineNumber: 215,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                lineNumber: 214,\n                columnNumber: 7\n            }, undefined),\n            isVisible && hasContent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-2 border-t border-gray-700/30 bg-gray-800/30\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between text-xs text-gray-400\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"Updates automatically\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                            lineNumber: 247,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"capitalize\",\n                            children: selectedTemplate.replace('_', ' ')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                            lineNumber: 248,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                    lineNumber: 246,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n                lineNumber: 245,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\NewBlinkFindAI\\\\frontend-master\\\\src\\\\components\\\\resume\\\\StreamlinedPreview.jsx\",\n        lineNumber: 185,\n        columnNumber: 5\n    }, undefined);\n};\n_s(StreamlinedPreview, \"C45KFF5iQHXNkju7O/pllv86QL4=\");\n_c = StreamlinedPreview;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (StreamlinedPreview);\nvar _c;\n$RefreshReg$(_c, \"StreamlinedPreview\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/resume/StreamlinedPreview.jsx\n"));

/***/ })

});